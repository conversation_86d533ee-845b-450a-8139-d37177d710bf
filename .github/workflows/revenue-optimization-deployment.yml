name: Revenue Optimization Platform - Enhanced Production Deployment

on:
  push:
    branches: [main]
    paths:
      - 'services/**'
      - 'infrastructure/**'
      - '.github/workflows/revenue-optimization-deployment.yml'
  pull_request:
    branches: [main]
    paths:
      - 'services/**'
      - 'infrastructure/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      performance_validation:
        description: 'Run full performance validation'
        required: false
        default: true
        type: boolean

env:
  AWS_REGION: us-east-1
  EKS_CLUSTER_NAME: revenue-optimization-production
  ECR_REGISTRY: ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-1.amazonaws.com
  PERFORMANCE_TARGET_EVENTS_PER_SEC: 24390
  PERFORMANCE_TARGET_QUERY_MS: 11
  PERFORMANCE_TARGET_API_MS: 500

jobs:
  # Enhanced security scanning with revenue optimization focus
  enhanced-security-scan:
    name: Enhanced Security & Compliance
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' || github.event_name == 'workflow_dispatch'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run comprehensive security scan
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH,MEDIUM'

      - name: Upload security scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Check for sensitive data
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD

      - name: Validate environment secrets
        run: |
          echo "Validating required secrets for production deployment..."
          required_secrets=(
            "AWS_ACCESS_KEY_ID"
            "AWS_SECRET_ACCESS_KEY"
            "DATABASE_PASSWORD"
            "REDIS_AUTH_TOKEN"
            "JWT_SECRET"
            "ENCRYPTION_KEY"
          )
          
          for secret in "${required_secrets[@]}"; do
            if [[ -z "${!secret}" ]]; then
              echo "❌ Missing required secret: $secret"
              exit 1
            else
              echo "✅ Secret $secret is configured"
            fi
          done

  # Revenue optimization specific testing
  revenue-optimization-tests:
    name: Revenue Optimization Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        test-suite: [unit, integration, performance, frontend]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Deno
        uses: denoland/setup-deno@v1
        with:
          deno-version: v1.38.x

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/deno
          key: ${{ runner.os }}-deno-${{ hashFiles('**/deps.ts', '**/deno.lock') }}

      - name: Start test infrastructure
        if: matrix.test-suite == 'integration' || matrix.test-suite == 'performance'
        run: |
          docker-compose -f docker-compose.test.yml up -d
          sleep 30

      - name: Run unit tests
        if: matrix.test-suite == 'unit'
        run: |
          for service in analytics-deno dashboard-fresh billing-deno; do
            echo "Running unit tests for $service..."
            cd services/$service
            deno task test
            deno task test:coverage
            cd ../..
          done

      - name: Run integration tests
        if: matrix.test-suite == 'integration'
        run: |
          echo "Running revenue optimization integration tests..."
          deno run --allow-net --allow-env --allow-read \
            tests/revenue-optimization-integration-tests.ts

      - name: Run performance tests
        if: matrix.test-suite == 'performance'
        run: |
          echo "Running revenue optimization performance validation..."
          deno run --allow-net --allow-env --allow-read \
            tests/revenue-optimization-performance-validation.ts \
            --target-events-per-sec ${{ env.PERFORMANCE_TARGET_EVENTS_PER_SEC }} \
            --target-query-ms ${{ env.PERFORMANCE_TARGET_QUERY_MS }} \
            --target-api-ms ${{ env.PERFORMANCE_TARGET_API_MS }}

      - name: Run frontend integration tests
        if: matrix.test-suite == 'frontend'
        run: |
          echo "Running frontend integration tests..."
          deno run --allow-net --allow-env --allow-read \
            scripts/run-frontend-integration-tests.ts \
            --frontend-url http://localhost:8000 \
            --backend-url http://localhost:3001 \
            --verbose

      - name: Cleanup test infrastructure
        if: always() && (matrix.test-suite == 'integration' || matrix.test-suite == 'performance')
        run: docker-compose -f docker-compose.test.yml down

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results-${{ matrix.test-suite }}
          path: |
            test-results/
            coverage/
            performance-reports/

  # Enhanced container build with optimization
  build-optimized-images:
    name: Build Optimized Images
    runs-on: ubuntu-latest
    needs: [enhanced-security-scan, revenue-optimization-tests]
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    
    strategy:
      matrix:
        service: [analytics-deno, dashboard-fresh, billing-deno]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push optimized image
        uses: docker/build-push-action@v5
        with:
          context: services/${{ matrix.service }}
          platforms: linux/amd64
          push: true
          tags: |
            ${{ env.ECR_REGISTRY }}/revenue-optimization-${{ matrix.service }}:${{ github.sha }}
            ${{ env.ECR_REGISTRY }}/revenue-optimization-${{ matrix.service }}:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            BUILDKIT_INLINE_CACHE=1
            VERSION=${{ github.sha }}
            BUILD_DATE=${{ github.event.head_commit.timestamp }}
            OPTIMIZATION_LEVEL=production
            PERFORMANCE_TARGET_EVENTS_PER_SEC=${{ env.PERFORMANCE_TARGET_EVENTS_PER_SEC }}

      - name: Scan image for vulnerabilities
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.ECR_REGISTRY }}/revenue-optimization-${{ matrix.service }}:${{ github.sha }}
          format: 'sarif'
          output: 'trivy-image-${{ matrix.service }}.sarif'

      - name: Upload image scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-image-${{ matrix.service }}.sarif'

  # Infrastructure deployment with validation
  deploy-infrastructure:
    name: Deploy Infrastructure
    runs-on: ubuntu-latest
    needs: [build-optimized-images]
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    environment: ${{ github.event.inputs.environment || 'staging' }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.6.0

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Terraform Init
        working-directory: infrastructure/terraform
        run: terraform init

      - name: Terraform Plan
        working-directory: infrastructure/terraform
        run: |
          terraform plan \
            -var="environment=${{ github.event.inputs.environment || 'staging' }}" \
            -var="aws_region=${{ env.AWS_REGION }}" \
            -var="performance_target_events_per_sec=${{ env.PERFORMANCE_TARGET_EVENTS_PER_SEC }}" \
            -var="performance_target_query_ms=${{ env.PERFORMANCE_TARGET_QUERY_MS }}" \
            -out=tfplan

      - name: Terraform Apply
        working-directory: infrastructure/terraform
        run: terraform apply -auto-approve tfplan

      - name: Validate infrastructure
        run: |
          echo "Validating infrastructure deployment..."
          
          # Check EKS cluster status
          aws eks describe-cluster \
            --name ${{ env.EKS_CLUSTER_NAME }}-${{ github.event.inputs.environment || 'staging' }} \
            --region ${{ env.AWS_REGION }}
          
          # Check RDS instance status
          aws rds describe-db-instances \
            --db-instance-identifier revenue-optimization-${{ github.event.inputs.environment || 'staging' }}-timescaledb-production \
            --region ${{ env.AWS_REGION }}
          
          # Check ElastiCache cluster status
          aws elasticache describe-replication-groups \
            --replication-group-id revenue-optimization-${{ github.event.inputs.environment || 'staging' }}-redis-production \
            --region ${{ env.AWS_REGION }}

  # Application deployment with performance validation
  deploy-application:
    name: Deploy Application
    runs-on: ubuntu-latest
    needs: [deploy-infrastructure]
    environment: ${{ github.event.inputs.environment || 'staging' }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 1.28.0

      - name: Setup Helm
        uses: azure/setup-helm@v3
        with:
          version: 3.13.0

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig \
            --region ${{ env.AWS_REGION }} \
            --name ${{ env.EKS_CLUSTER_NAME }}-${{ github.event.inputs.environment || 'staging' }}

      - name: Deploy Revenue Optimization Platform
        run: |
          helm upgrade --install revenue-optimization \
            ./helm/revenue-optimization \
            --namespace revenue-optimization \
            --create-namespace \
            --set image.tag=${{ github.sha }} \
            --set environment=${{ github.event.inputs.environment || 'staging' }} \
            --set performance.targetEventsPerSec=${{ env.PERFORMANCE_TARGET_EVENTS_PER_SEC }} \
            --set performance.targetQueryMs=${{ env.PERFORMANCE_TARGET_QUERY_MS }} \
            --set performance.targetApiMs=${{ env.PERFORMANCE_TARGET_API_MS }} \
            --values ./helm/revenue-optimization/values-${{ github.event.inputs.environment || 'staging' }}.yaml \
            --wait \
            --timeout=15m

      - name: Wait for deployment readiness
        run: |
          kubectl wait --for=condition=ready pod \
            -l app.kubernetes.io/name=revenue-optimization \
            -n revenue-optimization \
            --timeout=600s

      - name: Run comprehensive health checks
        run: |
          echo "Running comprehensive health checks..."
          
          # Check all services are running
          kubectl get pods -n revenue-optimization
          kubectl get services -n revenue-optimization
          kubectl get ingress -n revenue-optimization
          
          # Run application health checks
          kubectl exec -n revenue-optimization \
            deployment/analytics-service -- \
            deno run --allow-net scripts/comprehensive-health-check.ts

      - name: Run performance validation
        if: github.event.inputs.performance_validation == 'true' || github.event.inputs.performance_validation == ''
        run: |
          echo "Running performance validation..."
          
          # Get service endpoint
          ENDPOINT=$(kubectl get ingress -n revenue-optimization revenue-optimization -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
          
          # Run performance tests
          deno run --allow-net --allow-env \
            scripts/run-revenue-optimization-tests.ts \
            --base-url https://$ENDPOINT \
            --auth-token ${{ secrets.API_TOKEN }} \
            --tenant-id ${{ secrets.TENANT_ID }} \
            --target-events-per-sec ${{ env.PERFORMANCE_TARGET_EVENTS_PER_SEC }} \
            --target-query-ms ${{ env.PERFORMANCE_TARGET_QUERY_MS }} \
            --target-api-ms ${{ env.PERFORMANCE_TARGET_API_MS }} \
            --verbose

      - name: Generate deployment report
        run: |
          echo "Generating deployment report..."
          
          cat > deployment-report.md << EOF
          # Revenue Optimization Platform Deployment Report
          
          ## Deployment Details
          - **Environment**: ${{ github.event.inputs.environment || 'staging' }}
          - **Version**: ${{ github.sha }}
          - **Timestamp**: $(date -u)
          - **Cluster**: ${{ env.EKS_CLUSTER_NAME }}-${{ github.event.inputs.environment || 'staging' }}
          
          ## Performance Targets
          - **Events per Second**: ${{ env.PERFORMANCE_TARGET_EVENTS_PER_SEC }}
          - **Query Response Time**: ${{ env.PERFORMANCE_TARGET_QUERY_MS }}ms
          - **API Response Time**: ${{ env.PERFORMANCE_TARGET_API_MS }}ms
          
          ## Deployment Status
          $(kubectl get pods -n revenue-optimization -o wide)
          
          ## Service Status
          $(kubectl get services -n revenue-optimization)
          
          ## Resource Usage
          $(kubectl top pods -n revenue-optimization)
          EOF

      - name: Upload deployment report
        uses: actions/upload-artifact@v3
        with:
          name: deployment-report-${{ github.event.inputs.environment || 'staging' }}
          path: deployment-report.md

      - name: Notify deployment success
        if: success()
        run: |
          echo "🚀 Revenue Optimization Platform deployed successfully!"
          echo "Environment: ${{ github.event.inputs.environment || 'staging' }}"
          echo "Version: ${{ github.sha }}"
          echo "Performance targets validated: ✅"

      - name: Notify deployment failure
        if: failure()
        run: |
          echo "❌ Revenue Optimization Platform deployment failed!"
          echo "Environment: ${{ github.event.inputs.environment || 'staging' }}"
          echo "Version: ${{ github.sha }}"
          echo "Check logs for details"
