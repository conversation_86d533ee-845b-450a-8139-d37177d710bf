/**
 * Feedback Collection & Analysis Service
 * Systematic feedback collection with real-time analysis and actionable insights
 * 
 * Features:
 * - Multi-channel feedback collection (surveys, interviews, usage data)
 * - Real-time sentiment analysis and categorization
 * - Automated insight generation and trend detection
 * - Actionable recommendations for platform improvement
 * - Integration with product development workflows
 */

import { Pool } from "https://deno.land/x/postgres@v0.17.0/mod.ts";
import { Redis } from "https://deno.land/x/redis@v0.29.0/mod.ts";
import { logger } from "../utils/logger.ts";

export interface FeedbackItem {
  feedbackId: string;
  customerId: string;
  source: "survey" | "interview" | "support_ticket" | "usage_analytics" | "in_app" | "email";
  type: "feature_request" | "bug_report" | "improvement" | "praise" | "complaint" | "question";
  category: "performance" | "usability" | "features" | "support" | "onboarding" | "pricing" | "integration";
  content: string;
  sentiment: "positive" | "neutral" | "negative";
  priority: "low" | "medium" | "high" | "critical";
  status: "new" | "reviewed" | "in_progress" | "resolved" | "closed";
  submittedAt: Date;
  metadata: any;
}

export interface FeedbackAnalysis {
  analysisId: string;
  timeframe: "daily" | "weekly" | "monthly" | "quarterly";
  generatedAt: Date;
  
  // Volume Metrics
  totalFeedback: number;
  feedbackBySource: Record<string, number>;
  feedbackByType: Record<string, number>;
  feedbackByCategory: Record<string, number>;
  
  // Sentiment Analysis
  sentimentDistribution: Record<string, number>;
  sentimentTrend: "improving" | "stable" | "declining";
  
  // Key Insights
  topIssues: FeedbackInsight[];
  emergingTrends: FeedbackInsight[];
  customerRequests: FeedbackInsight[];
  
  // Actionable Recommendations
  recommendations: ActionableRecommendation[];
  priorityActions: string[];
}

export interface FeedbackInsight {
  insightId: string;
  title: string;
  description: string;
  category: string;
  frequency: number;
  impact: "low" | "medium" | "high" | "critical";
  affectedCustomers: string[];
  relatedFeedback: string[];
  trend: "increasing" | "stable" | "decreasing";
}

export interface ActionableRecommendation {
  recommendationId: string;
  title: string;
  description: string;
  category: "product" | "process" | "support" | "documentation" | "training";
  priority: "low" | "medium" | "high" | "urgent";
  effort: "low" | "medium" | "high";
  impact: "low" | "medium" | "high";
  timeline: string;
  assignedTeam: string;
  relatedInsights: string[];
}

export interface CustomerFeedbackProfile {
  customerId: string;
  companyName: string;
  feedbackHistory: {
    totalSubmissions: number;
    averageSentiment: number;
    lastSubmission: Date;
    responseRate: number;
  };
  preferences: {
    preferredChannels: string[];
    feedbackFrequency: string;
    topicInterests: string[];
  };
  engagement: {
    surveyParticipation: number;
    interviewParticipation: number;
    featureRequestSubmissions: number;
  };
}

export class FeedbackAnalysisService {
  private pool: Pool;
  private redis: Redis;

  constructor(databaseUrl: string, redisUrl: string) {
    this.pool = new Pool(databaseUrl, 10, true);
    this.redis = new Redis(redisUrl);
  }

  /**
   * Collect feedback from multiple sources
   */
  async collectFeedback(feedback: Omit<FeedbackItem, 'feedbackId' | 'submittedAt'>): Promise<FeedbackItem> {
    logger.info(`Collecting feedback from customer: ${feedback.customerId} via ${feedback.source}`);

    const feedbackItem: FeedbackItem = {
      ...feedback,
      feedbackId: `feedback_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
      submittedAt: new Date()
    };

    // Analyze sentiment if not provided
    if (!feedbackItem.sentiment) {
      feedbackItem.sentiment = await this.analyzeSentiment(feedbackItem.content);
    }

    // Categorize feedback if not provided
    if (!feedbackItem.category) {
      feedbackItem.category = await this.categorizeFeedback(feedbackItem.content);
    }

    // Determine priority based on content and sentiment
    if (!feedbackItem.priority) {
      feedbackItem.priority = await this.determinePriority(feedbackItem);
    }

    // Store feedback
    await this.storeFeedback(feedbackItem);

    // Trigger real-time analysis
    await this.triggerRealTimeAnalysis(feedbackItem);

    logger.info(`Feedback collected and analyzed: ${feedbackItem.feedbackId}`);
    return feedbackItem;
  }

  /**
   * Analyze feedback sentiment using NLP
   */
  async analyzeSentiment(content: string): Promise<"positive" | "neutral" | "negative"> {
    // Simple sentiment analysis - in production, use advanced NLP services
    const positiveWords = ["great", "excellent", "love", "amazing", "fantastic", "helpful", "easy", "fast"];
    const negativeWords = ["terrible", "awful", "hate", "slow", "difficult", "confusing", "broken", "frustrated"];
    
    const words = content.toLowerCase().split(/\s+/);
    let positiveScore = 0;
    let negativeScore = 0;

    for (const word of words) {
      if (positiveWords.includes(word)) positiveScore++;
      if (negativeWords.includes(word)) negativeScore++;
    }

    if (positiveScore > negativeScore) return "positive";
    if (negativeScore > positiveScore) return "negative";
    return "neutral";
  }

  /**
   * Categorize feedback using keyword analysis
   */
  async categorizeFeedback(content: string): Promise<string> {
    const categories = {
      performance: ["slow", "fast", "speed", "latency", "response", "performance"],
      usability: ["difficult", "easy", "confusing", "intuitive", "user", "interface"],
      features: ["feature", "functionality", "capability", "tool", "option"],
      support: ["help", "support", "assistance", "documentation", "guide"],
      onboarding: ["setup", "onboarding", "getting started", "initial", "first"],
      pricing: ["price", "cost", "expensive", "cheap", "value", "billing"],
      integration: ["integration", "connect", "sync", "api", "platform"]
    };

    const words = content.toLowerCase().split(/\s+/);
    const scores: Record<string, number> = {};

    for (const [category, keywords] of Object.entries(categories)) {
      scores[category] = 0;
      for (const word of words) {
        if (keywords.some(keyword => word.includes(keyword))) {
          scores[category]++;
        }
      }
    }

    // Return category with highest score
    const topCategory = Object.entries(scores).reduce((a, b) => scores[a[0]] > scores[b[0]] ? a : b);
    return topCategory[0];
  }

  /**
   * Determine feedback priority
   */
  async determinePriority(feedback: FeedbackItem): Promise<"low" | "medium" | "high" | "critical"> {
    let priority: "low" | "medium" | "high" | "critical" = "low";

    // High priority for negative sentiment
    if (feedback.sentiment === "negative") {
      priority = "high";
    }

    // Critical priority for bug reports with negative sentiment
    if (feedback.type === "bug_report" && feedback.sentiment === "negative") {
      priority = "critical";
    }

    // Medium priority for feature requests
    if (feedback.type === "feature_request") {
      priority = "medium";
    }

    // High priority for performance issues
    if (feedback.category === "performance" && feedback.sentiment === "negative") {
      priority = "high";
    }

    return priority;
  }

  /**
   * Generate comprehensive feedback analysis
   */
  async generateFeedbackAnalysis(timeframe: "daily" | "weekly" | "monthly" | "quarterly"): Promise<FeedbackAnalysis> {
    logger.info(`Generating feedback analysis for timeframe: ${timeframe}`);

    const startDate = this.getTimeframeStartDate(timeframe);
    const feedback = await this.getFeedbackInTimeframe(startDate);

    // Volume metrics
    const totalFeedback = feedback.length;
    const feedbackBySource = this.groupBy(feedback, 'source');
    const feedbackByType = this.groupBy(feedback, 'type');
    const feedbackByCategory = this.groupBy(feedback, 'category');

    // Sentiment analysis
    const sentimentDistribution = this.groupBy(feedback, 'sentiment');
    const sentimentTrend = await this.calculateSentimentTrend(timeframe);

    // Generate insights
    const topIssues = await this.identifyTopIssues(feedback);
    const emergingTrends = await this.identifyEmergingTrends(feedback);
    const customerRequests = await this.identifyCustomerRequests(feedback);

    // Generate recommendations
    const recommendations = await this.generateRecommendations(feedback, topIssues, emergingTrends);
    const priorityActions = this.extractPriorityActions(recommendations);

    const analysis: FeedbackAnalysis = {
      analysisId: `analysis_${timeframe}_${Date.now()}`,
      timeframe,
      generatedAt: new Date(),
      totalFeedback,
      feedbackBySource,
      feedbackByType,
      feedbackByCategory,
      sentimentDistribution,
      sentimentTrend,
      topIssues,
      emergingTrends,
      customerRequests,
      recommendations,
      priorityActions
    };

    // Store analysis
    await this.storeAnalysis(analysis);

    logger.info(`Feedback analysis generated: ${analysis.analysisId}`);
    return analysis;
  }

  /**
   * Identify top issues from feedback
   */
  async identifyTopIssues(feedback: FeedbackItem[]): Promise<FeedbackInsight[]> {
    const issues: Record<string, any> = {};

    // Group negative feedback by category and content similarity
    const negativeFeeback = feedback.filter(f => f.sentiment === "negative");
    
    for (const item of negativeFeeback) {
      const key = `${item.category}_${item.type}`;
      if (!issues[key]) {
        issues[key] = {
          insightId: `issue_${key}_${Date.now()}`,
          title: `${item.category} ${item.type}`,
          description: `Issues related to ${item.category}`,
          category: item.category,
          frequency: 0,
          impact: "medium",
          affectedCustomers: new Set(),
          relatedFeedback: [],
          trend: "stable"
        };
      }
      
      issues[key].frequency++;
      issues[key].affectedCustomers.add(item.customerId);
      issues[key].relatedFeedback.push(item.feedbackId);
    }

    // Convert to array and sort by frequency
    return Object.values(issues)
      .map(issue => ({
        ...issue,
        affectedCustomers: Array.from(issue.affectedCustomers),
        impact: issue.frequency > 5 ? "high" : issue.frequency > 2 ? "medium" : "low"
      }))
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, 10);
  }

  /**
   * Identify emerging trends
   */
  async identifyEmergingTrends(feedback: FeedbackItem[]): Promise<FeedbackInsight[]> {
    // Compare current period with previous period to identify trends
    const trends: FeedbackInsight[] = [];

    // Group feedback by week to identify trends
    const weeklyGroups = this.groupFeedbackByWeek(feedback);
    const categories = [...new Set(feedback.map(f => f.category))];

    for (const category of categories) {
      const weeklyData = weeklyGroups.map(week => 
        week.filter(f => f.category === category).length
      );

      // Simple trend detection - increasing frequency
      if (weeklyData.length >= 2) {
        const recent = weeklyData.slice(-2);
        if (recent[1] > recent[0] * 1.5) { // 50% increase
          trends.push({
            insightId: `trend_${category}_${Date.now()}`,
            title: `Increasing ${category} feedback`,
            description: `${category} feedback has increased significantly`,
            category,
            frequency: recent[1],
            impact: "medium",
            affectedCustomers: [],
            relatedFeedback: [],
            trend: "increasing"
          });
        }
      }
    }

    return trends;
  }

  /**
   * Identify customer requests and feature demands
   */
  async identifyCustomerRequests(feedback: FeedbackItem[]): Promise<FeedbackInsight[]> {
    const requests = feedback.filter(f => f.type === "feature_request");
    const requestGroups: Record<string, any> = {};

    for (const request of requests) {
      const key = request.category;
      if (!requestGroups[key]) {
        requestGroups[key] = {
          insightId: `request_${key}_${Date.now()}`,
          title: `${key} feature requests`,
          description: `Customer requests for ${key} improvements`,
          category: key,
          frequency: 0,
          impact: "medium",
          affectedCustomers: new Set(),
          relatedFeedback: [],
          trend: "stable"
        };
      }
      
      requestGroups[key].frequency++;
      requestGroups[key].affectedCustomers.add(request.customerId);
      requestGroups[key].relatedFeedback.push(request.feedbackId);
    }

    return Object.values(requestGroups)
      .map(request => ({
        ...request,
        affectedCustomers: Array.from(request.affectedCustomers)
      }))
      .sort((a, b) => b.frequency - a.frequency);
  }

  /**
   * Generate actionable recommendations
   */
  async generateRecommendations(
    feedback: FeedbackItem[], 
    topIssues: FeedbackInsight[], 
    trends: FeedbackInsight[]
  ): Promise<ActionableRecommendation[]> {
    const recommendations: ActionableRecommendation[] = [];

    // Recommendations for top issues
    for (const issue of topIssues.slice(0, 5)) {
      if (issue.impact === "high" || issue.frequency > 3) {
        recommendations.push({
          recommendationId: `rec_issue_${issue.insightId}`,
          title: `Address ${issue.title}`,
          description: `Prioritize fixing ${issue.description} affecting ${issue.affectedCustomers.length} customers`,
          category: "product",
          priority: issue.impact === "high" ? "urgent" : "high",
          effort: "medium",
          impact: "high",
          timeline: "2-4 weeks",
          assignedTeam: "product_team",
          relatedInsights: [issue.insightId]
        });
      }
    }

    // Recommendations for emerging trends
    for (const trend of trends) {
      if (trend.trend === "increasing") {
        recommendations.push({
          recommendationId: `rec_trend_${trend.insightId}`,
          title: `Monitor ${trend.title}`,
          description: `Investigate and address the increasing ${trend.category} concerns`,
          category: "process",
          priority: "medium",
          effort: "low",
          impact: "medium",
          timeline: "1-2 weeks",
          assignedTeam: "customer_success_team",
          relatedInsights: [trend.insightId]
        });
      }
    }

    // Performance-specific recommendations
    const performanceIssues = feedback.filter(f => 
      f.category === "performance" && f.sentiment === "negative"
    );
    
    if (performanceIssues.length > 2) {
      recommendations.push({
        recommendationId: `rec_performance_${Date.now()}`,
        title: "Performance Optimization Initiative",
        description: "Launch comprehensive performance optimization based on customer feedback",
        category: "product",
        priority: "high",
        effort: "high",
        impact: "high",
        timeline: "4-6 weeks",
        assignedTeam: "engineering_team",
        relatedInsights: []
      });
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Create customer feedback profile
   */
  async createCustomerFeedbackProfile(customerId: string): Promise<CustomerFeedbackProfile> {
    const customerFeedback = await this.getCustomerFeedback(customerId);
    const companyName = await this.getCompanyName(customerId);

    const profile: CustomerFeedbackProfile = {
      customerId,
      companyName,
      feedbackHistory: {
        totalSubmissions: customerFeedback.length,
        averageSentiment: this.calculateAverageSentiment(customerFeedback),
        lastSubmission: customerFeedback.length > 0 ? 
          new Date(Math.max(...customerFeedback.map(f => f.submittedAt.getTime()))) : 
          new Date(),
        responseRate: await this.calculateResponseRate(customerId)
      },
      preferences: {
        preferredChannels: this.identifyPreferredChannels(customerFeedback),
        feedbackFrequency: this.calculateFeedbackFrequency(customerFeedback),
        topicInterests: this.identifyTopicInterests(customerFeedback)
      },
      engagement: {
        surveyParticipation: await this.getSurveyParticipation(customerId),
        interviewParticipation: await this.getInterviewParticipation(customerId),
        featureRequestSubmissions: customerFeedback.filter(f => f.type === "feature_request").length
      }
    };

    await this.storeCustomerProfile(profile);
    return profile;
  }

  // Helper methods
  private getTimeframeStartDate(timeframe: string): Date {
    const now = new Date();
    switch (timeframe) {
      case "daily": return new Date(now.getTime() - 24 * 60 * 60 * 1000);
      case "weekly": return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case "monthly": return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      case "quarterly": return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      default: return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }
  }

  private groupBy(items: any[], key: string): Record<string, number> {
    return items.reduce((groups, item) => {
      const value = item[key];
      groups[value] = (groups[value] || 0) + 1;
      return groups;
    }, {});
  }

  private groupFeedbackByWeek(feedback: FeedbackItem[]): FeedbackItem[][] {
    // Group feedback by week for trend analysis
    const weeks: FeedbackItem[][] = [];
    const sortedFeedback = feedback.sort((a, b) => a.submittedAt.getTime() - b.submittedAt.getTime());
    
    // Simple weekly grouping implementation
    let currentWeek: FeedbackItem[] = [];
    let currentWeekStart = 0;
    
    for (const item of sortedFeedback) {
      const weekNumber = Math.floor(item.submittedAt.getTime() / (7 * 24 * 60 * 60 * 1000));
      if (weekNumber !== currentWeekStart) {
        if (currentWeek.length > 0) {
          weeks.push(currentWeek);
        }
        currentWeek = [item];
        currentWeekStart = weekNumber;
      } else {
        currentWeek.push(item);
      }
    }
    
    if (currentWeek.length > 0) {
      weeks.push(currentWeek);
    }
    
    return weeks;
  }

  private extractPriorityActions(recommendations: ActionableRecommendation[]): string[] {
    return recommendations
      .filter(r => r.priority === "urgent" || r.priority === "high")
      .slice(0, 5)
      .map(r => r.title);
  }

  private calculateAverageSentiment(feedback: FeedbackItem[]): number {
    if (feedback.length === 0) return 0;
    
    const sentimentScores = feedback.map(f => {
      switch (f.sentiment) {
        case "positive": return 1;
        case "neutral": return 0;
        case "negative": return -1;
        default: return 0;
      }
    });
    
    return sentimentScores.reduce((sum, score) => sum + score, 0) / sentimentScores.length;
  }

  private identifyPreferredChannels(feedback: FeedbackItem[]): string[] {
    const channelCounts = this.groupBy(feedback, 'source');
    return Object.entries(channelCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([channel]) => channel);
  }

  private calculateFeedbackFrequency(feedback: FeedbackItem[]): string {
    if (feedback.length === 0) return "none";
    
    const daysSinceFirst = (Date.now() - Math.min(...feedback.map(f => f.submittedAt.getTime()))) / (24 * 60 * 60 * 1000);
    const frequency = feedback.length / daysSinceFirst;
    
    if (frequency > 0.5) return "high";
    if (frequency > 0.1) return "medium";
    return "low";
  }

  private identifyTopicInterests(feedback: FeedbackItem[]): string[] {
    const categoryCounts = this.groupBy(feedback, 'category');
    return Object.entries(categoryCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([category]) => category);
  }

  // Database operations (simplified implementations)
  private async storeFeedback(feedback: FeedbackItem): Promise<void> {
    // Implementation to store feedback in database
  }

  private async triggerRealTimeAnalysis(feedback: FeedbackItem): Promise<void> {
    // Implementation to trigger real-time analysis
  }

  private async getFeedbackInTimeframe(startDate: Date): Promise<FeedbackItem[]> {
    // Implementation to get feedback from database
    return [];
  }

  private async calculateSentimentTrend(timeframe: string): Promise<"improving" | "stable" | "declining"> {
    // Implementation to calculate sentiment trend
    return "stable";
  }

  private async storeAnalysis(analysis: FeedbackAnalysis): Promise<void> {
    // Implementation to store analysis
  }

  private async getCustomerFeedback(customerId: string): Promise<FeedbackItem[]> {
    // Implementation to get customer feedback
    return [];
  }

  private async getCompanyName(customerId: string): Promise<string> {
    // Implementation to get company name
    return "Customer Company";
  }

  private async calculateResponseRate(customerId: string): Promise<number> {
    // Implementation to calculate response rate
    return 0.8;
  }

  private async getSurveyParticipation(customerId: string): Promise<number> {
    // Implementation to get survey participation
    return 0.9;
  }

  private async getInterviewParticipation(customerId: string): Promise<number> {
    // Implementation to get interview participation
    return 0.7;
  }

  private async storeCustomerProfile(profile: CustomerFeedbackProfile): Promise<void> {
    // Implementation to store customer profile
  }

  /**
   * Cleanup resources
   */
  async close(): Promise<void> {
    await this.pool.end();
    this.redis.close();
  }
}
