/**
 * Beta Customer Selection & Qualification Service
 * Systematic process for identifying and qualifying Tier 2+ beta customers
 * 
 * Features:
 * - Customer scoring and qualification
 * - Automated outreach campaigns
 * - Success criteria validation
 * - Onboarding workflow management
 */

import { Pool } from "https://deno.land/x/postgres@v0.17.0/mod.ts";
import { Redis } from "https://deno.land/x/redis@v0.29.0/mod.ts";
import { logger } from "../utils/logger.ts";

export interface CustomerProfile {
  customerId: string;
  companyName: string;
  website: string;
  industry: string;
  annualRevenue: number;
  monthlyTransactions: number;
  currentAnalytics: string[];
  painPoints: string[];
  decisionMakers: ContactInfo[];
  growthStage: "startup" | "scaling" | "enterprise";
  tier: "tier1" | "tier2" | "tier3";
}

export interface ContactInfo {
  name: string;
  title: string;
  email: string;
  phone?: string;
  linkedIn?: string;
}

export interface QualificationCriteria {
  minAnnualRevenue: number;
  minMonthlyTransactions: number;
  requiredIndustries: string[];
  excludedIndustries: string[];
  requiredGrowthStages: string[];
  minDecisionMakerLevel: string[];
}

export interface QualificationResult {
  customerId: string;
  score: number;
  tier: string;
  qualified: boolean;
  reasons: string[];
  recommendations: string[];
  nextSteps: string[];
}

export interface OnboardingWorkflow {
  customerId: string;
  workflowId: string;
  status: "initiated" | "in_progress" | "completed" | "failed";
  currentStep: string;
  completedSteps: string[];
  estimatedCompletion: Date;
  actualCompletion?: Date;
  successCriteria: SuccessCriteria;
}

export interface SuccessCriteria {
  performanceTargets: {
    queryResponseTime: number;
    eventThroughput: number;
    onboardingTime: number;
    systemUptime: number;
  };
  businessTargets: {
    dataAccuracy: number;
    userAdoption: number;
    timeToValue: number;
    satisfactionScore: number;
  };
  technicalTargets: {
    integrationSuccess: boolean;
    dataFlowValidation: boolean;
    performanceBenchmark: boolean;
    securityCompliance: boolean;
  };
}

export class CustomerSelectionService {
  private pool: Pool;
  private redis: Redis;
  private qualificationCriteria: QualificationCriteria;

  constructor(databaseUrl: string, redisUrl: string) {
    this.pool = new Pool(databaseUrl, 10, true);
    this.redis = new Redis(redisUrl);
    this.initializeQualificationCriteria();
  }

  /**
   * Initialize qualification criteria for Tier 2+ customers
   */
  private initializeQualificationCriteria(): void {
    this.qualificationCriteria = {
      minAnnualRevenue: 5000000, // $5M minimum
      minMonthlyTransactions: 10000, // 10K transactions/month
      requiredIndustries: [
        "ecommerce",
        "retail",
        "fashion",
        "electronics",
        "home_garden",
        "health_beauty",
        "sports_outdoors",
        "automotive"
      ],
      excludedIndustries: [
        "gambling",
        "adult_content",
        "cryptocurrency",
        "tobacco"
      ],
      requiredGrowthStages: ["scaling", "enterprise"],
      minDecisionMakerLevel: ["cmo", "vp_marketing", "head_of_growth", "ceo", "founder"]
    };
  }

  /**
   * Identify potential beta customers from various sources
   */
  async identifyPotentialCustomers(): Promise<CustomerProfile[]> {
    logger.info("Identifying potential beta customers...");

    const customers: CustomerProfile[] = [];

    // Source 1: Existing leads and prospects
    const existingLeads = await this.getExistingLeads();
    customers.push(...existingLeads);

    // Source 2: Industry databases and directories
    const industryProspects = await this.getIndustryProspects();
    customers.push(...industryProspects);

    // Source 3: Competitor analysis and market research
    const competitorAnalysis = await this.getCompetitorCustomers();
    customers.push(...competitorAnalysis);

    // Source 4: Partner referrals and network
    const partnerReferrals = await this.getPartnerReferrals();
    customers.push(...partnerReferrals);

    logger.info(`Identified ${customers.length} potential beta customers`);
    return customers;
  }

  /**
   * Qualify customer based on established criteria
   */
  async qualifyCustomer(customer: CustomerProfile): Promise<QualificationResult> {
    logger.info(`Qualifying customer: ${customer.companyName}`);

    const result: QualificationResult = {
      customerId: customer.customerId,
      score: 0,
      tier: customer.tier,
      qualified: false,
      reasons: [],
      recommendations: [],
      nextSteps: []
    };

    // Revenue qualification (30 points)
    if (customer.annualRevenue >= this.qualificationCriteria.minAnnualRevenue) {
      const revenueScore = Math.min(30, (customer.annualRevenue / 10000000) * 30);
      result.score += revenueScore;
      result.reasons.push(`Strong revenue: $${(customer.annualRevenue / 1000000).toFixed(1)}M`);
    } else {
      result.reasons.push(`Revenue below threshold: $${(customer.annualRevenue / 1000000).toFixed(1)}M < $5M`);
    }

    // Transaction volume qualification (25 points)
    if (customer.monthlyTransactions >= this.qualificationCriteria.minMonthlyTransactions) {
      const transactionScore = Math.min(25, (customer.monthlyTransactions / 100000) * 25);
      result.score += transactionScore;
      result.reasons.push(`High transaction volume: ${customer.monthlyTransactions.toLocaleString()}/month`);
    } else {
      result.reasons.push(`Transaction volume below threshold: ${customer.monthlyTransactions.toLocaleString()} < 10K`);
    }

    // Industry fit qualification (20 points)
    if (this.qualificationCriteria.requiredIndustries.includes(customer.industry)) {
      result.score += 20;
      result.reasons.push(`Target industry: ${customer.industry}`);
    } else if (this.qualificationCriteria.excludedIndustries.includes(customer.industry)) {
      result.score -= 10;
      result.reasons.push(`Excluded industry: ${customer.industry}`);
    }

    // Growth stage qualification (15 points)
    if (this.qualificationCriteria.requiredGrowthStages.includes(customer.growthStage)) {
      result.score += 15;
      result.reasons.push(`Optimal growth stage: ${customer.growthStage}`);
    } else {
      result.reasons.push(`Suboptimal growth stage: ${customer.growthStage}`);
    }

    // Decision maker qualification (10 points)
    const qualifiedDecisionMakers = customer.decisionMakers.filter(dm =>
      this.qualificationCriteria.minDecisionMakerLevel.some(level =>
        dm.title.toLowerCase().includes(level.replace('_', ' '))
      )
    );

    if (qualifiedDecisionMakers.length > 0) {
      result.score += 10;
      result.reasons.push(`Qualified decision makers: ${qualifiedDecisionMakers.length}`);
    } else {
      result.reasons.push("No qualified decision makers identified");
    }

    // Determine qualification status
    result.qualified = result.score >= 70; // 70% threshold

    // Generate recommendations
    if (result.qualified) {
      result.recommendations.push("Excellent beta customer candidate");
      result.recommendations.push("Prioritize for immediate outreach");
      result.nextSteps.push("Schedule demo call within 48 hours");
      result.nextSteps.push("Prepare customized value proposition");
    } else if (result.score >= 50) {
      result.recommendations.push("Potential candidate with reservations");
      result.recommendations.push("Consider for secondary outreach");
      result.nextSteps.push("Gather additional qualification data");
      result.nextSteps.push("Nurture with educational content");
    } else {
      result.recommendations.push("Not suitable for beta program");
      result.recommendations.push("Consider for future programs");
      result.nextSteps.push("Add to general nurture campaign");
    }

    // Store qualification result
    await this.storeQualificationResult(result);

    logger.info(`Customer qualification completed: ${customer.companyName} - Score: ${result.score}, Qualified: ${result.qualified}`);
    return result;
  }

  /**
   * Initiate onboarding workflow for qualified customers
   */
  async initiateOnboarding(customerId: string): Promise<OnboardingWorkflow> {
    logger.info(`Initiating onboarding workflow for customer: ${customerId}`);

    const workflowId = `onboarding_${customerId}_${Date.now()}`;
    
    const workflow: OnboardingWorkflow = {
      customerId,
      workflowId,
      status: "initiated",
      currentStep: "initial_contact",
      completedSteps: [],
      estimatedCompletion: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      successCriteria: this.getDefaultSuccessCriteria()
    };

    // Store workflow
    await this.storeOnboardingWorkflow(workflow);

    // Trigger initial contact
    await this.triggerInitialContact(customerId);

    logger.info(`Onboarding workflow initiated: ${workflowId}`);
    return workflow;
  }

  /**
   * Get default success criteria for beta customers
   */
  private getDefaultSuccessCriteria(): SuccessCriteria {
    return {
      performanceTargets: {
        queryResponseTime: 11, // <11ms
        eventThroughput: 24390, // 24,390 events/sec
        onboardingTime: 900, // <15 minutes
        systemUptime: 99.9 // 99.9%
      },
      businessTargets: {
        dataAccuracy: 99.5, // 99.5%
        userAdoption: 80, // 80% team adoption
        timeToValue: 15, // 15 minutes
        satisfactionScore: 4.5 // 4.5/5.0
      },
      technicalTargets: {
        integrationSuccess: true,
        dataFlowValidation: true,
        performanceBenchmark: true,
        securityCompliance: true
      }
    };
  }

  /**
   * Get existing leads from CRM and database
   */
  private async getExistingLeads(): Promise<CustomerProfile[]> {
    const client = await this.pool.connect();
    
    try {
      const result = await client.queryObject(`
        SELECT 
          customer_id,
          company_name,
          website,
          industry,
          annual_revenue,
          monthly_transactions,
          current_analytics,
          pain_points,
          decision_makers,
          growth_stage,
          tier
        FROM potential_customers
        WHERE status = 'lead'
        AND tier IN ('tier2', 'tier3')
        ORDER BY annual_revenue DESC
      `);

      return result.rows.map(row => this.mapRowToCustomerProfile(row));
    } finally {
      client.release();
    }
  }

  /**
   * Get prospects from industry databases
   */
  private async getIndustryProspects(): Promise<CustomerProfile[]> {
    // Mock implementation - in production, integrate with industry databases
    return [
      {
        customerId: "prospect_001",
        companyName: "TechStyle Fashion Group",
        website: "techstyle.com",
        industry: "fashion",
        annualRevenue: 25000000,
        monthlyTransactions: 150000,
        currentAnalytics: ["google_analytics", "shopify_analytics"],
        painPoints: ["limited_customer_insights", "poor_attribution"],
        decisionMakers: [
          {
            name: "Sarah Johnson",
            title: "VP of Marketing",
            email: "<EMAIL>"
          }
        ],
        growthStage: "scaling",
        tier: "tier2"
      }
    ];
  }

  /**
   * Get customers from competitor analysis
   */
  private async getCompetitorCustomers(): Promise<CustomerProfile[]> {
    // Mock implementation - in production, use market intelligence tools
    return [];
  }

  /**
   * Get referrals from partners
   */
  private async getPartnerReferrals(): Promise<CustomerProfile[]> {
    // Mock implementation - in production, integrate with partner systems
    return [];
  }

  /**
   * Store qualification result
   */
  private async storeQualificationResult(result: QualificationResult): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      await client.queryObject(`
        INSERT INTO customer_qualifications (
          customer_id, score, tier, qualified, reasons, recommendations, next_steps, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
        ON CONFLICT (customer_id) DO UPDATE SET
          score = $2, tier = $3, qualified = $4, reasons = $5,
          recommendations = $6, next_steps = $7, updated_at = NOW()
      `, [
        result.customerId,
        result.score,
        result.tier,
        result.qualified,
        JSON.stringify(result.reasons),
        JSON.stringify(result.recommendations),
        JSON.stringify(result.nextSteps)
      ]);
    } finally {
      client.release();
    }
  }

  /**
   * Store onboarding workflow
   */
  private async storeOnboardingWorkflow(workflow: OnboardingWorkflow): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      await client.queryObject(`
        INSERT INTO onboarding_workflows (
          workflow_id, customer_id, status, current_step, completed_steps,
          estimated_completion, success_criteria, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
      `, [
        workflow.workflowId,
        workflow.customerId,
        workflow.status,
        workflow.currentStep,
        JSON.stringify(workflow.completedSteps),
        workflow.estimatedCompletion,
        JSON.stringify(workflow.successCriteria)
      ]);
    } finally {
      client.release();
    }
  }

  /**
   * Trigger initial contact with qualified customer
   */
  private async triggerInitialContact(customerId: string): Promise<void> {
    // Mock implementation - in production, integrate with email/CRM systems
    logger.info(`Triggering initial contact for customer: ${customerId}`);
    
    // Send personalized email
    // Schedule follow-up tasks
    // Update CRM records
  }

  /**
   * Map database row to customer profile
   */
  private mapRowToCustomerProfile(row: any): CustomerProfile {
    return {
      customerId: row.customer_id,
      companyName: row.company_name,
      website: row.website,
      industry: row.industry,
      annualRevenue: row.annual_revenue,
      monthlyTransactions: row.monthly_transactions,
      currentAnalytics: JSON.parse(row.current_analytics || '[]'),
      painPoints: JSON.parse(row.pain_points || '[]'),
      decisionMakers: JSON.parse(row.decision_makers || '[]'),
      growthStage: row.growth_stage,
      tier: row.tier
    };
  }

  /**
   * Get qualified customers for beta program
   */
  async getQualifiedCustomers(): Promise<QualificationResult[]> {
    const client = await this.pool.connect();
    
    try {
      const result = await client.queryObject(`
        SELECT * FROM customer_qualifications
        WHERE qualified = true
        ORDER BY score DESC
      `);

      return result.rows.map(row => ({
        customerId: row.customer_id,
        score: row.score,
        tier: row.tier,
        qualified: row.qualified,
        reasons: JSON.parse(row.reasons),
        recommendations: JSON.parse(row.recommendations),
        nextSteps: JSON.parse(row.next_steps)
      }));
    } finally {
      client.release();
    }
  }

  /**
   * Cleanup resources
   */
  async close(): Promise<void> {
    await this.pool.end();
    this.redis.close();
  }
}
