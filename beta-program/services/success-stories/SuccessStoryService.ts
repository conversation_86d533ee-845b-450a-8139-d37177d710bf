/**
 * Success Story Generation Service
 * Automated case study generation, testimonial collection, and reference customer program
 * 
 * Features:
 * - Automated case study generation from customer data
 * - Testimonial collection and management
 * - Reference customer program coordination
 * - ROI story development and validation
 * - Sales enablement content creation
 */

import { Pool } from "https://deno.land/x/postgres@v0.17.0/mod.ts";
import { Redis } from "https://deno.land/x/redis@v0.29.0/mod.ts";
import { logger } from "../utils/logger.ts";

export interface SuccessStory {
  storyId: string;
  customerId: string;
  companyName: string;
  industry: string;
  storyType: "case_study" | "testimonial" | "roi_story" | "implementation_story";
  status: "draft" | "review" | "approved" | "published";
  
  // Story Content
  title: string;
  summary: string;
  challenge: string;
  solution: string;
  results: string;
  quote?: string;
  
  // Metrics and Data
  metrics: {
    implementationTime: number; // minutes
    performanceImprovement: number; // percentage
    costSavings: number; // dollars
    revenueIncrease: number; // dollars
    timeToValue: number; // days
    userAdoption: number; // percentage
    satisfactionScore: number; // 1-5
  };
  
  // Media and Assets
  assets: {
    customerLogo?: string;
    screenshots: string[];
    charts: string[];
    videos?: string[];
  };
  
  // Metadata
  createdAt: Date;
  publishedAt?: Date;
  lastUpdated: Date;
  approvedBy?: string;
  tags: string[];
  useCase: string;
}

export interface TestimonialRequest {
  requestId: string;
  customerId: string;
  requestType: "written" | "video" | "case_study" | "reference";
  status: "pending" | "sent" | "received" | "approved" | "declined";
  requestedAt: Date;
  respondedAt?: Date;
  content?: string;
  rating?: number;
  permissions: {
    useInMarketing: boolean;
    useCompanyName: boolean;
    useContactInfo: boolean;
    useOnWebsite: boolean;
  };
}

export interface ReferenceCustomer {
  customerId: string;
  companyName: string;
  contactPerson: string;
  contactEmail: string;
  contactPhone?: string;
  
  // Reference Details
  referenceType: "sales_call" | "case_study" | "conference_speaker" | "press_quote";
  availability: "high" | "medium" | "low";
  expertise: string[];
  successMetrics: any;
  
  // Program Status
  status: "active" | "inactive" | "pending";
  joinedAt: Date;
  lastActivity?: Date;
  referenceCount: number;
  
  // Incentives and Benefits
  benefits: string[];
  incentivesEarned: number;
  nextReward?: string;
}

export interface CaseStudyTemplate {
  templateId: string;
  name: string;
  industry: string;
  useCase: string;
  sections: {
    title: string;
    content: string;
    dataPoints: string[];
  }[];
  requiredMetrics: string[];
  estimatedLength: number; // words
}

export class SuccessStoryService {
  private pool: Pool;
  private redis: Redis;

  constructor(databaseUrl: string, redisUrl: string) {
    this.pool = new Pool(databaseUrl, 10, true);
    this.redis = new Redis(redisUrl);
  }

  /**
   * Generate automated case study from customer data
   */
  async generateCaseStudy(customerId: string, template?: string): Promise<SuccessStory> {
    logger.info(`Generating case study for customer: ${customerId}`);

    // Gather customer data
    const customerData = await this.getCustomerData(customerId);
    const successMetrics = await this.getSuccessMetrics(customerId);
    const implementationData = await this.getImplementationData(customerId);
    const feedbackData = await this.getCustomerFeedback(customerId);

    // Select appropriate template
    const caseStudyTemplate = template ? 
      await this.getTemplate(template) : 
      await this.selectBestTemplate(customerData);

    // Generate story content
    const story = await this.generateStoryContent(
      customerData, 
      successMetrics, 
      implementationData, 
      feedbackData, 
      caseStudyTemplate
    );

    // Create success story record
    const successStory: SuccessStory = {
      storyId: `story_${customerId}_${Date.now()}`,
      customerId,
      companyName: customerData.companyName,
      industry: customerData.industry,
      storyType: "case_study",
      status: "draft",
      title: story.title,
      summary: story.summary,
      challenge: story.challenge,
      solution: story.solution,
      results: story.results,
      quote: story.quote,
      metrics: {
        implementationTime: implementationData.totalTime || 15, // minutes
        performanceImprovement: successMetrics.performanceGain || 0,
        costSavings: successMetrics.costSavings || 0,
        revenueIncrease: successMetrics.revenueIncrease || 0,
        timeToValue: implementationData.timeToValue || 1,
        userAdoption: successMetrics.userAdoption || 0,
        satisfactionScore: successMetrics.satisfactionScore || 0
      },
      assets: {
        screenshots: [],
        charts: await this.generateCharts(successMetrics)
      },
      createdAt: new Date(),
      lastUpdated: new Date(),
      tags: this.generateTags(customerData, successMetrics),
      useCase: customerData.primaryUseCase || "analytics"
    };

    // Store success story
    await this.storeSuccessStory(successStory);

    // Generate supporting assets
    await this.generateSupportingAssets(successStory);

    logger.info(`Case study generated: ${successStory.storyId}`);
    return successStory;
  }

  /**
   * Request testimonial from customer
   */
  async requestTestimonial(
    customerId: string, 
    requestType: "written" | "video" | "case_study" | "reference"
  ): Promise<TestimonialRequest> {
    logger.info(`Requesting ${requestType} testimonial from customer: ${customerId}`);

    const request: TestimonialRequest = {
      requestId: `testimonial_${customerId}_${Date.now()}`,
      customerId,
      requestType,
      status: "pending",
      requestedAt: new Date(),
      permissions: {
        useInMarketing: false,
        useCompanyName: false,
        useContactInfo: false,
        useOnWebsite: false
      }
    };

    // Store request
    await this.storeTestimonialRequest(request);

    // Send testimonial request email
    await this.sendTestimonialRequest(request);

    // Schedule follow-up
    await this.scheduleTestimonialFollowUp(request);

    logger.info(`Testimonial request sent: ${request.requestId}`);
    return request;
  }

  /**
   * Enroll customer in reference program
   */
  async enrollInReferenceProgram(customerId: string): Promise<ReferenceCustomer> {
    logger.info(`Enrolling customer in reference program: ${customerId}`);

    const customerData = await this.getCustomerData(customerId);
    const successMetrics = await this.getSuccessMetrics(customerId);

    const referenceCustomer: ReferenceCustomer = {
      customerId,
      companyName: customerData.companyName,
      contactPerson: customerData.primaryContact.name,
      contactEmail: customerData.primaryContact.email,
      contactPhone: customerData.primaryContact.phone,
      referenceType: "sales_call",
      availability: "medium",
      expertise: [customerData.industry, customerData.primaryUseCase],
      successMetrics,
      status: "pending",
      joinedAt: new Date(),
      referenceCount: 0,
      benefits: [
        "Early access to new features",
        "Priority support",
        "Co-marketing opportunities",
        "Industry recognition"
      ],
      incentivesEarned: 0
    };

    // Store reference customer
    await this.storeReferenceCustomer(referenceCustomer);

    // Send welcome package
    await this.sendReferenceWelcomePackage(referenceCustomer);

    logger.info(`Customer enrolled in reference program: ${customerId}`);
    return referenceCustomer;
  }

  /**
   * Generate ROI story with validated metrics
   */
  async generateROIStory(customerId: string): Promise<SuccessStory> {
    logger.info(`Generating ROI story for customer: ${customerId}`);

    const customerData = await this.getCustomerData(customerId);
    const roiCalculation = await this.getROICalculation(customerId);
    const performanceMetrics = await this.getPerformanceMetrics(customerId);

    const roiStory: SuccessStory = {
      storyId: `roi_story_${customerId}_${Date.now()}`,
      customerId,
      companyName: customerData.companyName,
      industry: customerData.industry,
      storyType: "roi_story",
      status: "draft",
      title: `${customerData.companyName} Achieves ${roiCalculation.roi.toFixed(0)}% ROI with E-commerce Analytics`,
      summary: this.generateROISummary(customerData, roiCalculation),
      challenge: this.generateROIChallenge(customerData),
      solution: this.generateROISolution(performanceMetrics),
      results: this.generateROIResults(roiCalculation),
      metrics: {
        implementationTime: 15, // 15-minute setup
        performanceImprovement: performanceMetrics.improvementPercentage,
        costSavings: roiCalculation.costReduction,
        revenueIncrease: roiCalculation.revenueIncrease,
        timeToValue: roiCalculation.paybackPeriod * 30, // Convert months to days
        userAdoption: performanceMetrics.userAdoption,
        satisfactionScore: performanceMetrics.satisfactionScore
      },
      assets: {
        charts: await this.generateROICharts(roiCalculation, performanceMetrics)
      },
      createdAt: new Date(),
      lastUpdated: new Date(),
      tags: ["roi", "performance", "cost_savings", customerData.industry],
      useCase: "roi_validation"
    };

    await this.storeSuccessStory(roiStory);

    logger.info(`ROI story generated: ${roiStory.storyId}`);
    return roiStory;
  }

  /**
   * Create sales enablement package from success stories
   */
  async createSalesEnablementPackage(industry?: string, useCase?: string): Promise<any> {
    logger.info(`Creating sales enablement package for industry: ${industry}, use case: ${useCase}`);

    // Get relevant success stories
    const successStories = await this.getSuccessStories({ industry, useCase, status: "approved" });
    const testimonials = await this.getApprovedTestimonials({ industry, useCase });
    const referenceCustomers = await this.getActiveReferenceCustomers({ industry, useCase });

    const package = {
      packageId: `sales_package_${Date.now()}`,
      industry: industry || "all",
      useCase: useCase || "all",
      createdAt: new Date(),
      
      // Case Studies
      caseStudies: successStories.filter(s => s.storyType === "case_study").slice(0, 5),
      
      // ROI Stories
      roiStories: successStories.filter(s => s.storyType === "roi_story").slice(0, 3),
      
      // Testimonials
      testimonials: testimonials.slice(0, 10),
      
      // Reference Customers
      referenceCustomers: referenceCustomers.slice(0, 15),
      
      // Performance Highlights
      performanceHighlights: this.extractPerformanceHighlights(successStories),
      
      // Industry Insights
      industryInsights: await this.generateIndustryInsights(successStories, industry),
      
      // Competitive Advantages
      competitiveAdvantages: this.extractCompetitiveAdvantages(successStories),
      
      // Sales Tools
      salesTools: {
        onePagers: await this.generateOnePagers(successStories),
        battleCards: await this.generateBattleCards(successStories),
        roiCalculators: await this.generateROICalculators(successStories),
        demoScripts: await this.generateDemoScripts(successStories)
      }
    };

    // Store package
    await this.storeSalesPackage(package);

    logger.info(`Sales enablement package created: ${package.packageId}`);
    return package;
  }

  /**
   * Generate story content using templates and data
   */
  private async generateStoryContent(
    customerData: any,
    successMetrics: any,
    implementationData: any,
    feedbackData: any,
    template: CaseStudyTemplate
  ): Promise<any> {
    return {
      title: `${customerData.companyName} Achieves ${successMetrics.performanceGain}% Performance Improvement with 15-Minute Setup`,
      summary: `${customerData.companyName}, a ${customerData.industry} company, implemented our e-commerce analytics platform and achieved immediate results with our 15-minute automated setup process.`,
      challenge: `${customerData.companyName} was struggling with limited customer insights and slow analytics performance, hindering their ability to make data-driven decisions in their fast-paced ${customerData.industry} business.`,
      solution: `Using our automated onboarding system, ${customerData.companyName} was up and running in just ${implementationData.totalTime} minutes with full analytics capabilities, including cohort analysis, funnel tracking, and predictive insights.`,
      results: `The results were immediate: ${successMetrics.performanceGain}% improvement in query performance, ${successMetrics.userAdoption}% team adoption, and ${successMetrics.revenueIncrease ? `$${successMetrics.revenueIncrease.toLocaleString()} in additional revenue` : 'significant business impact'}.`,
      quote: feedbackData.bestQuote || `"The 15-minute setup was incredible. We went from no analytics to full insights faster than we ever imagined possible." - ${customerData.primaryContact.name}, ${customerData.primaryContact.title}`
    };
  }

  /**
   * Generate ROI-specific content
   */
  private generateROISummary(customerData: any, roiCalculation: any): string {
    return `${customerData.companyName} achieved ${roiCalculation.roi.toFixed(0)}% ROI within ${roiCalculation.paybackPeriod.toFixed(1)} months of implementing our e-commerce analytics platform, with ${roiCalculation.totalReturns.toLocaleString()} in total returns against ${roiCalculation.totalInvestment.toLocaleString()} investment.`;
  }

  private generateROIChallenge(customerData: any): string {
    return `${customerData.companyName} needed to improve their analytics capabilities while managing costs and ensuring quick time-to-value in their competitive ${customerData.industry} market.`;
  }

  private generateROISolution(performanceMetrics: any): string {
    return `Our 15-minute automated setup delivered immediate value with ${performanceMetrics.queryResponseTime}ms query performance (vs industry average of 100ms+) and ${performanceMetrics.eventThroughput.toLocaleString()} events/second processing capability.`;
  }

  private generateROIResults(roiCalculation: any): string {
    return `ROI: ${roiCalculation.roi.toFixed(0)}%, Payback Period: ${roiCalculation.paybackPeriod.toFixed(1)} months, Revenue Increase: $${roiCalculation.revenueIncrease.toLocaleString()}, Cost Savings: $${roiCalculation.costReduction.toLocaleString()}`;
  }

  /**
   * Extract performance highlights from success stories
   */
  private extractPerformanceHighlights(stories: SuccessStory[]): any {
    const highlights = {
      averageImplementationTime: 0,
      averagePerformanceImprovement: 0,
      totalCostSavings: 0,
      totalRevenueIncrease: 0,
      averageSatisfactionScore: 0,
      customerCount: stories.length
    };

    if (stories.length === 0) return highlights;

    highlights.averageImplementationTime = stories.reduce((sum, s) => sum + s.metrics.implementationTime, 0) / stories.length;
    highlights.averagePerformanceImprovement = stories.reduce((sum, s) => sum + s.metrics.performanceImprovement, 0) / stories.length;
    highlights.totalCostSavings = stories.reduce((sum, s) => sum + s.metrics.costSavings, 0);
    highlights.totalRevenueIncrease = stories.reduce((sum, s) => sum + s.metrics.revenueIncrease, 0);
    highlights.averageSatisfactionScore = stories.reduce((sum, s) => sum + s.metrics.satisfactionScore, 0) / stories.length;

    return highlights;
  }

  /**
   * Generate supporting assets (charts, graphics, etc.)
   */
  private async generateCharts(successMetrics: any): Promise<string[]> {
    // Mock implementation - in production, generate actual charts
    return [
      "performance_improvement_chart.png",
      "roi_timeline_chart.png",
      "user_adoption_chart.png"
    ];
  }

  private async generateROICharts(roiCalculation: any, performanceMetrics: any): Promise<string[]> {
    return [
      "roi_breakdown_chart.png",
      "payback_period_chart.png",
      "cost_benefit_analysis.png"
    ];
  }

  private generateTags(customerData: any, successMetrics: any): string[] {
    const tags = [customerData.industry, customerData.primaryUseCase];
    
    if (successMetrics.performanceGain > 50) tags.push("high_performance");
    if (successMetrics.userAdoption > 80) tags.push("high_adoption");
    if (successMetrics.satisfactionScore > 4.5) tags.push("high_satisfaction");
    
    return tags;
  }

  // Database and external service methods (simplified implementations)
  private async getCustomerData(customerId: string): Promise<any> {
    return {
      companyName: "Beta Customer Inc.",
      industry: "ecommerce",
      primaryUseCase: "customer_analytics",
      primaryContact: {
        name: "John Smith",
        title: "VP of Marketing",
        email: "<EMAIL>",
        phone: "******-0123"
      }
    };
  }

  private async getSuccessMetrics(customerId: string): Promise<any> {
    return {
      performanceGain: 75,
      userAdoption: 85,
      satisfactionScore: 4.7,
      costSavings: 50000,
      revenueIncrease: 125000
    };
  }

  private async getImplementationData(customerId: string): Promise<any> {
    return {
      totalTime: 12, // minutes
      timeToValue: 1, // days
      setupSteps: 5,
      automationLevel: 95
    };
  }

  private async getCustomerFeedback(customerId: string): Promise<any> {
    return {
      bestQuote: "The 15-minute setup was incredible. We went from no analytics to full insights faster than we ever imagined possible."
    };
  }

  private async getTemplate(templateId: string): Promise<CaseStudyTemplate> {
    // Mock template
    return {
      templateId,
      name: "Standard Case Study",
      industry: "ecommerce",
      useCase: "analytics",
      sections: [],
      requiredMetrics: [],
      estimatedLength: 800
    };
  }

  private async selectBestTemplate(customerData: any): Promise<CaseStudyTemplate> {
    return await this.getTemplate("standard");
  }

  private async getROICalculation(customerId: string): Promise<any> {
    return {
      roi: 245,
      paybackPeriod: 3.2,
      totalInvestment: 25000,
      totalReturns: 86250,
      revenueIncrease: 125000,
      costReduction: 35000
    };
  }

  private async getPerformanceMetrics(customerId: string): Promise<any> {
    return {
      queryResponseTime: 8.5,
      eventThroughput: 26500,
      improvementPercentage: 75,
      userAdoption: 85,
      satisfactionScore: 4.7
    };
  }

  // Storage methods
  private async storeSuccessStory(story: SuccessStory): Promise<void> {
    // Implementation to store success story
  }

  private async storeTestimonialRequest(request: TestimonialRequest): Promise<void> {
    // Implementation to store testimonial request
  }

  private async storeReferenceCustomer(customer: ReferenceCustomer): Promise<void> {
    // Implementation to store reference customer
  }

  private async storeSalesPackage(package: any): Promise<void> {
    // Implementation to store sales package
  }

  // Communication methods
  private async sendTestimonialRequest(request: TestimonialRequest): Promise<void> {
    // Implementation to send testimonial request email
  }

  private async scheduleTestimonialFollowUp(request: TestimonialRequest): Promise<void> {
    // Implementation to schedule follow-up
  }

  private async sendReferenceWelcomePackage(customer: ReferenceCustomer): Promise<void> {
    // Implementation to send welcome package
  }

  // Query methods
  private async getSuccessStories(filters: any): Promise<SuccessStory[]> {
    // Implementation to query success stories
    return [];
  }

  private async getApprovedTestimonials(filters: any): Promise<any[]> {
    // Implementation to query testimonials
    return [];
  }

  private async getActiveReferenceCustomers(filters: any): Promise<ReferenceCustomer[]> {
    // Implementation to query reference customers
    return [];
  }

  // Content generation methods
  private async generateSupportingAssets(story: SuccessStory): Promise<void> {
    // Implementation to generate supporting assets
  }

  private async generateIndustryInsights(stories: SuccessStory[], industry?: string): Promise<any> {
    // Implementation to generate industry insights
    return {};
  }

  private extractCompetitiveAdvantages(stories: SuccessStory[]): string[] {
    return [
      "15-minute setup vs industry 2-4 weeks",
      "6-11ms query response vs 100ms+ industry average",
      "24,390 events/sec processing capability",
      "97-98% performance advantage validated"
    ];
  }

  private async generateOnePagers(stories: SuccessStory[]): Promise<any[]> {
    // Implementation to generate one-pagers
    return [];
  }

  private async generateBattleCards(stories: SuccessStory[]): Promise<any[]> {
    // Implementation to generate battle cards
    return [];
  }

  private async generateROICalculators(stories: SuccessStory[]): Promise<any[]> {
    // Implementation to generate ROI calculators
    return [];
  }

  private async generateDemoScripts(stories: SuccessStory[]): Promise<any[]> {
    // Implementation to generate demo scripts
    return [];
  }

  /**
   * Cleanup resources
   */
  async close(): Promise<void> {
    await this.pool.end();
    this.redis.close();
  }
}
