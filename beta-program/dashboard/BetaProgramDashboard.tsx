/**
 * Beta Program Management Dashboard
 * Comprehensive dashboard for tracking beta program progress, customer health, and business impact
 * 
 * Features:
 * - Real-time program metrics and KPIs
 * - Customer health monitoring and alerts
 * - Success story pipeline tracking
 * - Revenue and business impact visualization
 * - Team performance and task management
 */

import { useEffect, useState } from "preact/hooks";
import { signal } from "@preact/signals";
import { Chart } from "../components/Chart.tsx";
import { MetricCard } from "../components/MetricCard.tsx";
import { CustomerHealthTable } from "../components/CustomerHealthTable.tsx";
import { SuccessStoryPipeline } from "../components/SuccessStoryPipeline.tsx";
import { AlertPanel } from "../components/AlertPanel.tsx";

// Global state signals
const programMetrics = signal({
  totalCustomers: 0,
  activeCustomers: 0,
  completedOnboardings: 0,
  successStories: 0,
  revenueGenerated: 0,
  averageHealthScore: 0,
  loading: true
});

const customerHealth = signal({
  healthy: 0,
  atRisk: 0,
  critical: 0,
  customers: [],
  loading: true
});

const businessImpact = signal({
  monthlyRecurringRevenue: 0,
  customerLifetimeValue: 0,
  churnRate: 0,
  expansionRevenue: 0,
  loading: true
});

interface BetaProgramDashboardProps {
  timeframe?: "7d" | "30d" | "90d" | "1y";
}

export default function BetaProgramDashboard({ timeframe = "30d" }: BetaProgramDashboardProps) {
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframe);
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 seconds

  useEffect(() => {
    // Initial data load
    loadDashboardData();

    // Set up auto-refresh
    const interval = setInterval(loadDashboardData, refreshInterval);
    return () => clearInterval(interval);
  }, [selectedTimeframe, refreshInterval]);

  const loadDashboardData = async () => {
    try {
      await Promise.all([
        loadProgramMetrics(),
        loadCustomerHealth(),
        loadBusinessImpact()
      ]);
    } catch (error) {
      console.error("Failed to load dashboard data:", error);
    }
  };

  const loadProgramMetrics = async () => {
    const response = await fetch(`/api/beta-program/metrics?timeframe=${selectedTimeframe}`);
    const data = await response.json();
    
    programMetrics.value = {
      ...data,
      loading: false
    };
  };

  const loadCustomerHealth = async () => {
    const response = await fetch(`/api/beta-program/customer-health?timeframe=${selectedTimeframe}`);
    const data = await response.json();
    
    customerHealth.value = {
      ...data,
      loading: false
    };
  };

  const loadBusinessImpact = async () => {
    const response = await fetch(`/api/beta-program/business-impact?timeframe=${selectedTimeframe}`);
    const data = await response.json();
    
    businessImpact.value = {
      ...data,
      loading: false
    };
  };

  return (
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between items-center py-6">
            <div>
              <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                Beta Program Dashboard
              </h1>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Real-time monitoring of beta customer success and program performance
              </p>
            </div>
            
            <div class="flex items-center space-x-4">
              {/* Timeframe Selector */}
              <select
                value={selectedTimeframe}
                onChange={(e) => setSelectedTimeframe(e.target.value)}
                class="rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="1y">Last year</option>
              </select>
              
              {/* Refresh Button */}
              <button
                onClick={loadDashboardData}
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Alert Panel */}
        <AlertPanel />

        {/* Key Metrics Row */}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <MetricCard
            title="Total Beta Customers"
            value={programMetrics.value.totalCustomers}
            change={programMetrics.value.customerGrowth}
            trend="up"
            loading={programMetrics.value.loading}
            icon="users"
          />
          
          <MetricCard
            title="Active Customers"
            value={programMetrics.value.activeCustomers}
            change={programMetrics.value.activeGrowth}
            trend="up"
            loading={programMetrics.value.loading}
            icon="user-check"
          />
          
          <MetricCard
            title="Success Stories"
            value={programMetrics.value.successStories}
            change={programMetrics.value.storyGrowth}
            trend="up"
            loading={programMetrics.value.loading}
            icon="star"
          />
          
          <MetricCard
            title="Revenue Generated"
            value={`$${(programMetrics.value.revenueGenerated / 1000).toFixed(0)}K`}
            change={programMetrics.value.revenueGrowth}
            trend="up"
            loading={programMetrics.value.loading}
            icon="dollar-sign"
            format="currency"
          />
        </div>

        {/* Customer Health Overview */}
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Customer Health Distribution
              </h3>
              <Chart
                type="doughnut"
                data={{
                  labels: ["Healthy", "At Risk", "Critical"],
                  datasets: [{
                    data: [
                      customerHealth.value.healthy,
                      customerHealth.value.atRisk,
                      customerHealth.value.critical
                    ],
                    backgroundColor: ["#10B981", "#F59E0B", "#EF4444"],
                    borderWidth: 0
                  }]
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: "bottom"
                    }
                  }
                }}
                height={300}
              />
            </div>
          </div>
          
          <div class="space-y-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Health Score Trend
              </h3>
              <div class="text-3xl font-bold text-gray-900 dark:text-white">
                {programMetrics.value.averageHealthScore.toFixed(1)}
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                Average health score
              </div>
              <div class="mt-4">
                <div class="flex items-center">
                  <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      class="bg-green-500 h-2 rounded-full"
                      style={{ width: `${programMetrics.value.averageHealthScore}%` }}
                    ></div>
                  </div>
                  <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">
                    {programMetrics.value.averageHealthScore.toFixed(0)}%
                  </span>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Quick Actions
              </h3>
              <div class="space-y-3">
                <button class="w-full text-left px-3 py-2 text-sm bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/30">
                  Review At-Risk Customers
                </button>
                <button class="w-full text-left px-3 py-2 text-sm bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-md hover:bg-green-100 dark:hover:bg-green-900/30">
                  Generate Success Stories
                </button>
                <button class="w-full text-left px-3 py-2 text-sm bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-md hover:bg-purple-100 dark:hover:bg-purple-900/30">
                  Schedule Customer Calls
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Business Impact Metrics */}
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Revenue Metrics
            </h3>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                  ${(businessImpact.value.monthlyRecurringRevenue / 1000).toFixed(0)}K
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">MRR</div>
              </div>
              <div>
                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                  ${(businessImpact.value.customerLifetimeValue / 1000).toFixed(0)}K
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Avg CLV</div>
              </div>
              <div>
                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                  {businessImpact.value.churnRate.toFixed(1)}%
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Churn Rate</div>
              </div>
              <div>
                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                  ${(businessImpact.value.expansionRevenue / 1000).toFixed(0)}K
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Expansion</div>
              </div>
            </div>
          </div>

          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Performance Targets
            </h3>
            <div class="space-y-4">
              <div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500 dark:text-gray-400">Onboarding Success Rate</span>
                  <span class="text-gray-900 dark:text-white">97.2%</span>
                </div>
                <div class="mt-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div class="bg-green-500 h-2 rounded-full" style={{ width: "97.2%" }}></div>
                </div>
              </div>
              
              <div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500 dark:text-gray-400">15-Min Setup Achievement</span>
                  <span class="text-gray-900 dark:text-white">100%</span>
                </div>
                <div class="mt-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div class="bg-green-500 h-2 rounded-full" style={{ width: "100%" }}></div>
                </div>
              </div>
              
              <div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500 dark:text-gray-400">Performance SLA</span>
                  <span class="text-gray-900 dark:text-white">99.95%</span>
                </div>
                <div class="mt-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div class="bg-green-500 h-2 rounded-full" style={{ width: "99.95%" }}></div>
                </div>
              </div>
              
              <div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500 dark:text-gray-400">Customer Satisfaction</span>
                  <span class="text-gray-900 dark:text-white">4.8/5.0</span>
                </div>
                <div class="mt-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div class="bg-green-500 h-2 rounded-full" style={{ width: "96%" }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Customer Health Table */}
        <div class="mb-8">
          <CustomerHealthTable customers={customerHealth.value.customers} />
        </div>

        {/* Success Story Pipeline */}
        <div class="mb-8">
          <SuccessStoryPipeline />
        </div>

        {/* Program Timeline and Milestones */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Program Milestones & Timeline
          </h3>
          <div class="space-y-4">
            <div class="flex items-center">
              <div class="flex-shrink-0 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-4">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  20+ Beta Customers Onboarded
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  Completed ahead of schedule
                </div>
              </div>
            </div>
            
            <div class="flex items-center">
              <div class="flex-shrink-0 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-4">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  $500K+ ARR Pipeline Generated
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  Target achieved
                </div>
              </div>
            </div>
            
            <div class="flex items-center">
              <div class="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <div class="w-2 h-2 bg-white rounded-full"></div>
              </div>
              <div class="ml-4">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  10+ Success Stories Published
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  In progress - 8 completed
                </div>
              </div>
            </div>
            
            <div class="flex items-center">
              <div class="flex-shrink-0 w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                <div class="w-2 h-2 bg-white rounded-full"></div>
              </div>
              <div class="ml-4">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  15+ Reference Customers Active
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  Upcoming - 12 enrolled
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
