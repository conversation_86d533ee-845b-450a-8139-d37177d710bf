# Revenue Analytics API Documentation
## Comprehensive Revenue Optimization & Growth Analytics Endpoints

**Base URL**: `https://api.ecommerce-analytics.com/v1/revenue-analytics`
**Authentication**: Bearer token required for all endpoints
**Rate Limiting**: 1000 requests per minute per tenant

## 📊 **Revenue Metrics Endpoints**

### Get Revenue Metrics
Retrieve comprehensive revenue analytics with time-series data and growth calculations.

```http
GET /api/revenue-analytics/metrics
```

**Query Parameters**:
- `timeRange` (optional): `7d`, `30d`, `90d`, `1y` (default: `30d`)
- `dateFrom` (optional): ISO 8601 date string
- `dateTo` (optional): ISO 8601 date string  
- `granularity` (optional): `hour`, `day`, `week`, `month` (default: `day`)
- `currency` (optional): 3-letter currency code (default: `USD`)

**Response Example**:
```json
{
  "success": true,
  "data": {
    "totalRevenue": 125000.00,
    "recurringRevenue": 98000.00,
    "oneTimeRevenue": 27000.00,
    "averageRevenuePerUser": 245.50,
    "monthlyRecurringRevenue": 32000.00,
    "annualRecurringRevenue": 384000.00,
    "revenueGrowthRate": 0.15,
    "customerLifetimeValue": 2450.00,
    "revenuePerCustomer": 245.50,
    "conversionRate": 0.035,
    "churnRate": 0.025,
    "expansionRevenue": 15000.00,
    "contractionRevenue": 2000.00,
    "netRevenueRetention": 1.12
  },
  "metadata": {
    "tenantId": "tenant-uuid",
    "queryTime": "2.3ms",
    "timeRange": "30d",
    "granularity": "day",
    "generatedAt": "2024-01-15T10:30:00Z"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Get Pricing Recommendations
Generate dynamic pricing optimization recommendations based on usage patterns and market intelligence.

```http
GET /api/revenue-analytics/pricing-recommendations
```

**Query Parameters**:
- `planId` (optional): Specific plan to analyze
- `customerSegment` (optional): Target customer segment
- `usagePattern` (optional): Usage pattern filter
- `competitorData` (optional): Include competitor analysis (default: `false`)

**Response Example**:
```json
{
  "success": true,
  "data": [
    {
      "planId": "pro",
      "currentPrice": 99.00,
      "recommendedPrice": 109.00,
      "priceChangePercentage": 10.1,
      "expectedRevenueImpact": 15000.00,
      "confidenceScore": 0.87,
      "reasoning": "High feature utilization and market positioning support price increase",
      "implementationRisk": "low",
      "customerSegmentImpact": {
        "high_usage": 12000.00,
        "medium_usage": 3000.00
      }
    }
  ],
  "metadata": {
    "tenantId": "tenant-uuid",
    "queryTime": "4.1ms",
    "recommendationsCount": 3,
    "totalPotentialImpact": 25000.00,
    "generatedAt": "2024-01-15T10:30:00Z"
  }
}
```

### Get Revenue Forecast
Generate predictive revenue forecasting with confidence intervals and trend analysis.

```http
GET /api/revenue-analytics/forecast
```

**Query Parameters**:
- `timeRange` (optional): Historical data range for modeling
- `granularity` (optional): Forecast granularity
- `forecastPeriods` (optional): Number of periods to forecast (default: 12)

**Response Example**:
```json
{
  "success": true,
  "data": [
    {
      "period": "2024-02",
      "forecastedRevenue": 135000.00,
      "confidenceInterval": {
        "lower": 125000.00,
        "upper": 145000.00
      },
      "growthRate": 0.08,
      "seasonalityFactor": 1.02,
      "trendComponent": 0.06,
      "forecastAccuracy": 0.92,
      "keyDrivers": ["subscription_growth", "expansion_revenue", "seasonal_uptick"]
    }
  ],
  "metadata": {
    "tenantId": "tenant-uuid",
    "queryTime": "6.7ms",
    "forecastPeriods": 12,
    "totalForecastedRevenue": 1620000.00,
    "generatedAt": "2024-01-15T10:30:00Z"
  }
}
```

## 🎯 **Customer Success Endpoints**

### Get Customer Health Scores
Retrieve comprehensive customer health scoring with risk assessment and engagement analysis.

```http
GET /api/revenue-analytics/customer-health
```

**Query Parameters**:
- `customerId` (optional): Specific customer analysis
- `segment` (optional): Customer segment filter
- `timeRange` (optional): Analysis time window
- `riskThreshold` (optional): Risk threshold (0-1, default: 0.5)

**Response Example**:
```json
{
  "success": true,
  "data": [
    {
      "customerId": "customer-uuid",
      "healthScore": 78,
      "riskLevel": "low",
      "churnProbability": 0.15,
      "expansionProbability": 0.65,
      "lastActivity": "2024-01-14T15:30:00Z",
      "engagementTrend": "increasing",
      "usageMetrics": {
        "featureAdoption": 0.82,
        "apiUsage": 1250,
        "loginFrequency": 18,
        "supportTickets": 1
      },
      "revenueMetrics": {
        "currentMrr": 299.00,
        "lifetimeValue": 3588.00,
        "paymentHistory": "excellent"
      },
      "predictiveInsights": {
        "expansionOpportunity": "API usage upgrade",
        "recommendedActions": [
          "Offer premium features demo",
          "Schedule success check-in"
        ]
      }
    }
  ],
  "summary": {
    "totalCustomers": 150,
    "averageHealthScore": 72.5,
    "riskDistribution": {
      "low": 95,
      "medium": 35,
      "high": 15,
      "critical": 5
    },
    "highRiskCustomers": 20
  },
  "metadata": {
    "tenantId": "tenant-uuid",
    "queryTime": "1.8ms",
    "generatedAt": "2024-01-15T10:30:00Z"
  }
}
```

### Get Churn Predictions
Generate ML-powered churn predictions with intervention recommendations and revenue impact analysis.

```http
GET /api/revenue-analytics/churn-predictions
```

**Query Parameters**:
- `riskThreshold` (optional): Minimum churn probability (0-1, default: 0.5)
- `timeRange` (optional): Analysis window
- `segment` (optional): Customer segment filter

**Response Example**:
```json
{
  "success": true,
  "data": [
    {
      "customerId": "customer-uuid",
      "churnProbability": 0.73,
      "churnRisk": "high",
      "predictedChurnDate": "2024-02-15T00:00:00Z",
      "confidenceScore": 0.89,
      "keyRiskFactors": [
        "Extended inactivity",
        "Low feature adoption",
        "Payment issues"
      ],
      "preventionRecommendations": [
        "Immediate intervention required",
        "Schedule customer success call",
        "Provide onboarding assistance"
      ],
      "revenueAtRisk": 3588.00,
      "interventionPriority": 8
    }
  ],
  "summary": {
    "totalPredictions": 25,
    "totalRevenueAtRisk": 89700.00,
    "riskDistribution": {
      "medium": 10,
      "high": 12,
      "critical": 3
    },
    "criticalRiskCustomers": 3,
    "averageChurnProbability": 0.62
  },
  "metadata": {
    "tenantId": "tenant-uuid",
    "queryTime": "4.2ms",
    "riskThreshold": 0.5,
    "generatedAt": "2024-01-15T10:30:00Z"
  }
}
```

### Get Expansion Opportunities
Identify revenue expansion opportunities with growth potential and recommended actions.

```http
GET /api/revenue-analytics/expansion-opportunities
```

**Query Parameters**:
- `segment` (optional): Customer segment filter
- `confidenceThreshold` (optional): Minimum confidence score (0-1, default: 0.6)

**Response Example**:
```json
{
  "success": true,
  "data": [
    {
      "customerId": "customer-uuid",
      "opportunityType": "upgrade",
      "expansionProbability": 0.78,
      "potentialRevenue": 149.50,
      "recommendedPlan": "enterprise",
      "recommendedFeatures": [
        "Advanced analytics",
        "Higher API limits",
        "Priority support"
      ],
      "timeToExpansion": 21,
      "confidenceScore": 0.85,
      "triggerEvents": [
        "High API usage",
        "Feature limit reached"
      ],
      "nextBestAction": "Proactive upgrade outreach"
    }
  ],
  "summary": {
    "totalOpportunities": 45,
    "totalPotentialRevenue": 6735.00,
    "opportunityTypes": {
      "upgrade": 25,
      "add_on": 15,
      "usage_increase": 5
    },
    "averageExpansionProbability": 0.71,
    "highConfidenceOpportunities": 32
  },
  "metadata": {
    "tenantId": "tenant-uuid",
    "queryTime": "3.1ms",
    "generatedAt": "2024-01-15T10:30:00Z"
  }
}
```

## 📈 **Dashboard Endpoints**

### Get Revenue Analytics Dashboard
Retrieve comprehensive revenue intelligence dashboard data with all key metrics.

```http
GET /api/revenue-analytics/dashboard
```

**Query Parameters**:
- `timeRange` (optional): Analysis time window (default: `30d`)

**Response Example**:
```json
{
  "success": true,
  "data": {
    "revenue": {
      "totalRevenue": 125000.00,
      "monthlyRecurringRevenue": 32000.00,
      "revenueGrowthRate": 0.15,
      "netRevenueRetention": 1.12
    },
    "customerHealth": {
      "averageScore": 72.5,
      "totalCustomers": 150,
      "riskDistribution": {
        "low": 95,
        "medium": 35,
        "high": 15,
        "critical": 5
      }
    },
    "churnRisk": {
      "totalAtRisk": 25,
      "revenueAtRisk": 89700.00,
      "criticalCustomers": 3
    },
    "expansion": {
      "totalOpportunities": 45,
      "potentialRevenue": 6735.00,
      "highConfidenceOpportunities": 32
    }
  },
  "metadata": {
    "tenantId": "tenant-uuid",
    "queryTime": "89ms",
    "timeRange": "30d",
    "generatedAt": "2024-01-15T10:30:00Z"
  }
}
```

## 🔧 **Error Handling**

### Error Response Format
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    "field": "validation error details"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Common Error Codes
- `VALIDATION_ERROR`: Invalid query parameters
- `AUTHENTICATION_REQUIRED`: Missing or invalid bearer token
- `TENANT_NOT_FOUND`: Invalid tenant ID
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `INTERNAL_SERVER_ERROR`: Server processing error

## 📊 **Performance Specifications**

### Response Time Targets
- Revenue metrics: <500ms
- Customer health: <300ms  
- Churn predictions: <200ms
- Expansion analysis: <400ms
- Dashboard load: <750ms

### Rate Limits
- Standard tier: 1,000 requests/minute
- Professional tier: 5,000 requests/minute
- Enterprise tier: 25,000 requests/minute

### Data Freshness
- Real-time metrics: <30 seconds
- Aggregated data: <5 minutes
- Predictive models: <1 hour
- Historical analysis: <24 hours
