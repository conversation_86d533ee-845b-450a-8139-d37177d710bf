-- Revenue Analytics Schema for Revenue Optimization & Growth Analytics
-- Implements comprehensive revenue tracking, customer success metrics, and predictive analytics

-- Enable TimescaleDB extension if not already enabled
CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;

-- Revenue events tracking table (hypertable for time-series data)
CREATE TABLE IF NOT EXISTS revenue_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Tenant and customer context
    tenant_id UUID NOT NULL,
    customer_id UUID NOT NULL,
    subscription_id UUID,
    
    -- Event details
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN ('subscription_created', 'subscription_upgraded', 'subscription_downgraded', 'subscription_canceled', 'payment_succeeded', 'payment_failed', 'refund_issued', 'chargeback', 'usage_charge', 'one_time_payment')),
    event_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Revenue details
    total_revenue DECIMAL(15,2) NOT NULL DEFAULT 0 CHECK (total_revenue >= 0),
    recurring_revenue DECIMAL(15,2) NOT NULL DEFAULT 0 CHECK (recurring_revenue >= 0),
    one_time_revenue DECIMAL(15,2) NOT NULL DEFAULT 0 CHECK (one_time_revenue >= 0),
    currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- Billing context
    billing_type VARCHAR(20) NOT NULL CHECK (billing_type IN ('recurring', 'one_time', 'usage_based', 'hybrid')),
    billing_cycle VARCHAR(20) CHECK (billing_cycle IN ('monthly', 'quarterly', 'annually', 'one_time')),
    
    -- Metadata
    event_metadata JSONB,
    processing_time_ms INTEGER,
    
    -- Constraints
    CONSTRAINT valid_revenue_split CHECK (total_revenue = recurring_revenue + one_time_revenue)
);

-- Convert to hypertable for time-series optimization
SELECT create_hypertable('revenue_events', 'event_timestamp', 
    chunk_time_interval => INTERVAL '1 month',
    if_not_exists => TRUE
);

-- Add tenant-based partitioning for multi-tenant isolation
SELECT add_dimension('revenue_events', 'tenant_id', number_partitions => 4, if_not_exists => TRUE);

-- Customer usage tracking for health scoring
CREATE TABLE IF NOT EXISTS customer_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Customer context
    tenant_id UUID NOT NULL,
    customer_id UUID NOT NULL,
    
    -- Usage metrics (updated daily)
    measurement_date DATE NOT NULL DEFAULT CURRENT_DATE,
    
    -- Feature adoption metrics
    feature_adoption_score DECIMAL(3,2) CHECK (feature_adoption_score >= 0 AND feature_adoption_score <= 1),
    features_used INTEGER DEFAULT 0,
    total_features_available INTEGER DEFAULT 0,
    
    -- API usage metrics
    api_calls_30d INTEGER DEFAULT 0,
    api_calls_7d INTEGER DEFAULT 0,
    api_calls_1d INTEGER DEFAULT 0,
    
    -- Engagement metrics
    login_count_30d INTEGER DEFAULT 0,
    login_count_7d INTEGER DEFAULT 0,
    session_duration_avg_minutes DECIMAL(8,2) DEFAULT 0,
    
    -- Support metrics
    support_tickets_30d INTEGER DEFAULT 0,
    support_tickets_resolved_30d INTEGER DEFAULT 0,
    
    -- Usage growth indicators
    usage_growth_rate DECIMAL(5,4), -- Month-over-month growth
    feature_limit_approaching BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tenant_id, customer_id, measurement_date)
);

-- Customer engagement daily tracking
CREATE TABLE IF NOT EXISTS customer_engagement_daily (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Customer context
    tenant_id UUID NOT NULL,
    customer_id UUID NOT NULL,
    event_date DATE NOT NULL DEFAULT CURRENT_DATE,
    
    -- Engagement score (0-1)
    engagement_score DECIMAL(3,2) CHECK (engagement_score >= 0 AND engagement_score <= 1),
    
    -- Activity metrics
    page_views INTEGER DEFAULT 0,
    feature_interactions INTEGER DEFAULT 0,
    time_spent_minutes INTEGER DEFAULT 0,
    
    -- Quality metrics
    error_rate DECIMAL(5,4) DEFAULT 0,
    success_rate DECIMAL(5,4) DEFAULT 1,
    
    -- Metadata
    calculated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tenant_id, customer_id, event_date)
);

-- Convert to hypertable for time-series optimization
SELECT create_hypertable('customer_engagement_daily', 'event_date', 
    chunk_time_interval => INTERVAL '1 month',
    if_not_exists => TRUE
);

-- Payment history for customer success analytics
CREATE TABLE IF NOT EXISTS payment_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Customer context
    tenant_id UUID NOT NULL,
    customer_id UUID NOT NULL,
    subscription_id UUID,
    
    -- Payment details
    amount DECIMAL(15,2) NOT NULL CHECK (amount >= 0),
    currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
    status VARCHAR(20) NOT NULL CHECK (status IN ('succeeded', 'failed', 'pending', 'canceled', 'refunded')),
    
    -- Payment method
    payment_method_type VARCHAR(50),
    payment_processor VARCHAR(50),
    
    -- Timing
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    
    -- Failure details
    failure_reason VARCHAR(255),
    failure_code VARCHAR(50),
    
    -- Metadata
    payment_metadata JSONB,
    
    -- Indexes for performance
    INDEX idx_payment_history_customer (tenant_id, customer_id, created_at DESC),
    INDEX idx_payment_history_status (tenant_id, status, created_at DESC)
);

-- Competitor pricing data for pricing optimization
CREATE TABLE IF NOT EXISTS competitor_pricing_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Competitor context
    competitor_name VARCHAR(100) NOT NULL,
    plan_category VARCHAR(50) NOT NULL,
    plan_name VARCHAR(100) NOT NULL,
    
    -- Pricing details
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
    billing_cycle VARCHAR(20) NOT NULL,
    
    -- Features comparison
    features JSONB,
    feature_count INTEGER,
    
    -- Data collection
    collected_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    source VARCHAR(100),
    
    -- Constraints
    UNIQUE(competitor_name, plan_category, plan_name, billing_cycle)
);

-- Continuous aggregates for performance optimization

-- Daily revenue metrics aggregate
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_revenue_metrics
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', event_timestamp) AS day,
    tenant_id,
    SUM(total_revenue) as total_revenue,
    SUM(recurring_revenue) as recurring_revenue,
    SUM(one_time_revenue) as one_time_revenue,
    COUNT(*) as event_count,
    COUNT(DISTINCT customer_id) as unique_customers,
    AVG(total_revenue) as avg_revenue_per_event
FROM revenue_events
GROUP BY day, tenant_id;

-- Weekly customer health metrics aggregate
CREATE MATERIALIZED VIEW IF NOT EXISTS weekly_customer_health_metrics
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 week', measurement_date) AS week,
    tenant_id,
    AVG(feature_adoption_score) as avg_feature_adoption,
    AVG(api_calls_30d) as avg_api_usage,
    AVG(login_count_30d) as avg_login_frequency,
    AVG(support_tickets_30d) as avg_support_tickets,
    COUNT(*) as customer_count,
    COUNT(*) FILTER (WHERE feature_adoption_score > 0.7) as high_adoption_customers,
    COUNT(*) FILTER (WHERE api_calls_30d > 1000) as high_usage_customers
FROM customer_usage
GROUP BY week, tenant_id;

-- Monthly revenue growth metrics
CREATE MATERIALIZED VIEW IF NOT EXISTS monthly_revenue_growth_metrics
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 month', event_timestamp) AS month,
    tenant_id,
    SUM(total_revenue) as monthly_revenue,
    SUM(recurring_revenue) as monthly_recurring_revenue,
    COUNT(DISTINCT customer_id) as active_customers,
    AVG(total_revenue) as avg_revenue_per_customer,
    COUNT(*) FILTER (WHERE event_type = 'subscription_created') as new_subscriptions,
    COUNT(*) FILTER (WHERE event_type = 'subscription_canceled') as cancellations,
    COUNT(*) FILTER (WHERE event_type = 'subscription_upgraded') as upgrades,
    COUNT(*) FILTER (WHERE event_type = 'subscription_downgraded') as downgrades
FROM revenue_events
GROUP BY month, tenant_id;

-- Refresh policies for continuous aggregates
SELECT add_continuous_aggregate_policy('daily_revenue_metrics',
    start_offset => INTERVAL '3 days',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy('weekly_customer_health_metrics',
    start_offset => INTERVAL '2 weeks',
    end_offset => INTERVAL '1 day',
    schedule_interval => INTERVAL '6 hours',
    if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy('monthly_revenue_growth_metrics',
    start_offset => INTERVAL '2 months',
    end_offset => INTERVAL '1 day',
    schedule_interval => INTERVAL '12 hours',
    if_not_exists => TRUE
);

-- Compression policies for data retention optimization
SELECT add_compression_policy('revenue_events', INTERVAL '7 days', if_not_exists => TRUE);
SELECT add_compression_policy('customer_engagement_daily', INTERVAL '30 days', if_not_exists => TRUE);

-- Data retention policies
SELECT add_retention_policy('revenue_events', INTERVAL '3 years', if_not_exists => TRUE);
SELECT add_retention_policy('customer_engagement_daily', INTERVAL '2 years', if_not_exists => TRUE);
SELECT add_retention_policy('payment_history', INTERVAL '7 years', if_not_exists => TRUE);

-- Row Level Security (RLS) for multi-tenant isolation
ALTER TABLE revenue_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_engagement_daily ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_history ENABLE ROW LEVEL SECURITY;

-- RLS policies for tenant isolation
CREATE POLICY revenue_events_tenant_isolation ON revenue_events
    FOR ALL TO authenticated
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

CREATE POLICY customer_usage_tenant_isolation ON customer_usage
    FOR ALL TO authenticated
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

CREATE POLICY customer_engagement_tenant_isolation ON customer_engagement_daily
    FOR ALL TO authenticated
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

CREATE POLICY payment_history_tenant_isolation ON payment_history
    FOR ALL TO authenticated
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_revenue_events_tenant_time ON revenue_events (tenant_id, event_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_revenue_events_customer ON revenue_events (tenant_id, customer_id, event_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_revenue_events_type ON revenue_events (tenant_id, event_type, event_timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_customer_usage_tenant_date ON customer_usage (tenant_id, measurement_date DESC);
CREATE INDEX IF NOT EXISTS idx_customer_usage_adoption ON customer_usage (tenant_id, feature_adoption_score DESC);
CREATE INDEX IF NOT EXISTS idx_customer_usage_api_calls ON customer_usage (tenant_id, api_calls_30d DESC);

CREATE INDEX IF NOT EXISTS idx_customer_engagement_tenant_date ON customer_engagement_daily (tenant_id, event_date DESC);
CREATE INDEX IF NOT EXISTS idx_customer_engagement_score ON customer_engagement_daily (tenant_id, engagement_score DESC);

-- Comments for documentation
COMMENT ON TABLE revenue_events IS 'Time-series revenue events for comprehensive revenue analytics and forecasting';
COMMENT ON TABLE customer_usage IS 'Daily customer usage metrics for health scoring and churn prediction';
COMMENT ON TABLE customer_engagement_daily IS 'Daily customer engagement tracking for success analytics';
COMMENT ON TABLE payment_history IS 'Payment transaction history for customer success and revenue analytics';
COMMENT ON TABLE competitor_pricing_data IS 'Competitor pricing intelligence for pricing optimization';

COMMENT ON MATERIALIZED VIEW daily_revenue_metrics IS 'Daily aggregated revenue metrics for performance optimization';
COMMENT ON MATERIALIZED VIEW weekly_customer_health_metrics IS 'Weekly customer health aggregates for trend analysis';
COMMENT ON MATERIALIZED VIEW monthly_revenue_growth_metrics IS 'Monthly revenue growth tracking and analysis';
