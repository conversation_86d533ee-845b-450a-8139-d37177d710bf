# Production Monitoring Stack for E-commerce Analytics SaaS
# Comprehensive observability for 24,390 events/sec, <11ms queries, 15-minute onboarding

apiVersion: v1
kind: Namespace
metadata:
  name: monitoring
  labels:
    name: monitoring

---
# Prometheus Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        cluster: 'ecommerce-analytics-production'
        environment: 'production'

    rule_files:
      - "/etc/prometheus/rules/*.yml"

    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093

    scrape_configs:
      # Kubernetes API Server
      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
        - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
        - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
          action: keep
          regex: default;kubernetes;https

      # Node Exporter
      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
        - role: node
        relabel_configs:
        - action: labelmap
          regex: __meta_kubernetes_node_label_(.+)

      # Kubelet
      - job_name: 'kubernetes-kubelet'
        kubernetes_sd_configs:
        - role: node
        relabel_configs:
        - action: labelmap
          regex: __meta_kubernetes_node_label_(.+)
        - target_label: __address__
          replacement: kubernetes.default.svc:443
        - source_labels: [__meta_kubernetes_node_name]
          regex: (.+)
          target_label: __metrics_path__
          replacement: /api/v1/nodes/${1}/proxy/metrics

      # Application Services
      - job_name: 'analytics-service'
        kubernetes_sd_configs:
        - role: endpoints
          namespaces:
            names:
            - production
        relabel_configs:
        - source_labels: [__meta_kubernetes_service_name]
          action: keep
          regex: analytics-service
        - source_labels: [__meta_kubernetes_endpoint_port_name]
          action: keep
          regex: metrics

      - job_name: 'onboarding-automation'
        kubernetes_sd_configs:
        - role: endpoints
          namespaces:
            names:
            - production
        relabel_configs:
        - source_labels: [__meta_kubernetes_service_name]
          action: keep
          regex: onboarding-automation
        - source_labels: [__meta_kubernetes_endpoint_port_name]
          action: keep
          regex: http

      - job_name: 'dashboard-service'
        kubernetes_sd_configs:
        - role: endpoints
          namespaces:
            names:
            - production
        relabel_configs:
        - source_labels: [__meta_kubernetes_service_name]
          action: keep
          regex: dashboard-service

      # Database Monitoring
      - job_name: 'postgres-exporter'
        static_configs:
        - targets: ['postgres-exporter:9187']

      - job_name: 'redis-exporter'
        static_configs:
        - targets: ['redis-exporter:9121']

---
# Prometheus Alerting Rules
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
  namespace: monitoring
data:
  performance.yml: |
    groups:
    - name: performance
      rules:
      # Query Performance Alerts
      - alert: HighQueryLatency
        expr: histogram_quantile(0.95, rate(query_duration_seconds_bucket[5m])) > 0.011
        for: 2m
        labels:
          severity: critical
          component: analytics
        annotations:
          summary: "Query latency exceeds 11ms target"
          description: "95th percentile query latency is {{ $value }}s, exceeding 11ms target"

      # Event Processing Alerts
      - alert: LowEventThroughput
        expr: rate(events_processed_total[5m]) < 24390
        for: 1m
        labels:
          severity: critical
          component: analytics
        annotations:
          summary: "Event processing below 24,390 events/sec target"
          description: "Current throughput: {{ $value }} events/sec"

      # Onboarding Performance
      - alert: OnboardingTimeout
        expr: histogram_quantile(0.95, rate(onboarding_duration_seconds_bucket[5m])) > 900
        for: 1m
        labels:
          severity: critical
          component: onboarding
        annotations:
          summary: "Onboarding exceeds 15-minute target"
          description: "95th percentile onboarding time: {{ $value }}s"

      # System Health
      - alert: HighCPUUsage
        expr: (100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"

      # Database Performance
      - alert: DatabaseConnectionsHigh
        expr: pg_stat_database_numbackends > 80
        for: 2m
        labels:
          severity: warning
          component: database
        annotations:
          summary: "High database connections"
          description: "Database has {{ $value }} active connections"

      - alert: DatabaseSlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 5m
        labels:
          severity: warning
          component: database
        annotations:
          summary: "Database slow queries detected"
          description: "Query efficiency ratio: {{ $value }}"

  business.yml: |
    groups:
    - name: business
      rules:
      # Business Metrics
      - alert: OnboardingFailureRate
        expr: (rate(onboarding_failures_total[5m]) / rate(onboarding_attempts_total[5m])) * 100 > 5
        for: 2m
        labels:
          severity: critical
          component: business
        annotations:
          summary: "High onboarding failure rate"
          description: "Onboarding failure rate: {{ $value }}%"

      - alert: CustomerChurnSpike
        expr: increase(customer_churn_total[1h]) > 10
        for: 1m
        labels:
          severity: warning
          component: business
        annotations:
          summary: "Customer churn spike detected"
          description: "{{ $value }} customers churned in the last hour"

      - alert: RevenueDropAlert
        expr: rate(revenue_total[1h]) < rate(revenue_total[1h] offset 24h) * 0.8
        for: 10m
        labels:
          severity: critical
          component: business
        annotations:
          summary: "Revenue drop detected"
          description: "Revenue is 20% below yesterday's rate"

---
# Prometheus Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: monitoring
spec:
  replicas: 2
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus
      containers:
      - name: prometheus
        image: prom/prometheus:v2.45.0
        args:
          - '--config.file=/etc/prometheus/prometheus.yml'
          - '--storage.tsdb.path=/prometheus/'
          - '--web.console.libraries=/etc/prometheus/console_libraries'
          - '--web.console.templates=/etc/prometheus/consoles'
          - '--storage.tsdb.retention.time=30d'
          - '--web.enable-lifecycle'
          - '--storage.tsdb.no-lockfile'
        ports:
        - containerPort: 9090
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "1"
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus/
        - name: prometheus-rules
          mountPath: /etc/prometheus/rules/
        - name: prometheus-storage
          mountPath: /prometheus/
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
      - name: prometheus-rules
        configMap:
          name: prometheus-rules
      - name: prometheus-storage
        persistentVolumeClaim:
          claimName: prometheus-storage

---
# Grafana Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-config
  namespace: monitoring
data:
  grafana.ini: |
    [analytics]
    check_for_updates = true
    
    [log]
    mode = console
    
    [paths]
    data = /var/lib/grafana/data
    logs = /var/log/grafana
    plugins = /var/lib/grafana/plugins
    provisioning = /etc/grafana/provisioning

  datasources.yml: |
    apiVersion: 1
    datasources:
    - name: Prometheus
      type: prometheus
      access: proxy
      url: http://prometheus:9090
      isDefault: true
    - name: Loki
      type: loki
      access: proxy
      url: http://loki:3100

  dashboards.yml: |
    apiVersion: 1
    providers:
    - name: 'default'
      orgId: 1
      folder: ''
      type: file
      disableDeletion: false
      updateIntervalSeconds: 10
      allowUiUpdates: true
      options:
        path: /var/lib/grafana/dashboards

---
# Custom Performance Dashboard
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards
  namespace: monitoring
data:
  performance-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "E-commerce Analytics SaaS - Performance Dashboard",
        "tags": ["performance", "sla"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Query Response Time (Target: <11ms)",
            "type": "stat",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, rate(query_duration_seconds_bucket[5m])) * 1000",
                "legendFormat": "95th Percentile"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "ms",
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": null},
                    {"color": "yellow", "value": 8},
                    {"color": "red", "value": 11}
                  ]
                }
              }
            }
          },
          {
            "id": 2,
            "title": "Event Processing Rate (Target: 24,390/sec)",
            "type": "stat",
            "targets": [
              {
                "expr": "rate(events_processed_total[5m])",
                "legendFormat": "Events/sec"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "ops",
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": null},
                    {"color": "yellow", "value": 20000},
                    {"color": "green", "value": 24390}
                  ]
                }
              }
            }
          },
          {
            "id": 3,
            "title": "Onboarding Time (Target: <15min)",
            "type": "stat",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, rate(onboarding_duration_seconds_bucket[5m])) / 60",
                "legendFormat": "95th Percentile"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "m",
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": null},
                    {"color": "yellow", "value": 12},
                    {"color": "red", "value": 15}
                  ]
                }
              }
            }
          },
          {
            "id": 4,
            "title": "System Uptime (Target: 99.9%)",
            "type": "stat",
            "targets": [
              {
                "expr": "(1 - rate(up{job=~\".*\"}[24h])) * 100",
                "legendFormat": "Uptime %"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "percent",
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": null},
                    {"color": "yellow", "value": 99.5},
                    {"color": "green", "value": 99.9}
                  ]
                }
              }
            }
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "5s"
      }
    }

  business-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "E-commerce Analytics SaaS - Business Metrics",
        "tags": ["business", "revenue"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Monthly Recurring Revenue",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(revenue_total[1h])) * 24 * 30",
                "legendFormat": "MRR"
              }
            ]
          },
          {
            "id": 2,
            "title": "Customer Acquisition Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(customers_acquired_total[1h])",
                "legendFormat": "New Customers/hour"
              }
            ]
          },
          {
            "id": 3,
            "title": "Onboarding Success Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "(rate(onboarding_success_total[1h]) / rate(onboarding_attempts_total[1h])) * 100",
                "legendFormat": "Success Rate"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "percent",
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": null},
                    {"color": "yellow", "value": 90},
                    {"color": "green", "value": 95}
                  ]
                }
              }
            }
          }
        ]
      }
    }

---
# Grafana Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:10.0.0
        ports:
        - containerPort: 3000
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-credentials
              key: admin-password
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: grafana-config
          mountPath: /etc/grafana/
        - name: grafana-dashboards
          mountPath: /var/lib/grafana/dashboards/
        - name: grafana-storage
          mountPath: /var/lib/grafana/
      volumes:
      - name: grafana-config
        configMap:
          name: grafana-config
      - name: grafana-dashboards
        configMap:
          name: grafana-dashboards
      - name: grafana-storage
        persistentVolumeClaim:
          claimName: grafana-storage

---
# Services
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: monitoring
spec:
  selector:
    app: prometheus
  ports:
  - port: 9090
    targetPort: 9090

---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: monitoring
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
spec:
  type: LoadBalancer
  selector:
    app: grafana
  ports:
  - port: 80
    targetPort: 3000

---
# Persistent Volume Claims
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: prometheus-storage
  namespace: monitoring
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: gp3

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-storage
  namespace: monitoring
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: gp3

---
# Service Account and RBAC
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus
  namespace: monitoring

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus
rules:
- apiGroups: [""]
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  verbs: ["get", "list", "watch"]
- apiGroups:
  - extensions
  resources:
  - ingresses
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus
subjects:
- kind: ServiceAccount
  name: prometheus
  namespace: monitoring
