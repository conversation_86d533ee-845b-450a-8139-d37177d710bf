# Revenue Optimization Platform - Alerting Rules
# Critical alerts for 24,390+ events/sec and <11ms query performance monitoring

groups:
  # Critical Performance Alerts
  - name: revenue-optimization.performance.critical
    interval: 30s
    rules:
      - alert: HighEventIngestionLatency
        expr: histogram_quantile(0.95, rate(revenue_analytics_event_ingestion_duration_seconds_bucket[5m])) > 0.1
        for: 2m
        labels:
          severity: critical
          component: analytics-service
          impact: high
        annotations:
          summary: "Event ingestion latency is critically high"
          description: "95th percentile event ingestion latency is {{ $value }}s, exceeding 100ms threshold. Target: 24,390+ events/sec capability at risk."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/event-ingestion-latency"

      - alert: QueryResponseTimeHigh
        expr: histogram_quantile(0.95, rate(revenue_analytics_query_duration_seconds_bucket[5m])) > 0.011
        for: 1m
        labels:
          severity: critical
          component: analytics-service
          impact: high
        annotations:
          summary: "Query response time exceeds target"
          description: "95th percentile query response time is {{ $value }}s, exceeding 11ms target. Performance degradation detected."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/query-performance"

      - alert: APIResponseTimeHigh
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="revenue-analytics-services"}[5m])) > 0.5
        for: 2m
        labels:
          severity: critical
          component: api-gateway
          impact: high
        annotations:
          summary: "API response time exceeds 500ms target"
          description: "95th percentile API response time is {{ $value }}s, exceeding 500ms target. User experience impact."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/api-performance"

      - alert: EventIngestionRateDropped
        expr: rate(revenue_analytics_events_ingested_total[5m]) < 20000
        for: 3m
        labels:
          severity: critical
          component: analytics-service
          impact: high
        annotations:
          summary: "Event ingestion rate dropped below threshold"
          description: "Current event ingestion rate is {{ $value }} events/sec, below 20,000 events/sec threshold. Target: 24,390+ events/sec."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/event-ingestion-rate"

  # Database Performance Alerts
  - name: revenue-optimization.database.performance
    interval: 30s
    rules:
      - alert: TimescaleDBHighConnections
        expr: pg_stat_database_numbackends{datname="revenue_analytics_production"} > 800
        for: 2m
        labels:
          severity: warning
          component: timescaledb
          impact: medium
        annotations:
          summary: "TimescaleDB connection count is high"
          description: "Current connections: {{ $value }}, threshold: 800. Max connections: 1000."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/database-connections"

      - alert: TimescaleDBSlowQueries
        expr: rate(pg_stat_statements_mean_time_seconds{datname="revenue_analytics_production"}[5m]) > 1
        for: 5m
        labels:
          severity: warning
          component: timescaledb
          impact: medium
        annotations:
          summary: "TimescaleDB slow queries detected"
          description: "Average query time is {{ $value }}s, exceeding 1s threshold."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/slow-queries"

      - alert: TimescaleDBReplicationLag
        expr: pg_stat_replication_lag_seconds > 30
        for: 2m
        labels:
          severity: critical
          component: timescaledb
          impact: high
        annotations:
          summary: "TimescaleDB replication lag is high"
          description: "Replication lag is {{ $value }}s, exceeding 30s threshold."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/replication-lag"

      - alert: RedisMemoryUsageHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.85
        for: 5m
        labels:
          severity: warning
          component: redis
          impact: medium
        annotations:
          summary: "Redis memory usage is high"
          description: "Redis memory usage is {{ $value | humanizePercentage }}, exceeding 85% threshold."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/redis-memory"

  # Business Logic Alerts
  - name: revenue-optimization.business.critical
    interval: 60s
    rules:
      - alert: ChurnPredictionAccuracyLow
        expr: revenue_analytics_churn_prediction_accuracy < 0.85
        for: 10m
        labels:
          severity: warning
          component: ml-service
          impact: medium
        annotations:
          summary: "Churn prediction accuracy below threshold"
          description: "Current churn prediction accuracy is {{ $value | humanizePercentage }}, below 85% threshold."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/ml-accuracy"

      - alert: RevenueOptimizationFailures
        expr: rate(revenue_optimization_failures_total[5m]) > 0.01
        for: 5m
        labels:
          severity: critical
          component: optimization-service
          impact: high
        annotations:
          summary: "Revenue optimization failures detected"
          description: "Revenue optimization failure rate is {{ $value }} failures/sec, exceeding 0.01 threshold."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/optimization-failures"

      - alert: DynamicPricingErrors
        expr: rate(dynamic_pricing_errors_total[5m]) > 0.005
        for: 3m
        labels:
          severity: warning
          component: pricing-service
          impact: medium
        annotations:
          summary: "Dynamic pricing errors detected"
          description: "Dynamic pricing error rate is {{ $value }} errors/sec, exceeding 0.005 threshold."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/pricing-errors"

  # Infrastructure Alerts
  - name: revenue-optimization.infrastructure.critical
    interval: 30s
    rules:
      - alert: KubernetesNodeNotReady
        expr: kube_node_status_condition{condition="Ready",status="true"} == 0
        for: 5m
        labels:
          severity: critical
          component: kubernetes
          impact: high
        annotations:
          summary: "Kubernetes node is not ready"
          description: "Node {{ $labels.node }} has been not ready for more than 5 minutes."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/node-not-ready"

      - alert: PodCrashLooping
        expr: rate(kube_pod_container_status_restarts_total{namespace="revenue-optimization"}[5m]) > 0
        for: 5m
        labels:
          severity: critical
          component: kubernetes
          impact: high
        annotations:
          summary: "Pod is crash looping"
          description: "Pod {{ $labels.namespace }}/{{ $labels.pod }} is crash looping."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/pod-crash-loop"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          component: infrastructure
          impact: medium
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value | humanizePercentage }} on node {{ $labels.instance }}."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/high-memory"

      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 85
        for: 10m
        labels:
          severity: warning
          component: infrastructure
          impact: medium
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% on node {{ $labels.instance }}."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/high-cpu"

      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) < 0.1
        for: 5m
        labels:
          severity: critical
          component: infrastructure
          impact: high
        annotations:
          summary: "Disk space is low"
          description: "Disk space is {{ $value | humanizePercentage }} available on node {{ $labels.instance }}."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/disk-space"

  # Application Health Alerts
  - name: revenue-optimization.application.health
    interval: 30s
    rules:
      - alert: ServiceDown
        expr: up{job="revenue-analytics-services"} == 0
        for: 1m
        labels:
          severity: critical
          component: application
          impact: high
        annotations:
          summary: "Service is down"
          description: "Service {{ $labels.instance }} has been down for more than 1 minute."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/service-down"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
          component: application
          impact: high
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for service {{ $labels.job }}."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/high-error-rate"

      - alert: HealthCheckFailing
        expr: revenue_analytics_health_check_status == 0
        for: 2m
        labels:
          severity: critical
          component: application
          impact: high
        annotations:
          summary: "Health check is failing"
          description: "Health check for {{ $labels.service }} has been failing for more than 2 minutes."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/health-check-failing"

  # Security Alerts
  - name: revenue-optimization.security
    interval: 60s
    rules:
      - alert: UnauthorizedAPIAccess
        expr: rate(http_requests_total{status="401"}[5m]) > 10
        for: 2m
        labels:
          severity: warning
          component: security
          impact: medium
        annotations:
          summary: "High rate of unauthorized API access attempts"
          description: "Unauthorized access rate is {{ $value }} requests/sec, exceeding 10 req/sec threshold."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/unauthorized-access"

      - alert: SuspiciousTrafficPattern
        expr: rate(http_requests_total[1m]) > 1000
        for: 5m
        labels:
          severity: warning
          component: security
          impact: medium
        annotations:
          summary: "Suspicious traffic pattern detected"
          description: "Request rate is {{ $value }} requests/sec, potentially indicating an attack."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/suspicious-traffic"

      - alert: TLSCertificateExpiring
        expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 7
        for: 1h
        labels:
          severity: warning
          component: security
          impact: medium
        annotations:
          summary: "TLS certificate expiring soon"
          description: "TLS certificate for {{ $labels.instance }} expires in {{ $value | humanizeDuration }}."
          runbook_url: "https://docs.revenue-optimization.com/runbooks/certificate-expiry"
