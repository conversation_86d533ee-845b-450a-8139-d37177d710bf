# Prometheus Configuration for Revenue Optimization & Growth Analytics Platform
# Optimized for monitoring 24,390+ events/sec and <11ms query performance

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'revenue-optimization-production'
    environment: 'production'
    platform: 'revenue-analytics'

rule_files:
  - "/etc/prometheus/rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Kubernetes API Server
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
    - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
    - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
      action: keep
      regex: default;kubernetes;https

  # Kubernetes Nodes
  - job_name: 'kubernetes-nodes'
    kubernetes_sd_configs:
    - role: node
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
    - action: labelmap
      regex: __meta_kubernetes_node_label_(.+)
    - target_label: __address__
      replacement: kubernetes.default.svc:443
    - source_labels: [__meta_kubernetes_node_name]
      regex: (.+)
      target_label: __metrics_path__
      replacement: /api/v1/nodes/${1}/proxy/metrics

  # Kubernetes Pods
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
    - role: pod
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
      action: keep
      regex: true
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
      action: replace
      target_label: __metrics_path__
      regex: (.+)
    - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
      action: replace
      regex: ([^:]+)(?::\d+)?;(\d+)
      replacement: $1:$2
      target_label: __address__
    - action: labelmap
      regex: __meta_kubernetes_pod_label_(.+)
    - source_labels: [__meta_kubernetes_namespace]
      action: replace
      target_label: kubernetes_namespace
    - source_labels: [__meta_kubernetes_pod_name]
      action: replace
      target_label: kubernetes_pod_name

  # Revenue Analytics Services
  - job_name: 'revenue-analytics-services'
    kubernetes_sd_configs:
    - role: pod
      namespaces:
        names:
        - revenue-optimization
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_name]
      action: keep
      regex: revenue-optimization
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
      action: keep
      regex: true
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
      action: replace
      target_label: __metrics_path__
      regex: (.+)
    - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
      action: replace
      regex: ([^:]+)(?::\d+)?;(\d+)
      replacement: $1:$2
      target_label: __address__
    - action: labelmap
      regex: __meta_kubernetes_pod_label_(.+)
    - source_labels: [__meta_kubernetes_namespace]
      action: replace
      target_label: kubernetes_namespace
    - source_labels: [__meta_kubernetes_pod_name]
      action: replace
      target_label: kubernetes_pod_name
    - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_component]
      action: replace
      target_label: component
    scrape_interval: 10s
    metrics_path: /metrics

  # TimescaleDB Metrics
  - job_name: 'timescaledb'
    static_configs:
    - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
    - source_labels: [__address__]
      target_label: instance
      replacement: 'timescaledb-production'

  # Redis Metrics
  - job_name: 'redis'
    static_configs:
    - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
    - source_labels: [__address__]
      target_label: instance
      replacement: 'redis-production'

  # Node Exporter
  - job_name: 'node-exporter'
    kubernetes_sd_configs:
    - role: endpoints
    relabel_configs:
    - source_labels: [__meta_kubernetes_endpoints_name]
      action: keep
      regex: node-exporter
    - source_labels: [__meta_kubernetes_endpoint_address_target_name]
      action: replace
      target_label: node
    scrape_interval: 30s

  # cAdvisor
  - job_name: 'cadvisor'
    kubernetes_sd_configs:
    - role: node
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
    - action: labelmap
      regex: __meta_kubernetes_node_label_(.+)
    - target_label: __address__
      replacement: kubernetes.default.svc:443
    - source_labels: [__meta_kubernetes_node_name]
      regex: (.+)
      target_label: __metrics_path__
      replacement: /api/v1/nodes/${1}/proxy/metrics/cadvisor
    scrape_interval: 30s

  # Kube State Metrics
  - job_name: 'kube-state-metrics'
    static_configs:
    - targets: ['kube-state-metrics:8080']
    scrape_interval: 30s

  # Application Load Balancer
  - job_name: 'aws-load-balancer'
    ec2_sd_configs:
    - region: us-east-1
      port: 9100
      filters:
      - name: tag:Name
        values: ['revenue-optimization-*-alb']
    scrape_interval: 60s

  # Custom Revenue Analytics Metrics
  - job_name: 'revenue-analytics-custom'
    kubernetes_sd_configs:
    - role: service
      namespaces:
        names:
        - revenue-optimization
    relabel_configs:
    - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
      action: keep
      regex: true
    - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
      action: replace
      target_label: __scheme__
      regex: (https?)
    - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
      action: replace
      target_label: __metrics_path__
      regex: (.+)
    - source_labels: [__address__, __meta_kubernetes_service_annotation_prometheus_io_port]
      action: replace
      target_label: __address__
      regex: ([^:]+)(?::\d+)?;(\d+)
      replacement: $1:$2
    - action: labelmap
      regex: __meta_kubernetes_service_label_(.+)
    - source_labels: [__meta_kubernetes_namespace]
      action: replace
      target_label: kubernetes_namespace
    - source_labels: [__meta_kubernetes_service_name]
      action: replace
      target_label: kubernetes_name
    scrape_interval: 15s

  # Business Metrics (Custom)
  - job_name: 'business-metrics'
    kubernetes_sd_configs:
    - role: pod
      namespaces:
        names:
        - revenue-optimization
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_name]
      action: keep
      regex: revenue-optimization
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape_business]
      action: keep
      regex: true
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_business_path]
      action: replace
      target_label: __metrics_path__
      regex: (.+)
      replacement: ${1}
    - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_business_port]
      action: replace
      regex: ([^:]+)(?::\d+)?;(\d+)
      replacement: $1:$2
      target_label: __address__
    - action: labelmap
      regex: __meta_kubernetes_pod_label_(.+)
    - source_labels: [__meta_kubernetes_namespace]
      action: replace
      target_label: kubernetes_namespace
    - source_labels: [__meta_kubernetes_pod_name]
      action: replace
      target_label: kubernetes_pod_name
    scrape_interval: 30s
    metrics_path: /business-metrics

# Remote write configuration for long-term storage
remote_write:
  - url: "https://prometheus-remote-write.revenue-optimization.com/api/v1/write"
    queue_config:
      max_samples_per_send: 10000
      max_shards: 200
      capacity: 100000
    write_relabel_configs:
    - source_labels: [__name__]
      regex: 'revenue_.*|customer_.*|analytics_.*|performance_.*'
      action: keep

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 100GB
    wal-compression: true
