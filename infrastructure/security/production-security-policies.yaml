# Production Security & Compliance Framework
# Enterprise-grade security for E-commerce Analytics SaaS Platform

# Namespace Security Configuration
apiVersion: v1
kind: Namespace
metadata:
  name: security
  labels:
    name: security
    security.policy: "strict"

---
# Pod Security Standards
apiVersion: v1
kind: Namespace
metadata:
  name: production
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted

---
# Network Security Policies
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: production
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-analytics-service
  namespace: production
spec:
  podSelector:
    matchLabels:
      app: analytics-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from load balancer
  - from: []
    ports:
    - protocol: TCP
      port: 3002
  # Allow monitoring
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
  # Allow inter-service communication
  - from:
    - podSelector:
        matchLabels:
          app: dashboard-service
    - podSelector:
        matchLabels:
          app: onboarding-automation
    ports:
    - protocol: TCP
      port: 3002
  egress:
  # Database access
  - to: []
    ports:
    - protocol: TCP
      port: 5432
  # Redis access
  - to: []
    ports:
    - protocol: TCP
      port: 6379
  # HTTPS for external APIs
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # DNS
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53

---
# Secrets Management
apiVersion: v1
kind: Secret
metadata:
  name: database-credentials
  namespace: production
  annotations:
    kubernetes.io/managed-by: "external-secrets-operator"
type: Opaque
data:
  # Managed by AWS Secrets Manager via External Secrets Operator
  url: ""
  username: ""
  password: ""

---
apiVersion: v1
kind: Secret
metadata:
  name: redis-credentials
  namespace: production
  annotations:
    kubernetes.io/managed-by: "external-secrets-operator"
type: Opaque
data:
  url: ""
  password: ""

---
apiVersion: v1
kind: Secret
metadata:
  name: jwt-signing-key
  namespace: production
  annotations:
    kubernetes.io/managed-by: "external-secrets-operator"
type: Opaque
data:
  private-key: ""
  public-key: ""

---
# External Secrets Operator Configuration
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: aws-secrets-manager
  namespace: production
spec:
  provider:
    aws:
      service: SecretsManager
      region: us-east-1
      auth:
        jwt:
          serviceAccountRef:
            name: external-secrets-sa

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: database-secret
  namespace: production
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-secrets-manager
    kind: SecretStore
  target:
    name: database-credentials
    creationPolicy: Owner
  data:
  - secretKey: url
    remoteRef:
      key: production/database
      property: url
  - secretKey: username
    remoteRef:
      key: production/database
      property: username
  - secretKey: password
    remoteRef:
      key: production/database
      property: password

---
# RBAC Configuration
apiVersion: v1
kind: ServiceAccount
metadata:
  name: analytics-service-sa
  namespace: production
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT_ID:role/analytics-service-role

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: production
  name: analytics-service-role
rules:
- apiGroups: [""]
  resources: ["secrets", "configmaps"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: analytics-service-binding
  namespace: production
subjects:
- kind: ServiceAccount
  name: analytics-service-sa
  namespace: production
roleRef:
  kind: Role
  name: analytics-service-role
  apiGroup: rbac.authorization.k8s.io

---
# Security Context Constraints
apiVersion: v1
kind: SecurityContextConstraints
metadata:
  name: restricted-scc
allowHostDirVolumePlugin: false
allowHostIPC: false
allowHostNetwork: false
allowHostPID: false
allowHostPorts: false
allowPrivilegedContainer: false
allowedCapabilities: []
defaultAddCapabilities: []
requiredDropCapabilities:
- ALL
runAsUser:
  type: MustRunAsNonRoot
seLinuxContext:
  type: MustRunAs
fsGroup:
  type: MustRunAs
  ranges:
  - min: 1000
    max: 65535
users:
- system:serviceaccount:production:analytics-service-sa
- system:serviceaccount:production:onboarding-automation-sa

---
# Pod Security Policy
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: restricted-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
  readOnlyRootFilesystem: true

---
# Admission Controller Configuration
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingAdmissionWebhook
metadata:
  name: security-policy-webhook
webhooks:
- name: security.ecommerce-analytics.com
  clientConfig:
    service:
      name: security-webhook
      namespace: security
      path: "/validate"
  rules:
  - operations: ["CREATE", "UPDATE"]
    apiGroups: ["apps"]
    apiVersions: ["v1"]
    resources: ["deployments", "daemonsets", "statefulsets"]
  - operations: ["CREATE", "UPDATE"]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods"]
  admissionReviewVersions: ["v1", "v1beta1"]
  sideEffects: None
  failurePolicy: Fail

---
# Certificate Management
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: alb

---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: analytics-tls
  namespace: production
spec:
  secretName: analytics-tls-secret
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - analytics.ecommerce-analytics.com
  - api.ecommerce-analytics.com

---
# Security Monitoring
apiVersion: v1
kind: ConfigMap
metadata:
  name: falco-config
  namespace: security
data:
  falco.yaml: |
    rules_file:
      - /etc/falco/falco_rules.yaml
      - /etc/falco/falco_rules.local.yaml
      - /etc/falco/k8s_audit_rules.yaml
      - /etc/falco/rules.d
    
    time_format_iso_8601: true
    json_output: true
    json_include_output_property: true
    
    log_stderr: true
    log_syslog: false
    log_level: info
    
    priority: debug
    
    outputs:
      rate: 1
      max_burst: 1000
    
    syslog_output:
      enabled: false
    
    file_output:
      enabled: false
    
    stdout_output:
      enabled: true
    
    webserver:
      enabled: true
      listen_port: 8765
      k8s_healthz_endpoint: /healthz
      ssl_enabled: false
    
    grpc:
      enabled: false
    
    grpc_output:
      enabled: false

  custom_rules.yaml: |
    - rule: Unauthorized Process in Container
      desc: Detect unauthorized processes in analytics containers
      condition: >
        spawned_process and
        container and
        k8s.ns.name="production" and
        k8s.pod.label.app in (analytics-service, onboarding-automation) and
        not proc.name in (deno, node, npm, yarn)
      output: >
        Unauthorized process in analytics container
        (user=%user.name command=%proc.cmdline container=%container.name
        pod=%k8s.pod.name ns=%k8s.ns.name)
      priority: WARNING
    
    - rule: Sensitive File Access
      desc: Detect access to sensitive files
      condition: >
        open_read and
        container and
        k8s.ns.name="production" and
        (fd.name startswith /etc/passwd or
         fd.name startswith /etc/shadow or
         fd.name startswith /etc/ssh/ or
         fd.name contains "secret" or
         fd.name contains "credential")
      output: >
        Sensitive file accessed
        (user=%user.name file=%fd.name container=%container.name
        pod=%k8s.pod.name ns=%k8s.ns.name)
      priority: CRITICAL

---
# Falco DaemonSet
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: falco
  namespace: security
spec:
  selector:
    matchLabels:
      app: falco
  template:
    metadata:
      labels:
        app: falco
    spec:
      serviceAccountName: falco
      hostNetwork: true
      hostPID: true
      containers:
      - name: falco
        image: falcosecurity/falco:0.35.0
        securityContext:
          privileged: true
        args:
          - /usr/bin/falco
          - --cri=/run/containerd/containerd.sock
          - --k8s-api=https://kubernetes.default.svc.cluster.local
          - --k8s-api-cert=/var/run/secrets/kubernetes.io/serviceaccount/ca.crt
          - --k8s-api-token=/var/run/secrets/kubernetes.io/serviceaccount/token
        volumeMounts:
        - mountPath: /host/var/run/docker.sock
          name: docker-socket
        - mountPath: /host/run/containerd/containerd.sock
          name: containerd-socket
        - mountPath: /host/dev
          name: dev-fs
        - mountPath: /host/proc
          name: proc-fs
          readOnly: true
        - mountPath: /host/boot
          name: boot-fs
          readOnly: true
        - mountPath: /host/lib/modules
          name: lib-modules
          readOnly: true
        - mountPath: /host/usr
          name: usr-fs
          readOnly: true
        - mountPath: /etc/falco
          name: falco-config
      volumes:
      - name: docker-socket
        hostPath:
          path: /var/run/docker.sock
      - name: containerd-socket
        hostPath:
          path: /run/containerd/containerd.sock
      - name: dev-fs
        hostPath:
          path: /dev
      - name: proc-fs
        hostPath:
          path: /proc
      - name: boot-fs
        hostPath:
          path: /boot
      - name: lib-modules
        hostPath:
          path: /lib/modules
      - name: usr-fs
        hostPath:
          path: /usr
      - name: falco-config
        configMap:
          name: falco-config

---
# Compliance Monitoring
apiVersion: v1
kind: ConfigMap
metadata:
  name: compliance-scanner-config
  namespace: security
data:
  scan-config.yaml: |
    compliance:
      standards:
        - SOC2
        - GDPR
        - CCPA
        - PCI-DSS
      
      checks:
        - name: "encryption-at-rest"
          description: "Verify data encryption at rest"
          severity: "critical"
        
        - name: "encryption-in-transit"
          description: "Verify TLS encryption for all communications"
          severity: "critical"
        
        - name: "access-controls"
          description: "Verify RBAC and least privilege access"
          severity: "high"
        
        - name: "audit-logging"
          description: "Verify comprehensive audit logging"
          severity: "high"
        
        - name: "data-retention"
          description: "Verify data retention policies"
          severity: "medium"
      
      reporting:
        format: "json"
        output: "/var/log/compliance/scan-results.json"
        schedule: "0 2 * * *"  # Daily at 2 AM

---
# Security Scanning CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: security-scan
  namespace: security
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: security-scanner
            image: aquasec/trivy:latest
            command:
            - /bin/sh
            - -c
            - |
              # Scan all production images
              kubectl get pods -n production -o jsonpath='{.items[*].spec.containers[*].image}' | \
              tr ' ' '\n' | sort -u | while read image; do
                echo "Scanning $image..."
                trivy image --format json --output /tmp/scan-$image.json $image
              done
              
              # Scan cluster configuration
              trivy k8s --format json --output /tmp/k8s-scan.json cluster
              
              # Upload results to S3
              aws s3 cp /tmp/ s3://security-scan-results/$(date +%Y-%m-%d)/ --recursive
            volumeMounts:
            - name: scan-results
              mountPath: /tmp
          volumes:
          - name: scan-results
            emptyDir: {}
          restartPolicy: OnFailure

---
# Service Account for Security Tools
apiVersion: v1
kind: ServiceAccount
metadata:
  name: falco
  namespace: security

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: falco
rules:
- apiGroups: [""]
  resources: ["nodes", "namespaces", "pods", "replicationcontrollers", "services"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["daemonsets", "deployments", "replicasets", "statefulsets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["extensions"]
  resources: ["daemonsets", "deployments", "replicasets"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: falco
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: falco
subjects:
- kind: ServiceAccount
  name: falco
  namespace: security
