# Revenue Optimization Platform - Production Security Configuration
# Enterprise-grade security for multi-tenant SaaS with GDPR/CCPA compliance

apiVersion: v1
kind: ConfigMap
metadata:
  name: security-config
  namespace: revenue-optimization
data:
  # Security Headers Configuration
  security-headers.conf: |
    # Strict Transport Security
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    
    # Content Security Policy
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' wss: https:; frame-ancestors 'none';" always;
    
    # X-Frame-Options
    add_header X-Frame-Options "DENY" always;
    
    # X-Content-Type-Options
    add_header X-Content-Type-Options "nosniff" always;
    
    # X-XSS-Protection
    add_header X-XSS-Protection "1; mode=block" always;
    
    # Referrer Policy
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Permissions Policy
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()" always;

  # Rate Limiting Configuration
  rate-limiting.conf: |
    # Global rate limiting
    limit_req_zone $binary_remote_addr zone=global:10m rate=100r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=1000r/m;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=10r/m;
    
    # Per-tenant rate limiting
    map $http_x_tenant_id $tenant_zone {
      default "tenant_default";
      ~^(.+)$ "tenant_$1";
    }
    
    # API endpoint specific limits
    location /api/auth/ {
      limit_req zone=auth burst=5 nodelay;
      limit_req zone=global burst=20 nodelay;
    }
    
    location /api/ {
      limit_req zone=api burst=50 nodelay;
      limit_req zone=global burst=20 nodelay;
    }

---
apiVersion: v1
kind: Secret
metadata:
  name: tls-certificates
  namespace: revenue-optimization
type: kubernetes.io/tls
data:
  # TLS certificates will be managed by cert-manager
  tls.crt: ""
  tls.key: ""

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: revenue-optimization-network-policy
  namespace: revenue-optimization
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: revenue-optimization
  policyTypes:
  - Ingress
  - Egress
  
  ingress:
  # Allow ingress from ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
  
  # Allow ingress from monitoring
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
  
  # Allow inter-service communication
  - from:
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: revenue-optimization
    ports:
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 3001
    - protocol: TCP
      port: 3002
  
  egress:
  # Allow egress to DNS
  - to: []
    ports:
    - protocol: UDP
      port: 53
  
  # Allow egress to TimescaleDB
  - to:
    - namespaceSelector: {}
    ports:
    - protocol: TCP
      port: 5432
  
  # Allow egress to Redis
  - to:
    - namespaceSelector: {}
    ports:
    - protocol: TCP
      port: 6379
  
  # Allow egress to external APIs (HTTPS)
  - to: []
    ports:
    - protocol: TCP
      port: 443
  
  # Allow egress to monitoring
  - to:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: revenue-optimization-sa
  namespace: revenue-optimization
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/revenue-optimization-service-role

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: revenue-optimization
  name: revenue-optimization-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: revenue-optimization-rolebinding
  namespace: revenue-optimization
subjects:
- kind: ServiceAccount
  name: revenue-optimization-sa
  namespace: revenue-optimization
roleRef:
  kind: Role
  name: revenue-optimization-role
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: revenue-optimization-pdb
  namespace: revenue-optimization
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: revenue-optimization

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: security-policies
  namespace: revenue-optimization
data:
  # Data Protection Policy
  data-protection.json: |
    {
      "gdpr_compliance": {
        "data_retention_days": 2555,
        "right_to_be_forgotten": true,
        "data_portability": true,
        "consent_management": true,
        "data_minimization": true,
        "purpose_limitation": true
      },
      "ccpa_compliance": {
        "right_to_know": true,
        "right_to_delete": true,
        "right_to_opt_out": true,
        "non_discrimination": true
      },
      "encryption": {
        "data_at_rest": "AES-256",
        "data_in_transit": "TLS-1.3",
        "key_rotation_days": 90
      },
      "access_control": {
        "multi_factor_authentication": true,
        "role_based_access": true,
        "principle_of_least_privilege": true,
        "session_timeout_minutes": 30
      }
    }

  # Security Monitoring Configuration
  security-monitoring.json: |
    {
      "threat_detection": {
        "sql_injection_detection": true,
        "xss_detection": true,
        "csrf_protection": true,
        "brute_force_protection": true,
        "anomaly_detection": true
      },
      "audit_logging": {
        "authentication_events": true,
        "authorization_events": true,
        "data_access_events": true,
        "configuration_changes": true,
        "admin_actions": true
      },
      "incident_response": {
        "automated_blocking": true,
        "alert_escalation": true,
        "forensic_logging": true,
        "backup_isolation": true
      }
    }

  # Compliance Configuration
  compliance.json: |
    {
      "soc2_type2": {
        "security": true,
        "availability": true,
        "processing_integrity": true,
        "confidentiality": true,
        "privacy": true
      },
      "iso27001": {
        "information_security_management": true,
        "risk_assessment": true,
        "security_controls": true,
        "continuous_monitoring": true
      },
      "pci_dss": {
        "secure_network": true,
        "protect_cardholder_data": true,
        "vulnerability_management": true,
        "access_control": true,
        "monitoring": true,
        "security_policies": true
      }
    }

---
apiVersion: v1
kind: Secret
metadata:
  name: encryption-keys
  namespace: revenue-optimization
type: Opaque
data:
  # Encryption keys for data protection (base64 encoded)
  database_encryption_key: ""
  jwt_signing_key: ""
  api_encryption_key: ""
  session_encryption_key: ""

---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: revenue-optimization-tls
  namespace: revenue-optimization
spec:
  secretName: revenue-optimization-tls-secret
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - app.revenue-optimization.com
  - api.revenue-optimization.com
  - dashboard.revenue-optimization.com

---
apiVersion: networking.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: revenue-optimization-authz
  namespace: revenue-optimization
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: revenue-optimization
  rules:
  # Allow authenticated users to access API
  - from:
    - source:
        principals: ["cluster.local/ns/revenue-optimization/sa/revenue-optimization-sa"]
    to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE"]
        paths: ["/api/*"]
    when:
    - key: request.headers[authorization]
      values: ["Bearer *"]
  
  # Allow health checks
  - from:
    - source:
        principals: ["cluster.local/ns/istio-system/sa/istio-proxy"]
    to:
    - operation:
        methods: ["GET"]
        paths: ["/health", "/metrics"]
  
  # Deny all other traffic
  - {}

---
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: revenue-optimization-mtls
  namespace: revenue-optimization
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: revenue-optimization
  mtls:
    mode: STRICT

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: security-scripts
  namespace: revenue-optimization
data:
  security-scan.sh: |
    #!/bin/bash
    # Security scanning script for revenue optimization platform
    
    echo "Starting security scan..."
    
    # Check for common vulnerabilities
    echo "Checking for SQL injection vulnerabilities..."
    grep -r "SELECT.*FROM.*WHERE.*=" /app/src/ || echo "No SQL injection patterns found"
    
    echo "Checking for XSS vulnerabilities..."
    grep -r "innerHTML\|outerHTML" /app/src/ || echo "No XSS patterns found"
    
    echo "Checking for hardcoded secrets..."
    grep -r "password\|secret\|key" /app/src/ | grep -v "\.example\|\.template" || echo "No hardcoded secrets found"
    
    echo "Security scan completed"

  compliance-check.sh: |
    #!/bin/bash
    # Compliance checking script
    
    echo "Starting compliance check..."
    
    # Check GDPR compliance
    echo "Checking GDPR compliance..."
    if [ -f "/app/config/gdpr-config.json" ]; then
      echo "✅ GDPR configuration found"
    else
      echo "❌ GDPR configuration missing"
    fi
    
    # Check CCPA compliance
    echo "Checking CCPA compliance..."
    if [ -f "/app/config/ccpa-config.json" ]; then
      echo "✅ CCPA configuration found"
    else
      echo "❌ CCPA configuration missing"
    fi
    
    # Check encryption
    echo "Checking encryption configuration..."
    if [ -f "/app/config/encryption-config.json" ]; then
      echo "✅ Encryption configuration found"
    else
      echo "❌ Encryption configuration missing"
    fi
    
    echo "Compliance check completed"
