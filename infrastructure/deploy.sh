#!/bin/bash

# E-commerce Analytics SaaS - Production Deployment Script
# Automated deployment supporting 15-minute onboarding promise
# Performance targets: 24,390 events/sec, <11ms queries

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="ecommerce-analytics-saas"
ENVIRONMENTS=("development" "staging" "production")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 <environment> [options]

Environments:
  development  - Development environment
  staging      - Staging environment  
  production   - Production environment

Options:
  --validate-performance  Run performance validation after deployment
  --skip-tests           Skip deployment validation tests
  --force                Force deployment without confirmation
  --help                 Show this help message

Examples:
  $0 production --validate-performance
  $0 staging --skip-tests
  $0 development --force

Performance Targets:
  - Event Processing: 24,390+ events/second
  - Query Response: <11ms average
  - Onboarding Time: <15 minutes
  - System Uptime: 99.9% SLA
EOF
}

# Validate prerequisites
validate_prerequisites() {
    log_info "Validating prerequisites..."
    
    local missing_tools=()
    
    # Check required tools
    command -v aws >/dev/null 2>&1 || missing_tools+=("aws-cli")
    command -v terraform >/dev/null 2>&1 || missing_tools+=("terraform")
    command -v kubectl >/dev/null 2>&1 || missing_tools+=("kubectl")
    command -v helm >/dev/null 2>&1 || missing_tools+=("helm")
    command -v docker >/dev/null 2>&1 || missing_tools+=("docker")
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_info "Please install missing tools and try again"
        exit 1
    fi
    
    # Validate AWS credentials
    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        log_error "AWS credentials not configured or invalid"
        log_info "Run 'aws configure' to set up credentials"
        exit 1
    fi
    
    # Validate Terraform version
    local tf_version=$(terraform version -json | jq -r '.terraform_version')
    if [[ $(echo "$tf_version 1.0.0" | tr " " "\n" | sort -V | head -n1) != "1.0.0" ]]; then
        log_error "Terraform version $tf_version is too old. Minimum required: 1.0.0"
        exit 1
    fi
    
    log_success "Prerequisites validation passed"
}

# Deploy infrastructure
deploy_infrastructure() {
    local environment=$1
    
    log_info "Deploying infrastructure for $environment environment..."
    
    cd "$SCRIPT_DIR/terraform"
    
    # Initialize Terraform
    log_info "Initializing Terraform..."
    terraform init -backend-config="key=$environment/terraform.tfstate"
    
    # Select or create workspace
    terraform workspace select "$environment" 2>/dev/null || terraform workspace new "$environment"
    
    # Plan deployment
    log_info "Planning infrastructure deployment..."
    terraform plan \
        -var-file="environments/$environment.tfvars" \
        -out="$environment.tfplan"
    
    # Apply deployment
    if [[ "$FORCE_DEPLOY" == "true" ]] || confirm_deployment "$environment"; then
        log_info "Applying infrastructure deployment..."
        terraform apply "$environment.tfplan"
        
        # Save outputs
        terraform output -json > "../outputs/$environment-outputs.json"
        
        log_success "Infrastructure deployment completed"
    else
        log_warning "Deployment cancelled by user"
        exit 0
    fi
}

# Deploy applications
deploy_applications() {
    local environment=$1
    
    log_info "Deploying applications for $environment environment..."
    
    # Get cluster credentials
    local cluster_name=$(jq -r '.cluster_name.value' "$SCRIPT_DIR/outputs/$environment-outputs.json")
    local aws_region=$(jq -r '.aws_region.value' "$SCRIPT_DIR/outputs/$environment-outputs.json")
    
    aws eks update-kubeconfig --region "$aws_region" --name "$cluster_name"
    
    # Deploy applications using Helm
    cd "$SCRIPT_DIR/k8s"
    
    # Deploy monitoring stack first
    log_info "Deploying monitoring stack..."
    helm upgrade --install monitoring-stack \
        ../monitoring/prometheus-stack.yaml \
        --namespace monitoring \
        --create-namespace \
        --values "values/$environment-monitoring.yaml"
    
    # Deploy application services
    log_info "Deploying application services..."
    
    # Analytics Service
    helm upgrade --install analytics-service \
        ./analytics-service \
        --namespace "$environment" \
        --create-namespace \
        --values "values/$environment-analytics.yaml"
    
    # Dashboard Service
    helm upgrade --install dashboard-service \
        ./dashboard-service \
        --namespace "$environment" \
        --create-namespace \
        --values "values/$environment-dashboard.yaml"
    
    # Onboarding Automation Service
    helm upgrade --install onboarding-automation \
        ./onboarding-automation \
        --namespace "$environment" \
        --create-namespace \
        --values "values/$environment-onboarding.yaml"
    
    # Integration Service
    helm upgrade --install integration-service \
        ./integration-service \
        --namespace "$environment" \
        --create-namespace \
        --values "values/$environment-integration.yaml"
    
    # Admin Service
    helm upgrade --install admin-service \
        ./admin-service \
        --namespace "$environment" \
        --create-namespace \
        --values "values/$environment-admin.yaml"
    
    # Billing Service
    helm upgrade --install billing-service \
        ./billing-service \
        --namespace "$environment" \
        --create-namespace \
        --values "values/$environment-billing.yaml"
    
    log_success "Application deployment completed"
}

# Validate deployment
validate_deployment() {
    local environment=$1
    
    log_info "Validating deployment for $environment environment..."
    
    # Wait for pods to be ready
    log_info "Waiting for pods to be ready..."
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/instance=analytics-service -n "$environment" --timeout=300s
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/instance=dashboard-service -n "$environment" --timeout=300s
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/instance=onboarding-automation -n "$environment" --timeout=300s
    
    # Health checks
    log_info "Running health checks..."
    
    local analytics_url=$(kubectl get svc analytics-service -n "$environment" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
    local dashboard_url=$(kubectl get svc dashboard-service -n "$environment" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
    local onboarding_url=$(kubectl get svc onboarding-automation -n "$environment" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
    
    # Test service endpoints
    if curl -f "http://$analytics_url:3002/health" >/dev/null 2>&1; then
        log_success "Analytics service health check passed"
    else
        log_error "Analytics service health check failed"
        return 1
    fi
    
    if curl -f "http://$dashboard_url:3000/health" >/dev/null 2>&1; then
        log_success "Dashboard service health check passed"
    else
        log_error "Dashboard service health check failed"
        return 1
    fi
    
    if curl -f "http://$onboarding_url:3006/health" >/dev/null 2>&1; then
        log_success "Onboarding automation service health check passed"
    else
        log_error "Onboarding automation service health check failed"
        return 1
    fi
    
    log_success "Deployment validation completed"
}

# Performance validation
validate_performance() {
    local environment=$1
    
    log_info "Running performance validation for $environment environment..."
    
    # Run performance tests
    cd "$SCRIPT_DIR/scripts"
    
    log_info "Testing event processing throughput..."
    if ./performance-tests/test-event-throughput.sh "$environment"; then
        log_success "Event throughput test passed (target: 24,390 events/sec)"
    else
        log_error "Event throughput test failed"
        return 1
    fi
    
    log_info "Testing query response times..."
    if ./performance-tests/test-query-performance.sh "$environment"; then
        log_success "Query performance test passed (target: <11ms)"
    else
        log_error "Query performance test failed"
        return 1
    fi
    
    log_info "Testing onboarding automation..."
    if ./performance-tests/test-onboarding-performance.sh "$environment"; then
        log_success "Onboarding performance test passed (target: <15 minutes)"
    else
        log_error "Onboarding performance test failed"
        return 1
    fi
    
    log_success "Performance validation completed"
}

# Confirm deployment
confirm_deployment() {
    local environment=$1
    
    echo
    log_warning "You are about to deploy to the $environment environment."
    log_warning "This will update infrastructure and applications."
    echo
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        return 0
    else
        return 1
    fi
}

# Main deployment function
main() {
    local environment=""
    local validate_perf=false
    local skip_tests=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            development|staging|production)
                environment="$1"
                shift
                ;;
            --validate-performance)
                validate_perf=true
                shift
                ;;
            --skip-tests)
                skip_tests=true
                shift
                ;;
            --force)
                FORCE_DEPLOY=true
                shift
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Validate environment
    if [[ -z "$environment" ]]; then
        log_error "Environment is required"
        usage
        exit 1
    fi
    
    if [[ ! " ${ENVIRONMENTS[*]} " =~ " $environment " ]]; then
        log_error "Invalid environment: $environment"
        log_info "Valid environments: ${ENVIRONMENTS[*]}"
        exit 1
    fi
    
    # Set defaults
    FORCE_DEPLOY=${FORCE_DEPLOY:-false}
    
    log_info "Starting deployment for $environment environment"
    log_info "Performance targets: 24,390 events/sec, <11ms queries, <15min onboarding"
    
    # Execute deployment steps
    validate_prerequisites
    deploy_infrastructure "$environment"
    deploy_applications "$environment"
    
    if [[ "$skip_tests" != "true" ]]; then
        validate_deployment "$environment"
    fi
    
    if [[ "$validate_perf" == "true" ]]; then
        validate_performance "$environment"
    fi
    
    log_success "Deployment completed successfully!"
    log_info "Environment: $environment"
    log_info "Cluster: $(jq -r '.cluster_name.value' "$SCRIPT_DIR/outputs/$environment-outputs.json")"
    log_info "Region: $(jq -r '.aws_region.value' "$SCRIPT_DIR/outputs/$environment-outputs.json")"
}

# Create outputs directory if it doesn't exist
mkdir -p "$SCRIPT_DIR/outputs"

# Run main function
main "$@"
