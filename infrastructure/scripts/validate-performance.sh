#!/bin/bash

# Performance Validation Script for E-commerce Analytics SaaS
# Validates 24,390 events/sec, <11ms queries, 15-minute onboarding targets

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENVIRONMENT=${1:-"production"}
PERFORMANCE_TARGETS_FILE="$SCRIPT_DIR/../config/performance-targets.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Performance targets
declare -A TARGETS=(
    ["event_throughput"]=24390
    ["query_response_time"]=11
    ["onboarding_time"]=900
    ["system_uptime"]=99.9
    ["concurrent_users"]=1000
    ["memory_usage"]=80
    ["cpu_usage"]=70
)

# Get service endpoints
get_service_endpoints() {
    log_info "Getting service endpoints for $ENVIRONMENT environment..."
    
    ANALYTICS_URL=$(kubectl get svc analytics-service -n "$ENVIRONMENT" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "localhost")
    ONBOARDING_URL=$(kubectl get svc onboarding-automation -n "$ENVIRONMENT" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "localhost")
    DASHBOARD_URL=$(kubectl get svc dashboard-service -n "$ENVIRONMENT" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "localhost")
    
    if [[ "$ANALYTICS_URL" == "localhost" ]]; then
        ANALYTICS_URL="http://localhost:3002"
        ONBOARDING_URL="http://localhost:3006"
        DASHBOARD_URL="http://localhost:3000"
    else
        ANALYTICS_URL="http://$ANALYTICS_URL:3002"
        ONBOARDING_URL="http://$ONBOARDING_URL:3006"
        DASHBOARD_URL="http://$DASHBOARD_URL:3000"
    fi
    
    log_info "Analytics Service: $ANALYTICS_URL"
    log_info "Onboarding Service: $ONBOARDING_URL"
    log_info "Dashboard Service: $DASHBOARD_URL"
}

# Test event processing throughput
test_event_throughput() {
    log_info "Testing event processing throughput (target: ${TARGETS[event_throughput]} events/sec)..."
    
    local test_duration=60
    local concurrent_clients=50
    local events_per_client=500
    
    # Create test events
    local test_events='[
        {
            "customer_id": "perf_test_customer_1",
            "event_type": "page_view",
            "event_data": {"page": "/test", "source": "performance_test"},
            "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"
        },
        {
            "customer_id": "perf_test_customer_1",
            "event_type": "purchase",
            "event_data": {"product_id": "test_product", "amount": 99.99},
            "revenue": 99.99,
            "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"
        }
    ]'
    
    # Run load test
    log_info "Starting load test with $concurrent_clients concurrent clients for ${test_duration}s..."
    
    local start_time=$(date +%s)
    local total_events=0
    
    # Use GNU parallel or xargs for concurrent requests
    seq 1 $concurrent_clients | xargs -n1 -P$concurrent_clients -I{} bash -c "
        for i in \$(seq 1 $events_per_client); do
            curl -s -X POST '$ANALYTICS_URL/api/events/batch' \
                -H 'Content-Type: application/json' \
                -H 'X-Tenant-ID: performance-test' \
                -d '{\"events\": $test_events}' >/dev/null 2>&1 || true
        done
    " &
    
    # Wait for test duration
    sleep $test_duration
    
    # Kill background processes
    pkill -f "curl.*events/batch" 2>/dev/null || true
    
    local end_time=$(date +%s)
    local actual_duration=$((end_time - start_time))
    local total_events=$((concurrent_clients * events_per_client * 2)) # 2 events per batch
    local throughput=$((total_events / actual_duration))
    
    log_info "Processed $total_events events in ${actual_duration}s"
    log_info "Throughput: $throughput events/sec"
    
    if [[ $throughput -ge ${TARGETS[event_throughput]} ]]; then
        log_success "Event throughput test PASSED: $throughput >= ${TARGETS[event_throughput]} events/sec"
        return 0
    else
        log_error "Event throughput test FAILED: $throughput < ${TARGETS[event_throughput]} events/sec"
        return 1
    fi
}

# Test query response times
test_query_performance() {
    log_info "Testing query response times (target: <${TARGETS[query_response_time]}ms)..."
    
    local queries=(
        "/api/analytics/cohorts/retention"
        "/api/analytics/funnel/conversion"
        "/api/analytics/clv/prediction"
        "/api/analytics/events?limit=100"
        "/api/analytics/dashboard/metrics"
    )
    
    local total_time=0
    local query_count=0
    local failed_queries=0
    
    for query in "${queries[@]}"; do
        log_info "Testing query: $query"
        
        # Run multiple iterations for statistical significance
        for i in {1..10}; do
            local start_time=$(date +%s%3N)
            
            local response=$(curl -s -w "%{http_code}" \
                -H "X-Tenant-ID: performance-test" \
                "$ANALYTICS_URL$query" 2>/dev/null || echo "000")
            
            local end_time=$(date +%s%3N)
            local response_time=$((end_time - start_time))
            
            if [[ "${response: -3}" == "200" ]]; then
                total_time=$((total_time + response_time))
                query_count=$((query_count + 1))
                
                if [[ $response_time -le ${TARGETS[query_response_time]} ]]; then
                    echo "  ✓ Query $i: ${response_time}ms"
                else
                    echo "  ⚠ Query $i: ${response_time}ms (exceeds target)"
                fi
            else
                failed_queries=$((failed_queries + 1))
                echo "  ✗ Query $i: Failed (HTTP ${response: -3})"
            fi
        done
    done
    
    if [[ $query_count -gt 0 ]]; then
        local avg_response_time=$((total_time / query_count))
        log_info "Average response time: ${avg_response_time}ms"
        log_info "Failed queries: $failed_queries"
        
        if [[ $avg_response_time -le ${TARGETS[query_response_time]} && $failed_queries -eq 0 ]]; then
            log_success "Query performance test PASSED: ${avg_response_time}ms <= ${TARGETS[query_response_time]}ms"
            return 0
        else
            log_error "Query performance test FAILED: ${avg_response_time}ms > ${TARGETS[query_response_time]}ms or $failed_queries failures"
            return 1
        fi
    else
        log_error "Query performance test FAILED: No successful queries"
        return 1
    fi
}

# Test onboarding performance
test_onboarding_performance() {
    log_info "Testing onboarding performance (target: <${TARGETS[onboarding_time]}s)..."
    
    local test_requests=(
        '{"companyName": "Performance Test Co 1", "email": "<EMAIL>", "plan": "professional", "expectedVolume": 10000}'
        '{"companyName": "Performance Test Co 2", "email": "<EMAIL>", "plan": "enterprise", "expectedVolume": 50000}'
        '{"companyName": "Performance Test Co 3", "email": "<EMAIL>", "plan": "professional", "expectedVolume": 25000}'
    )
    
    local total_time=0
    local successful_onboardings=0
    local failed_onboardings=0
    
    for request in "${test_requests[@]}"; do
        log_info "Starting onboarding test..."
        
        local start_time=$(date +%s)
        
        # Start onboarding
        local response=$(curl -s -X POST "$ONBOARDING_URL/api/onboarding/start" \
            -H "Content-Type: application/json" \
            -d "$request" 2>/dev/null || echo '{"success": false}')
        
        local onboarding_id=$(echo "$response" | jq -r '.data.onboardingId // empty' 2>/dev/null)
        
        if [[ -n "$onboarding_id" ]]; then
            # Poll for completion
            local max_wait=1200  # 20 minutes max
            local wait_time=0
            local completed=false
            
            while [[ $wait_time -lt $max_wait ]]; do
                sleep 10
                wait_time=$((wait_time + 10))
                
                local status_response=$(curl -s "$ONBOARDING_URL/api/onboarding/status/$onboarding_id" 2>/dev/null || echo '{"success": false}')
                local status=$(echo "$status_response" | jq -r '.data.status // "unknown"' 2>/dev/null)
                
                if [[ "$status" == "success" ]]; then
                    completed=true
                    break
                elif [[ "$status" == "failed" ]]; then
                    break
                fi
            done
            
            local end_time=$(date +%s)
            local onboarding_time=$((end_time - start_time))
            
            if [[ "$completed" == "true" ]]; then
                total_time=$((total_time + onboarding_time))
                successful_onboardings=$((successful_onboardings + 1))
                
                log_info "Onboarding completed in ${onboarding_time}s"
                
                if [[ $onboarding_time -le ${TARGETS[onboarding_time]} ]]; then
                    echo "  ✓ Onboarding time: ${onboarding_time}s"
                else
                    echo "  ⚠ Onboarding time: ${onboarding_time}s (exceeds target)"
                fi
            else
                failed_onboardings=$((failed_onboardings + 1))
                log_error "Onboarding failed or timed out after ${onboarding_time}s"
            fi
        else
            failed_onboardings=$((failed_onboardings + 1))
            log_error "Failed to start onboarding"
        fi
    done
    
    if [[ $successful_onboardings -gt 0 ]]; then
        local avg_onboarding_time=$((total_time / successful_onboardings))
        log_info "Average onboarding time: ${avg_onboarding_time}s"
        log_info "Successful onboardings: $successful_onboardings"
        log_info "Failed onboardings: $failed_onboardings"
        
        if [[ $avg_onboarding_time -le ${TARGETS[onboarding_time]} && $failed_onboardings -eq 0 ]]; then
            log_success "Onboarding performance test PASSED: ${avg_onboarding_time}s <= ${TARGETS[onboarding_time]}s"
            return 0
        else
            log_error "Onboarding performance test FAILED: ${avg_onboarding_time}s > ${TARGETS[onboarding_time]}s or $failed_onboardings failures"
            return 1
        fi
    else
        log_error "Onboarding performance test FAILED: No successful onboardings"
        return 1
    fi
}

# Test system resource usage
test_system_resources() {
    log_info "Testing system resource usage..."
    
    if command -v kubectl >/dev/null 2>&1; then
        # Get CPU and memory usage from Kubernetes metrics
        local cpu_usage=$(kubectl top nodes --no-headers 2>/dev/null | awk '{sum+=$3} END {print sum/NR}' | sed 's/%//' || echo "0")
        local memory_usage=$(kubectl top nodes --no-headers 2>/dev/null | awk '{sum+=$5} END {print sum/NR}' | sed 's/%//' || echo "0")
        
        log_info "Average CPU usage: ${cpu_usage}%"
        log_info "Average memory usage: ${memory_usage}%"
        
        local cpu_ok=true
        local memory_ok=true
        
        if (( $(echo "$cpu_usage > ${TARGETS[cpu_usage]}" | bc -l) )); then
            log_warning "CPU usage exceeds target: ${cpu_usage}% > ${TARGETS[cpu_usage]}%"
            cpu_ok=false
        fi
        
        if (( $(echo "$memory_usage > ${TARGETS[memory_usage]}" | bc -l) )); then
            log_warning "Memory usage exceeds target: ${memory_usage}% > ${TARGETS[memory_usage]}%"
            memory_ok=false
        fi
        
        if [[ "$cpu_ok" == "true" && "$memory_ok" == "true" ]]; then
            log_success "System resource test PASSED"
            return 0
        else
            log_error "System resource test FAILED"
            return 1
        fi
    else
        log_warning "kubectl not available, skipping system resource test"
        return 0
    fi
}

# Generate performance report
generate_report() {
    local test_results=("$@")
    local passed_tests=0
    local total_tests=${#test_results[@]}
    
    for result in "${test_results[@]}"; do
        if [[ "$result" == "0" ]]; then
            passed_tests=$((passed_tests + 1))
        fi
    done
    
    local success_rate=$((passed_tests * 100 / total_tests))
    
    echo
    echo "=========================================="
    echo "Performance Validation Report"
    echo "=========================================="
    echo "Environment: $ENVIRONMENT"
    echo "Timestamp: $(date -u +%Y-%m-%dT%H:%M:%SZ)"
    echo "Tests Passed: $passed_tests/$total_tests ($success_rate%)"
    echo
    echo "Performance Targets:"
    echo "  Event Throughput: ${TARGETS[event_throughput]} events/sec"
    echo "  Query Response: <${TARGETS[query_response_time]}ms"
    echo "  Onboarding Time: <${TARGETS[onboarding_time]}s"
    echo "  CPU Usage: <${TARGETS[cpu_usage]}%"
    echo "  Memory Usage: <${TARGETS[memory_usage]}%"
    echo
    
    if [[ $success_rate -eq 100 ]]; then
        log_success "All performance tests PASSED! 🎉"
        echo "✅ Platform ready for production deployment"
        echo "✅ 15-minute onboarding promise validated"
        echo "✅ Performance targets exceeded"
        return 0
    else
        log_error "Performance validation FAILED"
        echo "❌ $((total_tests - passed_tests)) test(s) failed"
        echo "❌ Performance optimization required"
        return 1
    fi
}

# Main execution
main() {
    log_info "Starting performance validation for $ENVIRONMENT environment"
    log_info "Performance targets: 24,390 events/sec, <11ms queries, <15min onboarding"
    
    # Get service endpoints
    get_service_endpoints
    
    # Run performance tests
    local test_results=()
    
    test_event_throughput
    test_results+=($?)
    
    test_query_performance
    test_results+=($?)
    
    test_onboarding_performance
    test_results+=($?)
    
    test_system_resources
    test_results+=($?)
    
    # Generate report
    generate_report "${test_results[@]}"
}

# Run main function
main "$@"
