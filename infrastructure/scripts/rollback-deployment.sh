#!/bin/bash

# Automated Rollback Script for E-commerce Analytics SaaS
# Zero-downtime rollback with performance validation

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENVIRONMENT=${1:-"production"}
ROLLBACK_VERSION=${2:-"previous"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Services to rollback
SERVICES=(
    "analytics-service"
    "dashboard-service"
    "onboarding-automation"
    "integration-service"
    "admin-service"
    "billing-service"
)

# Get previous deployment version
get_previous_version() {
    local service=$1
    
    log_info "Getting previous version for $service..."
    
    # Get rollout history
    local previous_revision=$(kubectl rollout history deployment "$service" -n "$ENVIRONMENT" --limit=2 | tail -n 1 | awk '{print $1}')
    
    if [[ -n "$previous_revision" && "$previous_revision" != "REVISION" ]]; then
        echo "$previous_revision"
    else
        log_error "Could not determine previous version for $service"
        return 1
    fi
}

# Rollback service
rollback_service() {
    local service=$1
    local target_revision=${2:-""}
    
    log_info "Rolling back $service in $ENVIRONMENT environment..."
    
    # Get current status
    local current_replicas=$(kubectl get deployment "$service" -n "$ENVIRONMENT" -o jsonpath='{.spec.replicas}')
    local ready_replicas=$(kubectl get deployment "$service" -n "$ENVIRONMENT" -o jsonpath='{.status.readyReplicas}')
    
    log_info "Current state: $ready_replicas/$current_replicas replicas ready"
    
    # Perform rollback
    if [[ -n "$target_revision" ]]; then
        kubectl rollout undo deployment "$service" -n "$ENVIRONMENT" --to-revision="$target_revision"
    else
        kubectl rollout undo deployment "$service" -n "$ENVIRONMENT"
    fi
    
    # Wait for rollback to complete
    log_info "Waiting for rollback to complete..."
    if kubectl rollout status deployment "$service" -n "$ENVIRONMENT" --timeout=600s; then
        log_success "Rollback completed for $service"
        return 0
    else
        log_error "Rollback failed for $service"
        return 1
    fi
}

# Validate service health after rollback
validate_service_health() {
    local service=$1
    
    log_info "Validating health for $service..."
    
    # Get service endpoint
    local service_url=""
    case $service in
        "analytics-service")
            service_url="http://$(kubectl get svc analytics-service -n "$ENVIRONMENT" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "localhost"):3002"
            ;;
        "dashboard-service")
            service_url="http://$(kubectl get svc dashboard-service -n "$ENVIRONMENT" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "localhost"):3000"
            ;;
        "onboarding-automation")
            service_url="http://$(kubectl get svc onboarding-automation -n "$ENVIRONMENT" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "localhost"):3006"
            ;;
        "integration-service")
            service_url="http://$(kubectl get svc integration-service -n "$ENVIRONMENT" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "localhost"):3001"
            ;;
        "admin-service")
            service_url="http://$(kubectl get svc admin-service -n "$ENVIRONMENT" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "localhost"):3005"
            ;;
        "billing-service")
            service_url="http://$(kubectl get svc billing-service -n "$ENVIRONMENT" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "localhost"):3003"
            ;;
    esac
    
    # Health check with retries
    local max_attempts=10
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "Health check attempt $attempt/$max_attempts for $service..."
        
        if curl -f -s "$service_url/health" >/dev/null 2>&1; then
            log_success "Health check passed for $service"
            return 0
        else
            log_warning "Health check failed for $service (attempt $attempt/$max_attempts)"
            sleep 30
            attempt=$((attempt + 1))
        fi
    done
    
    log_error "Health check failed for $service after $max_attempts attempts"
    return 1
}

# Run performance validation after rollback
validate_performance_post_rollback() {
    log_info "Running performance validation after rollback..."
    
    if [[ -f "$SCRIPT_DIR/validate-performance.sh" ]]; then
        if "$SCRIPT_DIR/validate-performance.sh" "$ENVIRONMENT"; then
            log_success "Performance validation passed after rollback"
            return 0
        else
            log_error "Performance validation failed after rollback"
            return 1
        fi
    else
        log_warning "Performance validation script not found, skipping..."
        return 0
    fi
}

# Notify team about rollback
notify_rollback() {
    local status=$1
    local failed_services=("${@:2}")
    
    log_info "Sending rollback notification..."
    
    local message=""
    if [[ "$status" == "success" ]]; then
        message="✅ Rollback completed successfully for $ENVIRONMENT environment"
    else
        message="❌ Rollback failed for $ENVIRONMENT environment"
        if [[ ${#failed_services[@]} -gt 0 ]]; then
            message="$message\nFailed services: ${failed_services[*]}"
        fi
    fi
    
    # Send Slack notification (if webhook configured)
    if [[ -n "${SLACK_WEBHOOK_URL:-}" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"$message\"}" \
            "$SLACK_WEBHOOK_URL" >/dev/null 2>&1 || true
    fi
    
    # Send email notification (if configured)
    if command -v aws >/dev/null 2>&1 && [[ -n "${SNS_TOPIC_ARN:-}" ]]; then
        aws sns publish \
            --topic-arn "$SNS_TOPIC_ARN" \
            --message "$message" \
            --subject "Deployment Rollback - $ENVIRONMENT" >/dev/null 2>&1 || true
    fi
    
    log_info "Notifications sent"
}

# Create rollback report
create_rollback_report() {
    local rollback_results=("$@")
    local successful_rollbacks=0
    local total_services=${#SERVICES[@]}
    local failed_services=()
    
    for i in "${!rollback_results[@]}"; do
        if [[ "${rollback_results[$i]}" == "0" ]]; then
            successful_rollbacks=$((successful_rollbacks + 1))
        else
            failed_services+=("${SERVICES[$i]}")
        fi
    done
    
    local success_rate=$((successful_rollbacks * 100 / total_services))
    
    # Create report
    local report_file="/tmp/rollback-report-$ENVIRONMENT-$(date +%Y%m%d-%H%M%S).json"
    
    cat > "$report_file" << EOF
{
    "rollback_summary": {
        "environment": "$ENVIRONMENT",
        "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "total_services": $total_services,
        "successful_rollbacks": $successful_rollbacks,
        "failed_rollbacks": $((total_services - successful_rollbacks)),
        "success_rate": $success_rate,
        "failed_services": [$(printf '"%s",' "${failed_services[@]}" | sed 's/,$//')]
    },
    "service_details": [
$(for i in "${!SERVICES[@]}"; do
    echo "        {"
    echo "            \"service\": \"${SERVICES[$i]}\","
    echo "            \"status\": \"$([ "${rollback_results[$i]}" == "0" ] && echo "success" || echo "failed")\","
    echo "            \"rollback_time\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\""
    echo "        }$([ $i -lt $((${#SERVICES[@]} - 1)) ] && echo ",")"
done)
    ]
}
EOF
    
    log_info "Rollback report created: $report_file"
    
    # Upload to S3 if configured
    if command -v aws >/dev/null 2>&1 && [[ -n "${ROLLBACK_REPORTS_BUCKET:-}" ]]; then
        aws s3 cp "$report_file" "s3://$ROLLBACK_REPORTS_BUCKET/rollback-reports/" || true
        log_info "Report uploaded to S3"
    fi
    
    echo
    echo "=========================================="
    echo "Rollback Report"
    echo "=========================================="
    echo "Environment: $ENVIRONMENT"
    echo "Timestamp: $(date -u +%Y-%m-%dT%H:%M:%SZ)"
    echo "Services Rolled Back: $successful_rollbacks/$total_services ($success_rate%)"
    
    if [[ ${#failed_services[@]} -gt 0 ]]; then
        echo "Failed Services: ${failed_services[*]}"
    fi
    
    echo "Report File: $report_file"
    echo
    
    if [[ $success_rate -eq 100 ]]; then
        log_success "Rollback completed successfully! 🎉"
        return 0
    else
        log_error "Rollback completed with failures"
        return 1
    fi
}

# Emergency rollback (faster, less validation)
emergency_rollback() {
    log_warning "Performing EMERGENCY rollback - minimal validation"
    
    local rollback_results=()
    
    # Rollback all services in parallel
    for service in "${SERVICES[@]}"; do
        (
            log_info "Emergency rollback: $service"
            kubectl rollout undo deployment "$service" -n "$ENVIRONMENT" --timeout=300s
        ) &
    done
    
    # Wait for all rollbacks to complete
    wait
    
    # Quick health checks
    sleep 60
    for service in "${SERVICES[@]}"; do
        if kubectl get deployment "$service" -n "$ENVIRONMENT" >/dev/null 2>&1; then
            rollback_results+=(0)
        else
            rollback_results+=(1)
        fi
    done
    
    create_rollback_report "${rollback_results[@]}"
}

# Main rollback function
main() {
    local emergency_mode=${EMERGENCY_ROLLBACK:-false}
    
    if [[ "$emergency_mode" == "true" ]]; then
        emergency_rollback
        return $?
    fi
    
    log_info "Starting rollback for $ENVIRONMENT environment"
    log_info "Target version: $ROLLBACK_VERSION"
    
    # Validate prerequisites
    if ! command -v kubectl >/dev/null 2>&1; then
        log_error "kubectl is required but not installed"
        exit 1
    fi
    
    # Check if environment exists
    if ! kubectl get namespace "$ENVIRONMENT" >/dev/null 2>&1; then
        log_error "Environment $ENVIRONMENT does not exist"
        exit 1
    fi
    
    # Perform rollback for each service
    local rollback_results=()
    local failed_services=()
    
    for service in "${SERVICES[@]}"; do
        log_info "Processing rollback for $service..."
        
        # Get target revision if not specified
        local target_revision=""
        if [[ "$ROLLBACK_VERSION" == "previous" ]]; then
            target_revision=$(get_previous_version "$service")
            if [[ $? -ne 0 ]]; then
                rollback_results+=(1)
                failed_services+=("$service")
                continue
            fi
        else
            target_revision="$ROLLBACK_VERSION"
        fi
        
        # Perform rollback
        if rollback_service "$service" "$target_revision"; then
            # Validate health
            if validate_service_health "$service"; then
                rollback_results+=(0)
                log_success "Rollback successful for $service"
            else
                rollback_results+=(1)
                failed_services+=("$service")
                log_error "Health validation failed for $service after rollback"
            fi
        else
            rollback_results+=(1)
            failed_services+=("$service")
        fi
        
        # Brief pause between services
        sleep 10
    done
    
    # Validate overall system performance
    if [[ ${#failed_services[@]} -eq 0 ]]; then
        validate_performance_post_rollback
    fi
    
    # Create report and notify
    local report_result
    create_rollback_report "${rollback_results[@]}"
    report_result=$?
    
    if [[ $report_result -eq 0 ]]; then
        notify_rollback "success"
    else
        notify_rollback "failure" "${failed_services[@]}"
    fi
    
    return $report_result
}

# Handle script arguments
case "${1:-}" in
    --emergency)
        EMERGENCY_ROLLBACK=true
        ENVIRONMENT=${2:-"production"}
        ;;
    --help)
        echo "Usage: $0 [environment] [version] [--emergency]"
        echo "  environment: Target environment (default: production)"
        echo "  version: Target version (default: previous)"
        echo "  --emergency: Perform emergency rollback with minimal validation"
        exit 0
        ;;
esac

# Run main function
main
