#!/bin/bash

# Revenue Analytics Node Initialization Script
# Optimized for high-performance analytics workloads with 24,390+ events/sec capability

set -o xtrace

# Variables
CLUSTER_NAME=${cluster_name}
B64_CLUSTER_CA=${ca_data}
API_SERVER_URL=${endpoint}

# System optimizations for high-performance analytics
echo "Applying system optimizations for revenue analytics workloads..."

# Kernel parameters for high-performance networking and I/O
cat <<EOF > /etc/sysctl.d/99-revenue-analytics.conf
# Network optimizations
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr
net.ipv4.tcp_slow_start_after_idle = 0

# File system optimizations
fs.file-max = 2097152
fs.nr_open = 1048576

# Memory management
vm.swappiness = 1
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
vm.vfs_cache_pressure = 50

# Process limits
kernel.pid_max = 4194304
EOF

sysctl -p /etc/sysctl.d/99-revenue-analytics.conf

# Increase file descriptor limits
cat <<EOF > /etc/security/limits.d/99-revenue-analytics.conf
* soft nofile 1048576
* hard nofile 1048576
* soft nproc 1048576
* hard nproc 1048576
root soft nofile 1048576
root hard nofile 1048576
root soft nproc 1048576
root hard nproc 1048576
EOF

# Configure CPU governor for performance
echo performance | tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# Disable transparent huge pages for better database performance
echo never > /sys/kernel/mm/transparent_hugepage/enabled
echo never > /sys/kernel/mm/transparent_hugepage/defrag

# Make transparent huge pages setting persistent
cat <<EOF > /etc/systemd/system/disable-thp.service
[Unit]
Description=Disable Transparent Huge Pages (THP)
DefaultDependencies=no
After=sysinit.target local-fs.target
Before=mongod.service

[Service]
Type=oneshot
ExecStart=/bin/sh -c 'echo never | tee /sys/kernel/mm/transparent_hugepage/enabled > /dev/null'
ExecStart=/bin/sh -c 'echo never | tee /sys/kernel/mm/transparent_hugepage/defrag > /dev/null'

[Install]
WantedBy=basic.target
EOF

systemctl enable disable-thp.service
systemctl start disable-thp.service

# Install additional packages for analytics workloads
yum update -y
yum install -y \
    htop \
    iotop \
    sysstat \
    perf \
    numactl \
    tcpdump \
    netstat-nat \
    jq \
    awscli \
    amazon-cloudwatch-agent

# Configure CloudWatch agent for enhanced monitoring
cat <<EOF > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json
{
    "agent": {
        "metrics_collection_interval": 10,
        "run_as_user": "cwagent"
    },
    "metrics": {
        "namespace": "EKS/RevenueAnalytics",
        "metrics_collected": {
            "cpu": {
                "measurement": [
                    "cpu_usage_idle",
                    "cpu_usage_iowait",
                    "cpu_usage_user",
                    "cpu_usage_system"
                ],
                "metrics_collection_interval": 10,
                "totalcpu": true
            },
            "disk": {
                "measurement": [
                    "used_percent"
                ],
                "metrics_collection_interval": 10,
                "resources": [
                    "*"
                ]
            },
            "diskio": {
                "measurement": [
                    "io_time",
                    "read_bytes",
                    "write_bytes",
                    "reads",
                    "writes"
                ],
                "metrics_collection_interval": 10,
                "resources": [
                    "*"
                ]
            },
            "mem": {
                "measurement": [
                    "mem_used_percent"
                ],
                "metrics_collection_interval": 10
            },
            "netstat": {
                "measurement": [
                    "tcp_established",
                    "tcp_time_wait"
                ],
                "metrics_collection_interval": 10
            },
            "swap": {
                "measurement": [
                    "swap_used_percent"
                ],
                "metrics_collection_interval": 10
            }
        }
    },
    "logs": {
        "logs_collected": {
            "files": {
                "collect_list": [
                    {
                        "file_path": "/var/log/messages",
                        "log_group_name": "/aws/eks/revenue-analytics/system",
                        "log_stream_name": "{instance_id}/messages"
                    },
                    {
                        "file_path": "/var/log/dmesg",
                        "log_group_name": "/aws/eks/revenue-analytics/system",
                        "log_stream_name": "{instance_id}/dmesg"
                    }
                ]
            }
        }
    }
}
EOF

# Start CloudWatch agent
/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl \
    -a fetch-config \
    -m ec2 \
    -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json \
    -s

# Configure EKS node
/etc/eks/bootstrap.sh $CLUSTER_NAME \
    --b64-cluster-ca $B64_CLUSTER_CA \
    --apiserver-endpoint $API_SERVER_URL \
    --container-runtime containerd \
    --kubelet-extra-args '--node-labels=role=revenue-analytics,workload=high-performance,analytics-tier=production,performance-tier=optimized --max-pods=110 --kube-reserved=cpu=1000m,memory=1Gi,ephemeral-storage=1Gi --system-reserved=cpu=1000m,memory=1Gi,ephemeral-storage=1Gi --eviction-hard=memory.available<500Mi,nodefs.available<10%'

# Configure containerd for performance
mkdir -p /etc/containerd
cat <<EOF > /etc/containerd/config.toml
version = 2

[plugins]
  [plugins."io.containerd.grpc.v1.cri"]
    [plugins."io.containerd.grpc.v1.cri".containerd]
      [plugins."io.containerd.grpc.v1.cri".containerd.runtimes]
        [plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc]
          runtime_type = "io.containerd.runc.v2"
          [plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc.options]
            SystemdCgroup = true
    [plugins."io.containerd.grpc.v1.cri".registry]
      [plugins."io.containerd.grpc.v1.cri".registry.mirrors]
        [plugins."io.containerd.grpc.v1.cri".registry.mirrors."docker.io"]
          endpoint = ["https://registry-1.docker.io"]
EOF

systemctl restart containerd

# Install and configure node exporter for Prometheus monitoring
useradd --no-create-home --shell /bin/false node_exporter
wget https://github.com/prometheus/node_exporter/releases/download/v1.6.1/node_exporter-1.6.1.linux-amd64.tar.gz
tar xvf node_exporter-1.6.1.linux-amd64.tar.gz
cp node_exporter-1.6.1.linux-amd64/node_exporter /usr/local/bin/
chown node_exporter:node_exporter /usr/local/bin/node_exporter

cat <<EOF > /etc/systemd/system/node_exporter.service
[Unit]
Description=Node Exporter
Wants=network-online.target
After=network-online.target

[Service]
User=node_exporter
Group=node_exporter
Type=simple
ExecStart=/usr/local/bin/node_exporter --web.listen-address=:9100

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable node_exporter
systemctl start node_exporter

# Configure log rotation for container logs
cat <<EOF > /etc/logrotate.d/containers
/var/log/containers/*.log {
    daily
    missingok
    rotate 7
    compress
    notifempty
    create 0644 root root
    postrotate
        /bin/kill -USR1 \$(cat /var/run/rsyslogd.pid 2> /dev/null) 2> /dev/null || true
    endscript
}
EOF

# Set up disk performance monitoring
cat <<EOF > /usr/local/bin/disk-performance-monitor.sh
#!/bin/bash
# Monitor disk performance for revenue analytics workloads

while true; do
    iostat -x 1 1 | grep -E "(Device|nvme)" | tee -a /var/log/disk-performance.log
    sleep 60
done
EOF

chmod +x /usr/local/bin/disk-performance-monitor.sh

cat <<EOF > /etc/systemd/system/disk-performance-monitor.service
[Unit]
Description=Disk Performance Monitor
After=network.target

[Service]
Type=simple
ExecStart=/usr/local/bin/disk-performance-monitor.sh
Restart=always
User=root

[Install]
WantedBy=multi-user.target
EOF

systemctl enable disk-performance-monitor.service
systemctl start disk-performance-monitor.service

# Configure network performance monitoring
cat <<EOF > /usr/local/bin/network-performance-monitor.sh
#!/bin/bash
# Monitor network performance for revenue analytics workloads

while true; do
    ss -tuln | wc -l | awk '{print "Active connections: " \$1}' | tee -a /var/log/network-performance.log
    netstat -i | grep -v "Kernel\|Iface\|lo" | awk '{print \$1 " RX: " \$3 " TX: " \$7}' | tee -a /var/log/network-performance.log
    echo "---" | tee -a /var/log/network-performance.log
    sleep 60
done
EOF

chmod +x /usr/local/bin/network-performance-monitor.sh

cat <<EOF > /etc/systemd/system/network-performance-monitor.service
[Unit]
Description=Network Performance Monitor
After=network.target

[Service]
Type=simple
ExecStart=/usr/local/bin/network-performance-monitor.sh
Restart=always
User=root

[Install]
WantedBy=multi-user.target
EOF

systemctl enable network-performance-monitor.service
systemctl start network-performance-monitor.service

# Create performance tuning script for runtime optimization
cat <<EOF > /usr/local/bin/runtime-performance-tuning.sh
#!/bin/bash
# Runtime performance tuning for revenue analytics workloads

# Optimize CPU frequency scaling
for cpu in /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor; do
    echo performance > \$cpu 2>/dev/null || true
done

# Optimize I/O scheduler for NVMe drives
for disk in /sys/block/nvme*; do
    echo none > \$disk/queue/scheduler 2>/dev/null || true
done

# Optimize network queue lengths
echo 5000 > /proc/sys/net/core/netdev_max_backlog 2>/dev/null || true

# Log optimization status
echo "\$(date): Performance optimizations applied" >> /var/log/performance-tuning.log
EOF

chmod +x /usr/local/bin/runtime-performance-tuning.sh

# Create systemd service for runtime tuning
cat <<EOF > /etc/systemd/system/runtime-performance-tuning.service
[Unit]
Description=Runtime Performance Tuning
After=multi-user.target

[Service]
Type=oneshot
ExecStart=/usr/local/bin/runtime-performance-tuning.sh
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

systemctl enable runtime-performance-tuning.service
systemctl start runtime-performance-tuning.service

# Final system status check
echo "Revenue Analytics node initialization completed at \$(date)" >> /var/log/node-initialization.log
echo "System ready for high-performance analytics workloads" >> /var/log/node-initialization.log

# Signal completion
/opt/aws/bin/cfn-signal -e \$? --stack \${AWS::StackName} --resource AutoScalingGroup --region \${AWS::Region} 2>/dev/null || true
