# Revenue Optimization & Growth Analytics Platform - Production Infrastructure
# Enhanced Terraform configuration optimized for 24,390+ events/sec and <11ms query performance

# Enhanced EKS Configuration for Revenue Analytics Workloads
resource "aws_eks_node_group" "revenue_analytics" {
  cluster_name    = module.eks.cluster_name
  node_group_name = "revenue-analytics-optimized"
  node_role_arn   = aws_iam_role.revenue_analytics_node_role.arn
  subnet_ids      = module.vpc.private_subnets

  # High-performance instances optimized for analytics workloads
  instance_types = ["c6i.4xlarge", "c6i.8xlarge", "m6i.4xlarge"]
  capacity_type  = "ON_DEMAND"

  scaling_config {
    desired_size = 3
    max_size     = 12
    min_size     = 2
  }

  # Enhanced storage for analytics data processing
  disk_size = 500
  
  # Performance optimizations
  launch_template {
    name    = aws_launch_template.revenue_analytics.name
    version = aws_launch_template.revenue_analytics.latest_version
  }

  labels = {
    role                = "revenue-analytics"
    workload           = "high-performance"
    "analytics-tier"   = "production"
    "performance-tier" = "optimized"
  }

  taints {
    key    = "revenue-analytics"
    value  = "dedicated"
    effect = "NO_SCHEDULE"
  }

  # Ensure proper update strategy
  update_config {
    max_unavailable_percentage = 25
  }

  depends_on = [
    aws_iam_role_policy_attachment.revenue_analytics_worker_node_policy,
    aws_iam_role_policy_attachment.revenue_analytics_cni_policy,
    aws_iam_role_policy_attachment.revenue_analytics_container_registry_policy,
  ]

  tags = merge(local.common_tags, {
    Name = "revenue-analytics-optimized"
    "Performance-Tier" = "High"
  })
}

# Launch template for revenue analytics nodes
resource "aws_launch_template" "revenue_analytics" {
  name_prefix   = "revenue-analytics-"
  image_id      = data.aws_ami.eks_worker.id
  instance_type = "c6i.4xlarge"

  vpc_security_group_ids = [aws_security_group.revenue_analytics_nodes.id]

  # Enhanced storage configuration
  block_device_mappings {
    device_name = "/dev/xvda"
    ebs {
      volume_size           = 500
      volume_type           = "gp3"
      iops                  = 16000
      throughput            = 1000
      encrypted             = true
      delete_on_termination = true
    }
  }

  # Performance optimizations
  user_data = base64encode(templatefile("${path.module}/templates/revenue-analytics-userdata.sh", {
    cluster_name = local.cluster_name
    endpoint     = module.eks.cluster_endpoint
    ca_data      = module.eks.cluster_certificate_authority_data
  }))

  tag_specifications {
    resource_type = "instance"
    tags = merge(local.common_tags, {
      Name = "revenue-analytics-worker"
    })
  }

  tags = local.common_tags
}

# IAM Role for Revenue Analytics Nodes
resource "aws_iam_role" "revenue_analytics_node_role" {
  name = "${local.cluster_name}-revenue-analytics-node-role"

  assume_role_policy = jsonencode({
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Principal = {
        Service = "ec2.amazonaws.com"
      }
    }]
    Version = "2012-10-17"
  })

  tags = local.common_tags
}

resource "aws_iam_role_policy_attachment" "revenue_analytics_worker_node_policy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy"
  role       = aws_iam_role.revenue_analytics_node_role.name
}

resource "aws_iam_role_policy_attachment" "revenue_analytics_cni_policy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy"
  role       = aws_iam_role.revenue_analytics_node_role.name
}

resource "aws_iam_role_policy_attachment" "revenue_analytics_container_registry_policy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly"
  role       = aws_iam_role.revenue_analytics_node_role.name
}

# Enhanced Security Group for Revenue Analytics Nodes
resource "aws_security_group" "revenue_analytics_nodes" {
  name_prefix = "${local.cluster_name}-revenue-analytics-nodes-"
  vpc_id      = module.vpc.vpc_id

  # Allow communication with EKS cluster
  ingress {
    description = "EKS Cluster Communication"
    from_port   = 1025
    to_port     = 65535
    protocol    = "tcp"
    security_groups = [aws_security_group.eks_cluster.id]
  }

  # Allow node-to-node communication
  ingress {
    description = "Node to Node Communication"
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    self        = true
  }

  # Allow TimescaleDB access
  ingress {
    description = "TimescaleDB Access"
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    security_groups = [aws_security_group.rds.id]
  }

  # Allow Redis access
  ingress {
    description = "Redis Access"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    security_groups = [aws_security_group.elasticache.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(local.common_tags, {
    Name = "${local.cluster_name}-revenue-analytics-nodes-sg"
  })
}

# Enhanced RDS Configuration for TimescaleDB Production
resource "aws_db_instance" "timescaledb_production" {
  identifier = "${local.cluster_name}-timescaledb-production"

  # High-performance instance for 24,390+ events/sec
  engine         = "postgres"
  engine_version = "15.4"
  instance_class = "db.r6i.4xlarge"  # Memory optimized for analytics

  # Enhanced storage configuration
  allocated_storage     = 2000
  max_allocated_storage = 10000
  storage_type          = "gp3"
  storage_encrypted     = true
  iops                  = 16000
  storage_throughput    = 1000

  # Database configuration
  db_name  = "revenue_analytics_production"
  username = var.database_username
  password = var.database_password

  # Network configuration
  db_subnet_group_name   = aws_db_subnet_group.main.name
  vpc_security_group_ids = [aws_security_group.rds_production.id]
  publicly_accessible    = false

  # Backup and maintenance
  backup_retention_period = 35
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:06:00"
  copy_tags_to_snapshot  = true

  # Performance configuration
  parameter_group_name = aws_db_parameter_group.timescaledb_production.name
  monitoring_interval  = 15  # Enhanced monitoring
  monitoring_role_arn  = aws_iam_role.rds_monitoring.arn

  # High availability and disaster recovery
  multi_az               = true
  deletion_protection    = true
  skip_final_snapshot    = false
  final_snapshot_identifier = "${local.cluster_name}-timescaledb-final-snapshot"

  # Performance Insights
  performance_insights_enabled          = true
  performance_insights_retention_period = 31

  # Enhanced security
  ca_cert_identifier = "rds-ca-2019"

  tags = merge(local.common_tags, {
    Name = "${local.cluster_name}-timescaledb-production"
    "Performance-Tier" = "High"
    "Backup-Tier" = "Critical"
  })
}

# Enhanced Parameter Group for Production TimescaleDB
resource "aws_db_parameter_group" "timescaledb_production" {
  family = "postgres15"
  name   = "${local.cluster_name}-timescaledb-production-params"

  # TimescaleDB optimizations for 24,390+ events/sec
  parameter {
    name  = "shared_preload_libraries"
    value = "timescaledb,pg_stat_statements,pg_hint_plan"
  }

  parameter {
    name  = "max_connections"
    value = "1000"
  }

  parameter {
    name  = "shared_buffers"
    value = "{DBInstanceClassMemory/4}"
  }

  parameter {
    name  = "effective_cache_size"
    value = "{DBInstanceClassMemory*3/4}"
  }

  parameter {
    name  = "maintenance_work_mem"
    value = "4194304"  # 4GB
  }

  parameter {
    name  = "checkpoint_completion_target"
    value = "0.9"
  }

  parameter {
    name  = "wal_buffers"
    value = "32768"
  }

  parameter {
    name  = "default_statistics_target"
    value = "500"
  }

  parameter {
    name  = "random_page_cost"
    value = "1.1"
  }

  parameter {
    name  = "effective_io_concurrency"
    value = "300"
  }

  parameter {
    name  = "work_mem"
    value = "262144"  # 256MB
  }

  parameter {
    name  = "max_worker_processes"
    value = "32"
  }

  parameter {
    name  = "max_parallel_workers"
    value = "16"
  }

  parameter {
    name  = "max_parallel_workers_per_gather"
    value = "8"
  }

  parameter {
    name  = "max_parallel_maintenance_workers"
    value = "8"
  }

  # TimescaleDB specific optimizations
  parameter {
    name  = "timescaledb.max_background_workers"
    value = "16"
  }

  parameter {
    name  = "log_statement"
    value = "mod"
  }

  parameter {
    name  = "log_min_duration_statement"
    value = "1000"  # Log queries > 1 second
  }

  tags = local.common_tags
}

# Enhanced Security Group for Production RDS
resource "aws_security_group" "rds_production" {
  name_prefix = "${local.cluster_name}-rds-production-"
  vpc_id      = module.vpc.vpc_id

  ingress {
    description     = "PostgreSQL from EKS Revenue Analytics"
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [
      aws_security_group.eks_cluster.id,
      aws_security_group.revenue_analytics_nodes.id
    ]
  }

  # Allow monitoring access
  ingress {
    description = "Monitoring Access"
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = module.vpc.private_subnets_cidr_blocks
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(local.common_tags, {
    Name = "${local.cluster_name}-rds-production-sg"
  })
}

# Enhanced ElastiCache Redis Cluster for Production
resource "aws_elasticache_replication_group" "redis_production" {
  replication_group_id       = "${local.cluster_name}-redis-production"
  description                = "Production Redis cluster for Revenue Analytics caching"

  # High-performance configuration
  node_type = "cache.r6g.2xlarge"
  port      = 6379

  # Multi-AZ cluster configuration
  num_cache_clusters         = 3
  automatic_failover_enabled = true
  multi_az_enabled          = true

  # Network configuration
  subnet_group_name  = aws_elasticache_subnet_group.main.name
  security_group_ids = [aws_security_group.elasticache_production.id]

  # Security
  at_rest_encryption_enabled = true
  transit_encryption_enabled = true
  auth_token                 = var.redis_auth_token

  # Backup configuration
  snapshot_retention_limit = 14
  snapshot_window         = "03:00-05:00"

  # Maintenance
  maintenance_window = "sun:05:00-sun:07:00"

  # Performance optimization
  parameter_group_name = aws_elasticache_parameter_group.redis_production.name

  # Logging
  log_delivery_configuration {
    destination      = aws_cloudwatch_log_group.redis_slow_log.name
    destination_type = "cloudwatch-logs"
    log_format       = "text"
    log_type         = "slow-log"
  }

  tags = merge(local.common_tags, {
    Name = "${local.cluster_name}-redis-production"
    "Performance-Tier" = "High"
  })
}

# Redis Parameter Group for Production
resource "aws_elasticache_parameter_group" "redis_production" {
  family = "redis7"
  name   = "${local.cluster_name}-redis-production-params"

  # Performance optimizations
  parameter {
    name  = "maxmemory-policy"
    value = "allkeys-lru"
  }

  parameter {
    name  = "timeout"
    value = "300"
  }

  parameter {
    name  = "tcp-keepalive"
    value = "300"
  }

  parameter {
    name  = "maxclients"
    value = "20000"
  }

  tags = local.common_tags
}

# Enhanced Security Group for Production Redis
resource "aws_security_group" "elasticache_production" {
  name_prefix = "${local.cluster_name}-redis-production-"
  vpc_id      = module.vpc.vpc_id

  ingress {
    description     = "Redis from EKS Revenue Analytics"
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [
      aws_security_group.eks_cluster.id,
      aws_security_group.revenue_analytics_nodes.id
    ]
  }

  tags = merge(local.common_tags, {
    Name = "${local.cluster_name}-redis-production-sg"
  })
}

# CloudWatch Log Group for Redis
resource "aws_cloudwatch_log_group" "redis_slow_log" {
  name              = "/aws/elasticache/${local.cluster_name}/redis-slow-log"
  retention_in_days = 30

  tags = local.common_tags
}

# Data source for EKS worker AMI
data "aws_ami" "eks_worker" {
  filter {
    name   = "name"
    values = ["amazon-eks-node-${var.kubernetes_version}-v*"]
  }

  most_recent = true
  owners      = ["************"] # Amazon EKS AMI Account ID
}
