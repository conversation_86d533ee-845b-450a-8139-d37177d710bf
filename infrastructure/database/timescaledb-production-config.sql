-- Revenue Optimization Platform - TimescaleDB Production Configuration
-- Optimized for 24,390+ events/sec ingestion and <11ms query performance

-- Enable TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
CREATE EXTENSION IF NOT EXISTS pg_hint_plan;

-- Create production database
CREATE DATABASE revenue_analytics_production 
  WITH ENCODING 'UTF8' 
  LC_COLLATE='en_US.UTF-8' 
  LC_CTYPE='en_US.UTF-8' 
  TEMPLATE=template0;

\c revenue_analytics_production;

-- Enable extensions in production database
CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
CREATE EXTENSION IF NOT EXISTS pg_hint_plan;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Create schemas for multi-tenant architecture
CREATE SCHEMA IF NOT EXISTS analytics;
CREATE SCHEMA IF NOT EXISTS revenue;
CREATE SCHEMA IF NOT EXISTS customers;
CREATE SCHEMA IF NOT EXISTS billing;
CREATE SCHEMA IF NOT EXISTS audit;

-- Set search path
SET search_path TO analytics, revenue, customers, billing, audit, public;

-- Create optimized customer_events hypertable
CREATE TABLE IF NOT EXISTS analytics.customer_events (
    id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    customer_id UUID NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB NOT NULL,
    session_id UUID,
    user_agent TEXT,
    ip_address INET,
    referrer TEXT,
    utm_source VARCHAR(100),
    utm_medium VARCHAR(100),
    utm_campaign VARCHAR(100),
    utm_term VARCHAR(100),
    utm_content VARCHAR(100),
    page_url TEXT,
    revenue_amount DECIMAL(12,2),
    currency_code CHAR(3),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    
    -- Performance indexes
    CONSTRAINT customer_events_pkey PRIMARY KEY (id, created_at),
    CONSTRAINT customer_events_tenant_check CHECK (tenant_id IS NOT NULL),
    CONSTRAINT customer_events_customer_check CHECK (customer_id IS NOT NULL)
);

-- Convert to hypertable with optimized chunk interval
SELECT create_hypertable(
    'analytics.customer_events', 
    'created_at',
    chunk_time_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

-- Create space partitioning for multi-tenant optimization
SELECT add_dimension(
    'analytics.customer_events',
    'tenant_id',
    number_partitions => 16,
    if_not_exists => TRUE
);

-- Create optimized link_clicks hypertable
CREATE TABLE IF NOT EXISTS analytics.link_clicks (
    id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    link_id UUID NOT NULL,
    customer_id UUID,
    session_id UUID,
    click_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    country_code CHAR(2),
    region VARCHAR(100),
    city VARCHAR(100),
    device_type VARCHAR(50),
    browser VARCHAR(50),
    os VARCHAR(50),
    conversion_value DECIMAL(12,2),
    conversion_currency CHAR(3),
    
    CONSTRAINT link_clicks_pkey PRIMARY KEY (id, click_timestamp),
    CONSTRAINT link_clicks_tenant_check CHECK (tenant_id IS NOT NULL)
);

-- Convert to hypertable
SELECT create_hypertable(
    'analytics.link_clicks', 
    'click_timestamp',
    chunk_time_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

-- Add space partitioning
SELECT add_dimension(
    'analytics.link_clicks',
    'tenant_id',
    number_partitions => 16,
    if_not_exists => TRUE
);

-- Create revenue analytics tables
CREATE TABLE IF NOT EXISTS revenue.revenue_events (
    id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    customer_id UUID NOT NULL,
    subscription_id UUID,
    event_type VARCHAR(50) NOT NULL, -- 'payment', 'refund', 'chargeback', 'upgrade', 'downgrade'
    amount DECIMAL(12,2) NOT NULL,
    currency_code CHAR(3) NOT NULL,
    payment_method VARCHAR(50),
    transaction_id VARCHAR(255),
    billing_period_start TIMESTAMPTZ,
    billing_period_end TIMESTAMPTZ,
    mrr_impact DECIMAL(12,2),
    arr_impact DECIMAL(12,2),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    CONSTRAINT revenue_events_pkey PRIMARY KEY (id, created_at),
    CONSTRAINT revenue_events_tenant_check CHECK (tenant_id IS NOT NULL)
);

-- Convert to hypertable
SELECT create_hypertable(
    'revenue.revenue_events', 
    'created_at',
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- Create customer health scores table
CREATE TABLE IF NOT EXISTS customers.health_scores (
    id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    customer_id UUID NOT NULL,
    health_score INTEGER NOT NULL CHECK (health_score >= 0 AND health_score <= 100),
    churn_probability DECIMAL(5,4) NOT NULL CHECK (churn_probability >= 0 AND churn_probability <= 1),
    expansion_probability DECIMAL(5,4) NOT NULL CHECK (expansion_probability >= 0 AND expansion_probability <= 1),
    engagement_score INTEGER NOT NULL CHECK (engagement_score >= 0 AND engagement_score <= 100),
    usage_score INTEGER NOT NULL CHECK (usage_score >= 0 AND usage_score <= 100),
    support_score INTEGER NOT NULL CHECK (support_score >= 0 AND support_score <= 100),
    payment_score INTEGER NOT NULL CHECK (payment_score >= 0 AND payment_score <= 100),
    calculated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    CONSTRAINT health_scores_pkey PRIMARY KEY (id, calculated_at),
    CONSTRAINT health_scores_tenant_check CHECK (tenant_id IS NOT NULL)
);

-- Convert to hypertable
SELECT create_hypertable(
    'customers.health_scores', 
    'calculated_at',
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- Create audit log table for compliance
CREATE TABLE IF NOT EXISTS audit.audit_log (
    id UUID DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    user_id UUID,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    CONSTRAINT audit_log_pkey PRIMARY KEY (id, created_at),
    CONSTRAINT audit_log_tenant_check CHECK (tenant_id IS NOT NULL)
);

-- Convert to hypertable
SELECT create_hypertable(
    'audit.audit_log', 
    'created_at',
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- Create performance-optimized indexes
-- Customer events indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_tenant_customer 
    ON analytics.customer_events (tenant_id, customer_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_event_type 
    ON analytics.customer_events (tenant_id, event_type, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_session 
    ON analytics.customer_events (tenant_id, session_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_revenue 
    ON analytics.customer_events (tenant_id, created_at DESC) 
    WHERE revenue_amount IS NOT NULL;

-- Link clicks indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_link_clicks_tenant_link 
    ON analytics.link_clicks (tenant_id, link_id, click_timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_link_clicks_customer 
    ON analytics.link_clicks (tenant_id, customer_id, click_timestamp DESC) 
    WHERE customer_id IS NOT NULL;

-- Revenue events indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_revenue_events_tenant_customer 
    ON revenue.revenue_events (tenant_id, customer_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_revenue_events_subscription 
    ON revenue.revenue_events (tenant_id, subscription_id, created_at DESC) 
    WHERE subscription_id IS NOT NULL;

-- Health scores indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_health_scores_tenant_customer 
    ON customers.health_scores (tenant_id, customer_id, calculated_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_health_scores_churn_risk 
    ON customers.health_scores (tenant_id, calculated_at DESC) 
    WHERE churn_probability > 0.5;

-- Audit log indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_log_tenant_user 
    ON audit.audit_log (tenant_id, user_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_log_resource 
    ON audit.audit_log (tenant_id, resource_type, resource_id, created_at DESC);

-- Create continuous aggregates for performance optimization
-- Hourly customer events aggregation
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.customer_events_hourly
WITH (timescaledb.continuous) AS
SELECT 
    tenant_id,
    customer_id,
    event_type,
    time_bucket('1 hour', created_at) AS hour,
    COUNT(*) AS event_count,
    SUM(COALESCE(revenue_amount, 0)) AS total_revenue,
    COUNT(DISTINCT session_id) AS unique_sessions
FROM analytics.customer_events
GROUP BY tenant_id, customer_id, event_type, hour
WITH NO DATA;

-- Daily revenue aggregation
CREATE MATERIALIZED VIEW IF NOT EXISTS revenue.revenue_daily
WITH (timescaledb.continuous) AS
SELECT 
    tenant_id,
    time_bucket('1 day', created_at) AS day,
    event_type,
    COUNT(*) AS event_count,
    SUM(amount) AS total_amount,
    SUM(mrr_impact) AS total_mrr_impact,
    SUM(arr_impact) AS total_arr_impact,
    COUNT(DISTINCT customer_id) AS unique_customers
FROM revenue.revenue_events
GROUP BY tenant_id, day, event_type
WITH NO DATA;

-- Customer health trends
CREATE MATERIALIZED VIEW IF NOT EXISTS customers.health_trends_daily
WITH (timescaledb.continuous) AS
SELECT 
    tenant_id,
    time_bucket('1 day', calculated_at) AS day,
    AVG(health_score) AS avg_health_score,
    AVG(churn_probability) AS avg_churn_probability,
    AVG(expansion_probability) AS avg_expansion_probability,
    COUNT(*) AS customer_count,
    COUNT(*) FILTER (WHERE health_score < 50) AS at_risk_customers,
    COUNT(*) FILTER (WHERE churn_probability > 0.7) AS high_churn_risk
FROM customers.health_scores
GROUP BY tenant_id, day
WITH NO DATA;

-- Add refresh policies for continuous aggregates
SELECT add_continuous_aggregate_policy('analytics.customer_events_hourly',
    start_offset => INTERVAL '3 hours',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE);

SELECT add_continuous_aggregate_policy('revenue.revenue_daily',
    start_offset => INTERVAL '2 days',
    end_offset => INTERVAL '1 day',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE);

SELECT add_continuous_aggregate_policy('customers.health_trends_daily',
    start_offset => INTERVAL '2 days',
    end_offset => INTERVAL '1 day',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE);

-- Create compression policies for cost optimization
SELECT add_compression_policy('analytics.customer_events', INTERVAL '7 days', if_not_exists => TRUE);
SELECT add_compression_policy('analytics.link_clicks', INTERVAL '7 days', if_not_exists => TRUE);
SELECT add_compression_policy('revenue.revenue_events', INTERVAL '30 days', if_not_exists => TRUE);
SELECT add_compression_policy('customers.health_scores', INTERVAL '30 days', if_not_exists => TRUE);
SELECT add_compression_policy('audit.audit_log', INTERVAL '90 days', if_not_exists => TRUE);

-- Create retention policies for data lifecycle management
SELECT add_retention_policy('analytics.customer_events', INTERVAL '7 years', if_not_exists => TRUE);
SELECT add_retention_policy('analytics.link_clicks', INTERVAL '7 years', if_not_exists => TRUE);
SELECT add_retention_policy('revenue.revenue_events', INTERVAL '10 years', if_not_exists => TRUE);
SELECT add_retention_policy('customers.health_scores', INTERVAL '5 years', if_not_exists => TRUE);
SELECT add_retention_policy('audit.audit_log', INTERVAL '7 years', if_not_exists => TRUE);

-- Create Row Level Security (RLS) policies for multi-tenant isolation
ALTER TABLE analytics.customer_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics.link_clicks ENABLE ROW LEVEL SECURITY;
ALTER TABLE revenue.revenue_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers.health_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit.audit_log ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY tenant_isolation_customer_events ON analytics.customer_events
    FOR ALL TO revenue_analytics_app
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

CREATE POLICY tenant_isolation_link_clicks ON analytics.link_clicks
    FOR ALL TO revenue_analytics_app
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

CREATE POLICY tenant_isolation_revenue_events ON revenue.revenue_events
    FOR ALL TO revenue_analytics_app
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

CREATE POLICY tenant_isolation_health_scores ON customers.health_scores
    FOR ALL TO revenue_analytics_app
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

CREATE POLICY tenant_isolation_audit_log ON audit.audit_log
    FOR ALL TO revenue_analytics_app
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

-- Create application user with limited privileges
CREATE USER revenue_analytics_app WITH PASSWORD 'CHANGE_ME_IN_PRODUCTION';

-- Grant necessary permissions
GRANT USAGE ON SCHEMA analytics, revenue, customers, billing, audit TO revenue_analytics_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA analytics TO revenue_analytics_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA revenue TO revenue_analytics_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA customers TO revenue_analytics_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA billing TO revenue_analytics_app;
GRANT SELECT, INSERT ON ALL TABLES IN SCHEMA audit TO revenue_analytics_app;

-- Grant permissions on continuous aggregates
GRANT SELECT ON analytics.customer_events_hourly TO revenue_analytics_app;
GRANT SELECT ON revenue.revenue_daily TO revenue_analytics_app;
GRANT SELECT ON customers.health_trends_daily TO revenue_analytics_app;

-- Create performance monitoring views
CREATE OR REPLACE VIEW analytics.performance_metrics AS
SELECT 
    'customer_events' AS table_name,
    COUNT(*) AS total_rows,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '1 hour') AS last_hour_rows,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '1 day') AS last_day_rows,
    MAX(created_at) AS latest_event,
    MIN(created_at) AS earliest_event
FROM analytics.customer_events
UNION ALL
SELECT 
    'link_clicks' AS table_name,
    COUNT(*) AS total_rows,
    COUNT(*) FILTER (WHERE click_timestamp >= NOW() - INTERVAL '1 hour') AS last_hour_rows,
    COUNT(*) FILTER (WHERE click_timestamp >= NOW() - INTERVAL '1 day') AS last_day_rows,
    MAX(click_timestamp) AS latest_event,
    MIN(click_timestamp) AS earliest_event
FROM analytics.link_clicks;

-- Create database health check function
CREATE OR REPLACE FUNCTION analytics.health_check()
RETURNS TABLE(
    component TEXT,
    status TEXT,
    details JSONB
) AS $$
BEGIN
    -- Check TimescaleDB extension
    RETURN QUERY
    SELECT 
        'timescaledb_extension'::TEXT,
        CASE WHEN EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'timescaledb') 
             THEN 'healthy'::TEXT 
             ELSE 'error'::TEXT 
        END,
        jsonb_build_object('version', (SELECT extversion FROM pg_extension WHERE extname = 'timescaledb'));
    
    -- Check hypertables
    RETURN QUERY
    SELECT 
        'hypertables'::TEXT,
        'healthy'::TEXT,
        jsonb_build_object(
            'count', (SELECT COUNT(*) FROM timescaledb_information.hypertables),
            'tables', (SELECT jsonb_agg(hypertable_name) FROM timescaledb_information.hypertables)
        );
    
    -- Check continuous aggregates
    RETURN QUERY
    SELECT 
        'continuous_aggregates'::TEXT,
        'healthy'::TEXT,
        jsonb_build_object(
            'count', (SELECT COUNT(*) FROM timescaledb_information.continuous_aggregates),
            'policies', (SELECT COUNT(*) FROM timescaledb_information.jobs WHERE job_type = 'continuous_aggregate')
        );
END;
$$ LANGUAGE plpgsql;
