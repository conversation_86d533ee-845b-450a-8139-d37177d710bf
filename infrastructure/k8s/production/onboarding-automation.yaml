# Onboarding Automation Service - Production Kubernetes Deployment
# Supports 15-minute automated client onboarding with performance validation

apiVersion: apps/v1
kind: Deployment
metadata:
  name: onboarding-automation
  namespace: production
  labels:
    app: onboarding-automation
    version: v1.0.0
    component: onboarding
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: onboarding-automation
  template:
    metadata:
      labels:
        app: onboarding-automation
        version: v1.0.0
        component: onboarding
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3006"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: onboarding-automation
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 2000
      containers:
      - name: onboarding-automation
        image: ecommerce-analytics/onboarding-automation:v1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 3006
          name: http
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: ONBOARDING_PORT
          value: "3006"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        - name: ANALYTICS_SERVICE_URL
          value: "http://analytics-service:3002"
        - name: ADMIN_SERVICE_URL
          value: "http://admin-service:3005"
        - name: LOG_LEVEL
          value: "info"
        - name: ENABLE_METRICS
          value: "true"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3006
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 3006
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: config
        configMap:
          name: onboarding-automation-config
      - name: logs
        emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - onboarding-automation
              topologyKey: kubernetes.io/hostname
      tolerations:
      - key: "high-performance"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"

---
apiVersion: v1
kind: Service
metadata:
  name: onboarding-automation
  namespace: production
  labels:
    app: onboarding-automation
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
spec:
  type: LoadBalancer
  ports:
  - port: 3006
    targetPort: 3006
    protocol: TCP
    name: http
  selector:
    app: onboarding-automation

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: onboarding-automation
  namespace: production
  labels:
    app: onboarding-automation
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT_ID:role/onboarding-automation-role

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: onboarding-automation-config
  namespace: production
data:
  config.json: |
    {
      "performance": {
        "targets": {
          "onboardingTime": 900000,
          "provisioningTime": 180000,
          "apiKeyGeneration": 30000,
          "validationTime": 300000,
          "queryResponseTime": 11
        }
      },
      "monitoring": {
        "enabled": true,
        "metricsPort": 3006,
        "healthCheckPath": "/health"
      },
      "security": {
        "enableRateLimit": true,
        "maxConcurrentOnboardings": 100,
        "enableAuditLogging": true
      }
    }

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: onboarding-automation-hpa
  namespace: production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: onboarding-automation
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: concurrent_onboardings
      target:
        type: AverageValue
        averageValue: "10"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: onboarding-automation-pdb
  namespace: production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: onboarding-automation

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: onboarding-automation-netpol
  namespace: production
spec:
  podSelector:
    matchLabels:
      app: onboarding-automation
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - podSelector:
        matchLabels:
          app: prometheus
    ports:
    - protocol: TCP
      port: 3006
  - from: []
    ports:
    - protocol: TCP
      port: 3006
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: analytics-service
    ports:
    - protocol: TCP
      port: 3002
  - to:
    - podSelector:
        matchLabels:
          app: admin-service
    ports:
    - protocol: TCP
      port: 3005
  - to: []
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
    - protocol: TCP
      port: 443   # HTTPS
    - protocol: TCP
      port: 53    # DNS
    - protocol: UDP
      port: 53    # DNS

---
apiVersion: v1
kind: Secret
metadata:
  name: database-credentials
  namespace: production
type: Opaque
data:
  url: # Base64 encoded database URL - set during deployment

---
apiVersion: v1
kind: Secret
metadata:
  name: redis-credentials
  namespace: production
type: Opaque
data:
  url: # Base64 encoded Redis URL - set during deployment

---
apiVersion: v1
kind: Secret
metadata:
  name: jwt-secret
  namespace: production
type: Opaque
data:
  secret: # Base64 encoded JWT secret - set during deployment
