# Auto-Scaling Configuration for Analytics Service
# Handles 24,390+ events/sec with dynamic scaling

apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: analytics-service-hpa
  namespace: production
  labels:
    app: analytics-service
    component: autoscaling
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: analytics-service
  minReplicas: 5
  maxReplicas: 50
  metrics:
  # CPU-based scaling
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  
  # Memory-based scaling
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  
  # Custom metrics for event processing
  - type: Pods
    pods:
      metric:
        name: events_per_second
      target:
        type: AverageValue
        averageValue: "5000"  # Scale when >5k events/sec per pod
  
  # Query latency scaling
  - type: Pods
    pods:
      metric:
        name: query_latency_p95
      target:
        type: AverageValue
        averageValue: "8m"  # Scale when p95 latency >8ms
  
  # Queue depth scaling
  - type: Pods
    pods:
      metric:
        name: event_queue_depth
      target:
        type: AverageValue
        averageValue: "1000"  # Scale when queue >1000 events
  
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 5
        periodSeconds: 15
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Min

---
# Vertical Pod Autoscaler for Analytics Service
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: analytics-service-vpa
  namespace: production
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: analytics-service
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: analytics-service
      minAllowed:
        cpu: 500m
        memory: 1Gi
      maxAllowed:
        cpu: 4
        memory: 8Gi
      controlledResources: ["cpu", "memory"]

---
# Pod Disruption Budget for Analytics Service
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: analytics-service-pdb
  namespace: production
spec:
  minAvailable: 3
  selector:
    matchLabels:
      app: analytics-service

---
# Load Balancer Service for Analytics
apiVersion: v1
kind: Service
metadata:
  name: analytics-service-lb
  namespace: production
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
    service.beta.kubernetes.io/aws-load-balancer-target-group-attributes: |
      deregistration_delay.timeout_seconds=30,
      stickiness.enabled=true,
      stickiness.type=source_ip
spec:
  type: LoadBalancer
  loadBalancerSourceRanges:
  - 0.0.0.0/0
  ports:
  - name: http
    port: 3002
    targetPort: 3002
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: 9090
    protocol: TCP
  selector:
    app: analytics-service

---
# Ingress for Analytics Service with Advanced Routing
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: analytics-service-ingress
  namespace: production
  annotations:
    kubernetes.io/ingress.class: "alb"
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/load-balancer-name: analytics-service-alb
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:ACCOUNT_ID:certificate/CERT_ID
    alb.ingress.kubernetes.io/healthcheck-path: /health
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '15'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '5'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '3'
    alb.ingress.kubernetes.io/target-group-attributes: |
      stickiness.enabled=true,
      stickiness.lb_cookie.duration_seconds=86400,
      load_balancing.algorithm.type=least_outstanding_requests
spec:
  rules:
  - host: analytics.ecommerce-analytics.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: analytics-service
            port:
              number: 3002
      - path: /api/events
        pathType: Prefix
        backend:
          service:
            name: analytics-service
            port:
              number: 3002
      - path: /api/analytics
        pathType: Prefix
        backend:
          service:
            name: analytics-service
            port:
              number: 3002

---
# Custom Metrics Server Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: custom-metrics-config
  namespace: monitoring
data:
  config.yaml: |
    rules:
    - seriesQuery: 'events_processed_total{namespace!="",pod!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          pod: {resource: "pod"}
      name:
        matches: "^events_processed_total"
        as: "events_per_second"
      metricsQuery: 'rate(<<.Series>>{<<.LabelMatchers>>}[2m])'
    
    - seriesQuery: 'query_duration_seconds{namespace!="",pod!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          pod: {resource: "pod"}
      name:
        matches: "^query_duration_seconds"
        as: "query_latency_p95"
      metricsQuery: 'histogram_quantile(0.95, rate(<<.Series>>_bucket{<<.LabelMatchers>>}[2m])) * 1000'
    
    - seriesQuery: 'event_queue_depth{namespace!="",pod!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          pod: {resource: "pod"}
      name:
        matches: "^event_queue_depth"
        as: "event_queue_depth"
      metricsQuery: '<<.Series>>{<<.LabelMatchers>>}'

---
# KEDA ScaledObject for Event-Driven Autoscaling
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: analytics-service-keda
  namespace: production
spec:
  scaleTargetRef:
    name: analytics-service
  minReplicaCount: 5
  maxReplicaCount: 50
  pollingInterval: 15
  cooldownPeriod: 300
  triggers:
  # Prometheus-based scaling
  - type: prometheus
    metadata:
      serverAddress: http://prometheus.monitoring.svc.cluster.local:9090
      metricName: events_per_second
      threshold: '5000'
      query: sum(rate(events_processed_total{job="analytics-service"}[2m]))
  
  # Redis queue depth scaling
  - type: redis
    metadata:
      address: redis-cluster.production.svc.cluster.local:6379
      listName: event_processing_queue
      listLength: '1000'
  
  # Kafka lag scaling (if using Kafka)
  - type: kafka
    metadata:
      bootstrapServers: kafka-cluster.production.svc.cluster.local:9092
      consumerGroup: analytics-consumer-group
      topic: user_events
      lagThreshold: '1000'

---
# Network Policy for Load Balancer Traffic
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: analytics-service-lb-netpol
  namespace: production
spec:
  podSelector:
    matchLabels:
      app: analytics-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from load balancer
  - from: []
    ports:
    - protocol: TCP
      port: 3002
  # Allow monitoring traffic
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
  egress:
  # Allow database connections
  - to: []
    ports:
    - protocol: TCP
      port: 5432
  # Allow Redis connections
  - to: []
    ports:
    - protocol: TCP
      port: 6379
  # Allow HTTPS for external APIs
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow DNS
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53

---
# Priority Class for Critical Services
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: high-priority
value: 1000
globalDefault: false
description: "High priority class for critical analytics services"

---
# Resource Quota for Production Namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: production-quota
  namespace: production
spec:
  hard:
    requests.cpu: "100"
    requests.memory: 200Gi
    limits.cpu: "200"
    limits.memory: 400Gi
    persistentvolumeclaims: "50"
    pods: "200"
    services: "50"
    secrets: "100"
    configmaps: "100"

---
# Limit Range for Pod Resources
apiVersion: v1
kind: LimitRange
metadata:
  name: production-limits
  namespace: production
spec:
  limits:
  - default:
      cpu: "1"
      memory: "2Gi"
    defaultRequest:
      cpu: "100m"
      memory: "256Mi"
    max:
      cpu: "8"
      memory: "16Gi"
    min:
      cpu: "50m"
      memory: "128Mi"
    type: Container
  - max:
      storage: "100Gi"
    min:
      storage: "1Gi"
    type: PersistentVolumeClaim
