#!/usr/bin/env -S deno run --allow-net --allow-env --allow-read --allow-write

// Production Readiness Validation Script
// Comprehensive validation for Revenue Optimization & Growth Analytics Platform

import { parse } from "https://deno.land/std@0.208.0/flags/mod.ts";

interface ValidationResult {
  category: string;
  test: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
  duration?: number;
}

interface ValidationSummary {
  totalTests: number;
  passed: number;
  failed: number;
  warnings: number;
  overallStatus: 'READY' | 'NOT_READY' | 'NEEDS_ATTENTION';
  categories: Record<string, {
    passed: number;
    failed: number;
    warnings: number;
    status: 'PASS' | 'FAIL' | 'WARNING';
  }>;
  executionTime: number;
}

class ProductionReadinessValidator {
  private baseUrl: string;
  private authToken: string;
  private tenantId: string;
  private results: ValidationResult[] = [];

  constructor(baseUrl: string, authToken: string, tenantId: string) {
    this.baseUrl = baseUrl;
    this.authToken = authToken;
    this.tenantId = tenantId;
  }

  async runAllValidations(): Promise<ValidationSummary> {
    console.log("🚀 Starting Production Readiness Validation");
    console.log("=" .repeat(80));
    
    const startTime = Date.now();

    // Run all validation categories
    await this.validateInfrastructure();
    await this.validateDatabase();
    await this.validateApplicationHealth();
    await this.validatePerformance();
    await this.validateSecurity();
    await this.validateCompliance();
    await this.validateMonitoring();
    await this.validateBackupAndRecovery();

    const executionTime = Date.now() - startTime;
    const summary = this.generateSummary(executionTime);
    
    this.displayResults(summary);
    return summary;
  }

  private async validateInfrastructure(): Promise<void> {
    console.log("\n🏗️  Validating Infrastructure...");

    // Check Kubernetes cluster health
    await this.runValidation(
      "Infrastructure",
      "Kubernetes Cluster Health",
      async () => {
        const response = await fetch(`${this.baseUrl}/health/cluster`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        if (!response.ok) {
          throw new Error(`Cluster health check failed: ${response.status}`);
        }
        
        const health = await response.json();
        if (health.status !== 'healthy') {
          throw new Error(`Cluster status: ${health.status}`);
        }
        
        return { nodes: health.nodes, pods: health.pods };
      }
    );

    // Check load balancer
    await this.runValidation(
      "Infrastructure",
      "Load Balancer Health",
      async () => {
        const response = await fetch(`${this.baseUrl}/health/loadbalancer`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        if (!response.ok) {
          throw new Error(`Load balancer health check failed: ${response.status}`);
        }
        
        const health = await response.json();
        return { status: health.status, targets: health.healthy_targets };
      }
    );

    // Check auto-scaling configuration
    await this.runValidation(
      "Infrastructure",
      "Auto-scaling Configuration",
      async () => {
        const response = await fetch(`${this.baseUrl}/health/autoscaling`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        if (!response.ok) {
          throw new Error(`Auto-scaling check failed: ${response.status}`);
        }
        
        const config = await response.json();
        if (!config.enabled) {
          throw new Error("Auto-scaling is not enabled");
        }
        
        return { 
          enabled: config.enabled, 
          min_replicas: config.min_replicas,
          max_replicas: config.max_replicas 
        };
      }
    );
  }

  private async validateDatabase(): Promise<void> {
    console.log("\n🗄️  Validating Database...");

    // Check TimescaleDB health
    await this.runValidation(
      "Database",
      "TimescaleDB Health",
      async () => {
        const response = await fetch(`${this.baseUrl}/health/database`, {
          headers: { 
            'Authorization': `Bearer ${this.authToken}`,
            'X-Tenant-ID': this.tenantId 
          }
        });
        
        if (!response.ok) {
          throw new Error(`Database health check failed: ${response.status}`);
        }
        
        const health = await response.json();
        if (health.status !== 'healthy') {
          throw new Error(`Database status: ${health.status}`);
        }
        
        return { 
          version: health.version,
          connections: health.connections,
          hypertables: health.hypertables 
        };
      }
    );

    // Check database performance
    await this.runValidation(
      "Database",
      "Query Performance",
      async () => {
        const startTime = Date.now();
        const response = await fetch(`${this.baseUrl}/api/revenue-analytics/metrics?timeRange=1d`, {
          headers: { 
            'Authorization': `Bearer ${this.authToken}`,
            'X-Tenant-ID': this.tenantId 
          }
        });
        const queryTime = Date.now() - startTime;
        
        if (!response.ok) {
          throw new Error(`Query failed: ${response.status}`);
        }
        
        if (queryTime > 11) {
          throw new Error(`Query time ${queryTime}ms exceeds 11ms target`);
        }
        
        return { queryTime, target: 11 };
      }
    );

    // Check Redis cache
    await this.runValidation(
      "Database",
      "Redis Cache Health",
      async () => {
        const response = await fetch(`${this.baseUrl}/health/cache`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        if (!response.ok) {
          throw new Error(`Cache health check failed: ${response.status}`);
        }
        
        const health = await response.json();
        if (health.status !== 'healthy') {
          throw new Error(`Cache status: ${health.status}`);
        }
        
        return { 
          memory_usage: health.memory_usage,
          hit_rate: health.hit_rate,
          connections: health.connections 
        };
      }
    );
  }

  private async validateApplicationHealth(): Promise<void> {
    console.log("\n🏥 Validating Application Health...");

    // Check all services
    const services = ['analytics', 'dashboard', 'billing'];
    
    for (const service of services) {
      await this.runValidation(
        "Application",
        `${service.charAt(0).toUpperCase() + service.slice(1)} Service Health`,
        async () => {
          const response = await fetch(`${this.baseUrl}/health/${service}`, {
            headers: { 'Authorization': `Bearer ${this.authToken}` }
          });
          
          if (!response.ok) {
            throw new Error(`${service} service health check failed: ${response.status}`);
          }
          
          const health = await response.json();
          if (health.status !== 'healthy') {
            throw new Error(`${service} service status: ${health.status}`);
          }
          
          return { 
            status: health.status,
            uptime: health.uptime,
            version: health.version 
          };
        }
      );
    }

    // Check API endpoints
    await this.runValidation(
      "Application",
      "Critical API Endpoints",
      async () => {
        const endpoints = [
          '/api/revenue-analytics/metrics',
          '/api/enhanced-subscriptions/dynamic-pricing',
          '/api/revenue-analytics/customer-health',
          '/api/revenue-analytics/ml-churn-predictions'
        ];
        
        const results = [];
        for (const endpoint of endpoints) {
          const startTime = Date.now();
          const response = await fetch(`${this.baseUrl}${endpoint}`, {
            method: endpoint.includes('dynamic-pricing') ? 'POST' : 'GET',
            headers: { 
              'Authorization': `Bearer ${this.authToken}`,
              'X-Tenant-ID': this.tenantId,
              'Content-Type': 'application/json'
            },
            body: endpoint.includes('dynamic-pricing') ? JSON.stringify({
              customerId: 'test-customer',
              planId: 'pro',
              usageMetrics: { apiCalls: 5000, dataVolume: 500, teamSize: 5, featureUsage: {} }
            }) : undefined
          });
          const responseTime = Date.now() - startTime;
          
          if (!response.ok) {
            throw new Error(`${endpoint} failed: ${response.status}`);
          }
          
          if (responseTime > 500) {
            throw new Error(`${endpoint} response time ${responseTime}ms exceeds 500ms target`);
          }
          
          results.push({ endpoint, responseTime, status: response.status });
        }
        
        return results;
      }
    );
  }

  private async validatePerformance(): Promise<void> {
    console.log("\n⚡ Validating Performance...");

    // Check event ingestion performance
    await this.runValidation(
      "Performance",
      "Event Ingestion Rate",
      async () => {
        const response = await fetch(`${this.baseUrl}/metrics/ingestion-rate`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        if (!response.ok) {
          throw new Error(`Ingestion rate check failed: ${response.status}`);
        }
        
        const metrics = await response.json();
        const currentRate = metrics.events_per_second;
        
        if (currentRate < 20000) {
          throw new Error(`Current ingestion rate ${currentRate} events/sec below 20,000 threshold`);
        }
        
        return { 
          current_rate: currentRate,
          target_rate: 24390,
          capacity_utilization: (currentRate / 24390) * 100 
        };
      }
    );

    // Check frontend performance
    await this.runValidation(
      "Performance",
      "Frontend Load Time",
      async () => {
        const startTime = Date.now();
        const response = await fetch(`${this.baseUrl}/`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        const loadTime = Date.now() - startTime;
        
        if (!response.ok) {
          throw new Error(`Frontend load failed: ${response.status}`);
        }
        
        if (loadTime > 2000) {
          throw new Error(`Frontend load time ${loadTime}ms exceeds 2000ms target`);
        }
        
        return { loadTime, target: 2000 };
      }
    );

    // Check memory usage
    await this.runValidation(
      "Performance",
      "Memory Usage",
      async () => {
        const response = await fetch(`${this.baseUrl}/metrics/memory`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        if (!response.ok) {
          throw new Error(`Memory metrics check failed: ${response.status}`);
        }
        
        const metrics = await response.json();
        const memoryUsage = metrics.memory_usage_percentage;
        
        if (memoryUsage > 85) {
          throw new Error(`Memory usage ${memoryUsage}% exceeds 85% threshold`);
        }
        
        return { 
          usage_percentage: memoryUsage,
          available_mb: metrics.available_memory_mb,
          threshold: 85 
        };
      }
    );
  }

  private async validateSecurity(): Promise<void> {
    console.log("\n🔒 Validating Security...");

    // Check TLS configuration
    await this.runValidation(
      "Security",
      "TLS Configuration",
      async () => {
        const response = await fetch(`${this.baseUrl}/health/tls`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        if (!response.ok) {
          throw new Error(`TLS check failed: ${response.status}`);
        }
        
        const tls = await response.json();
        if (!tls.valid || tls.expires_in_days < 30) {
          throw new Error(`TLS certificate expires in ${tls.expires_in_days} days`);
        }
        
        return { 
          valid: tls.valid,
          expires_in_days: tls.expires_in_days,
          issuer: tls.issuer 
        };
      }
    );

    // Check authentication
    await this.runValidation(
      "Security",
      "Authentication System",
      async () => {
        const response = await fetch(`${this.baseUrl}/auth/validate`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        if (!response.ok) {
          throw new Error(`Auth validation failed: ${response.status}`);
        }
        
        const auth = await response.json();
        if (!auth.valid) {
          throw new Error("Authentication token is invalid");
        }
        
        return { 
          valid: auth.valid,
          expires_in: auth.expires_in,
          permissions: auth.permissions 
        };
      }
    );

    // Check rate limiting
    await this.runValidation(
      "Security",
      "Rate Limiting",
      async () => {
        const response = await fetch(`${this.baseUrl}/health/rate-limits`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        if (!response.ok) {
          throw new Error(`Rate limiting check failed: ${response.status}`);
        }
        
        const limits = await response.json();
        if (!limits.enabled) {
          throw new Error("Rate limiting is not enabled");
        }
        
        return { 
          enabled: limits.enabled,
          global_limit: limits.global_limit,
          api_limit: limits.api_limit 
        };
      }
    );
  }

  private async validateCompliance(): Promise<void> {
    console.log("\n📋 Validating Compliance...");

    // Check GDPR compliance
    await this.runValidation(
      "Compliance",
      "GDPR Compliance",
      async () => {
        const response = await fetch(`${this.baseUrl}/compliance/gdpr`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        if (!response.ok) {
          throw new Error(`GDPR compliance check failed: ${response.status}`);
        }
        
        const gdpr = await response.json();
        if (!gdpr.compliant) {
          throw new Error(`GDPR compliance issues: ${gdpr.issues.join(', ')}`);
        }
        
        return { 
          compliant: gdpr.compliant,
          data_retention_policy: gdpr.data_retention_policy,
          consent_management: gdpr.consent_management 
        };
      }
    );

    // Check audit logging
    await this.runValidation(
      "Compliance",
      "Audit Logging",
      async () => {
        const response = await fetch(`${this.baseUrl}/compliance/audit-logs`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        if (!response.ok) {
          throw new Error(`Audit logging check failed: ${response.status}`);
        }
        
        const audit = await response.json();
        if (!audit.enabled) {
          throw new Error("Audit logging is not enabled");
        }
        
        return { 
          enabled: audit.enabled,
          retention_days: audit.retention_days,
          events_logged: audit.events_logged 
        };
      }
    );
  }

  private async validateMonitoring(): Promise<void> {
    console.log("\n📊 Validating Monitoring...");

    // Check Prometheus metrics
    await this.runValidation(
      "Monitoring",
      "Prometheus Metrics",
      async () => {
        const response = await fetch(`${this.baseUrl}/metrics`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        if (!response.ok) {
          throw new Error(`Metrics endpoint failed: ${response.status}`);
        }
        
        const metrics = await response.text();
        if (!metrics.includes('revenue_analytics_')) {
          throw new Error("Revenue analytics metrics not found");
        }
        
        return { 
          metrics_available: true,
          endpoint_status: response.status 
        };
      }
    );

    // Check alerting
    await this.runValidation(
      "Monitoring",
      "Alerting Configuration",
      async () => {
        const response = await fetch(`${this.baseUrl}/health/alerting`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        if (!response.ok) {
          throw new Error(`Alerting check failed: ${response.status}`);
        }
        
        const alerting = await response.json();
        if (!alerting.enabled) {
          throw new Error("Alerting is not enabled");
        }
        
        return { 
          enabled: alerting.enabled,
          rules_count: alerting.rules_count,
          active_alerts: alerting.active_alerts 
        };
      }
    );
  }

  private async validateBackupAndRecovery(): Promise<void> {
    console.log("\n💾 Validating Backup & Recovery...");

    // Check backup status
    await this.runValidation(
      "Backup",
      "Database Backup Status",
      async () => {
        const response = await fetch(`${this.baseUrl}/health/backups`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        if (!response.ok) {
          throw new Error(`Backup status check failed: ${response.status}`);
        }
        
        const backup = await response.json();
        if (!backup.enabled) {
          throw new Error("Database backups are not enabled");
        }
        
        const lastBackupHours = (Date.now() - new Date(backup.last_backup).getTime()) / (1000 * 60 * 60);
        if (lastBackupHours > 24) {
          throw new Error(`Last backup was ${Math.round(lastBackupHours)} hours ago`);
        }
        
        return { 
          enabled: backup.enabled,
          last_backup: backup.last_backup,
          retention_days: backup.retention_days 
        };
      }
    );

    // Check disaster recovery plan
    await this.runValidation(
      "Backup",
      "Disaster Recovery Configuration",
      async () => {
        const response = await fetch(`${this.baseUrl}/health/disaster-recovery`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });
        
        if (!response.ok) {
          throw new Error(`Disaster recovery check failed: ${response.status}`);
        }
        
        const dr = await response.json();
        if (!dr.configured) {
          throw new Error("Disaster recovery is not configured");
        }
        
        return { 
          configured: dr.configured,
          rto_minutes: dr.rto_minutes,
          rpo_minutes: dr.rpo_minutes 
        };
      }
    );
  }

  private async runValidation(
    category: string,
    test: string,
    validationFn: () => Promise<any>
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      const details = await validationFn();
      const duration = Date.now() - startTime;
      
      this.results.push({
        category,
        test,
        status: 'PASS',
        message: 'Validation passed',
        details,
        duration
      });
      
      console.log(`  ✅ ${test} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.results.push({
        category,
        test,
        status: 'FAIL',
        message: error instanceof Error ? error.message : 'Unknown error',
        duration
      });
      
      console.log(`  ❌ ${test} - ${error instanceof Error ? error.message : 'Unknown error'} (${duration}ms)`);
    }
  }

  private generateSummary(executionTime: number): ValidationSummary {
    const categories: Record<string, any> = {};
    
    // Group results by category
    for (const result of this.results) {
      if (!categories[result.category]) {
        categories[result.category] = { passed: 0, failed: 0, warnings: 0 };
      }
      
      if (result.status === 'PASS') categories[result.category].passed++;
      else if (result.status === 'FAIL') categories[result.category].failed++;
      else categories[result.category].warnings++;
    }
    
    // Determine category status
    for (const category in categories) {
      const cat = categories[category];
      if (cat.failed > 0) cat.status = 'FAIL';
      else if (cat.warnings > 0) cat.status = 'WARNING';
      else cat.status = 'PASS';
    }
    
    const totalTests = this.results.length;
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warnings = this.results.filter(r => r.status === 'WARNING').length;
    
    let overallStatus: 'READY' | 'NOT_READY' | 'NEEDS_ATTENTION';
    if (failed > 0) overallStatus = 'NOT_READY';
    else if (warnings > 0) overallStatus = 'NEEDS_ATTENTION';
    else overallStatus = 'READY';
    
    return {
      totalTests,
      passed,
      failed,
      warnings,
      overallStatus,
      categories,
      executionTime
    };
  }

  private displayResults(summary: ValidationSummary): void {
    console.log("\n" + "=" .repeat(80));
    console.log("📋 PRODUCTION READINESS VALIDATION SUMMARY");
    console.log("=" .repeat(80));
    
    // Overall status
    const statusIcon = summary.overallStatus === 'READY' ? '🟢' : 
                      summary.overallStatus === 'NEEDS_ATTENTION' ? '🟡' : '🔴';
    console.log(`\n${statusIcon} Overall Status: ${summary.overallStatus}`);
    
    // Test summary
    console.log(`\n📊 Test Results:`);
    console.log(`   Total Tests: ${summary.totalTests}`);
    console.log(`   ✅ Passed: ${summary.passed}`);
    console.log(`   ❌ Failed: ${summary.failed}`);
    console.log(`   ⚠️  Warnings: ${summary.warnings}`);
    console.log(`   ⏱️  Execution Time: ${(summary.executionTime / 1000).toFixed(1)}s`);
    
    // Category breakdown
    console.log(`\n📂 Category Breakdown:`);
    for (const [category, stats] of Object.entries(summary.categories)) {
      const icon = stats.status === 'PASS' ? '✅' : stats.status === 'WARNING' ? '⚠️' : '❌';
      console.log(`   ${icon} ${category}: ${stats.passed}/${stats.passed + stats.failed + stats.warnings} passed`);
    }
    
    // Failed tests
    if (summary.failed > 0) {
      console.log(`\n❌ Failed Tests:`);
      for (const result of this.results.filter(r => r.status === 'FAIL')) {
        console.log(`   • ${result.category} - ${result.test}: ${result.message}`);
      }
    }
    
    // Production readiness assessment
    console.log(`\n🎯 Production Readiness Assessment:`);
    if (summary.overallStatus === 'READY') {
      console.log(`   🚀 READY FOR PRODUCTION DEPLOYMENT`);
      console.log(`   • All critical systems are operational`);
      console.log(`   • Performance targets are met`);
      console.log(`   • Security and compliance validated`);
      console.log(`   • Monitoring and alerting configured`);
    } else if (summary.overallStatus === 'NEEDS_ATTENTION') {
      console.log(`   ⚠️  NEEDS ATTENTION BEFORE DEPLOYMENT`);
      console.log(`   • Some non-critical issues detected`);
      console.log(`   • Review warnings and address if necessary`);
    } else {
      console.log(`   🛑 NOT READY FOR PRODUCTION`);
      console.log(`   • Critical issues must be resolved`);
      console.log(`   • Do not proceed with deployment`);
    }
    
    console.log("=" .repeat(80));
  }
}

// Main execution
async function main() {
  const args = parse(Deno.args, {
    string: ["base-url", "auth-token", "tenant-id"],
    boolean: ["help", "verbose"],
    alias: {
      "h": "help",
      "v": "verbose",
      "u": "base-url",
      "t": "auth-token",
      "i": "tenant-id",
    },
  });

  if (args.help) {
    console.log(`
Production Readiness Validation Script

Usage: deno run --allow-net --allow-env --allow-read --allow-write production-readiness-validation.ts [options]

Options:
  -h, --help                    Show this help message
  -v, --verbose                 Enable verbose output
  -u, --base-url <url>          Base URL of the application
  -t, --auth-token <token>      Authentication token
  -i, --tenant-id <id>          Tenant ID for testing

Environment Variables:
  BASE_URL                      Base URL of the application
  AUTH_TOKEN                    Authentication token
  TENANT_ID                     Tenant ID for testing
`);
    Deno.exit(0);
  }

  const baseUrl = args["base-url"] || Deno.env.get("BASE_URL") || "https://app.revenue-optimization.com";
  const authToken = args["auth-token"] || Deno.env.get("AUTH_TOKEN") || "";
  const tenantId = args["tenant-id"] || Deno.env.get("TENANT_ID") || "";

  if (!authToken || !tenantId) {
    console.error("❌ Error: AUTH_TOKEN and TENANT_ID are required");
    Deno.exit(1);
  }

  const validator = new ProductionReadinessValidator(baseUrl, authToken, tenantId);
  const summary = await validator.runAllValidations();

  // Exit with appropriate code
  if (summary.overallStatus === 'NOT_READY') {
    Deno.exit(1);
  } else if (summary.overallStatus === 'NEEDS_ATTENTION') {
    Deno.exit(2);
  } else {
    Deno.exit(0);
  }
}

if (import.meta.main) {
  main().catch(console.error);
}
