// Enterprise Customer Onboarding Workflow
// Automated onboarding system for Revenue Optimization & Growth Analytics Platform

import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";

// Onboarding workflow types and interfaces
interface OnboardingCustomer {
  id: string;
  companyName: string;
  contactEmail: string;
  tier: 'starter' | 'growth' | 'professional' | 'enterprise';
  annualRevenue: number;
  ecommercePlatform: string[];
  teamSize: number;
  currentAnalytics: string[];
  goals: string[];
  timeline: string;
}

interface OnboardingStep {
  id: string;
  name: string;
  description: string;
  estimatedDuration: number; // in hours
  dependencies: string[];
  assignee: 'customer' | 'customer_success' | 'technical' | 'sales';
  status: 'pending' | 'in_progress' | 'completed' | 'blocked';
  dueDate: Date;
  completedDate?: Date;
  notes?: string;
}

interface OnboardingWorkflow {
  customerId: string;
  workflowId: string;
  startDate: Date;
  targetCompletionDate: Date;
  currentPhase: 'discovery' | 'setup' | 'integration' | 'training' | 'optimization' | 'success';
  steps: OnboardingStep[];
  successMetrics: {
    timeToFirstInsight: number; // target: <24 hours
    timeToValue: number; // target: <7 days
    userAdoption: number; // target: >90%
    performanceTargets: boolean; // target: achieved
  };
}

class EnterpriseOnboardingOrchestrator {
  private workflows: Map<string, OnboardingWorkflow> = new Map();

  /**
   * Initialize onboarding workflow for new enterprise customer
   */
  async initializeOnboarding(customer: OnboardingCustomer): Promise<OnboardingWorkflow> {
    const workflowId = `onboarding-${customer.id}-${Date.now()}`;
    
    // Generate customized onboarding steps based on customer profile
    const steps = this.generateOnboardingSteps(customer);
    
    const workflow: OnboardingWorkflow = {
      customerId: customer.id,
      workflowId,
      startDate: new Date(),
      targetCompletionDate: this.calculateTargetCompletion(customer.tier),
      currentPhase: 'discovery',
      steps,
      successMetrics: {
        timeToFirstInsight: 0,
        timeToValue: 0,
        userAdoption: 0,
        performanceTargets: false,
      },
    };

    this.workflows.set(workflowId, workflow);
    
    // Send welcome email and kickoff materials
    await this.sendWelcomePackage(customer, workflow);
    
    // Schedule kickoff meeting
    await this.scheduleKickoffMeeting(customer, workflow);
    
    return workflow;
  }

  /**
   * Generate customized onboarding steps based on customer profile
   */
  private generateOnboardingSteps(customer: OnboardingCustomer): OnboardingStep[] {
    const baseSteps: Omit<OnboardingStep, 'id' | 'dueDate'>[] = [
      // Phase 1: Discovery & Planning (Days 1-2)
      {
        name: "Kickoff Meeting & Discovery",
        description: "Initial meeting to understand goals, current setup, and success criteria",
        estimatedDuration: 2,
        dependencies: [],
        assignee: 'customer_success',
        status: 'pending',
        notes: "Schedule within 24 hours of contract signing"
      },
      {
        name: "Technical Architecture Review",
        description: "Review current e-commerce stack and plan integration approach",
        estimatedDuration: 1.5,
        dependencies: ["kickoff-meeting-discovery"],
        assignee: 'technical',
        status: 'pending',
      },
      {
        name: "Success Metrics Definition",
        description: "Define specific KPIs and success criteria for the implementation",
        estimatedDuration: 1,
        dependencies: ["kickoff-meeting-discovery"],
        assignee: 'customer_success',
        status: 'pending',
      },

      // Phase 2: Environment Setup (Days 2-3)
      {
        name: "Production Environment Provisioning",
        description: "Set up dedicated production environment with security and compliance",
        estimatedDuration: 4,
        dependencies: ["technical-architecture-review"],
        assignee: 'technical',
        status: 'pending',
      },
      {
        name: "Security & Compliance Configuration",
        description: "Configure GDPR/CCPA compliance, security policies, and access controls",
        estimatedDuration: 2,
        dependencies: ["production-environment-provisioning"],
        assignee: 'technical',
        status: 'pending',
      },
      {
        name: "Performance Monitoring Setup",
        description: "Configure monitoring, alerting, and performance tracking systems",
        estimatedDuration: 1.5,
        dependencies: ["production-environment-provisioning"],
        assignee: 'technical',
        status: 'pending',
      },

      // Phase 3: Data Integration (Days 3-5)
      {
        name: "E-commerce Platform Integration",
        description: "Connect primary e-commerce platform and validate data flow",
        estimatedDuration: 3,
        dependencies: ["security-compliance-configuration"],
        assignee: 'technical',
        status: 'pending',
      },
      {
        name: "Payment Processor Integration",
        description: "Integrate payment processors for revenue tracking",
        estimatedDuration: 2,
        dependencies: ["ecommerce-platform-integration"],
        assignee: 'technical',
        status: 'pending',
      },
      {
        name: "Marketing Tools Integration",
        description: "Connect marketing and analytics tools for comprehensive tracking",
        estimatedDuration: 2,
        dependencies: ["ecommerce-platform-integration"],
        assignee: 'technical',
        status: 'pending',
      },
      {
        name: "Historical Data Import",
        description: "Import 6-12 months of historical data for AI model training",
        estimatedDuration: 4,
        dependencies: ["payment-processor-integration", "marketing-tools-integration"],
        assignee: 'technical',
        status: 'pending',
      },

      // Phase 4: Configuration & Customization (Days 5-7)
      {
        name: "Dashboard Configuration",
        description: "Set up executive dashboards and custom reporting",
        estimatedDuration: 3,
        dependencies: ["historical-data-import"],
        assignee: 'customer_success',
        status: 'pending',
      },
      {
        name: "AI Model Training & Calibration",
        description: "Train churn prediction and pricing optimization models",
        estimatedDuration: 6,
        dependencies: ["historical-data-import"],
        assignee: 'technical',
        status: 'pending',
      },
      {
        name: "Custom Workflow Setup",
        description: "Configure automated workflows and business rules",
        estimatedDuration: 2,
        dependencies: ["dashboard-configuration"],
        assignee: 'customer_success',
        status: 'pending',
      },

      // Phase 5: Training & Enablement (Days 7-8)
      {
        name: "Admin Team Training",
        description: "Train administrators on platform management and configuration",
        estimatedDuration: 4,
        dependencies: ["ai-model-training-calibration"],
        assignee: 'customer_success',
        status: 'pending',
      },
      {
        name: "End User Training",
        description: "Train end users on dashboard usage and analytics interpretation",
        estimatedDuration: 3,
        dependencies: ["custom-workflow-setup"],
        assignee: 'customer_success',
        status: 'pending',
      },
      {
        name: "Best Practices Workshop",
        description: "Workshop on revenue optimization best practices and advanced features",
        estimatedDuration: 2,
        dependencies: ["admin-team-training", "end-user-training"],
        assignee: 'customer_success',
        status: 'pending',
      },

      // Phase 6: Go-Live & Optimization (Days 8-10)
      {
        name: "Go-Live Validation",
        description: "Validate all systems are working correctly and performance targets are met",
        estimatedDuration: 2,
        dependencies: ["best-practices-workshop"],
        assignee: 'technical',
        status: 'pending',
      },
      {
        name: "Performance Optimization",
        description: "Fine-tune performance and optimize for customer-specific workloads",
        estimatedDuration: 3,
        dependencies: ["go-live-validation"],
        assignee: 'technical',
        status: 'pending',
      },
      {
        name: "Success Metrics Review",
        description: "Review initial results and validate success criteria achievement",
        estimatedDuration: 1.5,
        dependencies: ["performance-optimization"],
        assignee: 'customer_success',
        status: 'pending',
      },

      // Phase 7: Success & Handoff (Days 10-14)
      {
        name: "30-Day Success Plan",
        description: "Create detailed plan for first 30 days of platform usage",
        estimatedDuration: 1,
        dependencies: ["success-metrics-review"],
        assignee: 'customer_success',
        status: 'pending',
      },
      {
        name: "Ongoing Support Handoff",
        description: "Transition from onboarding to ongoing customer success management",
        estimatedDuration: 1,
        dependencies: ["30-day-success-plan"],
        assignee: 'customer_success',
        status: 'pending',
      },
    ];

    // Generate IDs and due dates for steps
    const startDate = new Date();
    let currentDate = new Date(startDate);
    
    return baseSteps.map((step, index) => {
      const stepId = step.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
      
      // Calculate due date based on dependencies and estimated duration
      const dueDate = new Date(currentDate);
      dueDate.setHours(dueDate.getHours() + step.estimatedDuration);
      
      // Advance current date for next step
      if (index < baseSteps.length - 1) {
        currentDate = new Date(dueDate);
      }

      return {
        id: stepId,
        dueDate,
        ...step,
      };
    });
  }

  /**
   * Calculate target completion date based on customer tier
   */
  private calculateTargetCompletion(tier: string): Date {
    const startDate = new Date();
    const daysToAdd = tier === 'enterprise' ? 14 : tier === 'professional' ? 10 : 7;
    
    const targetDate = new Date(startDate);
    targetDate.setDate(targetDate.getDate() + daysToAdd);
    
    return targetDate;
  }

  /**
   * Send welcome package with onboarding materials
   */
  private async sendWelcomePackage(customer: OnboardingCustomer, workflow: OnboardingWorkflow): Promise<void> {
    const welcomeEmail = {
      to: customer.contactEmail,
      subject: `Welcome to Revenue Optimization Platform - Let's Get Started!`,
      template: 'enterprise-welcome',
      data: {
        companyName: customer.companyName,
        workflowId: workflow.workflowId,
        targetCompletion: workflow.targetCompletionDate.toLocaleDateString(),
        kickoffMeetingLink: `https://calendly.com/revenue-optimization/enterprise-kickoff`,
        onboardingPortal: `https://app.revenue-optimization.com/onboarding/${workflow.workflowId}`,
        supportContact: '<EMAIL>',
        emergencyContact: '+1-555-REVENUE',
      },
    };

    // Send email (implementation would integrate with email service)
    console.log('Sending welcome email:', welcomeEmail);
    
    // Create onboarding portal access
    await this.createOnboardingPortal(workflow);
  }

  /**
   * Schedule kickoff meeting with customer success team
   */
  private async scheduleKickoffMeeting(customer: OnboardingCustomer, workflow: OnboardingWorkflow): Promise<void> {
    const meeting = {
      title: `Revenue Optimization Platform - Kickoff Meeting`,
      attendees: [
        customer.contactEmail,
        '<EMAIL>',
        '<EMAIL>',
      ],
      duration: 120, // 2 hours
      agenda: [
        'Welcome and introductions',
        'Platform overview and capabilities',
        'Current state assessment',
        'Success criteria definition',
        'Implementation timeline review',
        'Next steps and action items',
      ],
      schedulingLink: `https://calendly.com/revenue-optimization/enterprise-kickoff?prefill_company=${encodeURIComponent(customer.companyName)}`,
    };

    console.log('Scheduling kickoff meeting:', meeting);
  }

  /**
   * Create personalized onboarding portal for customer
   */
  private async createOnboardingPortal(workflow: OnboardingWorkflow): Promise<void> {
    const portal = {
      workflowId: workflow.workflowId,
      url: `https://app.revenue-optimization.com/onboarding/${workflow.workflowId}`,
      features: [
        'Real-time progress tracking',
        'Step-by-step guidance',
        'Resource library access',
        'Direct communication with success team',
        'Performance metrics dashboard',
        'Training materials and videos',
      ],
    };

    console.log('Creating onboarding portal:', portal);
  }

  /**
   * Update step status and progress workflow
   */
  async updateStepStatus(
    workflowId: string, 
    stepId: string, 
    status: OnboardingStep['status'],
    notes?: string
  ): Promise<void> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    const step = workflow.steps.find(s => s.id === stepId);
    if (!step) {
      throw new Error(`Step ${stepId} not found in workflow ${workflowId}`);
    }

    step.status = status;
    if (notes) step.notes = notes;
    if (status === 'completed') {
      step.completedDate = new Date();
    }

    // Update workflow phase based on completed steps
    this.updateWorkflowPhase(workflow);

    // Check for blockers and send notifications
    await this.checkForBlockers(workflow);

    // Update success metrics
    await this.updateSuccessMetrics(workflow);
  }

  /**
   * Update workflow phase based on completed steps
   */
  private updateWorkflowPhase(workflow: OnboardingWorkflow): void {
    const completedSteps = workflow.steps.filter(s => s.status === 'completed').length;
    const totalSteps = workflow.steps.length;
    const completionPercentage = (completedSteps / totalSteps) * 100;

    if (completionPercentage >= 90) {
      workflow.currentPhase = 'success';
    } else if (completionPercentage >= 70) {
      workflow.currentPhase = 'optimization';
    } else if (completionPercentage >= 50) {
      workflow.currentPhase = 'training';
    } else if (completionPercentage >= 30) {
      workflow.currentPhase = 'integration';
    } else if (completionPercentage >= 10) {
      workflow.currentPhase = 'setup';
    } else {
      workflow.currentPhase = 'discovery';
    }
  }

  /**
   * Check for blockers and send notifications
   */
  private async checkForBlockers(workflow: OnboardingWorkflow): Promise<void> {
    const blockedSteps = workflow.steps.filter(s => s.status === 'blocked');
    const overdueSteps = workflow.steps.filter(s => 
      s.status !== 'completed' && 
      s.dueDate < new Date()
    );

    if (blockedSteps.length > 0 || overdueSteps.length > 0) {
      await this.sendEscalationNotification(workflow, blockedSteps, overdueSteps);
    }
  }

  /**
   * Send escalation notification for blockers or overdue steps
   */
  private async sendEscalationNotification(
    workflow: OnboardingWorkflow,
    blockedSteps: OnboardingStep[],
    overdueSteps: OnboardingStep[]
  ): Promise<void> {
    const notification = {
      workflowId: workflow.workflowId,
      customerId: workflow.customerId,
      type: 'escalation',
      blockedSteps: blockedSteps.map(s => ({ id: s.id, name: s.name, notes: s.notes })),
      overdueSteps: overdueSteps.map(s => ({ 
        id: s.id, 
        name: s.name, 
        dueDate: s.dueDate,
        daysPastDue: Math.floor((Date.now() - s.dueDate.getTime()) / (1000 * 60 * 60 * 24))
      })),
      escalationLevel: blockedSteps.length > 0 ? 'high' : 'medium',
    };

    console.log('Sending escalation notification:', notification);
  }

  /**
   * Update success metrics based on workflow progress
   */
  private async updateSuccessMetrics(workflow: OnboardingWorkflow): Promise<void> {
    const firstInsightStep = workflow.steps.find(s => s.id === 'dashboard-configuration');
    const goLiveStep = workflow.steps.find(s => s.id === 'go-live-validation');

    if (firstInsightStep?.completedDate) {
      workflow.successMetrics.timeToFirstInsight = 
        (firstInsightStep.completedDate.getTime() - workflow.startDate.getTime()) / (1000 * 60 * 60);
    }

    if (goLiveStep?.completedDate) {
      workflow.successMetrics.timeToValue = 
        (goLiveStep.completedDate.getTime() - workflow.startDate.getTime()) / (1000 * 60 * 60 * 24);
    }

    // Calculate user adoption (would integrate with analytics)
    workflow.successMetrics.userAdoption = 85; // Placeholder

    // Check performance targets (would integrate with monitoring)
    workflow.successMetrics.performanceTargets = true; // Placeholder
  }

  /**
   * Generate onboarding status report
   */
  generateStatusReport(workflowId: string): any {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    const completedSteps = workflow.steps.filter(s => s.status === 'completed').length;
    const totalSteps = workflow.steps.length;
    const completionPercentage = Math.round((completedSteps / totalSteps) * 100);

    const upcomingSteps = workflow.steps
      .filter(s => s.status === 'pending' || s.status === 'in_progress')
      .slice(0, 3);

    const blockedSteps = workflow.steps.filter(s => s.status === 'blocked');
    const overdueSteps = workflow.steps.filter(s => 
      s.status !== 'completed' && 
      s.dueDate < new Date()
    );

    return {
      workflowId: workflow.workflowId,
      customerId: workflow.customerId,
      currentPhase: workflow.currentPhase,
      completionPercentage,
      completedSteps,
      totalSteps,
      targetCompletionDate: workflow.targetCompletionDate,
      successMetrics: workflow.successMetrics,
      upcomingSteps: upcomingSteps.map(s => ({
        id: s.id,
        name: s.name,
        dueDate: s.dueDate,
        assignee: s.assignee,
      })),
      issues: {
        blockedSteps: blockedSteps.length,
        overdueSteps: overdueSteps.length,
        atRisk: overdueSteps.length > 2 || blockedSteps.length > 0,
      },
      nextActions: this.generateNextActions(workflow),
    };
  }

  /**
   * Generate next actions based on workflow state
   */
  private generateNextActions(workflow: OnboardingWorkflow): string[] {
    const actions: string[] = [];
    
    const blockedSteps = workflow.steps.filter(s => s.status === 'blocked');
    const overdueSteps = workflow.steps.filter(s => 
      s.status !== 'completed' && 
      s.dueDate < new Date()
    );

    if (blockedSteps.length > 0) {
      actions.push(`Resolve ${blockedSteps.length} blocked step(s)`);
    }

    if (overdueSteps.length > 0) {
      actions.push(`Address ${overdueSteps.length} overdue step(s)`);
    }

    const nextPendingStep = workflow.steps.find(s => s.status === 'pending');
    if (nextPendingStep) {
      actions.push(`Begin "${nextPendingStep.name}"`);
    }

    if (workflow.currentPhase === 'training') {
      actions.push('Schedule user training sessions');
    }

    if (workflow.currentPhase === 'optimization') {
      actions.push('Review performance metrics and optimize');
    }

    return actions;
  }
}

// Export the onboarding orchestrator
export { EnterpriseOnboardingOrchestrator, type OnboardingCustomer, type OnboardingWorkflow };
