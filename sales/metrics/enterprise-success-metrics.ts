// Enterprise Success Metrics System
// Comprehensive sales KPIs, conversion tracking, and success measurement for Revenue Optimization Platform

import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";

// Enterprise sales metrics and KPI tracking
interface SalesKPI {
  id: string;
  name: string;
  category: 'pipeline' | 'conversion' | 'revenue' | 'efficiency' | 'customer';
  target: number;
  actual: number;
  unit: 'number' | 'percentage' | 'currency' | 'days';
  period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annual';
  trend: 'up' | 'down' | 'stable';
  variance: number; // percentage difference from target
  lastUpdated: Date;
}

interface ConversionFunnel {
  stage: string;
  count: number;
  conversionRate: number;
  averageTime: number; // days in stage
  dropoffRate: number;
  revenueImpact: number;
}

interface SalesPerformance {
  rep: string;
  period: string;
  metrics: {
    leadsGenerated: number;
    leadsQualified: number;
    demosCompleted: number;
    pocsStarted: number;
    dealsWon: number;
    revenue: number;
    averageDealSize: number;
    salesCycleLength: number;
    quota: number;
    quotaAttainment: number;
  };
  ranking: number;
  trends: {
    weekOverWeek: number;
    monthOverMonth: number;
    quarterOverQuarter: number;
  };
}

interface CustomerSuccessMetrics {
  customerId: string;
  companyName: string;
  tier: string;
  onboardingMetrics: {
    timeToFirstInsight: number; // hours
    timeToValue: number; // days
    implementationScore: number; // 1-10
    userAdoption: number; // percentage
    trainingCompletion: number; // percentage
  };
  usageMetrics: {
    dailyActiveUsers: number;
    featureAdoption: number; // percentage
    apiCalls: number;
    dashboardViews: number;
    reportsGenerated: number;
  };
  businessImpact: {
    revenueIncrease: number; // percentage
    conversionImprovement: number; // percentage
    churnReduction: number; // percentage
    roi: number; // percentage
    paybackPeriod: number; // days
  };
  healthScore: number; // 1-100
  riskLevel: 'low' | 'medium' | 'high';
  expansionOpportunity: number; // potential additional revenue
}

class EnterpriseSalesMetricsSystem {
  private kpis: Map<string, SalesKPI> = new Map();
  private salesPerformance: Map<string, SalesPerformance> = new Map();
  private customerMetrics: Map<string, CustomerSuccessMetrics> = new Map();

  /**
   * Initialize enterprise sales KPIs with targets
   */
  initializeKPIs(): void {
    const kpis: Omit<SalesKPI, 'id' | 'actual' | 'trend' | 'variance' | 'lastUpdated'>[] = [
      // Pipeline KPIs
      {
        name: 'Monthly Qualified Leads',
        category: 'pipeline',
        target: 100,
        unit: 'number',
        period: 'monthly',
      },
      {
        name: 'Pipeline Value',
        category: 'pipeline',
        target: 2000000,
        unit: 'currency',
        period: 'quarterly',
      },
      {
        name: 'Pipeline Velocity',
        category: 'pipeline',
        target: 45,
        unit: 'days',
        period: 'monthly',
      },

      // Conversion KPIs
      {
        name: 'Lead to Qualified Conversion',
        category: 'conversion',
        target: 25,
        unit: 'percentage',
        period: 'monthly',
      },
      {
        name: 'Demo to POC Conversion',
        category: 'conversion',
        target: 40,
        unit: 'percentage',
        period: 'monthly',
      },
      {
        name: 'POC to Proposal Conversion',
        category: 'conversion',
        target: 70,
        unit: 'percentage',
        period: 'monthly',
      },
      {
        name: 'Proposal to Close Conversion',
        category: 'conversion',
        target: 60,
        unit: 'percentage',
        period: 'monthly',
      },

      // Revenue KPIs
      {
        name: 'Monthly Recurring Revenue',
        category: 'revenue',
        target: 500000,
        unit: 'currency',
        period: 'monthly',
      },
      {
        name: 'Annual Contract Value',
        category: 'revenue',
        target: 75000,
        unit: 'currency',
        period: 'quarterly',
      },
      {
        name: 'Revenue Growth Rate',
        category: 'revenue',
        target: 20,
        unit: 'percentage',
        period: 'quarterly',
      },

      // Efficiency KPIs
      {
        name: 'Sales Cycle Length',
        category: 'efficiency',
        target: 60,
        unit: 'days',
        period: 'monthly',
      },
      {
        name: 'Cost per Acquisition',
        category: 'efficiency',
        target: 5000,
        unit: 'currency',
        period: 'monthly',
      },
      {
        name: 'Sales Productivity',
        category: 'efficiency',
        target: 150000,
        unit: 'currency',
        period: 'monthly',
      },

      // Customer KPIs
      {
        name: 'Customer Satisfaction Score',
        category: 'customer',
        target: 95,
        unit: 'percentage',
        period: 'quarterly',
      },
      {
        name: 'Net Promoter Score',
        category: 'customer',
        target: 70,
        unit: 'number',
        period: 'quarterly',
      },
      {
        name: 'Customer Retention Rate',
        category: 'customer',
        target: 95,
        unit: 'percentage',
        period: 'annual',
      },
    ];

    kpis.forEach((kpi, index) => {
      const kpiWithDefaults: SalesKPI = {
        id: `kpi-${index + 1}`,
        actual: 0,
        trend: 'stable',
        variance: 0,
        lastUpdated: new Date(),
        ...kpi,
      };
      this.kpis.set(kpiWithDefaults.id, kpiWithDefaults);
    });
  }

  /**
   * Update KPI with actual performance data
   */
  updateKPI(kpiId: string, actualValue: number): void {
    const kpi = this.kpis.get(kpiId);
    if (!kpi) {
      throw new Error(`KPI ${kpiId} not found`);
    }

    const previousValue = kpi.actual;
    kpi.actual = actualValue;
    kpi.variance = ((actualValue - kpi.target) / kpi.target) * 100;
    kpi.lastUpdated = new Date();

    // Determine trend
    if (actualValue > previousValue) {
      kpi.trend = 'up';
    } else if (actualValue < previousValue) {
      kpi.trend = 'down';
    } else {
      kpi.trend = 'stable';
    }

    this.kpis.set(kpiId, kpi);
  }

  /**
   * Generate conversion funnel analysis
   */
  generateConversionFunnel(period: 'monthly' | 'quarterly'): ConversionFunnel[] {
    // Mock data - in production, this would query actual sales data
    const funnelData: ConversionFunnel[] = [
      {
        stage: 'Leads Generated',
        count: 500,
        conversionRate: 100,
        averageTime: 0,
        dropoffRate: 0,
        revenueImpact: 0,
      },
      {
        stage: 'Qualified Leads',
        count: 125,
        conversionRate: 25,
        averageTime: 2,
        dropoffRate: 75,
        revenueImpact: 0,
      },
      {
        stage: 'Demos Scheduled',
        count: 100,
        conversionRate: 80,
        averageTime: 5,
        dropoffRate: 20,
        revenueImpact: 0,
      },
      {
        stage: 'POCs Started',
        count: 40,
        conversionRate: 40,
        averageTime: 14,
        dropoffRate: 60,
        revenueImpact: 0,
      },
      {
        stage: 'Proposals Sent',
        count: 28,
        conversionRate: 70,
        averageTime: 21,
        dropoffRate: 30,
        revenueImpact: 0,
      },
      {
        stage: 'Deals Won',
        count: 17,
        conversionRate: 60,
        averageTime: 35,
        dropoffRate: 40,
        revenueImpact: 1275000, // $75k average deal size
      },
    ];

    return funnelData;
  }

  /**
   * Track sales rep performance
   */
  trackSalesPerformance(rep: string, period: string, metrics: any): void {
    const performance: SalesPerformance = {
      rep,
      period,
      metrics: {
        leadsGenerated: metrics.leadsGenerated || 0,
        leadsQualified: metrics.leadsQualified || 0,
        demosCompleted: metrics.demosCompleted || 0,
        pocsStarted: metrics.pocsStarted || 0,
        dealsWon: metrics.dealsWon || 0,
        revenue: metrics.revenue || 0,
        averageDealSize: metrics.revenue / (metrics.dealsWon || 1),
        salesCycleLength: metrics.salesCycleLength || 0,
        quota: metrics.quota || 100000,
        quotaAttainment: (metrics.revenue / (metrics.quota || 100000)) * 100,
      },
      ranking: 0, // Will be calculated
      trends: {
        weekOverWeek: 0,
        monthOverMonth: 0,
        quarterOverQuarter: 0,
      },
    };

    this.salesPerformance.set(`${rep}-${period}`, performance);
    this.calculateSalesRankings(period);
  }

  /**
   * Calculate sales rep rankings
   */
  private calculateSalesRankings(period: string): void {
    const repsInPeriod = Array.from(this.salesPerformance.values())
      .filter(p => p.period === period)
      .sort((a, b) => b.metrics.revenue - a.metrics.revenue);

    repsInPeriod.forEach((rep, index) => {
      rep.ranking = index + 1;
      this.salesPerformance.set(`${rep.rep}-${period}`, rep);
    });
  }

  /**
   * Track customer success metrics
   */
  trackCustomerSuccess(customerId: string, metrics: Partial<CustomerSuccessMetrics>): void {
    const existing = this.customerMetrics.get(customerId);
    
    const customerMetrics: CustomerSuccessMetrics = {
      customerId,
      companyName: metrics.companyName || existing?.companyName || '',
      tier: metrics.tier || existing?.tier || 'growth',
      onboardingMetrics: {
        timeToFirstInsight: metrics.onboardingMetrics?.timeToFirstInsight || existing?.onboardingMetrics?.timeToFirstInsight || 0,
        timeToValue: metrics.onboardingMetrics?.timeToValue || existing?.onboardingMetrics?.timeToValue || 0,
        implementationScore: metrics.onboardingMetrics?.implementationScore || existing?.onboardingMetrics?.implementationScore || 0,
        userAdoption: metrics.onboardingMetrics?.userAdoption || existing?.onboardingMetrics?.userAdoption || 0,
        trainingCompletion: metrics.onboardingMetrics?.trainingCompletion || existing?.onboardingMetrics?.trainingCompletion || 0,
      },
      usageMetrics: {
        dailyActiveUsers: metrics.usageMetrics?.dailyActiveUsers || existing?.usageMetrics?.dailyActiveUsers || 0,
        featureAdoption: metrics.usageMetrics?.featureAdoption || existing?.usageMetrics?.featureAdoption || 0,
        apiCalls: metrics.usageMetrics?.apiCalls || existing?.usageMetrics?.apiCalls || 0,
        dashboardViews: metrics.usageMetrics?.dashboardViews || existing?.usageMetrics?.dashboardViews || 0,
        reportsGenerated: metrics.usageMetrics?.reportsGenerated || existing?.usageMetrics?.reportsGenerated || 0,
      },
      businessImpact: {
        revenueIncrease: metrics.businessImpact?.revenueIncrease || existing?.businessImpact?.revenueIncrease || 0,
        conversionImprovement: metrics.businessImpact?.conversionImprovement || existing?.businessImpact?.conversionImprovement || 0,
        churnReduction: metrics.businessImpact?.churnReduction || existing?.businessImpact?.churnReduction || 0,
        roi: metrics.businessImpact?.roi || existing?.businessImpact?.roi || 0,
        paybackPeriod: metrics.businessImpact?.paybackPeriod || existing?.businessImpact?.paybackPeriod || 0,
      },
      healthScore: this.calculateHealthScore(metrics),
      riskLevel: this.calculateRiskLevel(metrics),
      expansionOpportunity: this.calculateExpansionOpportunity(metrics),
    };

    this.customerMetrics.set(customerId, customerMetrics);
  }

  /**
   * Calculate customer health score
   */
  private calculateHealthScore(metrics: Partial<CustomerSuccessMetrics>): number {
    let score = 0;

    // Onboarding success (30 points)
    if (metrics.onboardingMetrics?.timeToFirstInsight && metrics.onboardingMetrics.timeToFirstInsight <= 24) score += 10;
    if (metrics.onboardingMetrics?.timeToValue && metrics.onboardingMetrics.timeToValue <= 7) score += 10;
    if (metrics.onboardingMetrics?.userAdoption && metrics.onboardingMetrics.userAdoption >= 90) score += 10;

    // Usage engagement (40 points)
    if (metrics.usageMetrics?.featureAdoption && metrics.usageMetrics.featureAdoption >= 80) score += 15;
    if (metrics.usageMetrics?.dailyActiveUsers && metrics.usageMetrics.dailyActiveUsers > 0) score += 10;
    if (metrics.usageMetrics?.dashboardViews && metrics.usageMetrics.dashboardViews >= 50) score += 15;

    // Business impact (30 points)
    if (metrics.businessImpact?.revenueIncrease && metrics.businessImpact.revenueIncrease >= 15) score += 15;
    if (metrics.businessImpact?.roi && metrics.businessImpact.roi >= 300) score += 15;

    return Math.min(score, 100);
  }

  /**
   * Calculate customer risk level
   */
  private calculateRiskLevel(metrics: Partial<CustomerSuccessMetrics>): 'low' | 'medium' | 'high' {
    const healthScore = this.calculateHealthScore(metrics);
    
    if (healthScore >= 80) return 'low';
    if (healthScore >= 60) return 'medium';
    return 'high';
  }

  /**
   * Calculate expansion opportunity
   */
  private calculateExpansionOpportunity(metrics: Partial<CustomerSuccessMetrics>): number {
    const healthScore = this.calculateHealthScore(metrics);
    const currentTier = metrics.tier || 'growth';
    
    const tierValues = {
      starter: 1188, // $99/month
      growth: 5988, // $499/month
      professional: 17988, // $1499/month
      enterprise: 59988, // $4999/month
    };

    const currentValue = tierValues[currentTier as keyof typeof tierValues];
    
    if (healthScore >= 90 && currentTier !== 'enterprise') {
      // High health score suggests expansion opportunity
      if (currentTier === 'starter') return tierValues.growth - currentValue;
      if (currentTier === 'growth') return tierValues.professional - currentValue;
      if (currentTier === 'professional') return tierValues.enterprise - currentValue;
    }
    
    return 0;
  }

  /**
   * Generate comprehensive sales dashboard data
   */
  generateSalesDashboard(): any {
    const kpisByCategory = this.groupKPIsByCategory();
    const conversionFunnel = this.generateConversionFunnel('monthly');
    const topPerformers = this.getTopPerformers();
    const customerHealth = this.getCustomerHealthOverview();

    return {
      overview: {
        totalKPIs: this.kpis.size,
        kpisOnTarget: Array.from(this.kpis.values()).filter(k => k.variance >= 0).length,
        kpisAtRisk: Array.from(this.kpis.values()).filter(k => k.variance < -10).length,
        overallHealth: this.calculateOverallHealth(),
      },
      kpis: kpisByCategory,
      conversionFunnel,
      topPerformers,
      customerHealth,
      alerts: this.generateAlerts(),
      recommendations: this.generateRecommendations(),
    };
  }

  /**
   * Group KPIs by category
   */
  private groupKPIsByCategory(): Record<string, SalesKPI[]> {
    const grouped: Record<string, SalesKPI[]> = {};
    
    for (const kpi of this.kpis.values()) {
      if (!grouped[kpi.category]) {
        grouped[kpi.category] = [];
      }
      grouped[kpi.category].push(kpi);
    }
    
    return grouped;
  }

  /**
   * Get top performing sales reps
   */
  private getTopPerformers(): SalesPerformance[] {
    return Array.from(this.salesPerformance.values())
      .filter(p => p.period === 'current-month')
      .sort((a, b) => b.metrics.quotaAttainment - a.metrics.quotaAttainment)
      .slice(0, 5);
  }

  /**
   * Get customer health overview
   */
  private getCustomerHealthOverview(): any {
    const customers = Array.from(this.customerMetrics.values());
    
    return {
      totalCustomers: customers.length,
      healthyCustomers: customers.filter(c => c.healthScore >= 80).length,
      atRiskCustomers: customers.filter(c => c.riskLevel === 'high').length,
      expansionOpportunities: customers.filter(c => c.expansionOpportunity > 0).length,
      totalExpansionValue: customers.reduce((sum, c) => sum + c.expansionOpportunity, 0),
      averageHealthScore: customers.reduce((sum, c) => sum + c.healthScore, 0) / customers.length,
    };
  }

  /**
   * Calculate overall sales health
   */
  private calculateOverallHealth(): number {
    const kpis = Array.from(this.kpis.values());
    const onTargetKPIs = kpis.filter(k => k.variance >= 0).length;
    return Math.round((onTargetKPIs / kpis.length) * 100);
  }

  /**
   * Generate alerts for KPIs at risk
   */
  private generateAlerts(): any[] {
    const alerts = [];
    
    for (const kpi of this.kpis.values()) {
      if (kpi.variance < -20) {
        alerts.push({
          type: 'critical',
          kpi: kpi.name,
          message: `${kpi.name} is ${Math.abs(kpi.variance).toFixed(1)}% below target`,
          action: 'Immediate attention required',
        });
      } else if (kpi.variance < -10) {
        alerts.push({
          type: 'warning',
          kpi: kpi.name,
          message: `${kpi.name} is ${Math.abs(kpi.variance).toFixed(1)}% below target`,
          action: 'Monitor closely and take corrective action',
        });
      }
    }
    
    return alerts;
  }

  /**
   * Generate recommendations based on performance
   */
  private generateRecommendations(): string[] {
    const recommendations = [];
    const conversionFunnel = this.generateConversionFunnel('monthly');
    
    // Analyze conversion funnel for bottlenecks
    const biggestDropoff = conversionFunnel.reduce((max, stage) => 
      stage.dropoffRate > max.dropoffRate ? stage : max
    );
    
    if (biggestDropoff.dropoffRate > 50) {
      recommendations.push(`Focus on improving ${biggestDropoff.stage} conversion - highest dropoff at ${biggestDropoff.dropoffRate}%`);
    }
    
    // Check sales cycle length
    const salesCycleKPI = Array.from(this.kpis.values()).find(k => k.name === 'Sales Cycle Length');
    if (salesCycleKPI && salesCycleKPI.variance > 10) {
      recommendations.push('Sales cycle is longer than target - consider streamlining the sales process');
    }
    
    // Check customer health
    const atRiskCustomers = Array.from(this.customerMetrics.values()).filter(c => c.riskLevel === 'high').length;
    if (atRiskCustomers > 0) {
      recommendations.push(`${atRiskCustomers} customers at high risk - prioritize customer success interventions`);
    }
    
    return recommendations;
  }

  /**
   * Export metrics for external reporting
   */
  exportMetrics(format: 'json' | 'csv'): string {
    const data = this.generateSalesDashboard();
    
    if (format === 'json') {
      return JSON.stringify(data, null, 2);
    } else {
      // Convert to CSV format
      const kpis = Array.from(this.kpis.values());
      const csvHeaders = 'KPI,Category,Target,Actual,Variance,Trend';
      const csvRows = kpis.map(kpi => 
        `"${kpi.name}","${kpi.category}",${kpi.target},${kpi.actual},${kpi.variance.toFixed(2)},${kpi.trend}`
      );
      
      return [csvHeaders, ...csvRows].join('\n');
    }
  }
}

// Export the enterprise success metrics system
export { EnterpriseSalesMetricsSystem, type SalesKPI, type ConversionFunnel, type SalesPerformance, type CustomerSuccessMetrics };
