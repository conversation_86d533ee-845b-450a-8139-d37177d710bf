<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Revenue Optimization ROI Calculator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            padding: 40px;
        }

        .input-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
        }

        .results-section {
            background: #fff;
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #e9ecef;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .calculate-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .calculate-btn:hover {
            transform: translateY(-2px);
        }

        .result-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .result-card h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .result-card .value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .result-card .subtitle {
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .metric-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .metric-item .label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .metric-item .value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #495057;
        }

        .improvement {
            color: #28a745;
            font-weight: bold;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .comparison-table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .highlight {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Revenue Optimization ROI Calculator</h1>
            <p>Discover your potential revenue impact with our AI-powered platform</p>
        </div>

        <div class="content">
            <div class="input-section">
                <h2 style="margin-bottom: 25px; color: #495057;">Current Business Metrics</h2>
                
                <div class="form-group">
                    <label for="annualRevenue">Annual Revenue ($)</label>
                    <input type="number" id="annualRevenue" placeholder="50000000" value="50000000">
                </div>

                <div class="form-group">
                    <label for="monthlyTransactions">Monthly Transactions</label>
                    <input type="number" id="monthlyTransactions" placeholder="100000" value="100000">
                </div>

                <div class="form-group">
                    <label for="conversionRate">Conversion Rate (%)</label>
                    <input type="number" id="conversionRate" step="0.1" placeholder="2.5" value="2.5">
                </div>

                <div class="form-group">
                    <label for="averageOrderValue">Average Order Value ($)</label>
                    <input type="number" id="averageOrderValue" placeholder="125" value="125">
                </div>

                <div class="form-group">
                    <label for="customerLTV">Customer Lifetime Value ($)</label>
                    <input type="number" id="customerLTV" placeholder="450" value="450">
                </div>

                <div class="form-group">
                    <label for="churnRate">Monthly Churn Rate (%)</label>
                    <input type="number" id="churnRate" step="0.1" placeholder="15" value="15">
                </div>

                <div class="form-group">
                    <label for="teamSize">Team Size</label>
                    <input type="number" id="teamSize" placeholder="50" value="50">
                </div>

                <button class="calculate-btn" onclick="calculateROI()">Calculate ROI Impact</button>
            </div>

            <div class="results-section">
                <h2 style="margin-bottom: 25px; color: #495057;">Revenue Impact Analysis</h2>
                
                <div id="results">
                    <div class="result-card">
                        <h3>Annual Revenue Impact</h3>
                        <div class="value" id="revenueImpact">$0</div>
                        <div class="subtitle">Additional revenue in first year</div>
                    </div>

                    <div class="metrics-grid">
                        <div class="metric-item">
                            <div class="label">ROI</div>
                            <div class="value improvement" id="roi">0%</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">Payback Period</div>
                            <div class="value" id="payback">0 days</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">Platform Cost</div>
                            <div class="value" id="platformCost">$0</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">Net Benefit</div>
                            <div class="value improvement" id="netBenefit">$0</div>
                        </div>
                    </div>

                    <h3 style="margin: 30px 0 15px 0; color: #495057;">Performance Improvements</h3>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Metric</th>
                                <th>Current</th>
                                <th>With Platform</th>
                                <th>Improvement</th>
                            </tr>
                        </thead>
                        <tbody id="comparisonTable">
                            <!-- Dynamic content will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        function calculateROI() {
            // Get input values
            const annualRevenue = parseFloat(document.getElementById('annualRevenue').value) || 0;
            const monthlyTransactions = parseFloat(document.getElementById('monthlyTransactions').value) || 0;
            const conversionRate = parseFloat(document.getElementById('conversionRate').value) || 0;
            const averageOrderValue = parseFloat(document.getElementById('averageOrderValue').value) || 0;
            const customerLTV = parseFloat(document.getElementById('customerLTV').value) || 0;
            const churnRate = parseFloat(document.getElementById('churnRate').value) || 0;
            const teamSize = parseFloat(document.getElementById('teamSize').value) || 0;

            // Platform improvements (based on validated performance data)
            const improvements = {
                conversionRateIncrease: 0.28, // 28% improvement
                aovIncrease: 0.16, // 16% improvement
                ltvIncrease: 0.40, // 40% improvement
                churnReduction: 0.47, // 47% reduction
                operationalEfficiency: 0.35 // 35% efficiency gain
            };

            // Calculate improved metrics
            const improvedConversionRate = conversionRate * (1 + improvements.conversionRateIncrease);
            const improvedAOV = averageOrderValue * (1 + improvements.aovIncrease);
            const improvedLTV = customerLTV * (1 + improvements.ltvIncrease);
            const improvedChurnRate = churnRate * (1 - improvements.churnReduction);

            // Calculate revenue impact
            const currentMonthlyRevenue = annualRevenue / 12;
            const improvedMonthlyRevenue = (monthlyTransactions * improvedConversionRate / 100) * improvedAOV;
            const monthlyRevenueIncrease = improvedMonthlyRevenue - currentMonthlyRevenue;
            const annualRevenueIncrease = monthlyRevenueIncrease * 12;

            // Calculate platform cost based on revenue tier
            let platformCost;
            if (annualRevenue <= 1000000) {
                platformCost = 99 * 12; // Starter tier
            } else if (annualRevenue <= 10000000) {
                platformCost = 499 * 12; // Growth tier
            } else if (annualRevenue <= 50000000) {
                platformCost = 1499 * 12; // Professional tier
            } else {
                platformCost = 4999 * 12; // Enterprise tier
            }

            // Add implementation costs
            const implementationCost = Math.min(50000, annualRevenue * 0.001);
            const totalCost = platformCost + implementationCost;

            // Calculate ROI metrics
            const netBenefit = annualRevenueIncrease - totalCost;
            const roi = (netBenefit / totalCost) * 100;
            const paybackDays = (totalCost / (annualRevenueIncrease / 365));

            // Update display
            document.getElementById('revenueImpact').textContent = formatCurrency(annualRevenueIncrease);
            document.getElementById('roi').textContent = formatPercentage(roi);
            document.getElementById('payback').textContent = Math.round(paybackDays) + ' days';
            document.getElementById('platformCost').textContent = formatCurrency(totalCost);
            document.getElementById('netBenefit').textContent = formatCurrency(netBenefit);

            // Update comparison table
            const comparisonData = [
                {
                    metric: 'Conversion Rate',
                    current: formatPercentage(conversionRate),
                    improved: formatPercentage(improvedConversionRate),
                    improvement: '+' + formatPercentage(improvements.conversionRateIncrease * 100)
                },
                {
                    metric: 'Average Order Value',
                    current: formatCurrency(averageOrderValue),
                    improved: formatCurrency(improvedAOV),
                    improvement: '+' + formatPercentage(improvements.aovIncrease * 100)
                },
                {
                    metric: 'Customer LTV',
                    current: formatCurrency(customerLTV),
                    improved: formatCurrency(improvedLTV),
                    improvement: '+' + formatPercentage(improvements.ltvIncrease * 100)
                },
                {
                    metric: 'Monthly Churn Rate',
                    current: formatPercentage(churnRate),
                    improved: formatPercentage(improvedChurnRate),
                    improvement: '-' + formatPercentage(improvements.churnReduction * 100)
                }
            ];

            const tableBody = document.getElementById('comparisonTable');
            tableBody.innerHTML = comparisonData.map(row => `
                <tr>
                    <td><strong>${row.metric}</strong></td>
                    <td>${row.current}</td>
                    <td>${row.improved}</td>
                    <td><span class="highlight">${row.improvement}</span></td>
                </tr>
            `).join('');
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        }

        function formatPercentage(percent) {
            return percent.toFixed(1) + '%';
        }

        // Calculate initial ROI on page load
        window.onload = function() {
            calculateROI();
        };

        // Add event listeners for real-time calculation
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('input', calculateROI);
        });
    </script>
</body>
</html>
