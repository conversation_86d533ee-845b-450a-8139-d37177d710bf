// Sales Tools & CRM Integration
// Comprehensive sales enablement and CRM integration for Revenue Optimization Platform

import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";

// Lead qualification and scoring types
interface Lead {
  id: string;
  companyName: string;
  contactName: string;
  email: string;
  phone?: string;
  website?: string;
  annualRevenue?: number;
  employeeCount?: number;
  industry: string;
  ecommercePlatform?: string[];
  currentAnalytics?: string[];
  painPoints: string[];
  budget?: number;
  timeline: string;
  decisionMakers: string[];
  source: string;
  createdAt: Date;
  lastActivity: Date;
  score: number;
  stage: 'lead' | 'qualified' | 'demo' | 'poc' | 'proposal' | 'negotiation' | 'closed-won' | 'closed-lost';
  assignedTo?: string;
  notes: string[];
}

interface SalesActivity {
  id: string;
  leadId: string;
  type: 'call' | 'email' | 'demo' | 'meeting' | 'proposal' | 'follow-up';
  subject: string;
  description: string;
  outcome: string;
  nextAction?: string;
  scheduledDate?: Date;
  completedDate?: Date;
  createdBy: string;
  attachments?: string[];
}

interface SalesMetrics {
  period: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  startDate: Date;
  endDate: Date;
  metrics: {
    leadsGenerated: number;
    leadsQualified: number;
    demosScheduled: number;
    demosCompleted: number;
    pocsStarted: number;
    proposalsSent: number;
    dealsWon: number;
    dealsLost: number;
    totalRevenue: number;
    averageDealSize: number;
    salesCycleLength: number;
    conversionRates: {
      leadToQualified: number;
      qualifiedToDemo: number;
      demoToPoc: number;
      pocToProposal: number;
      proposalToWon: number;
    };
  };
}

class SalesEnablementSystem {
  private leads: Map<string, Lead> = new Map();
  private activities: Map<string, SalesActivity[]> = new Map();
  private crmConfig: any = {};

  /**
   * Lead qualification and scoring system
   */
  async qualifyLead(leadData: Partial<Lead>): Promise<Lead> {
    const leadId = `lead-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Calculate lead score based on qualification criteria
    const score = this.calculateLeadScore(leadData);
    
    const lead: Lead = {
      id: leadId,
      companyName: leadData.companyName || '',
      contactName: leadData.contactName || '',
      email: leadData.email || '',
      phone: leadData.phone,
      website: leadData.website,
      annualRevenue: leadData.annualRevenue,
      employeeCount: leadData.employeeCount,
      industry: leadData.industry || '',
      ecommercePlatform: leadData.ecommercePlatform || [],
      currentAnalytics: leadData.currentAnalytics || [],
      painPoints: leadData.painPoints || [],
      budget: leadData.budget,
      timeline: leadData.timeline || '',
      decisionMakers: leadData.decisionMakers || [],
      source: leadData.source || 'unknown',
      createdAt: new Date(),
      lastActivity: new Date(),
      score,
      stage: score >= 70 ? 'qualified' : 'lead',
      notes: [],
    };

    this.leads.set(leadId, lead);
    
    // Auto-assign based on lead score and territory
    await this.autoAssignLead(lead);
    
    // Trigger automated follow-up sequence
    await this.triggerFollowUpSequence(lead);
    
    // Sync with CRM
    await this.syncWithCRM('create', lead);
    
    return lead;
  }

  /**
   * Calculate lead score based on qualification criteria
   */
  private calculateLeadScore(leadData: Partial<Lead>): number {
    let score = 0;

    // Company size scoring (30 points max)
    if (leadData.annualRevenue) {
      if (leadData.annualRevenue >= *********) score += 30; // $100M+
      else if (leadData.annualRevenue >= 50000000) score += 25; // $50M-$100M
      else if (leadData.annualRevenue >= 10000000) score += 20; // $10M-$50M
      else if (leadData.annualRevenue >= 1000000) score += 15; // $1M-$10M
      else if (leadData.annualRevenue >= 100000) score += 10; // $100K-$1M
    }

    // Employee count scoring (15 points max)
    if (leadData.employeeCount) {
      if (leadData.employeeCount >= 500) score += 15;
      else if (leadData.employeeCount >= 100) score += 12;
      else if (leadData.employeeCount >= 50) score += 10;
      else if (leadData.employeeCount >= 10) score += 8;
      else score += 5;
    }

    // E-commerce platform scoring (20 points max)
    if (leadData.ecommercePlatform?.length) {
      const platforms = leadData.ecommercePlatform;
      if (platforms.includes('Shopify Plus') || platforms.includes('BigCommerce Enterprise')) {
        score += 20;
      } else if (platforms.includes('Shopify') || platforms.includes('WooCommerce')) {
        score += 15;
      } else if (platforms.includes('Magento') || platforms.includes('BigCommerce')) {
        score += 12;
      } else {
        score += 8;
      }
    }

    // Pain points scoring (20 points max)
    const highValuePainPoints = [
      'slow analytics',
      'manual pricing',
      'high churn',
      'poor conversion',
      'data silos',
      'revenue optimization'
    ];
    const painPointMatches = leadData.painPoints?.filter(pain => 
      highValuePainPoints.some(hvp => pain.toLowerCase().includes(hvp))
    ).length || 0;
    score += Math.min(painPointMatches * 5, 20);

    // Timeline scoring (10 points max)
    if (leadData.timeline) {
      if (leadData.timeline.includes('immediate') || leadData.timeline.includes('30 days')) {
        score += 10;
      } else if (leadData.timeline.includes('90 days') || leadData.timeline.includes('quarter')) {
        score += 8;
      } else if (leadData.timeline.includes('6 months')) {
        score += 5;
      }
    }

    // Budget scoring (5 points max)
    if (leadData.budget) {
      if (leadData.budget >= 50000) score += 5;
      else if (leadData.budget >= 10000) score += 3;
      else if (leadData.budget >= 1000) score += 1;
    }

    return Math.min(score, 100);
  }

  /**
   * Auto-assign leads based on score and territory
   */
  private async autoAssignLead(lead: Lead): Promise<void> {
    let assignee = '';

    // High-value enterprise leads
    if (lead.score >= 80 && lead.annualRevenue && lead.annualRevenue >= 50000000) {
      assignee = '<EMAIL>';
    }
    // Mid-market leads
    else if (lead.score >= 60 && lead.annualRevenue && lead.annualRevenue >= 10000000) {
      assignee = '<EMAIL>';
    }
    // SMB leads
    else if (lead.score >= 40) {
      assignee = '<EMAIL>';
    }
    // Low-score leads to marketing for nurturing
    else {
      assignee = '<EMAIL>';
    }

    lead.assignedTo = assignee;

    // Send assignment notification
    await this.sendAssignmentNotification(lead, assignee);
  }

  /**
   * Trigger automated follow-up sequence based on lead score
   */
  private async triggerFollowUpSequence(lead: Lead): Promise<void> {
    const sequence = this.getFollowUpSequence(lead.score, lead.stage);
    
    for (const step of sequence) {
      await this.scheduleFollowUp(lead.id, step);
    }
  }

  /**
   * Get follow-up sequence based on lead score and stage
   */
  private getFollowUpSequence(score: number, stage: string): any[] {
    if (score >= 80) {
      // High-value lead sequence
      return [
        {
          type: 'call',
          delay: 0, // Immediate
          subject: 'Welcome to Revenue Optimization - Executive Introduction',
          template: 'enterprise-welcome-call',
        },
        {
          type: 'email',
          delay: 2, // 2 hours
          subject: 'Your Personalized ROI Analysis - 97% Performance Advantage',
          template: 'enterprise-roi-analysis',
        },
        {
          type: 'demo',
          delay: 24, // 1 day
          subject: 'Executive Demo: See 24,390+ Events/Sec in Action',
          template: 'enterprise-demo-invite',
        },
      ];
    } else if (score >= 60) {
      // Mid-market lead sequence
      return [
        {
          type: 'email',
          delay: 1, // 1 hour
          subject: 'Welcome! See How We Deliver 15-20% Revenue Growth',
          template: 'midmarket-welcome',
        },
        {
          type: 'call',
          delay: 24, // 1 day
          subject: 'Revenue Optimization Discovery Call',
          template: 'midmarket-discovery-call',
        },
        {
          type: 'demo',
          delay: 72, // 3 days
          subject: 'Interactive Demo: Real-time Analytics & AI Optimization',
          template: 'midmarket-demo-invite',
        },
      ];
    } else {
      // Standard lead sequence
      return [
        {
          type: 'email',
          delay: 2, // 2 hours
          subject: 'Transform Your E-commerce Revenue with AI Analytics',
          template: 'standard-welcome',
        },
        {
          type: 'email',
          delay: 72, // 3 days
          subject: 'Case Study: How Similar Companies Increased Revenue 20%',
          template: 'case-study-follow-up',
        },
        {
          type: 'call',
          delay: 168, // 1 week
          subject: 'Quick Revenue Optimization Assessment',
          template: 'standard-discovery-call',
        },
      ];
    }
  }

  /**
   * Schedule follow-up activity
   */
  private async scheduleFollowUp(leadId: string, step: any): Promise<void> {
    const scheduledDate = new Date();
    scheduledDate.setHours(scheduledDate.getHours() + step.delay);

    const activity: SalesActivity = {
      id: `activity-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      leadId,
      type: step.type,
      subject: step.subject,
      description: `Automated follow-up: ${step.template}`,
      outcome: '',
      scheduledDate,
      createdBy: 'system',
    };

    const leadActivities = this.activities.get(leadId) || [];
    leadActivities.push(activity);
    this.activities.set(leadId, leadActivities);

    // Schedule actual execution (would integrate with email/calling system)
    console.log(`Scheduled ${step.type} for lead ${leadId} at ${scheduledDate}`);
  }

  /**
   * Send assignment notification to sales rep
   */
  private async sendAssignmentNotification(lead: Lead, assignee: string): Promise<void> {
    const notification = {
      to: assignee,
      subject: `New ${lead.score >= 80 ? 'High-Priority' : 'Qualified'} Lead Assigned: ${lead.companyName}`,
      template: 'lead-assignment',
      data: {
        leadId: lead.id,
        companyName: lead.companyName,
        contactName: lead.contactName,
        score: lead.score,
        annualRevenue: lead.annualRevenue,
        painPoints: lead.painPoints,
        timeline: lead.timeline,
        source: lead.source,
        crmLink: `https://crm.revenue-optimization.com/leads/${lead.id}`,
        nextAction: lead.score >= 80 ? 'Call within 1 hour' : 'Call within 24 hours',
      },
    };

    console.log('Sending assignment notification:', notification);
  }

  /**
   * Update lead stage and trigger stage-specific actions
   */
  async updateLeadStage(leadId: string, newStage: Lead['stage'], notes?: string): Promise<void> {
    const lead = this.leads.get(leadId);
    if (!lead) {
      throw new Error(`Lead ${leadId} not found`);
    }

    const oldStage = lead.stage;
    lead.stage = newStage;
    lead.lastActivity = new Date();
    
    if (notes) {
      lead.notes.push(`${new Date().toISOString()}: Stage changed from ${oldStage} to ${newStage}. ${notes}`);
    }

    // Trigger stage-specific actions
    await this.triggerStageActions(lead, oldStage, newStage);
    
    // Sync with CRM
    await this.syncWithCRM('update', lead);
  }

  /**
   * Trigger actions based on stage transitions
   */
  private async triggerStageActions(lead: Lead, oldStage: string, newStage: string): Promise<void> {
    switch (newStage) {
      case 'qualified':
        await this.scheduleDiscoveryCall(lead);
        break;
      case 'demo':
        await this.sendDemoPreparation(lead);
        break;
      case 'poc':
        await this.initiatePOC(lead);
        break;
      case 'proposal':
        await this.generateProposal(lead);
        break;
      case 'negotiation':
        await this.escalateToSalesManager(lead);
        break;
      case 'closed-won':
        await this.initiateOnboarding(lead);
        break;
      case 'closed-lost':
        await this.addToNurtureSequence(lead);
        break;
    }
  }

  /**
   * Schedule discovery call for qualified leads
   */
  private async scheduleDiscoveryCall(lead: Lead): Promise<void> {
    const activity: SalesActivity = {
      id: `activity-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      leadId: lead.id,
      type: 'call',
      subject: 'Revenue Optimization Discovery Call',
      description: 'Understand current analytics setup, pain points, and revenue optimization goals',
      outcome: '',
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      createdBy: 'system',
    };

    const leadActivities = this.activities.get(lead.id) || [];
    leadActivities.push(activity);
    this.activities.set(lead.id, leadActivities);

    // Send calendar invite
    await this.sendCalendarInvite(lead, activity);
  }

  /**
   * Send demo preparation materials
   */
  private async sendDemoPreparation(lead: Lead): Promise<void> {
    const email = {
      to: lead.email,
      subject: 'Your Revenue Optimization Demo - Preparation Materials',
      template: 'demo-preparation',
      data: {
        companyName: lead.companyName,
        contactName: lead.contactName,
        demoLink: 'https://demo.revenue-optimization.com/interactive',
        preparationGuide: 'https://docs.revenue-optimization.com/demo-prep',
        roiCalculator: 'https://revenue-optimization.com/roi-calculator',
        caseStudies: 'https://revenue-optimization.com/case-studies',
      },
    };

    console.log('Sending demo preparation email:', email);
  }

  /**
   * Initiate POC process
   */
  private async initiatePOC(lead: Lead): Promise<void> {
    // Create POC workflow (would integrate with POC system)
    const pocRequest = {
      leadId: lead.id,
      companyName: lead.companyName,
      contactEmail: lead.email,
      estimatedRevenue: lead.annualRevenue,
      platforms: lead.ecommercePlatform,
      timeline: '2 weeks',
      objectives: [
        'Validate 24,390+ events/sec processing',
        'Demonstrate <11ms query response',
        'Show 15-20% revenue optimization potential',
        'Prove AI accuracy and effectiveness',
      ],
    };

    console.log('Initiating POC:', pocRequest);
  }

  /**
   * Generate proposal based on lead data
   */
  private async generateProposal(lead: Lead): Promise<void> {
    const proposalData = {
      leadId: lead.id,
      companyName: lead.companyName,
      annualRevenue: lead.annualRevenue,
      recommendedTier: this.recommendTier(lead.annualRevenue),
      customizations: this.getCustomizations(lead),
      pricing: this.calculatePricing(lead),
      implementation: this.getImplementationPlan(lead),
      roi: this.calculateROI(lead),
    };

    console.log('Generating proposal:', proposalData);
  }

  /**
   * Recommend pricing tier based on company size
   */
  private recommendTier(annualRevenue?: number): string {
    if (!annualRevenue) return 'growth';
    
    if (annualRevenue >= 50000000) return 'enterprise';
    if (annualRevenue >= 10000000) return 'professional';
    if (annualRevenue >= 1000000) return 'growth';
    return 'starter';
  }

  /**
   * Get customizations based on lead requirements
   */
  private getCustomizations(lead: Lead): string[] {
    const customizations: string[] = [];
    
    if (lead.annualRevenue && lead.annualRevenue >= 50000000) {
      customizations.push('White-label solution');
      customizations.push('Dedicated infrastructure');
      customizations.push('Custom integrations');
    }
    
    if (lead.ecommercePlatform?.includes('Custom')) {
      customizations.push('Custom platform integration');
    }
    
    if (lead.painPoints.some(p => p.includes('compliance'))) {
      customizations.push('Enhanced compliance features');
    }
    
    return customizations;
  }

  /**
   * Calculate pricing based on lead profile
   */
  private calculatePricing(lead: Lead): any {
    const tier = this.recommendTier(lead.annualRevenue);
    const basePricing = {
      starter: { monthly: 99, annual: 990 },
      growth: { monthly: 499, annual: 4990 },
      professional: { monthly: 1499, annual: 14990 },
      enterprise: { monthly: 4999, annual: 49990 },
    };
    
    const pricing = basePricing[tier as keyof typeof basePricing];
    
    // Apply discounts for annual payment
    const discount = 0.2; // 20% annual discount
    
    return {
      tier,
      monthly: pricing.monthly,
      annual: pricing.annual,
      annualDiscount: discount,
      effectiveAnnual: pricing.annual * (1 - discount),
      implementation: Math.min(50000, (lead.annualRevenue || 1000000) * 0.001),
    };
  }

  /**
   * Calculate ROI for proposal
   */
  private calculateROI(lead: Lead): any {
    const annualRevenue = lead.annualRevenue || 10000000;
    const revenueIncrease = 0.175; // 17.5% average
    const pricing = this.calculatePricing(lead);
    
    const annualRevenueIncrease = annualRevenue * revenueIncrease;
    const totalCost = pricing.effectiveAnnual + pricing.implementation;
    const netBenefit = annualRevenueIncrease - totalCost;
    const roi = (netBenefit / totalCost) * 100;
    const paybackDays = (totalCost / (annualRevenueIncrease / 365));
    
    return {
      annualRevenueIncrease,
      totalCost,
      netBenefit,
      roi: Math.round(roi),
      paybackDays: Math.round(paybackDays),
      threeYearValue: netBenefit * 3,
    };
  }

  /**
   * Get implementation plan based on company size
   */
  private getImplementationPlan(lead: Lead): any {
    const tier = this.recommendTier(lead.annualRevenue);
    
    const plans = {
      starter: { duration: '1 week', complexity: 'Simple', support: 'Standard' },
      growth: { duration: '2 weeks', complexity: 'Moderate', support: 'Priority' },
      professional: { duration: '3 weeks', complexity: 'Advanced', support: 'Dedicated CSM' },
      enterprise: { duration: '4 weeks', complexity: 'Custom', support: 'White-glove' },
    };
    
    return plans[tier as keyof typeof plans];
  }

  /**
   * Sync with external CRM system
   */
  private async syncWithCRM(action: 'create' | 'update' | 'delete', lead: Lead): Promise<void> {
    // Integration with Salesforce, HubSpot, Pipedrive, etc.
    const crmData = {
      action,
      leadId: lead.id,
      data: {
        company: lead.companyName,
        contact: lead.contactName,
        email: lead.email,
        phone: lead.phone,
        revenue: lead.annualRevenue,
        score: lead.score,
        stage: lead.stage,
        source: lead.source,
        assignedTo: lead.assignedTo,
        lastActivity: lead.lastActivity,
      },
    };

    console.log('Syncing with CRM:', crmData);
  }

  /**
   * Generate sales metrics and reports
   */
  generateSalesMetrics(period: 'daily' | 'weekly' | 'monthly' | 'quarterly'): SalesMetrics {
    const now = new Date();
    let startDate: Date;
    
    switch (period) {
      case 'daily':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'weekly':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'monthly':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'quarterly':
        const quarter = Math.floor(now.getMonth() / 3);
        startDate = new Date(now.getFullYear(), quarter * 3, 1);
        break;
    }

    const leadsInPeriod = Array.from(this.leads.values()).filter(
      lead => lead.createdAt >= startDate && lead.createdAt <= now
    );

    const metrics = {
      leadsGenerated: leadsInPeriod.length,
      leadsQualified: leadsInPeriod.filter(l => l.score >= 60).length,
      demosScheduled: leadsInPeriod.filter(l => l.stage === 'demo').length,
      demosCompleted: leadsInPeriod.filter(l => ['poc', 'proposal', 'negotiation', 'closed-won'].includes(l.stage)).length,
      pocsStarted: leadsInPeriod.filter(l => l.stage === 'poc').length,
      proposalsSent: leadsInPeriod.filter(l => ['proposal', 'negotiation', 'closed-won'].includes(l.stage)).length,
      dealsWon: leadsInPeriod.filter(l => l.stage === 'closed-won').length,
      dealsLost: leadsInPeriod.filter(l => l.stage === 'closed-lost').length,
      totalRevenue: leadsInPeriod.filter(l => l.stage === 'closed-won').reduce((sum, l) => sum + (l.annualRevenue || 0), 0),
      averageDealSize: 0,
      salesCycleLength: 0,
      conversionRates: {
        leadToQualified: 0,
        qualifiedToDemo: 0,
        demoToPoc: 0,
        pocToProposal: 0,
        proposalToWon: 0,
      },
    };

    // Calculate conversion rates
    if (metrics.leadsGenerated > 0) {
      metrics.conversionRates.leadToQualified = (metrics.leadsQualified / metrics.leadsGenerated) * 100;
    }
    if (metrics.leadsQualified > 0) {
      metrics.conversionRates.qualifiedToDemo = (metrics.demosScheduled / metrics.leadsQualified) * 100;
    }
    if (metrics.demosScheduled > 0) {
      metrics.conversionRates.demoToPoc = (metrics.pocsStarted / metrics.demosScheduled) * 100;
    }
    if (metrics.pocsStarted > 0) {
      metrics.conversionRates.pocToProposal = (metrics.proposalsSent / metrics.pocsStarted) * 100;
    }
    if (metrics.proposalsSent > 0) {
      metrics.conversionRates.proposalToWon = (metrics.dealsWon / metrics.proposalsSent) * 100;
    }

    // Calculate average deal size
    if (metrics.dealsWon > 0) {
      metrics.averageDealSize = metrics.totalRevenue / metrics.dealsWon;
    }

    return {
      period,
      startDate,
      endDate: now,
      metrics,
    };
  }

  /**
   * Send calendar invite for scheduled activities
   */
  private async sendCalendarInvite(lead: Lead, activity: SalesActivity): Promise<void> {
    const invite = {
      to: lead.email,
      subject: activity.subject,
      startTime: activity.scheduledDate,
      duration: 60, // 1 hour
      description: activity.description,
      location: 'https://meet.revenue-optimization.com/discovery',
      attendees: [lead.email, lead.assignedTo || '<EMAIL>'],
    };

    console.log('Sending calendar invite:', invite);
  }

  // Additional helper methods for stage-specific actions
  private async escalateToSalesManager(lead: Lead): Promise<void> {
    console.log(`Escalating lead ${lead.id} to sales manager for negotiation support`);
  }

  private async initiateOnboarding(lead: Lead): Promise<void> {
    console.log(`Initiating onboarding for won deal: ${lead.id}`);
  }

  private async addToNurtureSequence(lead: Lead): Promise<void> {
    console.log(`Adding lost lead ${lead.id} to nurture sequence for future opportunities`);
  }
}

// Export the sales enablement system
export { SalesEnablementSystem, type Lead, type SalesActivity, type SalesMetrics };
