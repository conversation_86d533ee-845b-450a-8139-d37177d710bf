#!/usr/bin/env deno run --allow-read --allow-write

// Comprehensive script to fix all route handlers with proper getServices() pattern

const filePath = "src/routes/enhancedSubscriptions.ts";

// Read the file
const content = await Deno.readTextFile(filePath);
let lines = content.split('\n');

// Service names that need to be replaced
const serviceNames = [
  'enhancedSubscriptionService',
  'unifiedRevenueService', 
  'subscriptionLifecycleService',
  'usageBasedBillingService',
  'revenueIntelligenceService',
  'demoEnvironmentService',
  'salesMaterialsService',
  'trialEnvironmentService'
];

console.log("🔧 Starting comprehensive route handler fixes...");

// Step 1: Remove duplicate getServices() calls
console.log("📝 Step 1: Removing duplicate getServices() calls...");
for (let i = 0; i < lines.length; i++) {
  const line = lines[i];
  if (line.trim() === 'const services = getServices();') {
    // Check if there's another getServices() call nearby
    for (let j = i + 1; j < Math.min(i + 10, lines.length); j++) {
      if (lines[j].trim() === 'const services = getServices();') {
        // Remove the duplicate
        lines.splice(j, 1);
        console.log(`   ✅ Removed duplicate at line ${j + 1}`);
        break;
      }
    }
  }
}

// Step 2: Find all route handlers and ensure they have getServices()
console.log("📝 Step 2: Ensuring all route handlers have getServices()...");
const routePatterns = [
  /\.post\s*\(/,
  /\.get\s*\(/,
  /\.put\s*\(/,
  /\.delete\s*\(/,
  /\.patch\s*\(/
];

for (let i = 0; i < lines.length; i++) {
  const line = lines[i];
  
  // Check if this line contains a route definition
  if (routePatterns.some(pattern => pattern.test(line))) {
    console.log(`   🔍 Found route at line ${i + 1}: ${line.trim().substring(0, 50)}...`);
    
    // Find the async handler function
    let handlerStart = -1;
    for (let j = i; j < Math.min(i + 5, lines.length); j++) {
      if (lines[j].includes('async (ctx)')) {
        handlerStart = j;
        break;
      }
    }
    
    if (handlerStart === -1) continue;
    
    // Find the try block
    let tryBlockStart = -1;
    for (let j = handlerStart; j < Math.min(handlerStart + 5, lines.length); j++) {
      if (lines[j].trim().startsWith('try {')) {
        tryBlockStart = j;
        break;
      }
    }
    
    if (tryBlockStart === -1) continue;
    
    // Check if getServices() is already called in this handler
    let hasGetServices = false;
    let handlerEnd = tryBlockStart;
    let braceCount = 0;
    
    // Find the end of the handler
    for (let j = tryBlockStart; j < lines.length; j++) {
      const currentLine = lines[j];
      for (const char of currentLine) {
        if (char === '{') braceCount++;
        else if (char === '}') braceCount--;
      }
      
      if (currentLine.includes('getServices()')) {
        hasGetServices = true;
      }
      
      if (braceCount === 0 && j > tryBlockStart) {
        handlerEnd = j;
        break;
      }
    }
    
    // If no getServices() call, add it
    if (!hasGetServices) {
      const indent = lines[tryBlockStart].match(/^(\s*)/)?.[1] || '  ';
      const servicesLine = `${indent}  const services = getServices();`;
      lines.splice(tryBlockStart + 1, 0, servicesLine);
      console.log(`   ✅ Added getServices() to handler at line ${tryBlockStart + 2}`);
      
      // Update handlerEnd since we inserted a line
      handlerEnd++;
    }
    
    // Step 3: Replace all direct service calls with services.serviceName
    console.log(`   🔄 Updating service calls in handler (lines ${tryBlockStart + 1}-${handlerEnd})...`);
    for (let j = tryBlockStart + 1; j < handlerEnd; j++) {
      let currentLine = lines[j];
      let updated = false;
      
      for (const serviceName of serviceNames) {
        // Match patterns like "await serviceName." or "serviceName." but not "services.serviceName."
        const directCallPattern = new RegExp(`\\b${serviceName}\\.`, 'g');
        const servicesCallPattern = new RegExp(`services\\.${serviceName}\\.`, 'g');
        
        if (directCallPattern.test(currentLine) && !servicesCallPattern.test(currentLine)) {
          currentLine = currentLine.replace(directCallPattern, `services.${serviceName}.`);
          updated = true;
        }
      }
      
      if (updated) {
        lines[j] = currentLine;
        console.log(`   ✅ Updated service calls at line ${j + 1}`);
      }
    }
  }
}

// Step 4: Write the updated content
console.log("💾 Writing updated content...");
const updatedContent = lines.join('\n');
await Deno.writeTextFile(filePath, updatedContent);

console.log("✅ Route handler fixes completed!");
console.log("📊 Summary:");
console.log(`   - Processed ${lines.length} lines`);
console.log(`   - Fixed service calls for ${serviceNames.length} services`);
console.log(`   - Ensured proper getServices() initialization`);
