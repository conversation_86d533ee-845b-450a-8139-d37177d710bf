import { Application, Router } from "@oak/oak";
import { Pool } from "postgres";
import { connect, Redis } from "redis";

console.log("💰 Starting Billing Service...");

// Configuration from environment variables
const config = {
  port: parseInt(Deno.env.get("PORT") || "3003"),
  host: Deno.env.get("HOST") || "0.0.0.0",
  nodeEnv: Deno.env.get("NODE_ENV") || "development",
  database: {
    hostname: Deno.env.get("DB_HOST") || "localhost",
    port: parseInt(Deno.env.get("DB_PORT") || "5432"),
    database: Deno.env.get("DB_NAME") || "ecommerce_analytics",
    user: Deno.env.get("DB_USER") || "postgres",
    password: Deno.env.get("DB_PASSWORD") || "password",
  },
  redis: {
    hostname: Deno.env.get("REDIS_HOST") || "localhost",
    port: parseInt(Deno.env.get("REDIS_PORT") || "6379"),
  },
  stripe: {
    secretKey: Deno.env.get("STRIPE_SECRET_KEY") || "sk_test_dummy",
    webhookSecret: Deno.env.get("STRIPE_WEBHOOK_SECRET") || "whsec_dummy",
  },
  jwt: {
    secret: Deno.env.get("JWT_SECRET") || "development-secret",
  },
};

console.log("💰 Configuration loaded:", {
  port: config.port,
  host: config.host,
  nodeEnv: config.nodeEnv,
  database: { ...config.database, password: "***" },
  redis: config.redis,
  stripe: { secretKey: config.stripe.secretKey.substring(0, 10) + "...", webhookSecret: "***" },
});

// Initialize database pool
console.log("🗄️  Initializing database connection...");
const pool = new Pool(config.database, 10);

// Test database connection
try {
  const client = await pool.connect();
  await client.queryObject("SELECT 1");
  client.release();
  console.log("✅ Database connection successful");
} catch (error) {
  console.error("❌ Database connection failed:", error);
  Deno.exit(1);
}

// Initialize Redis connection
console.log("🔴 Initializing Redis connection...");
let redisClient: Redis;
try {
  redisClient = await connect(config.redis);
  await redisClient.ping();
  console.log("✅ Redis connection successful");
} catch (error) {
  console.error("❌ Redis connection failed:", error);
  Deno.exit(1);
}

// Create Oak application
const app = new Application();

// Global error handler
app.addEventListener("error", (evt) => {
  console.error("🚨 Unhandled application error:", evt.error?.message);
});

// Basic middleware for CORS
app.use(async (ctx, next) => {
  ctx.response.headers.set("Access-Control-Allow-Origin", "*");
  ctx.response.headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  ctx.response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
  
  if (ctx.request.method === "OPTIONS") {
    ctx.response.status = 200;
    return;
  }
  
  await next();
});

// Request logging middleware
app.use(async (ctx, next) => {
  const start = Date.now();
  await next();
  const ms = Date.now() - start;
  console.log(`${ctx.request.method} ${ctx.request.url.pathname} - ${ctx.response.status} - ${ms}ms`);
});

// Main router
const router = new Router();

// Health check endpoints
router.get("/health", (ctx) => {
  ctx.response.body = {
    success: true,
    service: "billing-service",
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
    environment: config.nodeEnv,
  };
});

router.get("/ready", async (ctx) => {
  try {
    // Test database
    const client = await pool.connect();
    await client.queryObject("SELECT 1");
    client.release();
    
    // Test Redis
    await redisClient.ping();
    
    ctx.response.body = {
      success: true,
      service: "billing-service",
      status: "ready",
      checks: {
        database: "healthy",
        redis: "healthy",
        stripe: config.nodeEnv === "development" ? "mocked" : "configured",
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    ctx.response.status = 503;
    ctx.response.body = {
      success: false,
      service: "billing-service",
      status: "not ready",
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
});

// API router
const apiRouter = new Router({ prefix: "/api" });

// Billing endpoints
apiRouter.get("/billing/plans", (ctx) => {
  ctx.response.body = {
    success: true,
    data: [
      {
        id: "basic",
        name: "Basic Plan",
        price: 29,
        currency: "USD",
        interval: "month",
        features: ["Up to 10,000 events/month", "Basic analytics", "Email support"],
      },
      {
        id: "pro",
        name: "Pro Plan", 
        price: 99,
        currency: "USD",
        interval: "month",
        features: ["Up to 100,000 events/month", "Advanced analytics", "Priority support", "Custom integrations"],
      },
      {
        id: "enterprise",
        name: "Enterprise Plan",
        price: 299,
        currency: "USD", 
        interval: "month",
        features: ["Unlimited events", "Full analytics suite", "24/7 support", "Custom development"],
      },
    ],
    timestamp: new Date().toISOString(),
  };
});

apiRouter.get("/billing/subscription", async (ctx) => {
  try {
    const tenantId = ctx.request.url.searchParams.get("tenantId");
    if (!tenantId) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "tenantId parameter is required",
      };
      return;
    }

    // Mock subscription data for development
    ctx.response.body = {
      success: true,
      data: {
        id: "sub_development_mock",
        tenantId: tenantId,
        planId: "pro",
        status: "active",
        currentPeriodStart: new Date().toISOString(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        cancelAtPeriodEnd: false,
        trialEnd: null,
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Subscription query error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to fetch subscription",
      timestamp: new Date().toISOString(),
    };
  }
});

// Usage tracking endpoint
apiRouter.get("/billing/usage", async (ctx) => {
  try {
    const tenantId = ctx.request.url.searchParams.get("tenantId");
    if (!tenantId) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "tenantId parameter is required",
      };
      return;
    }

    const client = await pool.connect();
    
    // Get current month usage
    const usageQuery = `
      SELECT 
        COUNT(*)::int as total_events,
        COUNT(*) FILTER (WHERE timestamp >= DATE_TRUNC('month', CURRENT_DATE))::int as current_month_events
      FROM customer_events 
      WHERE tenant_id = $1
    `;
    
    const usage = await client.queryObject(usageQuery, [tenantId]);
    client.release();
    
    ctx.response.body = {
      success: true,
      data: {
        tenantId: tenantId,
        currentMonth: usage.rows[0],
        limits: {
          events: 100000, // Pro plan limit
          apiCalls: 1000000,
        },
        billingPeriod: {
          start: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString(),
          end: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).toISOString(),
        },
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Usage query error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to fetch usage",
      timestamp: new Date().toISOString(),
    };
  }
});

// Import sophisticated endpoints
import { sophisticatedRouter } from "./create_working_sophisticated_service.ts";

// Register routes
app.use(router.routes());
app.use(router.allowedMethods());
app.use(apiRouter.routes());
app.use(apiRouter.allowedMethods());

// Register sophisticated endpoints
app.use(sophisticatedRouter.routes());
app.use(sophisticatedRouter.allowedMethods());

// 404 handler
app.use((ctx) => {
  ctx.response.status = 404;
  ctx.response.body = {
    success: false,
    error: "Endpoint not found",
    timestamp: new Date().toISOString(),
  };
});

// Graceful shutdown
async function gracefulShutdown(signal: string): Promise<void> {
  console.log(`\n🛑 Received ${signal}, starting graceful shutdown...`);
  
  try {
    await pool.end();
    redisClient.close();
    console.log("✅ Graceful shutdown completed");
    Deno.exit(0);
  } catch (error) {
    console.error("❌ Error during graceful shutdown:", error);
    Deno.exit(1);
  }
}

// Setup signal handlers
Deno.addSignalListener("SIGINT", () => gracefulShutdown("SIGINT"));
Deno.addSignalListener("SIGTERM", () => gracefulShutdown("SIGTERM"));

// Start server
console.log(`💰 Starting server on ${config.host}:${config.port}...`);

await app.listen({ 
  hostname: config.host, 
  port: config.port 
});
