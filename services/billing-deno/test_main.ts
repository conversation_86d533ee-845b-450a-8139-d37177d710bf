#!/usr/bin/env deno run --allow-net --allow-env --allow-read --allow-write

// Test wrapper for main.ts to catch any errors

try {
  console.log("🚀 Starting billing service test...");
  
  // Import and run the main module
  const mainModule = await import("./src/main.ts");
  
  console.log("✅ Main module imported successfully");
  
  // Keep the process alive
  setInterval(() => {
    console.log("💓 Service heartbeat - still running...");
  }, 5000);
  
} catch (error) {
  console.error("❌ Error starting billing service:");
  console.error(error);
  console.error("Stack trace:", error.stack);
  Deno.exit(1);
}

// Handle unhandled promise rejections
globalThis.addEventListener("unhandledrejection", (event) => {
  console.error("❌ Unhandled promise rejection:");
  console.error(event.reason);
  console.error("Stack trace:", event.reason?.stack);
  event.preventDefault();
});

// Handle uncaught exceptions
globalThis.addEventListener("error", (event) => {
  console.error("❌ Uncaught exception:");
  console.error(event.error);
  console.error("Stack trace:", event.error?.stack);
  event.preventDefault();
});
