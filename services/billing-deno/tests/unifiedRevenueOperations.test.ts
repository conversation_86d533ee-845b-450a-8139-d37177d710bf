// Unified Revenue Operations Integration Tests
// Comprehensive test suite for the unified revenue operations platform

import { assertEquals, assertExists, assert } from "https://deno.land/std@0.208.0/assert/mod.ts";
import { describe, it, beforeEach, afterEach } from "https://deno.land/std@0.208.0/testing/bdd.ts";
import { UnifiedRevenueOperationsService } from "../src/services/unifiedRevenueOperationsService.ts";
import { AutomatedWorkflowService } from "../src/services/automatedWorkflowService.ts";
import { RealtimeMonitoringService } from "../src/services/realtimeMonitoringService.ts";

// Test configuration
const TEST_TENANT_ID = "test_tenant_unified_123";
const TEST_SUBSCRIPTION_ID = "test_subscription_unified_456";
const TEST_CUSTOMER_ID = "test_customer_unified_789";

describe("Unified Revenue Operations Integration Tests", () => {
  let unifiedService: UnifiedRevenueOperationsService;
  let workflowService: AutomatedWorkflowService;
  let monitoringService: RealtimeMonitoringService;

  beforeEach(() => {
    unifiedService = new UnifiedRevenueOperationsService();
    workflowService = new AutomatedWorkflowService();
    monitoringService = new RealtimeMonitoringService();
  });

  afterEach(() => {
    // Cleanup monitoring
    monitoringService.stopMonitoring();
  });

  describe("Unified Revenue Operations Service", () => {
    it("should get comprehensive unified subscription data", async () => {
      const unifiedData = await unifiedService.getUnifiedSubscriptionData(
        TEST_TENANT_ID,
        TEST_SUBSCRIPTION_ID
      );

      // Validate unified data structure
      assertExists(unifiedData.subscription);
      assertExists(unifiedData.usageAnalytics);
      assertExists(unifiedData.churnPrediction);
      assertExists(unifiedData.expansionOpportunities);
      assertExists(unifiedData.revenueIntelligence);
      assertExists(unifiedData.financialMetrics);
      assertExists(unifiedData.healthScore);
      assertExists(unifiedData.riskLevel);

      // Validate data types
      assertEquals(typeof unifiedData.healthScore, "number");
      assert(unifiedData.healthScore >= 0 && unifiedData.healthScore <= 100);
      assert(['low', 'medium', 'high', 'critical'].includes(unifiedData.riskLevel));
      assertEquals(Array.isArray(unifiedData.expansionOpportunities), true);

      console.log(`Unified subscription data retrieved with health score: ${unifiedData.healthScore}`);
    });

    it("should generate comprehensive revenue operations dashboard", async () => {
      const dashboard = await unifiedService.generateRevenueOperationsDashboard(TEST_TENANT_ID);

      // Validate dashboard structure
      assertExists(dashboard.summary);
      assertExists(dashboard.subscriptions);
      assertExists(dashboard.insights);
      assertExists(dashboard.trends);

      // Validate summary metrics
      assertEquals(typeof dashboard.summary.totalSubscriptions, "number");
      assertEquals(typeof dashboard.summary.totalMRR, "number");
      assertEquals(typeof dashboard.summary.totalARR, "number");
      assertEquals(typeof dashboard.summary.averageHealthScore, "number");
      assertEquals(typeof dashboard.summary.churnRate, "number");
      assertEquals(typeof dashboard.summary.expansionRate, "number");

      // Validate subscriptions array
      assertEquals(Array.isArray(dashboard.subscriptions), true);
      if (dashboard.subscriptions.length > 0) {
        const subscription = dashboard.subscriptions[0];
        assertExists(subscription.subscription);
        assertExists(subscription.healthScore);
        assertExists(subscription.riskLevel);
      }

      // Validate insights
      assertExists(dashboard.insights.topExpansionOpportunities);
      assertExists(dashboard.insights.criticalRisks);
      assertEquals(Array.isArray(dashboard.insights.topExpansionOpportunities), true);
      assertEquals(Array.isArray(dashboard.insights.criticalRisks), true);

      // Validate trends
      assertExists(dashboard.trends.mrrGrowth);
      assertExists(dashboard.trends.churnTrend);
      assertExists(dashboard.trends.expansionTrend);
      assertExists(dashboard.trends.healthScoreTrend);
      assertEquals(Array.isArray(dashboard.trends.mrrGrowth), true);

      console.log(`Dashboard generated with ${dashboard.summary.totalSubscriptions} subscriptions, MRR: $${dashboard.summary.totalMRR}`);
    });

    it("should process subscription events and trigger workflows", async () => {
      const eventType = "subscription_created";
      const eventData = {
        subscriptionId: TEST_SUBSCRIPTION_ID,
        customerId: TEST_CUSTOMER_ID,
        planId: "professional",
        amount: 1499,
      };

      // Process subscription event
      await unifiedService.processSubscriptionEvent(
        TEST_TENANT_ID,
        TEST_SUBSCRIPTION_ID,
        eventType,
        eventData
      );

      // Verify event was processed (no exceptions thrown)
      assert(true, "Subscription event processed successfully");

      console.log(`Subscription event '${eventType}' processed successfully`);
    });

    it("should monitor subscription health and generate alerts", async () => {
      const alerts = await unifiedService.monitorSubscriptionHealth(TEST_TENANT_ID);

      // Validate alerts structure
      assertEquals(Array.isArray(alerts), true);

      for (const alert of alerts) {
        assertExists(alert.id);
        assertExists(alert.type);
        assertExists(alert.severity);
        assertExists(alert.subscriptionId);
        assertExists(alert.customerId);
        assertExists(alert.message);
        assertExists(alert.data);
        assertExists(alert.createdAt);
        assertEquals(typeof alert.acknowledged, "boolean");

        // Validate alert types
        assert(['churn_risk', 'expansion_opportunity', 'payment_failure', 'usage_spike', 'revenue_milestone'].includes(alert.type));
        assert(['low', 'medium', 'high', 'critical'].includes(alert.severity));
      }

      console.log(`Health monitoring generated ${alerts.length} alerts`);
    });

    it("should optimize subscription pricing with comprehensive analysis", async () => {
      const optimization = await unifiedService.optimizeSubscriptionPricing(
        TEST_TENANT_ID,
        TEST_SUBSCRIPTION_ID
      );

      // Validate optimization structure
      assertExists(optimization.currentPricing);
      assertExists(optimization.optimizedPricing);
      assertExists(optimization.expectedImpact);
      assertExists(optimization.recommendations);

      // Validate current pricing
      assertExists(optimization.currentPricing.amount);
      assertEquals(typeof optimization.currentPricing.amount, "number");

      // Validate optimized pricing
      assertExists(optimization.optimizedPricing.recommendedPrice);
      assertEquals(typeof optimization.optimizedPricing.recommendedPrice, "number");

      // Validate expected impact
      assertExists(optimization.expectedImpact.revenueChange);
      assertExists(optimization.expectedImpact.churnRiskChange);
      assertExists(optimization.expectedImpact.expansionProbabilityChange);
      assertEquals(typeof optimization.expectedImpact.revenueChange, "number");

      // Validate recommendations
      assertEquals(Array.isArray(optimization.recommendations), true);

      console.log(`Pricing optimization: Current $${optimization.currentPricing.amount} → Optimized $${optimization.optimizedPricing.recommendedPrice}`);
    });
  });

  describe("Automated Workflow Service", () => {
    it("should create and execute automated workflows", async () => {
      // Create workflow
      const workflow = await workflowService.createWorkflow(TEST_TENANT_ID, {
        name: "Churn Risk Alert",
        description: "Alert when subscription has high churn risk",
        trigger: {
          type: 'churn_risk',
          conditions: {
            riskLevel: 'high',
          },
        },
        actions: [
          {
            type: 'email_notification',
            config: {
              recipients: ['<EMAIL>'],
              template: 'churn_risk_alert',
              priority: 'high',
            },
          },
          {
            type: 'customer_success_task',
            config: {
              taskType: 'retention_outreach',
              priority: 'high',
            },
          },
        ],
      });

      // Validate workflow creation
      assertExists(workflow.id);
      assertEquals(workflow.tenantId, TEST_TENANT_ID);
      assertEquals(workflow.name, "Churn Risk Alert");
      assertEquals(workflow.trigger.type, 'churn_risk');
      assertEquals(workflow.actions.length, 2);
      assertEquals(workflow.isActive, true);

      // Execute workflow
      const executions = await workflowService.evaluateAndExecuteWorkflows(
        TEST_TENANT_ID,
        'churn_risk_detected',
        {
          subscriptionId: TEST_SUBSCRIPTION_ID,
          customerId: TEST_CUSTOMER_ID,
          riskLevel: 'high',
          churnProbability: 0.8,
        }
      );

      // Validate execution
      assertEquals(Array.isArray(executions), true);

      console.log(`Workflow created and executed: ${workflow.id}`);
    });

    it("should get workflow metrics and performance data", async () => {
      const metrics = await workflowService.getWorkflowMetrics(TEST_TENANT_ID);

      // Validate metrics structure
      assertExists(metrics.totalWorkflows);
      assertExists(metrics.activeWorkflows);
      assertExists(metrics.totalExecutions);
      assertExists(metrics.successfulExecutions);
      assertExists(metrics.failedExecutions);
      assertExists(metrics.averageExecutionTime);
      assertExists(metrics.executionsByTrigger);
      assertExists(metrics.executionsByAction);
      assertExists(metrics.recentExecutions);

      // Validate data types
      assertEquals(typeof metrics.totalWorkflows, "number");
      assertEquals(typeof metrics.activeWorkflows, "number");
      assertEquals(typeof metrics.totalExecutions, "number");
      assertEquals(typeof metrics.averageExecutionTime, "number");
      assertEquals(Array.isArray(metrics.recentExecutions), true);

      console.log(`Workflow metrics: ${metrics.totalWorkflows} workflows, ${metrics.totalExecutions} executions`);
    });
  });

  describe("Real-time Monitoring Service", () => {
    it("should create monitoring rules and record metrics", async () => {
      // Create monitoring rule
      const rule = await monitoringService.createMonitoringRule(TEST_TENANT_ID, {
        name: "High MRR Growth",
        type: 'threshold',
        metric: 'mrr',
        conditions: {
          operator: '>',
          value: 100000,
        },
        severity: 'medium',
        alertChannels: [
          {
            type: 'email',
            config: {
              recipients: ['<EMAIL>'],
            },
          },
        ],
        suppressionWindow: 60,
      });

      // Validate rule creation
      assertExists(rule.id);
      assertEquals(rule.tenantId, TEST_TENANT_ID);
      assertEquals(rule.name, "High MRR Growth");
      assertEquals(rule.type, 'threshold');
      assertEquals(rule.metric, 'mrr');
      assertEquals(rule.severity, 'medium');
      assertEquals(rule.isActive, true);

      // Record metric
      const metric = await monitoringService.recordMetric(TEST_TENANT_ID, {
        subscriptionId: TEST_SUBSCRIPTION_ID,
        customerId: TEST_CUSTOMER_ID,
        metricName: 'mrr',
        metricValue: 125000,
        metricUnit: 'usd',
        metadata: { source: 'test' },
      });

      // Validate metric recording
      assertExists(metric.id);
      assertEquals(metric.tenantId, TEST_TENANT_ID);
      assertEquals(metric.metricName, 'mrr');
      assertEquals(metric.metricValue, 125000);
      assertEquals(metric.metricUnit, 'usd');

      console.log(`Monitoring rule created: ${rule.id}, metric recorded: ${metric.metricValue} ${metric.metricUnit}`);
    });

    it("should generate monitoring dashboard with health metrics", async () => {
      const dashboard = await monitoringService.getMonitoringDashboard(TEST_TENANT_ID);

      // Validate dashboard structure
      assertExists(dashboard.summary);
      assertExists(dashboard.recentAlerts);
      assertExists(dashboard.metricTrends);
      assertExists(dashboard.systemHealth);

      // Validate summary
      assertEquals(typeof dashboard.summary.totalRules, "number");
      assertEquals(typeof dashboard.summary.activeRules, "number");
      assertEquals(typeof dashboard.summary.totalAlerts, "number");
      assertEquals(typeof dashboard.summary.activeAlerts, "number");
      assertEquals(typeof dashboard.summary.criticalAlerts, "number");
      assertEquals(typeof dashboard.summary.averageResolutionTime, "number");

      // Validate recent alerts
      assertEquals(Array.isArray(dashboard.recentAlerts), true);

      // Validate metric trends
      assertEquals(Array.isArray(dashboard.metricTrends), true);
      for (const trend of dashboard.metricTrends) {
        assertExists(trend.metricName);
        assertExists(trend.currentValue);
        assertExists(trend.previousValue);
        assertExists(trend.changePercentage);
        assertExists(trend.trend);
        assert(['up', 'down', 'stable'].includes(trend.trend));
      }

      // Validate system health
      assertEquals(typeof dashboard.systemHealth.subscriptionHealth, "number");
      assertEquals(typeof dashboard.systemHealth.revenueHealth, "number");
      assertEquals(typeof dashboard.systemHealth.customerHealth, "number");
      assertEquals(typeof dashboard.systemHealth.overallHealth, "number");

      console.log(`Monitoring dashboard: ${dashboard.summary.totalAlerts} alerts, ${dashboard.systemHealth.overallHealth}% health`);
    });

    it("should start and stop real-time monitoring", async () => {
      // Start monitoring
      monitoringService.startMonitoring();
      
      // Wait a short time to ensure monitoring is running
      await new Promise(resolve => setTimeout(resolve, 100));

      // Stop monitoring
      monitoringService.stopMonitoring();

      // Verify no exceptions were thrown
      assert(true, "Real-time monitoring started and stopped successfully");

      console.log("Real-time monitoring lifecycle tested successfully");
    });
  });

  describe("Integration Workflows", () => {
    it("should handle complete revenue operations workflow", async () => {
      // 1. Get unified subscription data
      const unifiedData = await unifiedService.getUnifiedSubscriptionData(
        TEST_TENANT_ID,
        TEST_SUBSCRIPTION_ID
      );

      // 2. Process subscription event
      await unifiedService.processSubscriptionEvent(
        TEST_TENANT_ID,
        TEST_SUBSCRIPTION_ID,
        'usage_spike_detected',
        {
          subscriptionId: TEST_SUBSCRIPTION_ID,
          customerId: TEST_CUSTOMER_ID,
          usageValue: 150000,
          threshold: 100000,
        }
      );

      // 3. Monitor subscription health
      const alerts = await unifiedService.monitorSubscriptionHealth(TEST_TENANT_ID);

      // 4. Optimize pricing if needed
      const optimization = await unifiedService.optimizeSubscriptionPricing(
        TEST_TENANT_ID,
        TEST_SUBSCRIPTION_ID
      );

      // 5. Generate dashboard
      const dashboard = await unifiedService.generateRevenueOperationsDashboard(TEST_TENANT_ID);

      // Validate end-to-end workflow
      assertExists(unifiedData.healthScore);
      assertEquals(Array.isArray(alerts), true);
      assertExists(optimization.expectedImpact);
      assertExists(dashboard.summary);

      console.log("Complete revenue operations workflow executed successfully");
    });

    it("should handle high-volume operations efficiently", async () => {
      const startTime = performance.now();
      const operations = [];

      // Simulate multiple concurrent operations
      for (let i = 0; i < 10; i++) {
        operations.push(
          unifiedService.getUnifiedSubscriptionData(TEST_TENANT_ID, `sub_${i}`)
        );
      }

      const results = await Promise.all(operations);
      const endTime = performance.now();
      const duration = endTime - startTime;

      // Validate results
      assertEquals(results.length, 10);
      for (const result of results) {
        assertExists(result.healthScore);
        assertExists(result.riskLevel);
      }

      // Performance validation
      console.log(`Processed 10 unified operations in ${duration.toFixed(2)}ms`);
      assert(duration < 5000, "High-volume operations should complete within 5 seconds");
    });
  });

  describe("Performance Tests", () => {
    it("should meet unified data retrieval performance targets", async () => {
      const iterations = 20;
      const startTime = performance.now();

      for (let i = 0; i < iterations; i++) {
        await unifiedService.getUnifiedSubscriptionData(TEST_TENANT_ID, `sub_${i}`);
      }

      const endTime = performance.now();
      const duration = endTime - startTime;
      const avgTime = duration / iterations;

      console.log(`Average unified data retrieval time: ${avgTime.toFixed(2)}ms`);
      
      // Target: <500ms per unified data retrieval
      assert(avgTime < 500, `Average time ${avgTime.toFixed(2)}ms should be under 500ms`);
    });

    it("should meet dashboard generation performance targets", async () => {
      const startTime = performance.now();

      const dashboard = await unifiedService.generateRevenueOperationsDashboard(TEST_TENANT_ID);

      const endTime = performance.now();
      const duration = endTime - startTime;

      console.log(`Dashboard generation time: ${duration.toFixed(2)}ms`);
      
      // Target: <2000ms for dashboard generation
      assert(duration < 2000, `Dashboard generation ${duration.toFixed(2)}ms should be under 2000ms`);
      assertExists(dashboard.summary);
    });

    it("should handle concurrent monitoring operations", async () => {
      const startTime = performance.now();
      const promises = [];

      // Record multiple metrics concurrently
      for (let i = 0; i < 50; i++) {
        promises.push(
          monitoringService.recordMetric(TEST_TENANT_ID, {
            metricName: `test_metric_${i}`,
            metricValue: Math.random() * 1000,
            metricUnit: 'count',
          })
        );
      }

      const results = await Promise.all(promises);
      const endTime = performance.now();
      const duration = endTime - startTime;

      console.log(`Recorded 50 metrics concurrently in ${duration.toFixed(2)}ms`);
      
      // Validate results
      assertEquals(results.length, 50);
      
      // Target: <3000ms for 50 concurrent metric recordings
      assert(duration < 3000, `Concurrent operations ${duration.toFixed(2)}ms should be under 3000ms`);
    });
  });
});

// Performance benchmark tests
describe("Unified Revenue Operations Performance Benchmarks", () => {
  it("should meet subscription health monitoring targets", async () => {
    const service = new UnifiedRevenueOperationsService();
    const startTime = performance.now();

    const alerts = await service.monitorSubscriptionHealth(TEST_TENANT_ID);

    const endTime = performance.now();
    const duration = endTime - startTime;

    console.log(`Subscription health monitoring time: ${duration.toFixed(2)}ms`);
    
    // Target: <1000ms for health monitoring
    assert(duration < 1000, `Health monitoring ${duration.toFixed(2)}ms should be under 1000ms`);
    assertEquals(Array.isArray(alerts), true);
  });

  it("should meet workflow execution performance targets", async () => {
    const service = new AutomatedWorkflowService();
    const startTime = performance.now();

    const executions = await service.evaluateAndExecuteWorkflows(
      TEST_TENANT_ID,
      'test_event',
      {
        subscriptionId: TEST_SUBSCRIPTION_ID,
        customerId: TEST_CUSTOMER_ID,
        testData: 'performance_test',
      }
    );

    const endTime = performance.now();
    const duration = endTime - startTime;

    console.log(`Workflow evaluation and execution time: ${duration.toFixed(2)}ms`);
    
    // Target: <500ms for workflow evaluation and execution
    assert(duration < 500, `Workflow execution ${duration.toFixed(2)}ms should be under 500ms`);
    assertEquals(Array.isArray(executions), true);
  });
});

// Run tests with: deno test --allow-net --allow-env --allow-read services/billing-deno/tests/unifiedRevenueOperations.test.ts
