// Revenue Operations Integration Tests
// Comprehensive test suite for subscription lifecycle, usage billing, revenue recognition, and financial reporting

import { assertEquals, assertExists } from "https://deno.land/std@0.208.0/assert/mod.ts";
import { describe, it, beforeEach, afterEach } from "https://deno.land/std@0.208.0/testing/bdd.ts";
import { SubscriptionLifecycleService } from "../src/services/subscriptionLifecycleService.ts";
import { UsageBasedBillingService } from "../src/services/usageBasedBillingService.ts";
import { RevenueRecognitionService } from "../src/services/revenueRecognitionService.ts";
import { FinancialReportingService } from "../src/services/financialReportingService.ts";
import { RevenueIntelligenceService } from "../src/services/revenueIntelligenceService.ts";

// Test configuration
const TEST_TENANT_ID = "test_tenant_123";
const TEST_CUSTOMER_ID = "test_customer_456";
const TEST_PLAN_ID = "professional";

describe("Revenue Operations Integration Tests", () => {
  let subscriptionService: SubscriptionLifecycleService;
  let usageService: UsageBasedBillingService;
  let revenueService: RevenueRecognitionService;
  let reportingService: FinancialReportingService;
  let intelligenceService: RevenueIntelligenceService;

  beforeEach(() => {
    subscriptionService = new SubscriptionLifecycleService();
    usageService = new UsageBasedBillingService();
    revenueService = new RevenueRecognitionService();
    reportingService = new FinancialReportingService();
    intelligenceService = new RevenueIntelligenceService();
  });

  describe("Subscription Lifecycle Management", () => {
    it("should create subscription successfully", async () => {
      const subscription = await subscriptionService.createSubscription(
        TEST_TENANT_ID,
        TEST_CUSTOMER_ID,
        TEST_PLAN_ID,
        {
          trialDays: 14,
          metadata: { source: "test" },
        }
      );

      assertExists(subscription.id);
      assertEquals(subscription.tenantId, TEST_TENANT_ID);
      assertEquals(subscription.customerId, TEST_CUSTOMER_ID);
      assertEquals(subscription.planId, TEST_PLAN_ID);
      assertEquals(subscription.status, "trialing");
      assertExists(subscription.trialEnd);
    });

    it("should update subscription status", async () => {
      // Create subscription first
      const subscription = await subscriptionService.createSubscription(
        TEST_TENANT_ID,
        TEST_CUSTOMER_ID,
        TEST_PLAN_ID
      );

      // Update status
      const updatedSubscription = await subscriptionService.updateSubscriptionStatus(
        TEST_TENANT_ID,
        subscription.id,
        "active",
        { activatedAt: new Date().toISOString() }
      );

      assertEquals(updatedSubscription.status, "active");
      assertExists(updatedSubscription.metadata.activatedAt);
    });

    it("should process subscription renewal", async () => {
      // Create active subscription
      const subscription = await subscriptionService.createSubscription(
        TEST_TENANT_ID,
        TEST_CUSTOMER_ID,
        TEST_PLAN_ID
      );

      // Process renewal
      const renewalResult = await subscriptionService.processRenewal(
        TEST_TENANT_ID,
        subscription.id
      );

      assertEquals(renewalResult.success, true);
      assertExists(renewalResult.newPeriodEnd);
    });

    it("should change subscription plan with proration", async () => {
      // Create subscription
      const subscription = await subscriptionService.createSubscription(
        TEST_TENANT_ID,
        TEST_CUSTOMER_ID,
        "growth"
      );

      // Change to professional plan
      const changeResult = await subscriptionService.changeSubscriptionPlan(
        TEST_TENANT_ID,
        subscription.id,
        "professional",
        { prorationBehavior: "create_prorations" }
      );

      assertEquals(changeResult.success, true);
      assertExists(changeResult.proratedAmount);
    });

    it("should predict churn risk accurately", async () => {
      // Create subscription
      const subscription = await subscriptionService.createSubscription(
        TEST_TENANT_ID,
        TEST_CUSTOMER_ID,
        TEST_PLAN_ID
      );

      // Get churn prediction
      const prediction = await subscriptionService.predictChurnRisk(
        TEST_TENANT_ID,
        subscription.id
      );

      assertExists(prediction.churnProbability);
      assertExists(prediction.riskLevel);
      assertExists(prediction.factors);
      assertExists(prediction.recommendations);
      assertEquals(typeof prediction.churnProbability, "number");
    });
  });

  describe("Usage-Based Billing", () => {
    it("should record usage metrics", async () => {
      const usage = await usageService.recordUsage(
        TEST_TENANT_ID,
        TEST_CUSTOMER_ID,
        "test_subscription_123",
        "api_calls",
        5000,
        "calls",
        { source: "api" }
      );

      assertExists(usage.id);
      assertEquals(usage.tenantId, TEST_TENANT_ID);
      assertEquals(usage.metricType, "api_calls");
      assertEquals(usage.value, 5000);
      assertEquals(usage.unit, "calls");
    });

    it("should generate usage analytics", async () => {
      const period = {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end: new Date(),
      };

      const analytics = await usageService.getUsageAnalytics(
        TEST_TENANT_ID,
        "test_subscription_123",
        period
      );

      assertExists(analytics.subscriptionId);
      assertExists(analytics.metrics);
      assertExists(analytics.recommendations);
      assertEquals(analytics.period.start.getTime(), period.start.getTime());
      assertEquals(analytics.period.end.getTime(), period.end.getTime());
    });

    it("should calculate overage billing", async () => {
      const billingPeriod = {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end: new Date(),
      };

      const overage = await usageService.calculateOverageBilling(
        TEST_TENANT_ID,
        "test_subscription_123",
        billingPeriod
      );

      assertExists(overage.subscriptionId);
      assertExists(overage.overages);
      assertExists(overage.totalOverageAmount);
      assertEquals(typeof overage.totalOverageAmount, "number");
    });

    it("should provide tier recommendations", async () => {
      const recommendation = await usageService.getTierRecommendation(
        TEST_TENANT_ID,
        "test_subscription_123"
      );

      assertExists(recommendation.currentTier);
      assertExists(recommendation.recommendedTier);
      assertExists(recommendation.reason);
      assertEquals(typeof recommendation.potentialSavings, "number");
      assertEquals(typeof recommendation.confidence, "number");
    });
  });

  describe("Revenue Recognition", () => {
    it("should process revenue transaction", async () => {
      const transactionData = {
        subscriptionId: "test_subscription_123",
        customerId: TEST_CUSTOMER_ID,
        transactionType: "subscription" as const,
        amount: 1499,
        currency: "USD",
        contractualObligations: ["service_delivery", "support"],
        performanceObligations: [
          {
            description: "Software license",
            allocatedAmount: 1200,
          },
          {
            description: "Support services",
            allocatedAmount: 299,
          },
        ],
      };

      const transaction = await revenueService.processRevenueTransaction(
        TEST_TENANT_ID,
        transactionData
      );

      assertExists(transaction.id);
      assertEquals(transaction.tenantId, TEST_TENANT_ID);
      assertEquals(transaction.amount, 1499);
      assertEquals(transaction.currency, "USD");
      assertEquals(transaction.performanceObligations.length, 2);
    });

    it("should recognize deferred revenue", async () => {
      const result = await revenueService.recognizeDeferredRevenue(
        TEST_TENANT_ID,
        new Date()
      );

      assertEquals(typeof result.processedTransactions, "number");
      assertEquals(typeof result.totalRecognized, "number");
      assertExists(result.errors);
    });

    it("should generate compliance report", async () => {
      const period = {
        start: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
        end: new Date(),
      };

      const report = await revenueService.generateComplianceReport(
        TEST_TENANT_ID,
        "quarterly",
        period
      );

      assertExists(report.id);
      assertEquals(report.tenantId, TEST_TENANT_ID);
      assertEquals(report.reportType, "quarterly");
      assertExists(report.metrics);
      assertExists(report.complianceChecks);
    });

    it("should get revenue schedule", async () => {
      const schedule = await revenueService.getRevenueSchedule(
        TEST_TENANT_ID,
        "test_transaction_123"
      );

      assertExists(schedule.transactionId);
      assertExists(schedule.schedule);
      assertEquals(typeof schedule.totalScheduled, "number");
      assertEquals(typeof schedule.totalRecognized, "number");
      assertEquals(typeof schedule.totalRemaining, "number");
    });
  });

  describe("Financial Reporting", () => {
    it("should calculate financial metrics", async () => {
      const period = {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end: new Date(),
        type: "monthly" as const,
      };

      const metrics = await reportingService.calculateFinancialMetrics(
        TEST_TENANT_ID,
        period
      );

      assertExists(metrics.revenue);
      assertExists(metrics.subscriptions);
      assertExists(metrics.customers);
      assertExists(metrics.financial);
      assertEquals(typeof metrics.revenue.totalRevenue, "number");
      assertEquals(typeof metrics.revenue.mrr, "number");
      assertEquals(typeof metrics.subscriptions.churnRate, "number");
    });

    it("should generate cohort analysis", async () => {
      const analysis = await reportingService.generateCohortAnalysis(
        TEST_TENANT_ID,
        6
      );

      assertExists(analysis);
      assertEquals(Array.isArray(analysis), true);
      
      if (analysis.length > 0) {
        const cohort = analysis[0];
        assertExists(cohort.cohortPeriod);
        assertExists(cohort.cohortSize);
        assertExists(cohort.revenueByPeriod);
        assertExists(cohort.metrics);
      }
    });

    it("should generate revenue waterfall", async () => {
      const period = {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end: new Date(),
      };

      const waterfall = await reportingService.generateRevenueWaterfall(
        TEST_TENANT_ID,
        period
      );

      assertExists(waterfall.period);
      assertEquals(typeof waterfall.startingMRR, "number");
      assertEquals(typeof waterfall.endingMRR, "number");
      assertEquals(typeof waterfall.newMRR, "number");
      assertEquals(typeof waterfall.churnMRR, "number");
      assertExists(waterfall.components);
    });

    it("should generate executive report", async () => {
      const period = {
        start: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
        end: new Date(),
      };

      const report = await reportingService.generateExecutiveReport(
        TEST_TENANT_ID,
        "quarterly",
        period
      );

      assertExists(report.id);
      assertEquals(report.tenantId, TEST_TENANT_ID);
      assertEquals(report.reportType, "quarterly");
      assertExists(report.summary);
      assertExists(report.keyMetrics);
      assertExists(report.cohortAnalysis);
      assertExists(report.revenueWaterfall);
      assertExists(report.insights);
    });
  });

  describe("Revenue Intelligence", () => {
    it("should generate revenue intelligence", async () => {
      const intelligence = await intelligenceService.generateRevenueIntelligence(
        TEST_TENANT_ID
      );

      assertEquals(intelligence.tenantId, TEST_TENANT_ID);
      assertExists(intelligence.predictions);
      assertExists(intelligence.opportunities);
      assertExists(intelligence.risks);
      assertExists(intelligence.insights);
      assertExists(intelligence.recommendations);

      // Validate predictions structure
      assertExists(intelligence.predictions.nextMonthRevenue);
      assertExists(intelligence.predictions.quarterlyRevenue);
      assertExists(intelligence.predictions.annualRevenue);
    });

    it("should generate revenue scenarios", async () => {
      const scenarios = await intelligenceService.generateRevenueScenarios(
        TEST_TENANT_ID,
        12
      );

      assertExists(scenarios);
      assertEquals(Array.isArray(scenarios), true);
      assertEquals(scenarios.length, 3); // Conservative, Base, Optimistic

      for (const scenario of scenarios) {
        assertExists(scenario.name);
        assertExists(scenario.description);
        assertExists(scenario.assumptions);
        assertExists(scenario.projections);
        assertEquals(typeof scenario.probability, "number");
        assertEquals(typeof scenario.impact, "number");
      }
    });
  });

  describe("Integration Workflows", () => {
    it("should handle complete subscription lifecycle", async () => {
      // 1. Create subscription
      const subscription = await subscriptionService.createSubscription(
        TEST_TENANT_ID,
        TEST_CUSTOMER_ID,
        TEST_PLAN_ID,
        { trialDays: 14 }
      );

      // 2. Record usage
      await usageService.recordUsage(
        TEST_TENANT_ID,
        TEST_CUSTOMER_ID,
        subscription.id,
        "api_calls",
        5000,
        "calls"
      );

      // 3. Process revenue transaction
      await revenueService.processRevenueTransaction(TEST_TENANT_ID, {
        subscriptionId: subscription.id,
        customerId: TEST_CUSTOMER_ID,
        transactionType: "subscription",
        amount: 1499,
        currency: "USD",
        contractualObligations: ["service_delivery"],
        performanceObligations: [
          {
            description: "Software license",
            allocatedAmount: 1499,
          },
        ],
      });

      // 4. Generate intelligence
      const intelligence = await intelligenceService.generateRevenueIntelligence(
        TEST_TENANT_ID
      );

      // Validate end-to-end workflow
      assertExists(subscription.id);
      assertExists(intelligence.predictions);
      assertEquals(intelligence.tenantId, TEST_TENANT_ID);
    });

    it("should handle revenue recognition workflow", async () => {
      // 1. Process transaction
      const transaction = await revenueService.processRevenueTransaction(TEST_TENANT_ID, {
        subscriptionId: "test_sub_123",
        customerId: TEST_CUSTOMER_ID,
        transactionType: "subscription",
        amount: 1499,
        currency: "USD",
        contractualObligations: ["service_delivery"],
        performanceObligations: [
          {
            description: "Software license",
            allocatedAmount: 1499,
          },
        ],
      });

      // 2. Get revenue schedule
      const schedule = await revenueService.getRevenueSchedule(
        TEST_TENANT_ID,
        transaction.id
      );

      // 3. Recognize deferred revenue
      const recognition = await revenueService.recognizeDeferredRevenue(
        TEST_TENANT_ID
      );

      // 4. Generate compliance report
      const report = await revenueService.generateComplianceReport(
        TEST_TENANT_ID,
        "monthly",
        {
          start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          end: new Date(),
        }
      );

      // Validate workflow
      assertExists(transaction.id);
      assertExists(schedule.schedule);
      assertEquals(typeof recognition.totalRecognized, "number");
      assertExists(report.metrics);
    });
  });

  describe("Performance Tests", () => {
    it("should handle high-volume usage recording", async () => {
      const startTime = Date.now();
      const promises = [];

      // Record 100 usage metrics concurrently
      for (let i = 0; i < 100; i++) {
        promises.push(
          usageService.recordUsage(
            TEST_TENANT_ID,
            `customer_${i}`,
            `subscription_${i}`,
            "api_calls",
            Math.floor(Math.random() * 10000),
            "calls"
          )
        );
      }

      const results = await Promise.all(promises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Validate performance
      assertEquals(results.length, 100);
      console.log(`Processed 100 usage records in ${duration}ms`);
      
      // Should complete within 5 seconds
      assertEquals(duration < 5000, true);
    });

    it("should generate financial metrics efficiently", async () => {
      const startTime = Date.now();

      const metrics = await reportingService.calculateFinancialMetrics(
        TEST_TENANT_ID,
        {
          start: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
          end: new Date(),
          type: "annual",
        }
      );

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Validate performance and results
      assertExists(metrics.revenue);
      console.log(`Generated financial metrics in ${duration}ms`);
      
      // Should complete within 2 seconds
      assertEquals(duration < 2000, true);
    });
  });
});

// Performance benchmark tests
describe("Revenue Operations Performance Benchmarks", () => {
  it("should meet subscription creation performance targets", async () => {
    const service = new SubscriptionLifecycleService();
    const iterations = 50;
    const startTime = Date.now();

    for (let i = 0; i < iterations; i++) {
      await service.createSubscription(
        TEST_TENANT_ID,
        `customer_${i}`,
        TEST_PLAN_ID
      );
    }

    const endTime = Date.now();
    const duration = endTime - startTime;
    const avgTime = duration / iterations;

    console.log(`Average subscription creation time: ${avgTime.toFixed(2)}ms`);
    
    // Target: <100ms per subscription creation
    assertEquals(avgTime < 100, true);
  });

  it("should meet revenue intelligence generation targets", async () => {
    const service = new RevenueIntelligenceService();
    const startTime = Date.now();

    const intelligence = await service.generateRevenueIntelligence(TEST_TENANT_ID);

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`Revenue intelligence generation time: ${duration}ms`);
    
    // Target: <500ms for intelligence generation
    assertEquals(duration < 500, true);
    assertExists(intelligence.predictions);
  });
});

// Run tests with: deno test --allow-net --allow-env --allow-read services/billing-deno/tests/revenueOperations.test.ts
