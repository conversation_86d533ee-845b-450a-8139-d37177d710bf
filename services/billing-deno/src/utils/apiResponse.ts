// API Response Utility
// Standardized response format for all API endpoints

export interface ApiSuccessResponse<T = any> {
  success: true;
  data: T;
  metadata?: {
    tenantId?: string;
    queryTime?: string;
    generatedAt?: string;
    demoMode?: boolean;
    [key: string]: any;
  };
}

export interface ApiErrorResponse {
  success: false;
  error: {
    message: string;
    code: string;
    statusCode: number;
    details?: any;
  };
}

export type ApiResponseType<T = any> = ApiSuccessResponse<T> | ApiErrorResponse;

export class ApiResponse {
  /**
   * Create a successful API response
   */
  static success<T = any>(data: T, metadata?: Record<string, any>): ApiSuccessResponse<T> {
    return {
      success: true,
      data,
      metadata,
    };
  }

  /**
   * Create an error API response
   */
  static error(
    message: string,
    code: string,
    statusCode: number,
    details?: any
  ): ApiErrorResponse {
    return {
      success: false,
      error: {
        message,
        code,
        statusCode,
        details,
      },
    };
  }

  /**
   * Create a validation error response
   */
  static validationError(details: any): ApiErrorResponse {
    return this.error(
      "Validation failed",
      "VALIDATION_ERROR",
      400,
      details
    );
  }

  /**
   * Create a not found error response
   */
  static notFound(resource: string): ApiErrorResponse {
    return this.error(
      `${resource} not found`,
      "NOT_FOUND",
      404
    );
  }

  /**
   * Create an unauthorized error response
   */
  static unauthorized(message = "Unauthorized"): ApiErrorResponse {
    return this.error(
      message,
      "UNAUTHORIZED",
      401
    );
  }

  /**
   * Create a forbidden error response
   */
  static forbidden(message = "Forbidden"): ApiErrorResponse {
    return this.error(
      message,
      "FORBIDDEN",
      403
    );
  }

  /**
   * Create an internal server error response
   */
  static internalError(message = "Internal server error"): ApiErrorResponse {
    return this.error(
      message,
      "INTERNAL_ERROR",
      500
    );
  }
}
