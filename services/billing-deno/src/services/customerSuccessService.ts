// Customer Success Service
// Comprehensive tracking and health scoring for trial environments and customer onboarding

import { logger } from "../utils/logger.ts";
import type { TrialEnvironment } from "./trialEnvironmentService.ts";

export interface CustomerHealthScore {
  trialId: string;
  overallScore: number; // 0-100
  scoreComponents: {
    engagement: number;
    featureAdoption: number;
    timeToValue: number;
    integrationProgress: number;
    onboardingCompletion: number;
  };
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  interventionTriggers: string[];
  recommendations: string[];
  lastUpdated: Date;
}

export interface SuccessMilestone {
  id: string;
  name: string;
  description: string;
  category: 'onboarding' | 'adoption' | 'value_realization' | 'expansion';
  weight: number; // Impact on health score
  criteria: {
    metric: string;
    operator: 'gt' | 'gte' | 'lt' | 'lte' | 'eq' | 'contains';
    value: number | string;
  }[];
  achievedAt?: Date;
  valueImpact: string;
}

export interface InterventionTrigger {
  id: string;
  name: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  conditions: {
    metric: string;
    operator: 'gt' | 'gte' | 'lt' | 'lte' | 'eq';
    value: number;
    timeWindow?: number; // hours
  }[];
  actions: {
    type: 'email' | 'notification' | 'call' | 'demo' | 'support';
    template: string;
    assignee?: string;
    priority: number;
  }[];
  cooldownPeriod: number; // hours before trigger can fire again
}

export interface CustomerJourneyEvent {
  id: string;
  trialId: string;
  eventType: string;
  eventData: Record<string, unknown>;
  timestamp: Date;
  source: 'platform' | 'api' | 'manual';
  impact: 'positive' | 'negative' | 'neutral';
  scoreChange?: number;
}

export class CustomerSuccessService {
  private healthScores: Map<string, CustomerHealthScore> = new Map();
  private successMilestones: Map<string, SuccessMilestone[]> = new Map();
  private interventionTriggers: InterventionTrigger[] = [];
  private journeyEvents: Map<string, CustomerJourneyEvent[]> = new Map();

  constructor() {
    this.initializeSuccessMilestones();
    this.initializeInterventionTriggers();
  }

  /**
   * Initialize success milestones for different scenarios
   */
  private initializeSuccessMilestones(): void {
    // E-commerce SMB milestones
    this.successMilestones.set("ecommerce_smb", [
      {
        id: "first_login",
        name: "First Platform Access",
        description: "User successfully logged into trial environment",
        category: "onboarding",
        weight: 10,
        criteria: [
          { metric: "totalSessions", operator: "gte", value: 1 }
        ],
        valueImpact: "Initial platform engagement"
      },
      {
        id: "performance_validation",
        name: "Performance Validation",
        description: "User experienced sub-15ms query performance",
        category: "onboarding",
        weight: 15,
        criteria: [
          { metric: "averageQueryTime", operator: "lt", value: 15 },
          { metric: "totalQueries", operator: "gte", value: 5 }
        ],
        valueImpact: "Performance advantage demonstrated"
      },
      {
        id: "feature_exploration",
        name: "Core Feature Exploration",
        description: "User explored at least 3 core analytics features",
        category: "adoption",
        weight: 20,
        criteria: [
          { metric: "featuresExplored", operator: "gte", value: 3 }
        ],
        valueImpact: "Platform capability understanding"
      },
      {
        id: "sample_data_analysis",
        name: "Sample Data Analysis",
        description: "User performed meaningful analysis on sample data",
        category: "value_realization",
        weight: 25,
        criteria: [
          { metric: "timeSpent", operator: "gte", value: 30 },
          { metric: "totalQueries", operator: "gte", value: 20 }
        ],
        valueImpact: "Analytical value demonstrated"
      },
      {
        id: "integration_test",
        name: "Integration Testing",
        description: "User tested integration capabilities",
        category: "adoption",
        weight: 20,
        criteria: [
          { metric: "integrationsTested", operator: "gte", value: 1 }
        ],
        valueImpact: "Technical feasibility validated"
      },
      {
        id: "roi_calculation",
        name: "ROI Calculation",
        description: "User calculated potential ROI using our calculator",
        category: "value_realization",
        weight: 30,
        criteria: [
          { metric: "roiCalculated", operator: "eq", value: "true" }
        ],
        valueImpact: "Business value quantified"
      }
    ]);

    // Enterprise retail milestones
    this.successMilestones.set("enterprise_retail", [
      {
        id: "enterprise_access",
        name: "Enterprise Dashboard Access",
        description: "User accessed enterprise-grade dashboard",
        category: "onboarding",
        weight: 10,
        criteria: [
          { metric: "enterpriseFeaturesAccessed", operator: "gte", value: 1 }
        ],
        valueImpact: "Enterprise capability awareness"
      },
      {
        id: "scalability_demo",
        name: "Scalability Demonstration",
        description: "User experienced platform scalability features",
        category: "adoption",
        weight: 20,
        criteria: [
          { metric: "scalabilityTestsRun", operator: "gte", value: 1 },
          { metric: "largeDatasetQueries", operator: "gte", value: 5 }
        ],
        valueImpact: "Enterprise scalability validated"
      },
      {
        id: "multi_channel_analysis",
        name: "Multi-Channel Analysis",
        description: "User explored multi-channel analytics capabilities",
        category: "value_realization",
        weight: 25,
        criteria: [
          { metric: "channelsAnalyzed", operator: "gte", value: 3 },
          { metric: "timeSpent", operator: "gte", value: 45 }
        ],
        valueImpact: "Unified analytics value demonstrated"
      },
      {
        id: "predictive_analytics",
        name: "Predictive Analytics Exploration",
        description: "User engaged with ML-powered predictive features",
        category: "value_realization",
        weight: 30,
        criteria: [
          { metric: "predictiveModelsUsed", operator: "gte", value: 2 },
          { metric: "forecastsGenerated", operator: "gte", value: 1 }
        ],
        valueImpact: "Advanced analytics value realized"
      },
      {
        id: "enterprise_integration",
        name: "Enterprise Integration Validation",
        description: "User validated enterprise integration capabilities",
        category: "adoption",
        weight: 25,
        criteria: [
          { metric: "enterpriseIntegrationsTest", operator: "gte", value: 1 },
          { metric: "securityValidation", operator: "eq", value: "passed" }
        ],
        valueImpact: "Enterprise readiness confirmed"
      },
      {
        id: "business_case_development",
        name: "Business Case Development",
        description: "User developed comprehensive business case",
        category: "expansion",
        weight: 35,
        criteria: [
          { metric: "enterpriseRoiCalculated", operator: "eq", value: "true" },
          { metric: "stakeholderPresentations", operator: "gte", value: 1 }
        ],
        valueImpact: "Executive buy-in achieved"
      }
    ]);
  }

  /**
   * Initialize intervention triggers
   */
  private initializeInterventionTriggers(): void {
    this.interventionTriggers = [
      {
        id: "low_engagement",
        name: "Low Engagement Alert",
        description: "User has not accessed platform in 48 hours",
        severity: "medium",
        conditions: [
          { metric: "hoursSinceLastAccess", operator: "gte", value: 48 }
        ],
        actions: [
          {
            type: "email",
            template: "re_engagement_email",
            priority: 2
          }
        ],
        cooldownPeriod: 24
      },
      {
        id: "no_feature_exploration",
        name: "Limited Feature Exploration",
        description: "User has explored fewer than 2 features after 24 hours",
        severity: "medium",
        conditions: [
          { metric: "featuresExplored", operator: "lt", value: 2 },
          { metric: "hoursInTrial", operator: "gte", value: 24 }
        ],
        actions: [
          {
            type: "email",
            template: "feature_guidance_email",
            priority: 2
          },
          {
            type: "notification",
            template: "guided_tour_notification",
            priority: 1
          }
        ],
        cooldownPeriod: 48
      },
      {
        id: "poor_performance_experience",
        name: "Performance Issues Detected",
        description: "User experiencing slower than expected query times",
        severity: "high",
        conditions: [
          { metric: "averageQueryTime", operator: "gt", value: 50 },
          { metric: "totalQueries", operator: "gte", value: 10 }
        ],
        actions: [
          {
            type: "support",
            template: "performance_investigation",
            assignee: "technical_support",
            priority: 1
          }
        ],
        cooldownPeriod: 12
      },
      {
        id: "trial_expiring_soon",
        name: "Trial Expiring Soon",
        description: "Trial expires in 3 days with low engagement",
        severity: "high",
        conditions: [
          { metric: "daysUntilExpiry", operator: "lte", value: 3 },
          { metric: "overallHealthScore", operator: "lt", value: 60 }
        ],
        actions: [
          {
            type: "call",
            template: "trial_extension_call",
            assignee: "sales_team",
            priority: 1
          },
          {
            type: "email",
            template: "trial_extension_offer",
            priority: 1
          }
        ],
        cooldownPeriod: 24
      },
      {
        id: "high_value_prospect",
        name: "High-Value Prospect Identified",
        description: "Prospect showing strong engagement and value realization",
        severity: "low",
        conditions: [
          { metric: "overallHealthScore", operator: "gte", value: 85 },
          { metric: "successMilestonesAchieved", operator: "gte", value: 4 }
        ],
        actions: [
          {
            type: "notification",
            template: "high_value_prospect_alert",
            assignee: "sales_team",
            priority: 1
          },
          {
            type: "demo",
            template: "advanced_features_demo",
            assignee: "solutions_engineer",
            priority: 2
          }
        ],
        cooldownPeriod: 72
      }
    ];
  }

  /**
   * Calculate health score for trial environment
   */
  calculateHealthScore(trial: TrialEnvironment): CustomerHealthScore {
    const startTime = performance.now();
    
    // Calculate component scores
    const engagement = this.calculateEngagementScore(trial);
    const featureAdoption = this.calculateFeatureAdoptionScore(trial);
    const timeToValue = this.calculateTimeToValueScore(trial);
    const integrationProgress = this.calculateIntegrationProgressScore(trial);
    const onboardingCompletion = this.calculateOnboardingCompletionScore(trial);

    // Calculate weighted overall score
    const overallScore = Math.round(
      (engagement * 0.25) +
      (featureAdoption * 0.20) +
      (timeToValue * 0.25) +
      (integrationProgress * 0.15) +
      (onboardingCompletion * 0.15)
    );

    // Determine risk level
    const riskLevel = this.determineRiskLevel(overallScore, trial);

    // Check for intervention triggers
    const interventionTriggers = this.checkInterventionTriggers(trial, overallScore);

    // Generate recommendations
    const recommendations = this.generateRecommendations(trial, overallScore);

    const healthScore: CustomerHealthScore = {
      trialId: trial.id,
      overallScore,
      scoreComponents: {
        engagement,
        featureAdoption,
        timeToValue,
        integrationProgress,
        onboardingCompletion
      },
      riskLevel,
      interventionTriggers,
      recommendations,
      lastUpdated: new Date()
    };

    this.healthScores.set(trial.id, healthScore);

    const calculationTime = performance.now() - startTime;

    logger.info("Health score calculated", {
      trialId: trial.id,
      overallScore,
      riskLevel,
      calculationTime: `${calculationTime.toFixed(2)}ms`
    });

    return healthScore;
  }

  /**
   * Track customer journey event
   */
  trackJourneyEvent(trialId: string, eventType: string, eventData: Record<string, unknown>): void {
    const event: CustomerJourneyEvent = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      trialId,
      eventType,
      eventData,
      timestamp: new Date(),
      source: 'platform',
      impact: this.determineEventImpact(eventType, eventData),
      scoreChange: this.calculateEventScoreImpact(eventType, eventData)
    };

    const events = this.journeyEvents.get(trialId) || [];
    events.push(event);
    this.journeyEvents.set(trialId, events);

    logger.info("Journey event tracked", {
      trialId,
      eventType,
      impact: event.impact,
      scoreChange: event.scoreChange
    });
  }

  /**
   * Get health score for trial
   */
  getHealthScore(trialId: string): CustomerHealthScore | undefined {
    return this.healthScores.get(trialId);
  }

  /**
   * Get journey events for trial
   */
  getJourneyEvents(trialId: string): CustomerJourneyEvent[] {
    return this.journeyEvents.get(trialId) || [];
  }

  /**
   * Check success milestones
   */
  checkSuccessMilestones(trial: TrialEnvironment): SuccessMilestone[] {
    const milestones = this.successMilestones.get(trial.scenario) || [];
    const achievedMilestones: SuccessMilestone[] = [];

    for (const milestone of milestones) {
      if (!milestone.achievedAt && this.isMilestoneAchieved(milestone, trial)) {
        milestone.achievedAt = new Date();
        achievedMilestones.push(milestone);
        
        // Track milestone achievement
        this.trackJourneyEvent(trial.id, "milestone_achieved", {
          milestoneId: milestone.id,
          milestoneName: milestone.name,
          category: milestone.category,
          weight: milestone.weight
        });
      }
    }

    return achievedMilestones;
  }

  /**
   * Calculate engagement score
   */
  private calculateEngagementScore(trial: TrialEnvironment): number {
    const { totalSessions, timeSpent, lastActivity } = trial.metrics;
    const daysSinceCreated = (Date.now() - trial.createdAt.getTime()) / (1000 * 60 * 60 * 24);
    const hoursSinceLastActivity = (Date.now() - lastActivity.getTime()) / (1000 * 60 * 60);

    let score = 0;

    // Session frequency (40 points)
    const sessionsPerDay = totalSessions / Math.max(daysSinceCreated, 1);
    score += Math.min(sessionsPerDay * 20, 40);

    // Time spent (30 points)
    const timeSpentScore = Math.min(timeSpent / 60, 30); // 1 point per minute, max 30
    score += timeSpentScore;

    // Recency (30 points)
    if (hoursSinceLastActivity < 24) score += 30;
    else if (hoursSinceLastActivity < 48) score += 20;
    else if (hoursSinceLastActivity < 72) score += 10;

    return Math.min(Math.round(score), 100);
  }

  /**
   * Calculate feature adoption score
   */
  private calculateFeatureAdoptionScore(trial: TrialEnvironment): number {
    const { featuresExplored } = trial.metrics;
    const availableFeatures = trial.configuration.features.length;
    
    const adoptionRate = featuresExplored.length / availableFeatures;
    return Math.min(Math.round(adoptionRate * 100), 100);
  }

  /**
   * Calculate time to value score
   */
  private calculateTimeToValueScore(trial: TrialEnvironment): number {
    const { totalQueries, averageQueryTime } = trial.metrics;
    const daysSinceCreated = (Date.now() - trial.createdAt.getTime()) / (1000 * 60 * 60 * 24);

    let score = 0;

    // Query activity (50 points)
    const queriesPerDay = totalQueries / Math.max(daysSinceCreated, 1);
    score += Math.min(queriesPerDay * 5, 50);

    // Performance experience (50 points)
    if (averageQueryTime > 0) {
      if (averageQueryTime < 15) score += 50;
      else if (averageQueryTime < 30) score += 35;
      else if (averageQueryTime < 50) score += 20;
      else score += 10;
    }

    return Math.min(Math.round(score), 100);
  }

  /**
   * Calculate integration progress score
   */
  private calculateIntegrationProgressScore(trial: TrialEnvironment): number {
    // This would be based on actual integration testing metrics
    // For now, return a placeholder based on configuration
    const recommendedIntegrations = trial.configuration.integrations.length;
    return recommendedIntegrations > 0 ? 75 : 25; // Placeholder logic
  }

  /**
   * Calculate onboarding completion score
   */
  private calculateOnboardingCompletionScore(trial: TrialEnvironment): number {
    return trial.onboardingProgress.progressPercentage;
  }

  /**
   * Determine risk level based on score and trial status
   */
  private determineRiskLevel(score: number, trial: TrialEnvironment): 'low' | 'medium' | 'high' | 'critical' {
    const daysUntilExpiry = (trial.expiresAt.getTime() - Date.now()) / (1000 * 60 * 60 * 24);
    
    if (score >= 80) return 'low';
    if (score >= 60) return 'medium';
    if (score >= 40 || daysUntilExpiry <= 3) return 'high';
    return 'critical';
  }

  /**
   * Check intervention triggers
   */
  private checkInterventionTriggers(trial: TrialEnvironment, overallScore: number): string[] {
    const triggeredInterventions: string[] = [];
    
    // This would check actual trigger conditions
    // For now, return sample triggers based on score
    if (overallScore < 40) {
      triggeredInterventions.push("low_engagement", "feature_guidance_needed");
    }
    if (overallScore >= 85) {
      triggeredInterventions.push("high_value_prospect");
    }
    
    return triggeredInterventions;
  }

  /**
   * Generate recommendations based on trial status
   */
  private generateRecommendations(trial: TrialEnvironment, score: number): string[] {
    const recommendations: string[] = [];
    
    if (score < 60) {
      recommendations.push("Schedule guided demo session");
      recommendations.push("Provide feature-specific tutorials");
    }
    
    if (trial.metrics.featuresExplored.length < 3) {
      recommendations.push("Encourage exploration of core analytics features");
    }
    
    if (trial.onboardingProgress.progressPercentage < 50) {
      recommendations.push("Focus on completing onboarding milestones");
    }
    
    if (score >= 80) {
      recommendations.push("Initiate sales conversation");
      recommendations.push("Provide advanced feature demonstration");
    }
    
    return recommendations;
  }

  /**
   * Check if milestone is achieved
   */
  private isMilestoneAchieved(milestone: SuccessMilestone, trial: TrialEnvironment): boolean {
    return milestone.criteria.every(criterion => {
      const value = this.getMetricValue(criterion.metric, trial);
      return this.evaluateCondition(value, criterion.operator, criterion.value);
    });
  }

  /**
   * Get metric value from trial
   */
  private getMetricValue(metric: string, trial: TrialEnvironment): number | string {
    // Map metric names to actual trial data
    const metricMap: Record<string, number | string> = {
      totalSessions: trial.metrics.totalSessions,
      totalQueries: trial.metrics.totalQueries,
      averageQueryTime: trial.metrics.averageQueryTime,
      featuresExplored: trial.metrics.featuresExplored.length,
      timeSpent: trial.metrics.timeSpent,
      // Add more metric mappings as needed
    };
    
    return metricMap[metric] || 0;
  }

  /**
   * Evaluate condition
   */
  private evaluateCondition(value: number | string, operator: string, target: number | string): boolean {
    if (typeof value === 'number' && typeof target === 'number') {
      switch (operator) {
        case 'gt': return value > target;
        case 'gte': return value >= target;
        case 'lt': return value < target;
        case 'lte': return value <= target;
        case 'eq': return value === target;
        default: return false;
      }
    }
    
    if (typeof value === 'string' && typeof target === 'string') {
      switch (operator) {
        case 'eq': return value === target;
        case 'contains': return value.includes(target);
        default: return false;
      }
    }
    
    return false;
  }

  /**
   * Determine event impact
   */
  private determineEventImpact(eventType: string, eventData: Record<string, unknown>): 'positive' | 'negative' | 'neutral' {
    const positiveEvents = ['feature_explored', 'milestone_achieved', 'integration_tested', 'roi_calculated'];
    const negativeEvents = ['error_encountered', 'session_timeout', 'performance_issue'];
    
    if (positiveEvents.includes(eventType)) return 'positive';
    if (negativeEvents.includes(eventType)) return 'negative';
    return 'neutral';
  }

  /**
   * Calculate event score impact
   */
  private calculateEventScoreImpact(eventType: string, eventData: Record<string, unknown>): number {
    const impactMap: Record<string, number> = {
      'feature_explored': 2,
      'milestone_achieved': 5,
      'integration_tested': 3,
      'roi_calculated': 8,
      'error_encountered': -3,
      'performance_issue': -5
    };
    
    return impactMap[eventType] || 0;
  }
}
