// Usage-Based Billing Optimization Service
// Dynamic usage tracking, overage billing, tier recommendations, and consumption analytics

import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { query, queryOne, transaction } from "../utils/database.ts";

// Usage-based billing types and interfaces
interface UsageMetric {
  id: string;
  tenantId: string;
  customerId: string;
  subscriptionId: string;
  metricType: 'api_calls' | 'data_volume' | 'active_users' | 'storage' | 'bandwidth' | 'custom';
  value: number;
  unit: string;
  timestamp: Date;
  metadata: Record<string, any>;
}

interface UsageTier {
  id: string;
  name: string;
  minUsage: number;
  maxUsage: number | null; // null for unlimited
  unitPrice: number;
  flatFee?: number;
  overage?: {
    enabled: boolean;
    rate: number;
    threshold: number;
  };
}

interface BillingPlan {
  id: string;
  tenantId: string;
  name: string;
  basePrice: number;
  billingCycle: 'monthly' | 'yearly';
  usageMetrics: Array<{
    metricType: string;
    tiers: UsageTier[];
    aggregationMethod: 'sum' | 'max' | 'average' | 'unique_count';
    resetCycle: 'billing_period' | 'calendar_month' | 'never';
  }>;
  createdAt: Date;
  updatedAt: Date;
}

interface UsageAnalytics {
  subscriptionId: string;
  period: {
    start: Date;
    end: Date;
  };
  metrics: Array<{
    metricType: string;
    totalUsage: number;
    averageDaily: number;
    peakUsage: number;
    trend: 'increasing' | 'decreasing' | 'stable';
    projectedMonthly: number;
    costBreakdown: {
      baseAmount: number;
      usageAmount: number;
      overageAmount: number;
      totalAmount: number;
    };
  }>;
  recommendations: Array<{
    type: 'tier_upgrade' | 'tier_downgrade' | 'usage_optimization' | 'cost_optimization';
    description: string;
    potentialSavings: number;
    confidence: number;
  }>;
  calculatedAt: Date;
}

interface OverageBilling {
  subscriptionId: string;
  billingPeriod: {
    start: Date;
    end: Date;
  };
  overages: Array<{
    metricType: string;
    allowedUsage: number;
    actualUsage: number;
    overageAmount: number;
    overageRate: number;
    overageUnits: number;
  }>;
  totalOverageAmount: number;
  status: 'pending' | 'billed' | 'paid';
  createdAt: Date;
}

export class UsageBasedBillingService {
  constructor() {
  }

  /**
   * Record usage metric for a subscription
   */
  async recordUsage(
    tenantId: string,
    customerId: string,
    subscriptionId: string,
    metricType: UsageMetric['metricType'],
    value: number,
    unit: string,
    metadata: Record<string, any> = {}
  ): Promise<UsageMetric> {
    const usageId = `usage_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timestamp = new Date();

    const usage: UsageMetric = {
      id: usageId,
      tenantId,
      customerId,
      subscriptionId,
      metricType,
      value,
      unit,
      timestamp,
      metadata,
    };

    // Store usage in TimescaleDB for time-series optimization
    await query(`
      INSERT INTO usage_metrics (
        id, tenant_id, customer_id, subscription_id, metric_type,
        value, unit, timestamp, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `, [
      usage.id,
      usage.tenantId,
      usage.customerId,
      usage.subscriptionId,
      usage.metricType,
      usage.value,
      usage.unit,
      usage.timestamp,
      JSON.stringify(usage.metadata),
    ], tenantId);

    // Check for overage thresholds
    await this.checkOverageThresholds(tenantId, subscriptionId, metricType);

    // Update real-time usage aggregates
    await this.updateUsageAggregates(tenantId, subscriptionId, metricType, value);

    logger.info("Usage metric recorded", {
      tenantId,
      subscriptionId,
      metricType,
      value,
      unit,
    });

    return usage;
  }

  /**
   * Get usage analytics for a subscription
   */
  async getUsageAnalytics(
    tenantId: string,
    subscriptionId: string,
    period: { start: Date; end: Date }
  ): Promise<UsageAnalytics> {
    // Get subscription billing plan
    const billingPlan = await this.getBillingPlan(tenantId, subscriptionId);
    
    // Get usage data for the period
    const usageData = await this.getUsageData(tenantId, subscriptionId, period);

    // Calculate analytics for each metric
    const metrics = await Promise.all(
      billingPlan.usageMetrics.map(async (planMetric) => {
        const metricUsage = usageData.filter(u => u.metricType === planMetric.metricType);
        
        // Aggregate usage based on plan configuration
        const totalUsage = this.aggregateUsage(metricUsage, planMetric.aggregationMethod);
        
        // Calculate daily average
        const periodDays = Math.ceil((period.end.getTime() - period.start.getTime()) / (1000 * 60 * 60 * 24));
        const averageDaily = totalUsage / periodDays;

        // Find peak usage
        const dailyUsage = this.groupUsageByDay(metricUsage, planMetric.aggregationMethod);
        const peakUsage = Math.max(...Object.values(dailyUsage));

        // Calculate trend
        const trend = this.calculateUsageTrend(dailyUsage);

        // Project monthly usage
        const projectedMonthly = averageDaily * 30;

        // Calculate cost breakdown
        const costBreakdown = this.calculateCostBreakdown(totalUsage, planMetric.tiers, billingPlan.basePrice);

        return {
          metricType: planMetric.metricType,
          totalUsage,
          averageDaily,
          peakUsage,
          trend,
          projectedMonthly,
          costBreakdown,
        };
      })
    );

    // Generate recommendations
    const recommendations = this.generateUsageRecommendations(metrics, billingPlan);

    const analytics: UsageAnalytics = {
      subscriptionId,
      period,
      metrics,
      recommendations,
      calculatedAt: new Date(),
    };

    logger.info("Usage analytics calculated", {
      tenantId,
      subscriptionId,
      period: `${period.start.toISOString()} - ${period.end.toISOString()}`,
      metricsCount: metrics.length,
      recommendationsCount: recommendations.length,
    });

    return analytics;
  }

  /**
   * Calculate overage billing for a subscription
   */
  async calculateOverageBilling(
    tenantId: string,
    subscriptionId: string,
    billingPeriod: { start: Date; end: Date }
  ): Promise<OverageBilling> {
    const billingPlan = await this.getBillingPlan(tenantId, subscriptionId);
    const usageData = await this.getUsageData(tenantId, subscriptionId, billingPeriod);

    const overages: OverageBilling['overages'] = [];
    let totalOverageAmount = 0;

    for (const planMetric of billingPlan.usageMetrics) {
      const metricUsage = usageData.filter(u => u.metricType === planMetric.metricType);
      const totalUsage = this.aggregateUsage(metricUsage, planMetric.aggregationMethod);

      // Find the applicable tier
      const applicableTier = this.findApplicableTier(totalUsage, planMetric.tiers);
      
      if (applicableTier?.overage?.enabled && totalUsage > applicableTier.overage.threshold) {
        const overageUnits = totalUsage - applicableTier.overage.threshold;
        const overageAmount = overageUnits * applicableTier.overage.rate;

        overages.push({
          metricType: planMetric.metricType,
          allowedUsage: applicableTier.overage.threshold,
          actualUsage: totalUsage,
          overageAmount,
          overageRate: applicableTier.overage.rate,
          overageUnits,
        });

        totalOverageAmount += overageAmount;
      }
    }

    const overageBilling: OverageBilling = {
      subscriptionId,
      billingPeriod,
      overages,
      totalOverageAmount,
      status: 'pending',
      createdAt: new Date(),
    };

    // Store overage billing record
    if (totalOverageAmount > 0) {
      await this.storeOverageBilling(tenantId, overageBilling);
    }

    logger.info("Overage billing calculated", {
      tenantId,
      subscriptionId,
      totalOverageAmount,
      overagesCount: overages.length,
    });

    return overageBilling;
  }

  /**
   * Optimize billing plan based on usage patterns
   */
  async optimizeBillingPlan(
    tenantId: string,
    subscriptionId: string,
    historicalPeriods: number = 6
  ): Promise<{
    currentPlan: BillingPlan;
    optimizedPlan: BillingPlan;
    projectedSavings: number;
    confidence: number;
    recommendations: string[];
  }> {
    const currentPlan = await this.getBillingPlan(tenantId, subscriptionId);
    
    // Get historical usage data
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - historicalPeriods);
    
    const historicalUsage = await this.getUsageData(tenantId, subscriptionId, {
      start: startDate,
      end: endDate,
    });

    // Analyze usage patterns
    const usagePatterns = this.analyzeUsagePatterns(historicalUsage, currentPlan);

    // Generate optimized plan
    const optimizedPlan = this.generateOptimizedPlan(currentPlan, usagePatterns);

    // Calculate projected savings
    const currentCost = this.calculatePlanCost(currentPlan, usagePatterns.averageMonthlyUsage);
    const optimizedCost = this.calculatePlanCost(optimizedPlan, usagePatterns.averageMonthlyUsage);
    const projectedSavings = currentCost - optimizedCost;

    // Calculate confidence based on usage stability
    const confidence = this.calculateOptimizationConfidence(usagePatterns);

    // Generate recommendations
    const recommendations = this.generatePlanOptimizationRecommendations(
      currentPlan,
      optimizedPlan,
      usagePatterns
    );

    logger.info("Billing plan optimized", {
      tenantId,
      subscriptionId,
      projectedSavings,
      confidence,
      recommendationsCount: recommendations.length,
    });

    return {
      currentPlan,
      optimizedPlan,
      projectedSavings,
      confidence,
      recommendations,
    };
  }

  /**
   * Get tier recommendation for customer based on usage
   */
  async getTierRecommendation(
    tenantId: string,
    subscriptionId: string
  ): Promise<{
    currentTier: string;
    recommendedTier: string;
    reason: string;
    potentialSavings: number;
    confidence: number;
  }> {
    const analytics = await this.getUsageAnalytics(tenantId, subscriptionId, {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
      end: new Date(),
    });

    const billingPlan = await this.getBillingPlan(tenantId, subscriptionId);
    
    // Find current tier based on usage
    const primaryMetric = analytics.metrics[0]; // Assume first metric is primary
    const currentTier = this.findApplicableTier(primaryMetric.totalUsage, billingPlan.usageMetrics[0].tiers);
    
    // Find optimal tier based on projected usage
    const projectedUsage = primaryMetric.projectedMonthly;
    const recommendedTier = this.findOptimalTier(projectedUsage, billingPlan.usageMetrics[0].tiers);

    // Calculate potential savings
    const currentCost = this.calculateTierCost(currentTier!, primaryMetric.totalUsage);
    const recommendedCost = this.calculateTierCost(recommendedTier, projectedUsage);
    const potentialSavings = currentCost - recommendedCost;

    // Generate reason
    let reason = '';
    if (recommendedTier.id !== currentTier?.id) {
      if (recommendedCost < currentCost) {
        reason = `Based on your usage patterns, downgrading to ${recommendedTier.name} could save $${potentialSavings.toFixed(2)} per month`;
      } else {
        reason = `Your usage suggests upgrading to ${recommendedTier.name} for better value and avoiding overages`;
      }
    } else {
      reason = 'Your current tier is optimal for your usage patterns';
    }

    // Calculate confidence based on usage stability
    const confidence = primaryMetric.trend === 'stable' ? 0.9 : 0.7;

    return {
      currentTier: currentTier?.name || 'Unknown',
      recommendedTier: recommendedTier.name,
      reason,
      potentialSavings,
      confidence,
    };
  }

  // Helper methods
  private async getBillingPlan(tenantId: string, subscriptionId: string): Promise<BillingPlan> {
    // Mock billing plan - in production, this would query the database
    return {
      id: 'plan_professional',
      tenantId,
      name: 'Professional',
      basePrice: 1499,
      billingCycle: 'monthly',
      usageMetrics: [
        {
          metricType: 'api_calls',
          aggregationMethod: 'sum',
          resetCycle: 'billing_period',
          tiers: [
            {
              id: 'tier_1',
              name: 'Base',
              minUsage: 0,
              maxUsage: 100000,
              unitPrice: 0,
              flatFee: 1499,
              overage: {
                enabled: true,
                rate: 0.01,
                threshold: 100000,
              },
            },
            {
              id: 'tier_2',
              name: 'High Volume',
              minUsage: 100001,
              maxUsage: null,
              unitPrice: 0.005,
              overage: {
                enabled: false,
                rate: 0,
                threshold: 0,
              },
            },
          ],
        },
      ],
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  private async getUsageData(
    tenantId: string,
    subscriptionId: string,
    period: { start: Date; end: Date }
  ): Promise<UsageMetric[]> {
    const result = await query(`
      SELECT * FROM usage_metrics
      WHERE tenant_id = $1 AND subscription_id = $2
        AND timestamp >= $3 AND timestamp <= $4
      ORDER BY timestamp ASC
    `, [tenantId, subscriptionId, period.start, period.end], tenantId);

    return result.map((row: any) => ({
      id: row.id,
      tenantId: row.tenant_id,
      customerId: row.customer_id,
      subscriptionId: row.subscription_id,
      metricType: row.metric_type,
      value: row.value,
      unit: row.unit,
      timestamp: new Date(row.timestamp),
      metadata: JSON.parse(row.metadata || '{}'),
    }));
  }

  private aggregateUsage(usage: UsageMetric[], method: string): number {
    if (usage.length === 0) return 0;

    switch (method) {
      case 'sum':
        return usage.reduce((sum, u) => sum + u.value, 0);
      case 'max':
        return Math.max(...usage.map(u => u.value));
      case 'average':
        return usage.reduce((sum, u) => sum + u.value, 0) / usage.length;
      case 'unique_count':
        return new Set(usage.map(u => u.metadata.identifier || u.value)).size;
      default:
        return usage.reduce((sum, u) => sum + u.value, 0);
    }
  }

  private groupUsageByDay(usage: UsageMetric[], method: string): Record<string, number> {
    const dailyUsage: Record<string, UsageMetric[]> = {};
    
    for (const u of usage) {
      const day = u.timestamp.toISOString().split('T')[0];
      if (!dailyUsage[day]) dailyUsage[day] = [];
      dailyUsage[day].push(u);
    }

    const result: Record<string, number> = {};
    for (const [day, dayUsage] of Object.entries(dailyUsage)) {
      result[day] = this.aggregateUsage(dayUsage, method);
    }

    return result;
  }

  private calculateUsageTrend(dailyUsage: Record<string, number>): 'increasing' | 'decreasing' | 'stable' {
    const values = Object.values(dailyUsage);
    if (values.length < 2) return 'stable';

    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));

    const firstAvg = firstHalf.reduce((sum, v) => sum + v, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, v) => sum + v, 0) / secondHalf.length;

    const change = (secondAvg - firstAvg) / firstAvg;

    if (change > 0.1) return 'increasing';
    if (change < -0.1) return 'decreasing';
    return 'stable';
  }

  private calculateCostBreakdown(usage: number, tiers: UsageTier[], basePrice: number): any {
    const tier = this.findApplicableTier(usage, tiers);
    
    let baseAmount = basePrice;
    let usageAmount = 0;
    let overageAmount = 0;

    if (tier) {
      if (tier.flatFee) {
        baseAmount = tier.flatFee;
      } else {
        usageAmount = usage * tier.unitPrice;
      }

      if (tier.overage?.enabled && usage > tier.overage.threshold) {
        overageAmount = (usage - tier.overage.threshold) * tier.overage.rate;
      }
    }

    return {
      baseAmount,
      usageAmount,
      overageAmount,
      totalAmount: baseAmount + usageAmount + overageAmount,
    };
  }

  private findApplicableTier(usage: number, tiers: UsageTier[]): UsageTier | null {
    for (const tier of tiers) {
      if (usage >= tier.minUsage && (tier.maxUsage === null || usage <= tier.maxUsage)) {
        return tier;
      }
    }
    return tiers[tiers.length - 1] || null; // Return last tier if no match
  }

  private findOptimalTier(projectedUsage: number, tiers: UsageTier[]): UsageTier {
    let optimalTier = tiers[0];
    let lowestCost = this.calculateTierCost(tiers[0], projectedUsage);

    for (const tier of tiers) {
      const cost = this.calculateTierCost(tier, projectedUsage);
      if (cost < lowestCost) {
        lowestCost = cost;
        optimalTier = tier;
      }
    }

    return optimalTier;
  }

  private calculateTierCost(tier: UsageTier, usage: number): number {
    let cost = tier.flatFee || 0;
    
    if (!tier.flatFee) {
      cost += usage * tier.unitPrice;
    }

    if (tier.overage?.enabled && usage > tier.overage.threshold) {
      cost += (usage - tier.overage.threshold) * tier.overage.rate;
    }

    return cost;
  }

  private generateUsageRecommendations(metrics: any[], billingPlan: BillingPlan): any[] {
    const recommendations = [];

    for (const metric of metrics) {
      if (metric.costBreakdown.overageAmount > 0) {
        recommendations.push({
          type: 'tier_upgrade',
          description: `Consider upgrading your plan to avoid $${metric.costBreakdown.overageAmount.toFixed(2)} in overage charges for ${metric.metricType}`,
          potentialSavings: metric.costBreakdown.overageAmount,
          confidence: 0.9,
        });
      }

      if (metric.trend === 'decreasing' && metric.averageDaily < metric.totalUsage * 0.5) {
        recommendations.push({
          type: 'tier_downgrade',
          description: `Your ${metric.metricType} usage is decreasing. Consider downgrading to save costs`,
          potentialSavings: billingPlan.basePrice * 0.3,
          confidence: 0.7,
        });
      }
    }

    return recommendations;
  }

  private async checkOverageThresholds(tenantId: string, subscriptionId: string, metricType: string): Promise<void> {
    // Implementation for checking overage thresholds and sending alerts
    logger.info("Checking overage thresholds", { tenantId, subscriptionId, metricType });
  }

  private async updateUsageAggregates(tenantId: string, subscriptionId: string, metricType: string, value: number): Promise<void> {
    // Implementation for updating real-time usage aggregates
    logger.info("Updating usage aggregates", { tenantId, subscriptionId, metricType, value });
  }

  private async storeOverageBilling(tenantId: string, overageBilling: OverageBilling): Promise<void> {
    await query(`
      INSERT INTO overage_billing (
        subscription_id, tenant_id, billing_period_start, billing_period_end,
        overages, total_overage_amount, status, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `, [
      overageBilling.subscriptionId,
      tenantId,
      overageBilling.billingPeriod.start,
      overageBilling.billingPeriod.end,
      JSON.stringify(overageBilling.overages),
      overageBilling.totalOverageAmount,
      overageBilling.status,
      overageBilling.createdAt,
    ], tenantId);
  }

  private analyzeUsagePatterns(usage: UsageMetric[], plan: BillingPlan): any {
    // Mock implementation for analyzing usage patterns
    return {
      averageMonthlyUsage: { api_calls: 75000 },
      variability: 0.2,
      seasonality: false,
      growthRate: 0.05,
    };
  }

  private generateOptimizedPlan(currentPlan: BillingPlan, patterns: any): BillingPlan {
    // Mock implementation for generating optimized plan
    return { ...currentPlan, basePrice: currentPlan.basePrice * 0.9 };
  }

  private calculatePlanCost(plan: BillingPlan, usage: any): number {
    // Mock implementation for calculating plan cost
    return plan.basePrice + (usage.api_calls || 0) * 0.01;
  }

  private calculateOptimizationConfidence(patterns: any): number {
    // Mock implementation for calculating optimization confidence
    return patterns.variability < 0.3 ? 0.9 : 0.6;
  }

  private generatePlanOptimizationRecommendations(currentPlan: BillingPlan, optimizedPlan: BillingPlan, patterns: any): string[] {
    // Mock implementation for generating recommendations
    return [
      'Reduce base price based on consistent usage patterns',
      'Adjust overage thresholds to better match usage spikes',
    ];
  }
}

export { UsageBasedBillingService, type UsageMetric, type UsageAnalytics, type OverageBilling };
