// Demo Environment Service
// Specialized service for generating sales demonstration data and customer trial environments

import { logger } from "../utils/logger.ts";

export interface DemoCustomerProfile {
  companyName: string;
  industry: string;
  monthlyEvents: number;
  currentAnalyticsCost: number;
  teamSize: number;
  currentQueryTime: number;
  painPoints: string[];
  goals: string[];
}

export interface PerformanceComparison {
  competitor: string;
  queryResponseTime: number;
  eventProcessingDelay: number;
  realTimeCapability: boolean;
  dataAccuracy: number;
  costPerEvent: number;
  setupTime: number; // hours
}

export interface DemoScenario {
  id: string;
  name: string;
  description: string;
  customerProfile: DemoCustomerProfile;
  performanceMetrics: any;
  roiProjection: any;
  competitiveAdvantages: string[];
}

export interface IndustryROITemplate {
  id: string;
  name: string;
  industry: string;
  description: string;
  benchmarks: {
    averageAnalyticsCost: number;
    typicalEventVolume: number;
    averageTeamSize: number;
    commonPainPoints: string[];
    industrySpecificMetrics: Record<string, number>;
  };
  roiMultipliers: {
    costSavingsMultiplier: number;
    revenueImpactMultiplier: number;
    efficiencyGainMultiplier: number;
    timeToValueMultiplier: number;
  };
  caseStudyData: {
    customerName: string;
    beforeMetrics: Record<string, number>;
    afterMetrics: Record<string, number>;
    revenueImpact: number;
    implementationTime: number;
  };
}

export interface PerformanceBenchmark {
  competitor: string;
  category: string;
  metrics: {
    queryResponseTime: number;
    eventProcessingDelay: number;
    realTimeCapability: boolean;
    dataAccuracy: number;
    costPerEvent: number;
    setupTime: number;
    scalabilityLimit: number;
    uptimeGuarantee: number;
  };
  limitations: string[];
  strengths: string[];
}

export class DemoEnvironmentService {
  private demoScenarios: Map<string, DemoScenario> = new Map();
  private performanceBaselines: Map<string, PerformanceComparison> = new Map();
  private industryROITemplates: Map<string, IndustryROITemplate> = new Map();
  private performanceBenchmarks: Map<string, PerformanceBenchmark> = new Map();

  constructor() {
    this.initializeDemoData();
    this.initializeIndustryTemplates();
    this.initializePerformanceBenchmarks();
  }

  /**
   * Initialize demo scenarios and competitive baselines
   */
  private initializeDemoData(): void {
    // Competitive performance baselines
    this.performanceBaselines.set("google_analytics", {
      competitor: "Google Analytics",
      queryResponseTime: 2500,
      eventProcessingDelay: 24 * 60 * 60 * 1000, // 24 hours
      realTimeCapability: false,
      dataAccuracy: 85.2,
      costPerEvent: 0.0005,
      setupTime: 48,
    });

    this.performanceBaselines.set("mixpanel", {
      competitor: "Mixpanel",
      queryResponseTime: 1200,
      eventProcessingDelay: 2 * 60 * 60 * 1000, // 2 hours
      realTimeCapability: true,
      dataAccuracy: 92.1,
      costPerEvent: 0.0008,
      setupTime: 24,
    });

    this.performanceBaselines.set("adobe_analytics", {
      competitor: "Adobe Analytics",
      queryResponseTime: 3200,
      eventProcessingDelay: 4 * 60 * 60 * 1000, // 4 hours
      realTimeCapability: false,
      dataAccuracy: 88.7,
      costPerEvent: 0.0012,
      setupTime: 72,
    });

    this.performanceBaselines.set("our_platform", {
      competitor: "Our Platform",
      queryResponseTime: 8, // 6-11ms average
      eventProcessingDelay: 50, // 50ms
      realTimeCapability: true,
      dataAccuracy: 99.95,
      costPerEvent: 0.0002,
      setupTime: 0.25, // 15 minutes
    });

    // Demo scenarios
    this.createDemoScenarios();
  }

  /**
   * Initialize industry-specific ROI templates
   */
  private initializeIndustryTemplates(): void {
    // E-commerce industry template
    this.industryROITemplates.set("ecommerce", {
      id: "ecommerce",
      name: "E-commerce & Retail",
      industry: "E-commerce",
      description: "ROI template for e-commerce and online retail businesses",
      benchmarks: {
        averageAnalyticsCost: 3500,
        typicalEventVolume: 750000,
        averageTeamSize: 8,
        commonPainPoints: [
          "Delayed conversion insights",
          "High customer acquisition costs",
          "Limited real-time personalization",
          "Complex multi-channel attribution"
        ],
        industrySpecificMetrics: {
          conversionRate: 2.8,
          averageOrderValue: 85,
          customerLifetimeValue: 245,
          cartAbandonmentRate: 69.8,
          returnCustomerRate: 27.5
        }
      },
      roiMultipliers: {
        costSavingsMultiplier: 1.2,
        revenueImpactMultiplier: 1.8,
        efficiencyGainMultiplier: 1.5,
        timeToValueMultiplier: 2.0
      },
      caseStudyData: {
        customerName: "TechGear Plus",
        beforeMetrics: {
          conversionRate: 2.1,
          averageOrderValue: 78,
          customerAcquisitionCost: 45,
          timeToInsight: 24
        },
        afterMetrics: {
          conversionRate: 2.9,
          averageOrderValue: 95,
          customerAcquisitionCost: 32,
          timeToInsight: 0.25
        },
        revenueImpact: 285000,
        implementationTime: 0.25
      }
    });

    // SaaS industry template
    this.industryROITemplates.set("saas", {
      id: "saas",
      name: "SaaS & Technology",
      industry: "SaaS",
      description: "ROI template for SaaS and technology companies",
      benchmarks: {
        averageAnalyticsCost: 8500,
        typicalEventVolume: 2500000,
        averageTeamSize: 15,
        commonPainPoints: [
          "Complex user journey tracking",
          "High churn rates",
          "Limited product usage insights",
          "Expensive analytics infrastructure"
        ],
        industrySpecificMetrics: {
          monthlyChurnRate: 5.2,
          customerLifetimeValue: 1850,
          timeToValue: 14,
          featureAdoptionRate: 35.7,
          netRevenueRetention: 108
        }
      },
      roiMultipliers: {
        costSavingsMultiplier: 1.5,
        revenueImpactMultiplier: 2.2,
        efficiencyGainMultiplier: 1.8,
        timeToValueMultiplier: 2.5
      },
      caseStudyData: {
        customerName: "CloudTech Solutions",
        beforeMetrics: {
          monthlyChurnRate: 6.8,
          customerLifetimeValue: 1420,
          timeToValue: 21,
          featureAdoptionRate: 28.3
        },
        afterMetrics: {
          monthlyChurnRate: 3.9,
          customerLifetimeValue: 2180,
          timeToValue: 8,
          featureAdoptionRate: 47.2
        },
        revenueImpact: 750000,
        implementationTime: 0.5
      }
    });

    // Enterprise retail template
    this.industryROITemplates.set("enterprise_retail", {
      id: "enterprise_retail",
      name: "Enterprise Retail",
      industry: "Retail",
      description: "ROI template for large enterprise retail chains",
      benchmarks: {
        averageAnalyticsCost: 25000,
        typicalEventVolume: 15000000,
        averageTeamSize: 35,
        commonPainPoints: [
          "Siloed channel analytics",
          "Slow inventory optimization",
          "Limited cross-channel insights",
          "High infrastructure costs"
        ],
        industrySpecificMetrics: {
          inventoryTurnover: 6.2,
          grossMargin: 42.5,
          customerRetentionRate: 68.3,
          crossChannelConversion: 12.8,
          supplyChainEfficiency: 78.5
        }
      },
      roiMultipliers: {
        costSavingsMultiplier: 1.8,
        revenueImpactMultiplier: 2.5,
        efficiencyGainMultiplier: 2.0,
        timeToValueMultiplier: 1.5
      },
      caseStudyData: {
        customerName: "MegaRetail Corp",
        beforeMetrics: {
          inventoryTurnover: 4.8,
          grossMargin: 38.2,
          customerRetentionRate: 61.5,
          crossChannelConversion: 8.9
        },
        afterMetrics: {
          inventoryTurnover: 7.1,
          grossMargin: 45.8,
          customerRetentionRate: 74.2,
          crossChannelConversion: 16.7
        },
        revenueImpact: 2850000,
        implementationTime: 1.0
      }
    });
  }

  /**
   * Initialize comprehensive performance benchmarks
   */
  private initializePerformanceBenchmarks(): void {
    // Google Analytics benchmark
    this.performanceBenchmarks.set("google_analytics", {
      competitor: "Google Analytics",
      category: "Web Analytics",
      metrics: {
        queryResponseTime: 2500,
        eventProcessingDelay: 24 * 60 * 60 * 1000, // 24 hours
        realTimeCapability: false,
        dataAccuracy: 85.2,
        costPerEvent: 0.0005,
        setupTime: 48,
        scalabilityLimit: 10000000, // 10M events/month
        uptimeGuarantee: 99.5
      },
      limitations: [
        "24-hour data processing delay",
        "Limited real-time capabilities",
        "Sampling at high volumes",
        "Complex custom event setup",
        "Limited data export options"
      ],
      strengths: [
        "Free tier available",
        "Wide integration ecosystem",
        "Familiar interface",
        "Google Ads integration"
      ]
    });

    // Mixpanel benchmark
    this.performanceBenchmarks.set("mixpanel", {
      competitor: "Mixpanel",
      category: "Product Analytics",
      metrics: {
        queryResponseTime: 1200,
        eventProcessingDelay: 2 * 60 * 60 * 1000, // 2 hours
        realTimeCapability: true,
        dataAccuracy: 92.1,
        costPerEvent: 0.0008,
        setupTime: 24,
        scalabilityLimit: 100000000, // 100M events/month
        uptimeGuarantee: 99.9
      },
      limitations: [
        "High cost at scale",
        "Complex pricing structure",
        "Limited historical data",
        "Steep learning curve",
        "Query performance degrades with volume"
      ],
      strengths: [
        "Strong cohort analysis",
        "Good funnel visualization",
        "Real-time capabilities",
        "Advanced segmentation"
      ]
    });

    // Adobe Analytics benchmark
    this.performanceBenchmarks.set("adobe_analytics", {
      competitor: "Adobe Analytics",
      category: "Enterprise Analytics",
      metrics: {
        queryResponseTime: 3200,
        eventProcessingDelay: 4 * 60 * 60 * 1000, // 4 hours
        realTimeCapability: false,
        dataAccuracy: 88.7,
        costPerEvent: 0.0012,
        setupTime: 72,
        scalabilityLimit: 1000000000, // 1B events/month
        uptimeGuarantee: 99.9
      },
      limitations: [
        "Very expensive licensing",
        "Complex implementation",
        "Slow query performance",
        "Limited real-time features",
        "Requires extensive training"
      ],
      strengths: [
        "Enterprise-grade features",
        "Advanced attribution",
        "Comprehensive reporting",
        "Adobe ecosystem integration"
      ]
    });

    // Our Platform benchmark
    this.performanceBenchmarks.set("our_platform", {
      competitor: "Our Platform",
      category: "Real-time Analytics",
      metrics: {
        queryResponseTime: 8,
        eventProcessingDelay: 50, // 50ms
        realTimeCapability: true,
        dataAccuracy: 99.95,
        costPerEvent: 0.0002,
        setupTime: 0.25, // 15 minutes
        scalabilityLimit: 10000000000, // 10B events/month
        uptimeGuarantee: 99.97
      },
      limitations: [
        "Newer platform (less ecosystem)",
        "Requires modern infrastructure"
      ],
      strengths: [
        "Industry-leading performance",
        "Real-time processing",
        "Cost-effective scaling",
        "15-minute setup",
        "99.95% data accuracy",
        "Advanced ML capabilities",
        "Multi-tenant architecture"
      ]
    });
  }

  /**
   * Create predefined demo scenarios for different customer types
   */
  private createDemoScenarios(): void {
    // E-commerce SMB scenario
    this.demoScenarios.set("ecommerce_smb", {
      id: "ecommerce_smb",
      name: "E-commerce SMB Growth",
      description: "Small to medium e-commerce business looking to optimize conversion rates",
      customerProfile: {
        companyName: "TechGear Plus",
        industry: "E-commerce Electronics",
        monthlyEvents: 500000,
        currentAnalyticsCost: 2500,
        teamSize: 5,
        currentQueryTime: 2500,
        painPoints: [
          "Delayed insights affecting marketing decisions",
          "High analytics costs eating into margins",
          "Complex setup requiring technical expertise",
          "Limited real-time capabilities"
        ],
        goals: [
          "Reduce customer acquisition cost by 25%",
          "Improve conversion rates by 15%",
          "Get real-time insights for marketing optimization",
          "Reduce analytics costs while improving capabilities"
        ]
      },
      performanceMetrics: {},
      roiProjection: {},
      competitiveAdvantages: [
        "97% faster query response times",
        "Real-time event processing vs 24-hour delays",
        "60% cost reduction with better features",
        "15-minute setup vs 48-hour implementation"
      ]
    });

    // Enterprise scenario
    this.demoScenarios.set("enterprise_retail", {
      id: "enterprise_retail",
      name: "Enterprise Retail Chain",
      description: "Large retail chain with multiple channels and complex analytics needs",
      customerProfile: {
        companyName: "MegaRetail Corp",
        industry: "Multi-channel Retail",
        monthlyEvents: 10000000,
        currentAnalyticsCost: 25000,
        teamSize: 25,
        currentQueryTime: 3200,
        painPoints: [
          "Siloed analytics across channels",
          "Expensive enterprise analytics licenses",
          "Slow query performance affecting operations",
          "Limited predictive capabilities"
        ],
        goals: [
          "Unify analytics across all channels",
          "Implement predictive analytics for inventory",
          "Reduce analytics infrastructure costs",
          "Enable real-time personalization"
        ]
      },
      performanceMetrics: {},
      roiProjection: {},
      competitiveAdvantages: [
        "Unified multi-tenant architecture",
        "Advanced ML-powered predictions",
        "Enterprise-grade security and compliance",
        "Massive scale with consistent performance"
      ]
    });
  }

  /**
   * Generate comprehensive demo dashboard data
   */
  async generateDemoDashboard(scenarioId?: string): Promise<any> {
    const startTime = performance.now();
    
    const scenario = scenarioId ? this.demoScenarios.get(scenarioId) : 
      this.demoScenarios.get("ecommerce_smb");
    
    if (!scenario) {
      throw new Error(`Demo scenario not found: ${scenarioId}`);
    }

    // Generate real-time performance metrics
    const performanceMetrics = {
      eventIngestionRate: 24390 + Math.floor(Math.random() * 1000), // 24,390+ events/sec
      queryResponseTime: Math.random() * 5 + 6, // 6-11ms range
      systemUptime: 99.97 + Math.random() * 0.02,
      dataAccuracy: 99.95 + Math.random() * 0.04,
      compressionRatio: 70 + Math.random() * 5, // 70-75%
      concurrentUsers: Math.floor(Math.random() * 500) + 1500,
      activeConnections: Math.floor(Math.random() * 200) + 800,
      memoryUsage: Math.random() * 20 + 45, // 45-65%
      cpuUsage: Math.random() * 15 + 25, // 25-40%
    };

    // Generate competitive comparison
    const competitiveComparison = Array.from(this.performanceBaselines.values()).map(baseline => ({
      ...baseline,
      performanceAdvantage: baseline.competitor === "Our Platform" ? 0 : 
        ((baseline.queryResponseTime - 8) / baseline.queryResponseTime * 100),
      costAdvantage: baseline.competitor === "Our Platform" ? 0 :
        ((baseline.costPerEvent - 0.0002) / baseline.costPerEvent * 100),
    }));

    // Generate revenue impact projections
    const revenueImpact = this.calculateRevenueImpact(scenario.customerProfile);

    const queryTime = performance.now() - startTime;

    const demoData = {
      scenario,
      performanceMetrics,
      competitiveComparison,
      revenueImpact,
      realTimeMetrics: this.generateRealTimeMetrics(),
      metadata: {
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
      },
    };

    logger.info("Demo dashboard generated", {
      scenarioId: scenario.id,
      queryTime: `${queryTime.toFixed(2)}ms`,
      eventIngestionRate: performanceMetrics.eventIngestionRate,
    });

    return demoData;
  }

  /**
   * Calculate revenue impact for customer profile with industry template
   */
  private calculateRevenueImpact(profile: DemoCustomerProfile, industryTemplate?: IndustryROITemplate | null): Record<string, unknown> {
    // Apply industry-specific multipliers if template is available
    const multipliers = industryTemplate?.roiMultipliers || {
      costSavingsMultiplier: 1.0,
      revenueImpactMultiplier: 1.0,
      efficiencyGainMultiplier: 1.0,
      timeToValueMultiplier: 1.0
    };

    const currentCosts = {
      analyticsTools: profile.currentAnalyticsCost,
      developerTime: profile.teamSize * 2000, // $2000/month per developer
      infrastructureCosts: profile.monthlyEvents * 0.0005,
      opportunityCost: profile.currentAnalyticsCost * 0.3, // 30% opportunity cost
    };

    const projectedSavings = {
      analyticsToolReplacement: currentCosts.analyticsTools * 0.6 * multipliers.costSavingsMultiplier,
      developerTimeValue: currentCosts.developerTime * 0.4 * multipliers.efficiencyGainMultiplier,
      infrastructureSavings: profile.monthlyEvents * 0.0003 * multipliers.costSavingsMultiplier,
      fasterDecisionMaking: currentCosts.opportunityCost * 0.8 * multipliers.timeToValueMultiplier,
    };

    const revenueGains = {
      conversionImprovement: profile.currentAnalyticsCost * 0.25 * multipliers.revenueImpactMultiplier,
      churnReduction: profile.currentAnalyticsCost * 0.15 * multipliers.revenueImpactMultiplier,
      newInsightsValue: profile.teamSize * 1500 * multipliers.revenueImpactMultiplier,
      realTimeOptimization: profile.monthlyEvents * 0.0001 * multipliers.efficiencyGainMultiplier,
    };

    const totalMonthlySavings = Object.values(projectedSavings).reduce((a, b) => a + b, 0);
    const totalMonthlyRevenue = Object.values(revenueGains).reduce((a, b) => a + b, 0);
    const totalMonthlyValue = totalMonthlySavings + totalMonthlyRevenue;

    return {
      currentCosts,
      projectedSavings,
      revenueGains,
      industryMultipliers: multipliers,
      summary: {
        totalMonthlySavings: Math.round(totalMonthlySavings),
        totalMonthlyRevenue: Math.round(totalMonthlyRevenue),
        totalMonthlyValue: Math.round(totalMonthlyValue),
        annualValue: Math.round(totalMonthlyValue * 12),
        roiPercentage: Math.round((totalMonthlyValue / profile.currentAnalyticsCost) * 100),
        paybackPeriod: Math.ceil(profile.currentAnalyticsCost / totalMonthlyValue), // months
      },
    };
  }

  /**
   * Generate real-time metrics for live demo
   */
  private generateRealTimeMetrics(): Array<Record<string, unknown>> {
    const now = Date.now();
    const metrics = [];
    
    for (let i = 0; i < 60; i++) {
      metrics.push({
        timestamp: new Date(now - (i * 1000)),
        eventsPerSecond: 24390 + Math.floor(Math.random() * 1000),
        queryResponseTime: Math.random() * 5 + 6,
        activeUsers: Math.floor(Math.random() * 100) + 1500,
        revenue: Math.random() * 1000 + 500,
      });
    }
    
    return metrics.reverse(); // Most recent first
  }

  /**
   * Get available demo scenarios
   */
  getDemoScenarios(): DemoScenario[] {
    return Array.from(this.demoScenarios.values());
  }

  /**
   * Get specific demo scenario
   */
  getDemoScenario(scenarioId: string): DemoScenario | undefined {
    return this.demoScenarios.get(scenarioId);
  }

  /**
   * Generate custom ROI calculation with industry-specific templates
   */
  calculateCustomROI(customerData: Record<string, unknown>): Record<string, unknown> {
    const startTime = performance.now();

    const profile: DemoCustomerProfile = {
      companyName: (customerData.companyName as string) || "Demo Company",
      industry: (customerData.industry as string) || "E-commerce",
      monthlyEvents: (customerData.monthlyEvents as number) || 1000000,
      currentAnalyticsCost: (customerData.currentAnalyticsCost as number) || 5000,
      teamSize: (customerData.teamSize as number) || 10,
      currentQueryTime: (customerData.currentQueryTime as number) || 2500,
      painPoints: (customerData.painPoints as string[]) || [],
      goals: (customerData.goals as string[]) || [],
    };

    // Get industry-specific template
    const industryTemplate = this.getIndustryTemplate(profile.industry.toLowerCase());
    const revenueImpact = this.calculateRevenueImpact(profile, industryTemplate);
    const competitiveComparison = Array.from(this.performanceBenchmarks.values());

    const queryTime = performance.now() - startTime;

    logger.info("Custom ROI calculation generated", {
      queryTime: `${queryTime.toFixed(2)}ms`,
      monthlyEvents: profile.monthlyEvents,
      industry: profile.industry,
    });

    return {
      customerProfile: profile,
      industryTemplate,
      revenueImpact,
      competitiveComparison,
      metadata: {
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
      },
    };
  }

  /**
   * Get industry-specific ROI template
   */
  getIndustryTemplate(industry: string): IndustryROITemplate | null {
    const normalizedIndustry = industry.toLowerCase().replace(/[^a-z]/g, '_');
    return this.industryROITemplates.get(normalizedIndustry) ||
           this.industryROITemplates.get("ecommerce") || null;
  }

  /**
   * Get all available industry templates
   */
  getIndustryTemplates(): IndustryROITemplate[] {
    return Array.from(this.industryROITemplates.values());
  }

  /**
   * Get comprehensive performance benchmarks
   */
  getPerformanceBenchmarks(): PerformanceBenchmark[] {
    return Array.from(this.performanceBenchmarks.values());
  }

  /**
   * Generate performance comparison report
   */
  generatePerformanceComparison(includeCompetitors?: string[]): Record<string, unknown> {
    const startTime = performance.now();

    const competitors = includeCompetitors || ["google_analytics", "mixpanel", "adobe_analytics"];
    const benchmarks = competitors.map(id => this.performanceBenchmarks.get(id)).filter(Boolean);
    const ourPlatform = this.performanceBenchmarks.get("our_platform");

    if (!ourPlatform) {
      throw new Error("Our platform benchmark not found");
    }

    const comparison = benchmarks.map(competitor => {
      if (!competitor) return null;

      return {
        competitor: competitor.competitor,
        category: competitor.category,
        performanceAdvantages: {
          querySpeedImprovement: ((competitor.metrics.queryResponseTime - ourPlatform.metrics.queryResponseTime) / competitor.metrics.queryResponseTime * 100).toFixed(1),
          processingSpeedImprovement: ((competitor.metrics.eventProcessingDelay - ourPlatform.metrics.eventProcessingDelay) / competitor.metrics.eventProcessingDelay * 100).toFixed(1),
          costAdvantage: ((competitor.metrics.costPerEvent - ourPlatform.metrics.costPerEvent) / competitor.metrics.costPerEvent * 100).toFixed(1),
          setupTimeAdvantage: ((competitor.metrics.setupTime - ourPlatform.metrics.setupTime) / competitor.metrics.setupTime * 100).toFixed(1),
          accuracyImprovement: (ourPlatform.metrics.dataAccuracy - competitor.metrics.dataAccuracy).toFixed(2),
          uptimeImprovement: (ourPlatform.metrics.uptimeGuarantee - competitor.metrics.uptimeGuarantee).toFixed(2)
        },
        limitations: competitor.limitations,
        strengths: competitor.strengths
      };
    }).filter(Boolean);

    const queryTime = performance.now() - startTime;

    return {
      ourPlatform: {
        name: ourPlatform.competitor,
        metrics: ourPlatform.metrics,
        strengths: ourPlatform.strengths
      },
      competitorComparison: comparison,
      summary: {
        averageQuerySpeedImprovement: comparison.reduce((avg, comp) => avg + parseFloat(comp!.performanceAdvantages.querySpeedImprovement), 0) / comparison.length,
        averageCostAdvantage: comparison.reduce((avg, comp) => avg + parseFloat(comp!.performanceAdvantages.costAdvantage), 0) / comparison.length,
        competitorsAnalyzed: comparison.length
      },
      metadata: {
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
      }
    };
  }
}
