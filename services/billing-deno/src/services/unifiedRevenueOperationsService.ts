// Unified Revenue Operations Service
// Comprehensive integration of subscription management, revenue intelligence, usage billing, and financial reporting

import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { SubscriptionLifecycleService } from "./subscriptionLifecycleService.ts";
import { UsageBasedBillingService } from "./usageBasedBillingService.ts";
import { RevenueRecognitionService } from "./revenueRecognitionService.ts";
import { FinancialReportingService } from "./financialReportingService.ts";
import { RevenueIntelligenceService } from "./revenueIntelligenceService.ts";
import { EnhancedSubscriptionService } from "./enhancedSubscriptionService.ts";

// Unified revenue operations types
interface UnifiedSubscriptionData {
  subscription: any;
  usageAnalytics: any;
  churnPrediction: any;
  expansionOpportunities: any[];
  revenueIntelligence: any;
  financialMetrics: any;
  healthScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

interface RevenueOperationsDashboard {
  summary: {
    totalSubscriptions: number;
    totalMRR: number;
    totalARR: number;
    averageHealthScore: number;
    churnRate: number;
    expansionRate: number;
    totalExpansionOpportunities: number;
    totalRisks: number;
    potentialExpansionRevenue: number;
    atRiskRevenue: number;
  };
  subscriptions: UnifiedSubscriptionData[];
  insights: {
    topExpansionOpportunities: any[];
    criticalRisks: any[];
    revenueRecommendations: any[];
    performanceAlerts: any[];
  };
  trends: {
    mrrGrowth: number[];
    churnTrend: number[];
    expansionTrend: number[];
    healthScoreTrend: number[];
  };
}

interface AutomatedWorkflow {
  id: string;
  name: string;
  trigger: {
    type: 'subscription_event' | 'usage_threshold' | 'churn_risk' | 'expansion_opportunity' | 'revenue_milestone';
    conditions: Record<string, any>;
  };
  actions: Array<{
    type: 'email_notification' | 'webhook' | 'subscription_update' | 'billing_action' | 'sales_alert';
    config: Record<string, any>;
    delay?: number;
  }>;
  isActive: boolean;
}

interface RealtimeAlert {
  id: string;
  type: 'churn_risk' | 'expansion_opportunity' | 'payment_failure' | 'usage_spike' | 'revenue_milestone';
  severity: 'low' | 'medium' | 'high' | 'critical';
  subscriptionId: string;
  customerId: string;
  message: string;
  data: Record<string, any>;
  createdAt: Date;
  acknowledged: boolean;
}

export class UnifiedRevenueOperationsService {
  private subscriptionService: SubscriptionLifecycleService;
  private usageService: UsageBasedBillingService;
  private revenueService: RevenueRecognitionService;
  private reportingService: FinancialReportingService;
  private intelligenceService: RevenueIntelligenceService;
  private enhancedService: EnhancedSubscriptionService;

  constructor() {
    this.subscriptionService = new SubscriptionLifecycleService();
    this.usageService = new UsageBasedBillingService();
    this.revenueService = new RevenueRecognitionService();
    this.reportingService = new FinancialReportingService();
    this.intelligenceService = new RevenueIntelligenceService();
    this.enhancedService = new EnhancedSubscriptionService();
  }

  /**
   * Get comprehensive subscription data with all revenue operations insights
   */
  async getUnifiedSubscriptionData(tenantId: string, subscriptionId: string): Promise<UnifiedSubscriptionData> {
    const startTime = performance.now();

    try {
      // Get core subscription data
      const subscription = await this.getSubscriptionDetails(tenantId, subscriptionId);

      // Get usage analytics
      const usageAnalytics = await this.usageService.getUsageAnalytics(tenantId, subscriptionId, {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end: new Date(),
      });

      // Get churn prediction
      const churnPrediction = await this.subscriptionService.predictChurnRisk(tenantId, subscriptionId);

      // Get expansion opportunities
      const expansionOpportunities = await this.getExpansionOpportunities(tenantId, subscriptionId);

      // Get revenue intelligence
      const revenueIntelligence = await this.enhancedService.getRevenueIntelligenceInsights(tenantId, subscriptionId);

      // Get financial metrics
      const financialMetrics = await this.getSubscriptionFinancialMetrics(tenantId, subscriptionId);

      // Calculate unified health score
      const healthScore = this.calculateUnifiedHealthScore({
        subscription,
        usageAnalytics,
        churnPrediction,
        financialMetrics,
      });

      // Determine risk level
      const riskLevel = this.determineRiskLevel(churnPrediction, healthScore);

      const unifiedData: UnifiedSubscriptionData = {
        subscription,
        usageAnalytics,
        churnPrediction,
        expansionOpportunities,
        revenueIntelligence,
        financialMetrics,
        healthScore,
        riskLevel,
      };

      const queryTime = performance.now() - startTime;

      logger.info("Unified subscription data retrieved", {
        tenantId,
        subscriptionId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        healthScore,
        riskLevel,
        expansionOpportunities: expansionOpportunities.length,
      });

      return unifiedData;
    } catch (error) {
      logger.error("Failed to get unified subscription data", {
        tenantId,
        subscriptionId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Generate comprehensive revenue operations dashboard
   */
  async generateRevenueOperationsDashboard(tenantId: string): Promise<RevenueOperationsDashboard> {
    const startTime = performance.now();

    try {
      // Get all active subscriptions
      const subscriptions = await this.getActiveSubscriptions(tenantId);

      // Get unified data for each subscription (limit to 50 for performance)
      const unifiedSubscriptions = await Promise.all(
        subscriptions.slice(0, 50).map(async (sub) => {
          try {
            return await this.getUnifiedSubscriptionData(tenantId, sub.id);
          } catch (error) {
            logger.warn("Failed to get unified data for subscription", {
              subscriptionId: sub.id,
              error: (error as Error).message,
            });
            return null;
          }
        })
      );

      const validSubscriptions = unifiedSubscriptions.filter(Boolean) as UnifiedSubscriptionData[];

      // Calculate summary metrics
      const summary = this.calculateDashboardSummary(validSubscriptions);

      // Generate insights
      const insights = this.generateDashboardInsights(validSubscriptions);

      // Calculate trends
      const trends = await this.calculateRevenueTrends(tenantId);

      const dashboard: RevenueOperationsDashboard = {
        summary,
        subscriptions: validSubscriptions,
        insights,
        trends,
      };

      const queryTime = performance.now() - startTime;

      logger.info("Revenue operations dashboard generated", {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        totalSubscriptions: summary.totalSubscriptions,
        totalMRR: summary.totalMRR,
        averageHealthScore: summary.averageHealthScore,
      });

      return dashboard;
    } catch (error) {
      logger.error("Failed to generate revenue operations dashboard", {
        tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Process subscription event and trigger automated workflows
   */
  async processSubscriptionEvent(
    tenantId: string,
    subscriptionId: string,
    eventType: string,
    eventData: Record<string, any>
  ): Promise<void> {
    try {
      // Record the event
      await this.recordSubscriptionEvent(tenantId, subscriptionId, eventType, eventData);

      // Get applicable workflows
      const workflows = await this.getApplicableWorkflows(tenantId, eventType, eventData);

      // Execute workflows
      for (const workflow of workflows) {
        await this.executeWorkflow(tenantId, subscriptionId, workflow, eventData);
      }

      // Check for real-time alerts
      await this.checkRealtimeAlerts(tenantId, subscriptionId, eventType, eventData);

      // Update revenue recognition if needed
      if (this.isRevenueEvent(eventType)) {
        await this.processRevenueEvent(tenantId, subscriptionId, eventType, eventData);
      }

      logger.info("Subscription event processed", {
        tenantId,
        subscriptionId,
        eventType,
        workflowsTriggered: workflows.length,
      });
    } catch (error) {
      logger.error("Failed to process subscription event", {
        tenantId,
        subscriptionId,
        eventType,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Monitor subscription health and generate alerts
   */
  async monitorSubscriptionHealth(tenantId: string): Promise<RealtimeAlert[]> {
    const alerts: RealtimeAlert[] = [];

    try {
      // Get all active subscriptions
      const subscriptions = await this.getActiveSubscriptions(tenantId);

      for (const subscription of subscriptions) {
        // Check churn risk
        const churnPrediction = await this.subscriptionService.predictChurnRisk(tenantId, subscription.id);
        if (churnPrediction.riskLevel === 'critical' || churnPrediction.riskLevel === 'high') {
          alerts.push({
            id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: 'churn_risk',
            severity: churnPrediction.riskLevel === 'critical' ? 'critical' : 'high',
            subscriptionId: subscription.id,
            customerId: subscription.customerId,
            message: `High churn risk detected: ${(churnPrediction.churnProbability * 100).toFixed(1)}% probability`,
            data: {
              churnProbability: churnPrediction.churnProbability,
              factors: churnPrediction.factors,
              recommendations: churnPrediction.recommendations,
            },
            createdAt: new Date(),
            acknowledged: false,
          });
        }

        // Check expansion opportunities
        const expansionOpportunities = await this.getExpansionOpportunities(tenantId, subscription.id);
        const highValueOpportunities = expansionOpportunities.filter(o => o.potentialValue > 1000 && o.probability > 0.7);
        
        if (highValueOpportunities.length > 0) {
          alerts.push({
            id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: 'expansion_opportunity',
            severity: 'medium',
            subscriptionId: subscription.id,
            customerId: subscription.customerId,
            message: `${highValueOpportunities.length} high-value expansion opportunities detected`,
            data: {
              opportunities: highValueOpportunities,
              totalPotentialValue: highValueOpportunities.reduce((sum, o) => sum + o.potentialValue, 0),
            },
            createdAt: new Date(),
            acknowledged: false,
          });
        }

        // Check usage spikes
        const usageAnalytics = await this.usageService.getUsageAnalytics(tenantId, subscription.id, {
          start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          end: new Date(),
        });

        for (const metric of usageAnalytics.metrics) {
          if (metric.trend === 'increasing' && metric.projectedMonthly > metric.totalUsage * 2) {
            alerts.push({
              id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              type: 'usage_spike',
              severity: 'medium',
              subscriptionId: subscription.id,
              customerId: subscription.customerId,
              message: `Usage spike detected for ${metric.metricType}: ${metric.projectedMonthly.toFixed(0)} projected`,
              data: {
                metricType: metric.metricType,
                currentUsage: metric.totalUsage,
                projectedUsage: metric.projectedMonthly,
                trend: metric.trend,
              },
              createdAt: new Date(),
              acknowledged: false,
            });
          }
        }
      }

      logger.info("Subscription health monitoring completed", {
        tenantId,
        subscriptionsMonitored: subscriptions.length,
        alertsGenerated: alerts.length,
      });

      return alerts;
    } catch (error) {
      logger.error("Failed to monitor subscription health", {
        tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Optimize subscription pricing based on comprehensive data
   */
  async optimizeSubscriptionPricing(tenantId: string, subscriptionId: string): Promise<{
    currentPricing: any;
    optimizedPricing: any;
    expectedImpact: {
      revenueChange: number;
      churnRiskChange: number;
      expansionProbabilityChange: number;
    };
    recommendations: string[];
  }> {
    try {
      // Get unified subscription data
      const unifiedData = await this.getUnifiedSubscriptionData(tenantId, subscriptionId);

      // Get current pricing
      const currentPricing = await this.getCurrentPricing(tenantId, subscriptionId);

      // Generate dynamic pricing recommendation
      const dynamicPricing = await this.enhancedService.generateDynamicPricing({
        tenantId,
        customerId: unifiedData.subscription.customerId,
        planId: unifiedData.subscription.planId,
        usageMetrics: this.extractUsageMetrics(unifiedData.usageAnalytics),
        customerProfile: {
          lifetimeValue: unifiedData.financialMetrics.lifetimeValue || 5000,
          churnRisk: unifiedData.churnPrediction.churnProbability,
          expansionProbability: unifiedData.expansionOpportunities.length > 0 ? 0.7 : 0.3,
          paymentReliability: 0.95, // Default high reliability
        },
      });

      // Calculate expected impact
      const expectedImpact = {
        revenueChange: dynamicPricing.recommendedPrice - currentPricing.amount,
        churnRiskChange: dynamicPricing.pricingStrategy === 'retention_focused' ? -0.1 : 0.05,
        expansionProbabilityChange: dynamicPricing.pricingStrategy === 'expansion_focused' ? 0.15 : 0,
      };

      // Generate recommendations
      const recommendations = this.generatePricingRecommendations(
        unifiedData,
        currentPricing,
        dynamicPricing,
        expectedImpact
      );

      logger.info("Subscription pricing optimized", {
        tenantId,
        subscriptionId,
        currentPrice: currentPricing.amount,
        optimizedPrice: dynamicPricing.recommendedPrice,
        strategy: dynamicPricing.pricingStrategy,
        expectedRevenueChange: expectedImpact.revenueChange,
      });

      return {
        currentPricing,
        optimizedPricing: dynamicPricing,
        expectedImpact,
        recommendations,
      };
    } catch (error) {
      logger.error("Failed to optimize subscription pricing", {
        tenantId,
        subscriptionId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  // Helper methods
  private async getSubscriptionDetails(tenantId: string, subscriptionId: string): Promise<any> {
    // Mock implementation - in production, this would query the database
    return {
      id: subscriptionId,
      tenantId,
      customerId: `customer_${subscriptionId}`,
      planId: 'professional',
      status: 'active',
      mrr: 1499,
      createdAt: new Date(),
    };
  }

  private async getActiveSubscriptions(tenantId: string): Promise<any[]> {
    // Mock implementation
    return Array.from({ length: 25 }, (_, i) => ({
      id: `sub_${i}`,
      customerId: `customer_${i}`,
      planId: 'professional',
      status: 'active',
      mrr: 1499,
    }));
  }

  private async getExpansionOpportunities(tenantId: string, subscriptionId: string): Promise<any[]> {
    // Mock implementation
    return [
      {
        type: 'upsell',
        potentialValue: 2000,
        probability: 0.8,
        timeframe: 30,
        description: 'Upgrade to enterprise plan',
      },
    ];
  }

  private async getSubscriptionFinancialMetrics(tenantId: string, subscriptionId: string): Promise<any> {
    // Mock implementation
    return {
      mrr: 1499,
      arr: 17988,
      lifetimeValue: 8500,
      acquisitionCost: 1200,
      paybackPeriod: 3.2,
    };
  }

  private calculateUnifiedHealthScore(data: any): number {
    // Weighted health score calculation
    let score = 100;

    // Churn risk impact (40% weight)
    score -= data.churnPrediction.churnProbability * 40;

    // Usage trend impact (30% weight)
    const usageTrend = data.usageAnalytics.metrics[0]?.trend || 'stable';
    if (usageTrend === 'decreasing') score -= 20;
    else if (usageTrend === 'increasing') score += 10;

    // Financial health impact (30% weight)
    const ltv_cac_ratio = data.financialMetrics.lifetimeValue / data.financialMetrics.acquisitionCost;
    if (ltv_cac_ratio > 3) score += 15;
    else if (ltv_cac_ratio < 2) score -= 15;

    return Math.max(0, Math.min(100, score));
  }

  private determineRiskLevel(churnPrediction: any, healthScore: number): 'low' | 'medium' | 'high' | 'critical' {
    if (churnPrediction.churnProbability > 0.7 || healthScore < 30) return 'critical';
    if (churnPrediction.churnProbability > 0.5 || healthScore < 50) return 'high';
    if (churnPrediction.churnProbability > 0.3 || healthScore < 70) return 'medium';
    return 'low';
  }

  private calculateDashboardSummary(subscriptions: UnifiedSubscriptionData[]): RevenueOperationsDashboard['summary'] {
    const totalMRR = subscriptions.reduce((sum, s) => sum + (s.subscription.mrr || 0), 0);
    const totalExpansionOpportunities = subscriptions.reduce((sum, s) => sum + s.expansionOpportunities.length, 0);
    const potentialExpansionRevenue = subscriptions.reduce((sum, s) => 
      sum + s.expansionOpportunities.reduce((oSum, o) => oSum + o.potentialValue, 0), 0
    );
    const atRiskRevenue = subscriptions
      .filter(s => s.riskLevel === 'high' || s.riskLevel === 'critical')
      .reduce((sum, s) => sum + (s.subscription.mrr || 0), 0);

    return {
      totalSubscriptions: subscriptions.length,
      totalMRR,
      totalARR: totalMRR * 12,
      averageHealthScore: subscriptions.reduce((sum, s) => sum + s.healthScore, 0) / subscriptions.length,
      churnRate: subscriptions.filter(s => s.riskLevel === 'critical').length / subscriptions.length,
      expansionRate: subscriptions.filter(s => s.expansionOpportunities.length > 0).length / subscriptions.length,
      totalExpansionOpportunities,
      totalRisks: subscriptions.filter(s => s.riskLevel === 'high' || s.riskLevel === 'critical').length,
      potentialExpansionRevenue,
      atRiskRevenue,
    };
  }

  private generateDashboardInsights(subscriptions: UnifiedSubscriptionData[]): RevenueOperationsDashboard['insights'] {
    // Top expansion opportunities
    const allOpportunities = subscriptions.flatMap(s => 
      s.expansionOpportunities.map(o => ({ ...o, subscriptionId: s.subscription.id }))
    );
    const topExpansionOpportunities = allOpportunities
      .sort((a, b) => (b.potentialValue * b.probability) - (a.potentialValue * a.probability))
      .slice(0, 5);

    // Critical risks
    const criticalRisks = subscriptions
      .filter(s => s.riskLevel === 'critical')
      .map(s => ({
        subscriptionId: s.subscription.id,
        customerId: s.subscription.customerId,
        churnProbability: s.churnPrediction.churnProbability,
        potentialLoss: s.subscription.mrr * 12,
        factors: s.churnPrediction.factors,
      }))
      .slice(0, 5);

    return {
      topExpansionOpportunities,
      criticalRisks,
      revenueRecommendations: [],
      performanceAlerts: [],
    };
  }

  private async calculateRevenueTrends(tenantId: string): Promise<RevenueOperationsDashboard['trends']> {
    // Mock implementation - in production, this would query historical data
    return {
      mrrGrowth: [100000, 105000, 110000, 115000, 120000, 125000],
      churnTrend: [0.05, 0.04, 0.03, 0.035, 0.03, 0.025],
      expansionTrend: [0.15, 0.18, 0.20, 0.22, 0.25, 0.28],
      healthScoreTrend: [75, 78, 80, 82, 85, 87],
    };
  }

  private async recordSubscriptionEvent(tenantId: string, subscriptionId: string, eventType: string, eventData: any): Promise<void> {
    // Implementation would record event in database
    logger.info("Subscription event recorded", { tenantId, subscriptionId, eventType });
  }

  private async getApplicableWorkflows(tenantId: string, eventType: string, eventData: any): Promise<AutomatedWorkflow[]> {
    // Mock implementation - in production, this would query workflow rules
    return [];
  }

  private async executeWorkflow(tenantId: string, subscriptionId: string, workflow: AutomatedWorkflow, eventData: any): Promise<void> {
    // Implementation would execute workflow actions
    logger.info("Workflow executed", { tenantId, subscriptionId, workflowId: workflow.id });
  }

  private async checkRealtimeAlerts(tenantId: string, subscriptionId: string, eventType: string, eventData: any): Promise<void> {
    // Implementation would check for alert conditions
    logger.info("Real-time alerts checked", { tenantId, subscriptionId, eventType });
  }

  private isRevenueEvent(eventType: string): boolean {
    return ['subscription_created', 'subscription_renewed', 'plan_changed', 'subscription_canceled'].includes(eventType);
  }

  private async processRevenueEvent(tenantId: string, subscriptionId: string, eventType: string, eventData: any): Promise<void> {
    // Implementation would process revenue recognition
    logger.info("Revenue event processed", { tenantId, subscriptionId, eventType });
  }

  private async getCurrentPricing(tenantId: string, subscriptionId: string): Promise<any> {
    // Mock implementation
    return { amount: 1499, currency: 'USD', billingCycle: 'monthly' };
  }

  private extractUsageMetrics(usageAnalytics: any): any {
    // Extract usage metrics for pricing optimization
    return {
      apiCalls: usageAnalytics.metrics[0]?.totalUsage || 50000,
      dataVolume: 500,
      teamSize: 25,
      featureUsage: { analytics: 0.9, reporting: 0.8, integrations: 0.6 },
    };
  }

  private generatePricingRecommendations(unifiedData: any, currentPricing: any, optimizedPricing: any, expectedImpact: any): string[] {
    const recommendations = [];

    if (expectedImpact.revenueChange > 0) {
      recommendations.push(`Increase pricing by $${expectedImpact.revenueChange} to optimize revenue`);
    }

    if (unifiedData.churnPrediction.churnProbability > 0.5) {
      recommendations.push('Consider retention-focused pricing to reduce churn risk');
    }

    if (unifiedData.expansionOpportunities.length > 0) {
      recommendations.push('Implement expansion-focused pricing to capture growth opportunities');
    }

    return recommendations;
  }
}

export { UnifiedRevenueOperationsService, type UnifiedSubscriptionData, type RevenueOperationsDashboard, type RealtimeAlert };
