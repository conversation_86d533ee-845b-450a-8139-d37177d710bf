// Subscription Lifecycle Management Service
// Comprehensive subscription lifecycle tracking, automated renewals, upgrade/downgrade workflows, and churn prevention

import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { query, queryOne, transaction } from "../utils/database.ts";
import { NotificationService } from "./notificationService.ts";

// Subscription lifecycle types and interfaces
interface Subscription {
  id: string;
  tenantId: string;
  customerId: string;
  planId: string;
  status: 'active' | 'past_due' | 'canceled' | 'unpaid' | 'trialing' | 'paused';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  trialEnd?: Date;
  canceledAt?: Date;
  pausedAt?: Date;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

interface SubscriptionEvent {
  id: string;
  subscriptionId: string;
  tenantId: string;
  eventType: 'created' | 'updated' | 'canceled' | 'renewed' | 'upgraded' | 'downgraded' | 'paused' | 'resumed' | 'trial_ended' | 'payment_failed' | 'payment_succeeded';
  eventData: Record<string, any>;
  processedAt: Date;
  createdAt: Date;
}

interface LifecycleRule {
  id: string;
  tenantId: string;
  name: string;
  trigger: {
    eventType: string;
    conditions: Record<string, any>;
  };
  actions: Array<{
    type: 'email' | 'webhook' | 'subscription_update' | 'billing_action' | 'analytics_event';
    config: Record<string, any>;
    delay?: number; // milliseconds
  }>;
  isActive: boolean;
  createdAt: Date;
}

interface ChurnPrediction {
  subscriptionId: string;
  churnProbability: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  factors: Array<{
    factor: string;
    impact: number;
    description: string;
  }>;
  recommendations: Array<{
    action: string;
    priority: 'low' | 'medium' | 'high';
    expectedImpact: number;
    description: string;
  }>;
  calculatedAt: Date;
}

interface RenewalForecast {
  subscriptionId: string;
  renewalProbability: number;
  expectedRenewalDate: Date;
  expectedRevenue: number;
  riskFactors: string[];
  opportunities: string[];
  calculatedAt: Date;
}

export class SubscriptionLifecycleService {
  private notifications: NotificationService;

  constructor() {
    this.notifications = new NotificationService();
  }

  /**
   * Create new subscription with lifecycle tracking
   */
  async createSubscription(
    tenantId: string,
    customerId: string,
    planId: string,
    options: {
      trialDays?: number;
      startDate?: Date;
      metadata?: Record<string, any>;
    } = {}
  ): Promise<Subscription> {
    const subscriptionId = `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date();
    const startDate = options.startDate || now;
    const trialEnd = options.trialDays ? new Date(startDate.getTime() + options.trialDays * 24 * 60 * 60 * 1000) : undefined;

    // Get plan details for billing cycle
    const plan = await this.getPlanDetails(tenantId, planId);
    const periodEnd = this.calculatePeriodEnd(startDate, plan.billingCycle);

    const subscription: Subscription = {
      id: subscriptionId,
      tenantId,
      customerId,
      planId,
      status: trialEnd ? 'trialing' : 'active',
      currentPeriodStart: startDate,
      currentPeriodEnd: periodEnd,
      trialEnd,
      metadata: options.metadata || {},
      createdAt: now,
      updatedAt: now,
    };

    // Store subscription in database
    await query(`
      INSERT INTO subscriptions (
        id, tenant_id, customer_id, plan_id, status,
        current_period_start, current_period_end, trial_end,
        metadata, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
    `, [
      subscription.id,
      subscription.tenantId,
      subscription.customerId,
      subscription.planId,
      subscription.status,
      subscription.currentPeriodStart,
      subscription.currentPeriodEnd,
      subscription.trialEnd,
      JSON.stringify(subscription.metadata),
      subscription.createdAt,
      subscription.updatedAt,
    ], tenantId);

    // Record lifecycle event
    await this.recordLifecycleEvent(subscriptionId, tenantId, 'created', {
      planId,
      customerId,
      trialDays: options.trialDays,
      startDate: startDate.toISOString(),
    });

    // Trigger lifecycle rules
    await this.triggerLifecycleRules(tenantId, 'created', subscription);

    // Schedule renewal reminder if not trial
    if (!trialEnd) {
      await this.scheduleRenewalReminder(subscription);
    }

    logger.info("Subscription created", {
      tenantId,
      subscriptionId,
      customerId,
      planId,
      status: subscription.status,
    });

    return subscription;
  }

  /**
   * Update subscription status and trigger lifecycle events
   */
  async updateSubscriptionStatus(
    tenantId: string,
    subscriptionId: string,
    newStatus: Subscription['status'],
    metadata?: Record<string, any>
  ): Promise<Subscription> {
    const subscription = await this.getSubscription(tenantId, subscriptionId);
    if (!subscription) {
      throw new Error(`Subscription ${subscriptionId} not found`);
    }

    const oldStatus = subscription.status;
    const now = new Date();

    // Update subscription
    subscription.status = newStatus;
    subscription.updatedAt = now;
    if (metadata) {
      subscription.metadata = { ...subscription.metadata, ...metadata };
    }

    // Handle status-specific updates
    if (newStatus === 'canceled') {
      subscription.canceledAt = now;
    } else if (newStatus === 'paused') {
      subscription.pausedAt = now;
    }

    // Update in database
    await query(`
      UPDATE subscriptions
      SET status = $1, updated_at = $2, canceled_at = $3, paused_at = $4, metadata = $5
      WHERE id = $6 AND tenant_id = $7
    `, [
      subscription.status,
      subscription.updatedAt,
      subscription.canceledAt,
      subscription.pausedAt,
      JSON.stringify(subscription.metadata),
      subscriptionId,
      tenantId,
    ], tenantId);

    // Record lifecycle event
    await this.recordLifecycleEvent(subscriptionId, tenantId, 'updated', {
      oldStatus,
      newStatus,
      metadata,
    });

    // Trigger lifecycle rules
    await this.triggerLifecycleRules(tenantId, 'updated', subscription);

    logger.info("Subscription status updated", {
      tenantId,
      subscriptionId,
      oldStatus,
      newStatus,
    });

    return subscription;
  }

  /**
   * Process subscription renewal
   */
  async processRenewal(tenantId: string, subscriptionId: string): Promise<{
    success: boolean;
    newPeriodEnd: Date;
    invoiceId?: string;
    error?: string;
  }> {
    const subscription = await this.getSubscription(tenantId, subscriptionId);
    if (!subscription) {
      throw new Error(`Subscription ${subscriptionId} not found`);
    }

    try {
      // Get plan details for billing
      const plan = await this.getPlanDetails(tenantId, subscription.planId);
      
      // Calculate new period
      const newPeriodStart = subscription.currentPeriodEnd;
      const newPeriodEnd = this.calculatePeriodEnd(newPeriodStart, plan.billingCycle);

      // Process payment (integrate with payment processor)
      const paymentResult = await this.processRenewalPayment(subscription, plan);
      
      if (!paymentResult.success) {
        // Update subscription to past_due
        await this.updateSubscriptionStatus(tenantId, subscriptionId, 'past_due', {
          lastPaymentError: paymentResult.error,
          paymentAttempts: (subscription.metadata.paymentAttempts || 0) + 1,
        });

        return {
          success: false,
          newPeriodEnd: subscription.currentPeriodEnd,
          error: paymentResult.error,
        };
      }

      // Update subscription with new period
      await query(`
        UPDATE subscriptions
        SET current_period_start = $1, current_period_end = $2, updated_at = $3,
            metadata = $4
        WHERE id = $5 AND tenant_id = $6
      `, [
        newPeriodStart,
        newPeriodEnd,
        new Date(),
        JSON.stringify({ ...subscription.metadata, lastRenewal: new Date().toISOString() }),
        subscriptionId,
        tenantId,
      ], tenantId);

      // Record renewal event
      await this.recordLifecycleEvent(subscriptionId, tenantId, 'renewed', {
        previousPeriodEnd: subscription.currentPeriodEnd.toISOString(),
        newPeriodEnd: newPeriodEnd.toISOString(),
        amount: plan.price,
        invoiceId: paymentResult.invoiceId,
      });

      // Trigger lifecycle rules
      await this.triggerLifecycleRules(tenantId, 'renewed', subscription);

      // Schedule next renewal reminder
      await this.scheduleRenewalReminder({
        ...subscription,
        currentPeriodEnd: newPeriodEnd,
      });

      logger.info("Subscription renewed successfully", {
        tenantId,
        subscriptionId,
        newPeriodEnd: newPeriodEnd.toISOString(),
        amount: plan.price,
      });

      return {
        success: true,
        newPeriodEnd,
        invoiceId: paymentResult.invoiceId,
      };
    } catch (error) {
      logger.error("Subscription renewal failed", {
        tenantId,
        subscriptionId,
        error: (error as Error).message,
      });

      return {
        success: false,
        newPeriodEnd: subscription.currentPeriodEnd,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Upgrade or downgrade subscription
   */
  async changeSubscriptionPlan(
    tenantId: string,
    subscriptionId: string,
    newPlanId: string,
    options: {
      prorationBehavior?: 'create_prorations' | 'none' | 'always_invoice';
      effectiveDate?: Date;
    } = {}
  ): Promise<{
    success: boolean;
    proratedAmount?: number;
    newPeriodEnd?: Date;
    error?: string;
  }> {
    const subscription = await this.getSubscription(tenantId, subscriptionId);
    if (!subscription) {
      throw new Error(`Subscription ${subscriptionId} not found`);
    }

    try {
      const oldPlan = await this.getPlanDetails(tenantId, subscription.planId);
      const newPlan = await this.getPlanDetails(tenantId, newPlanId);
      const effectiveDate = options.effectiveDate || new Date();

      // Calculate proration if needed
      let proratedAmount = 0;
      if (options.prorationBehavior !== 'none') {
        proratedAmount = this.calculateProration(
          subscription,
          oldPlan,
          newPlan,
          effectiveDate
        );
      }

      // Process proration payment/credit
      if (proratedAmount !== 0) {
        const prorationResult = await this.processProration(subscription, proratedAmount);
        if (!prorationResult.success) {
          return {
            success: false,
            error: prorationResult.error,
          };
        }
      }

      // Update subscription plan
      const newPeriodEnd = this.calculatePeriodEnd(effectiveDate, newPlan.billingCycle);
      
      await query(`
        UPDATE subscriptions
        SET plan_id = $1, current_period_start = $2, current_period_end = $3,
            updated_at = $4, metadata = $5
        WHERE id = $6 AND tenant_id = $7
      `, [
        newPlanId,
        effectiveDate,
        newPeriodEnd,
        new Date(),
        JSON.stringify({
          ...subscription.metadata,
          previousPlanId: subscription.planId,
          planChangeDate: effectiveDate.toISOString(),
          proratedAmount,
        }),
        subscriptionId,
        tenantId,
      ], tenantId);

      // Record plan change event
      const eventType = newPlan.price > oldPlan.price ? 'upgraded' : 'downgraded';
      await this.recordLifecycleEvent(subscriptionId, tenantId, eventType, {
        oldPlanId: subscription.planId,
        newPlanId,
        oldPrice: oldPlan.price,
        newPrice: newPlan.price,
        proratedAmount,
        effectiveDate: effectiveDate.toISOString(),
      });

      // Trigger lifecycle rules
      await this.triggerLifecycleRules(tenantId, eventType, {
        ...subscription,
        planId: newPlanId,
      });

      logger.info("Subscription plan changed", {
        tenantId,
        subscriptionId,
        oldPlanId: subscription.planId,
        newPlanId,
        eventType,
        proratedAmount,
      });

      return {
        success: true,
        proratedAmount,
        newPeriodEnd,
      };
    } catch (error) {
      logger.error("Subscription plan change failed", {
        tenantId,
        subscriptionId,
        newPlanId,
        error: (error as Error).message,
      });

      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Predict subscription churn risk
   */
  async predictChurnRisk(tenantId: string, subscriptionId: string): Promise<ChurnPrediction> {
    const subscription = await this.getSubscription(tenantId, subscriptionId);
    if (!subscription) {
      throw new Error(`Subscription ${subscriptionId} not found`);
    }

    // Get subscription usage and engagement data
    const usageData = await this.getSubscriptionUsageData(tenantId, subscriptionId);
    const paymentHistory = await this.getPaymentHistory(tenantId, subscriptionId);
    const supportTickets = await this.getSupportTickets(tenantId, subscription.customerId);

    // Calculate churn probability using ML model
    const churnProbability = this.calculateChurnProbability({
      subscription,
      usageData,
      paymentHistory,
      supportTickets,
    });

    // Determine risk level
    let riskLevel: ChurnPrediction['riskLevel'];
    if (churnProbability >= 0.8) riskLevel = 'critical';
    else if (churnProbability >= 0.6) riskLevel = 'high';
    else if (churnProbability >= 0.3) riskLevel = 'medium';
    else riskLevel = 'low';

    // Identify risk factors
    const factors = this.identifyChurnFactors({
      subscription,
      usageData,
      paymentHistory,
      supportTickets,
      churnProbability,
    });

    // Generate recommendations
    const recommendations = this.generateChurnPreventionRecommendations(factors, riskLevel);

    const prediction: ChurnPrediction = {
      subscriptionId,
      churnProbability,
      riskLevel,
      factors,
      recommendations,
      calculatedAt: new Date(),
    };

    // Store prediction for tracking
    await this.storeChurnPrediction(tenantId, prediction);

    logger.info("Churn prediction calculated", {
      tenantId,
      subscriptionId,
      churnProbability,
      riskLevel,
      factorsCount: factors.length,
    });

    return prediction;
  }

  /**
   * Generate renewal forecast
   */
  async generateRenewalForecast(tenantId: string, subscriptionId: string): Promise<RenewalForecast> {
    const subscription = await this.getSubscription(tenantId, subscriptionId);
    if (!subscription) {
      throw new Error(`Subscription ${subscriptionId} not found`);
    }

    const plan = await this.getPlanDetails(tenantId, subscription.planId);
    const churnPrediction = await this.predictChurnRisk(tenantId, subscriptionId);

    // Calculate renewal probability (inverse of churn probability)
    const renewalProbability = 1 - churnPrediction.churnProbability;

    // Expected renewal date
    const expectedRenewalDate = subscription.currentPeriodEnd;

    // Expected revenue (probability * plan price)
    const expectedRevenue = renewalProbability * plan.price;

    // Identify risk factors and opportunities
    const riskFactors = churnPrediction.factors
      .filter(f => f.impact > 0.1)
      .map(f => f.description);

    const opportunities = churnPrediction.recommendations
      .filter(r => r.priority === 'high')
      .map(r => r.description);

    const forecast: RenewalForecast = {
      subscriptionId,
      renewalProbability,
      expectedRenewalDate,
      expectedRevenue,
      riskFactors,
      opportunities,
      calculatedAt: new Date(),
    };

    logger.info("Renewal forecast generated", {
      tenantId,
      subscriptionId,
      renewalProbability,
      expectedRevenue,
      riskFactorsCount: riskFactors.length,
    });

    return forecast;
  }

  // Helper methods
  private async getSubscription(tenantId: string, subscriptionId: string): Promise<Subscription | null> {
    const result = await query(`
      SELECT * FROM subscriptions
      WHERE id = $1 AND tenant_id = $2
    `, [subscriptionId, tenantId], tenantId);

    if (result.length === 0) return null;

    const row = result[0] as any;
    return {
      id: row.id,
      tenantId: row.tenant_id,
      customerId: row.customer_id,
      planId: row.plan_id,
      status: row.status,
      currentPeriodStart: new Date(row.current_period_start),
      currentPeriodEnd: new Date(row.current_period_end),
      trialEnd: row.trial_end ? new Date(row.trial_end) : undefined,
      canceledAt: row.canceled_at ? new Date(row.canceled_at) : undefined,
      pausedAt: row.paused_at ? new Date(row.paused_at) : undefined,
      metadata: JSON.parse(row.metadata || '{}'),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }

  private async getPlanDetails(tenantId: string, planId: string): Promise<any> {
    // Mock plan details - in production, this would query the plans table
    return {
      id: planId,
      price: planId === 'pro' ? 1499 : planId === 'enterprise' ? 4999 : 499,
      billingCycle: 'monthly',
      features: [],
    };
  }

  private calculatePeriodEnd(startDate: Date, billingCycle: string): Date {
    const endDate = new Date(startDate);
    if (billingCycle === 'monthly') {
      endDate.setMonth(endDate.getMonth() + 1);
    } else if (billingCycle === 'yearly') {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }
    return endDate;
  }

  private async recordLifecycleEvent(
    subscriptionId: string,
    tenantId: string,
    eventType: SubscriptionEvent['eventType'],
    eventData: Record<string, any>
  ): Promise<void> {
    const eventId = `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    await query(`
      INSERT INTO subscription_events (
        id, subscription_id, tenant_id, event_type, event_data, processed_at, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [
      eventId,
      subscriptionId,
      tenantId,
      eventType,
      JSON.stringify(eventData),
      new Date(),
      new Date(),
    ], tenantId);
  }

  private async triggerLifecycleRules(
    tenantId: string,
    eventType: string,
    subscription: Subscription
  ): Promise<void> {
    // Get applicable lifecycle rules
    const rules = await this.getLifecycleRules(tenantId, eventType);
    
    for (const rule of rules) {
      if (this.evaluateRuleConditions(rule, subscription)) {
        await this.executeRuleActions(rule, subscription);
      }
    }
  }

  private async getLifecycleRules(tenantId: string, eventType: string): Promise<LifecycleRule[]> {
    // Mock implementation - in production, this would query the lifecycle_rules table
    return [];
  }

  private evaluateRuleConditions(rule: LifecycleRule, subscription: Subscription): boolean {
    // Mock implementation - in production, this would evaluate rule conditions
    return true;
  }

  private async executeRuleActions(rule: LifecycleRule, subscription: Subscription): Promise<void> {
    // Mock implementation - in production, this would execute rule actions
    logger.info("Executing lifecycle rule actions", {
      ruleId: rule.id,
      subscriptionId: subscription.id,
      actionsCount: rule.actions.length,
    });
  }

  private async scheduleRenewalReminder(subscription: Subscription): Promise<void> {
    // Mock implementation - in production, this would schedule renewal reminders
    logger.info("Renewal reminder scheduled", {
      subscriptionId: subscription.id,
      renewalDate: subscription.currentPeriodEnd.toISOString(),
    });
  }

  private async processRenewalPayment(subscription: Subscription, plan: any): Promise<{
    success: boolean;
    invoiceId?: string;
    error?: string;
  }> {
    // Mock payment processing - in production, this would integrate with payment processor
    return {
      success: true,
      invoiceId: `inv_${Date.now()}`,
    };
  }

  private calculateProration(
    subscription: Subscription,
    oldPlan: any,
    newPlan: any,
    effectiveDate: Date
  ): number {
    // Mock proration calculation
    const remainingDays = Math.ceil(
      (subscription.currentPeriodEnd.getTime() - effectiveDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    const totalDays = Math.ceil(
      (subscription.currentPeriodEnd.getTime() - subscription.currentPeriodStart.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    const priceDifference = newPlan.price - oldPlan.price;
    return (priceDifference * remainingDays) / totalDays;
  }

  private async processProration(subscription: Subscription, amount: number): Promise<{
    success: boolean;
    error?: string;
  }> {
    // Mock proration processing
    return { success: true };
  }

  private async getSubscriptionUsageData(tenantId: string, subscriptionId: string): Promise<any> {
    // Mock usage data
    return {
      apiCalls: 5000,
      dataVolume: 500,
      activeUsers: 25,
      lastActivity: new Date(),
    };
  }

  private async getPaymentHistory(tenantId: string, subscriptionId: string): Promise<any[]> {
    // Mock payment history
    return [];
  }

  private async getSupportTickets(tenantId: string, customerId: string): Promise<any[]> {
    // Mock support tickets
    return [];
  }

  private calculateChurnProbability(data: any): number {
    // Mock ML model for churn prediction
    // In production, this would use a trained ML model
    let probability = 0.1; // Base probability

    // Usage-based factors
    if (data.usageData.apiCalls < 1000) probability += 0.2;
    if (data.usageData.activeUsers < 5) probability += 0.15;

    // Payment history factors
    if (data.paymentHistory.filter((p: any) => p.failed).length > 0) probability += 0.3;

    // Support ticket factors
    if (data.supportTickets.length > 5) probability += 0.1;

    return Math.min(probability, 1.0);
  }

  private identifyChurnFactors(data: any): ChurnPrediction['factors'] {
    const factors: ChurnPrediction['factors'] = [];

    if (data.usageData.apiCalls < 1000) {
      factors.push({
        factor: 'low_usage',
        impact: 0.2,
        description: 'Low API usage indicates reduced engagement',
      });
    }

    if (data.paymentHistory.filter((p: any) => p.failed).length > 0) {
      factors.push({
        factor: 'payment_issues',
        impact: 0.3,
        description: 'Recent payment failures indicate billing issues',
      });
    }

    return factors;
  }

  private generateChurnPreventionRecommendations(
    factors: ChurnPrediction['factors'],
    riskLevel: ChurnPrediction['riskLevel']
  ): ChurnPrediction['recommendations'] {
    const recommendations: ChurnPrediction['recommendations'] = [];

    if (factors.some(f => f.factor === 'low_usage')) {
      recommendations.push({
        action: 'engagement_campaign',
        priority: 'high',
        expectedImpact: 0.3,
        description: 'Launch targeted engagement campaign to increase usage',
      });
    }

    if (factors.some(f => f.factor === 'payment_issues')) {
      recommendations.push({
        action: 'billing_support',
        priority: 'high',
        expectedImpact: 0.4,
        description: 'Proactive billing support to resolve payment issues',
      });
    }

    return recommendations;
  }

  private async storeChurnPrediction(tenantId: string, prediction: ChurnPrediction): Promise<void> {
    await query(`
      INSERT INTO churn_predictions (
        subscription_id, tenant_id, churn_probability, risk_level,
        factors, recommendations, calculated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (subscription_id) DO UPDATE SET
        churn_probability = $3,
        risk_level = $4,
        factors = $5,
        recommendations = $6,
        calculated_at = $7
    `, [
      prediction.subscriptionId,
      tenantId,
      prediction.churnProbability,
      prediction.riskLevel,
      JSON.stringify(prediction.factors),
      JSON.stringify(prediction.recommendations),
      prediction.calculatedAt,
    ], tenantId);
  }
}

export type { Subscription, SubscriptionEvent, ChurnPrediction, RenewalForecast };
