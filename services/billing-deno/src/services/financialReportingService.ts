// Financial Reporting & Analytics Service
// Comprehensive financial dashboards, MRR/ARR tracking, cohort revenue analysis, and executive reporting

import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { DatabaseService } from "./databaseService.ts";

// Financial reporting types and interfaces
interface FinancialMetrics {
  period: {
    start: Date;
    end: Date;
    type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annual';
  };
  revenue: {
    totalRevenue: number;
    recurringRevenue: number;
    oneTimeRevenue: number;
    recognizedRevenue: number;
    deferredRevenue: number;
    mrr: number;
    arr: number;
    growth: {
      mrrGrowth: number;
      arrGrowth: number;
      revenueGrowth: number;
    };
  };
  subscriptions: {
    totalSubscriptions: number;
    activeSubscriptions: number;
    newSubscriptions: number;
    canceledSubscriptions: number;
    upgrades: number;
    downgrades: number;
    churnRate: number;
    retentionRate: number;
  };
  customers: {
    totalCustomers: number;
    newCustomers: number;
    churnedCustomers: number;
    averageRevenuePerUser: number;
    customerLifetimeValue: number;
    customerAcquisitionCost: number;
  };
  financial: {
    grossMargin: number;
    netMargin: number;
    operatingExpenses: number;
    ebitda: number;
    cashFlow: number;
    burnRate: number;
    runway: number; // months
  };
}

interface CohortAnalysis {
  cohortPeriod: string; // YYYY-MM
  cohortSize: number;
  revenueByPeriod: Array<{
    period: number; // months since cohort start
    revenue: number;
    cumulativeRevenue: number;
    retainedCustomers: number;
    retentionRate: number;
    averageRevenuePerCustomer: number;
  }>;
  metrics: {
    totalRevenue: number;
    averageLifetimeValue: number;
    paybackPeriod: number; // months
    retentionAt12Months: number;
    revenueRetentionAt12Months: number;
  };
}

interface RevenueWaterfall {
  period: {
    start: Date;
    end: Date;
  };
  startingMRR: number;
  newMRR: number;
  expansionMRR: number;
  contractionMRR: number;
  churnMRR: number;
  endingMRR: number;
  netGrowth: number;
  growthRate: number;
  components: Array<{
    type: 'new' | 'expansion' | 'contraction' | 'churn';
    amount: number;
    customerCount: number;
    averageAmount: number;
  }>;
}

interface ExecutiveReport {
  id: string;
  tenantId: string;
  reportType: 'monthly' | 'quarterly' | 'annual' | 'board';
  period: {
    start: Date;
    end: Date;
  };
  summary: {
    totalRevenue: number;
    revenueGrowth: number;
    mrr: number;
    mrrGrowth: number;
    customerCount: number;
    customerGrowth: number;
    churnRate: number;
    netRevenueRetention: number;
  };
  keyMetrics: FinancialMetrics;
  cohortAnalysis: CohortAnalysis[];
  revenueWaterfall: RevenueWaterfall;
  insights: Array<{
    category: 'growth' | 'retention' | 'efficiency' | 'risk';
    insight: string;
    impact: 'positive' | 'negative' | 'neutral';
    recommendation?: string;
    priority: 'low' | 'medium' | 'high';
  }>;
  generatedAt: Date;
}

interface FinancialForecast {
  forecastPeriod: {
    start: Date;
    end: Date;
  };
  methodology: 'linear' | 'exponential' | 'seasonal' | 'ml_model';
  confidence: number; // 0-1
  scenarios: Array<{
    name: 'conservative' | 'base' | 'optimistic';
    probability: number;
    projections: Array<{
      period: string; // YYYY-MM
      revenue: number;
      mrr: number;
      customers: number;
      churnRate: number;
    }>;
  }>;
  assumptions: Array<{
    parameter: string;
    value: number;
    rationale: string;
  }>;
  risks: Array<{
    risk: string;
    probability: number;
    impact: number;
    mitigation: string;
  }>;
}

export class FinancialReportingService {
  private db: DatabaseService;

  constructor() {
    this.db = new DatabaseService();
  }

  /**
   * Calculate comprehensive financial metrics for a period
   */
  async calculateFinancialMetrics(
    tenantId: string,
    period: {
      start: Date;
      end: Date;
      type: FinancialMetrics['period']['type'];
    }
  ): Promise<FinancialMetrics> {
    // Get revenue data
    const revenueData = await this.getRevenueData(tenantId, period);
    const subscriptionData = await this.getSubscriptionData(tenantId, period);
    const customerData = await this.getCustomerData(tenantId, period);

    // Calculate revenue metrics
    const revenue = {
      totalRevenue: revenueData.total,
      recurringRevenue: revenueData.recurring,
      oneTimeRevenue: revenueData.oneTime,
      recognizedRevenue: revenueData.recognized,
      deferredRevenue: revenueData.deferred,
      mrr: revenueData.mrr,
      arr: revenueData.mrr * 12,
      growth: {
        mrrGrowth: await this.calculateMRRGrowth(tenantId, period),
        arrGrowth: await this.calculateARRGrowth(tenantId, period),
        revenueGrowth: await this.calculateRevenueGrowth(tenantId, period),
      },
    };

    // Calculate subscription metrics
    const subscriptions = {
      totalSubscriptions: subscriptionData.total,
      activeSubscriptions: subscriptionData.active,
      newSubscriptions: subscriptionData.new,
      canceledSubscriptions: subscriptionData.canceled,
      upgrades: subscriptionData.upgrades,
      downgrades: subscriptionData.downgrades,
      churnRate: subscriptionData.canceled / subscriptionData.active,
      retentionRate: 1 - (subscriptionData.canceled / subscriptionData.active),
    };

    // Calculate customer metrics
    const customers = {
      totalCustomers: customerData.total,
      newCustomers: customerData.new,
      churnedCustomers: customerData.churned,
      averageRevenuePerUser: revenue.totalRevenue / customerData.total,
      customerLifetimeValue: await this.calculateCustomerLTV(tenantId, period),
      customerAcquisitionCost: await this.calculateCustomerCAC(tenantId, period),
    };

    // Calculate financial metrics
    const financial = {
      grossMargin: await this.calculateGrossMargin(tenantId, period),
      netMargin: await this.calculateNetMargin(tenantId, period),
      operatingExpenses: await this.getOperatingExpenses(tenantId, period),
      ebitda: await this.calculateEBITDA(tenantId, period),
      cashFlow: await this.calculateCashFlow(tenantId, period),
      burnRate: await this.calculateBurnRate(tenantId, period),
      runway: await this.calculateRunway(tenantId, period),
    };

    const metrics: FinancialMetrics = {
      period,
      revenue,
      subscriptions,
      customers,
      financial,
    };

    logger.info("Financial metrics calculated", {
      tenantId,
      period: `${period.start.toISOString()} - ${period.end.toISOString()}`,
      totalRevenue: revenue.totalRevenue,
      mrr: revenue.mrr,
      churnRate: subscriptions.churnRate,
    });

    return metrics;
  }

  /**
   * Generate cohort revenue analysis
   */
  async generateCohortAnalysis(
    tenantId: string,
    cohortPeriods: number = 12
  ): Promise<CohortAnalysis[]> {
    const cohorts: CohortAnalysis[] = [];
    const endDate = new Date();
    
    for (let i = 0; i < cohortPeriods; i++) {
      const cohortDate = new Date(endDate);
      cohortDate.setMonth(cohortDate.getMonth() - i);
      const cohortPeriod = cohortDate.toISOString().substring(0, 7); // YYYY-MM

      // Get customers who started in this cohort period
      const cohortCustomers = await this.getCohortCustomers(tenantId, cohortDate);
      
      if (cohortCustomers.length === 0) continue;

      // Calculate revenue by period for this cohort
      const revenueByPeriod = await this.calculateCohortRevenueByPeriod(
        tenantId,
        cohortCustomers,
        cohortDate,
        12 // Track for 12 months
      );

      // Calculate cohort metrics
      const totalRevenue = revenueByPeriod.reduce((sum, p) => sum + p.revenue, 0);
      const averageLifetimeValue = totalRevenue / cohortCustomers.length;
      const paybackPeriod = this.calculatePaybackPeriod(revenueByPeriod, averageLifetimeValue);
      const retentionAt12Months = revenueByPeriod[11]?.retentionRate || 0;
      const revenueRetentionAt12Months = this.calculateRevenueRetention(revenueByPeriod, 12);

      cohorts.push({
        cohortPeriod,
        cohortSize: cohortCustomers.length,
        revenueByPeriod,
        metrics: {
          totalRevenue,
          averageLifetimeValue,
          paybackPeriod,
          retentionAt12Months,
          revenueRetentionAt12Months,
        },
      });
    }

    logger.info("Cohort analysis generated", {
      tenantId,
      cohortsCount: cohorts.length,
      totalCohortSize: cohorts.reduce((sum, c) => sum + c.cohortSize, 0),
    });

    return cohorts;
  }

  /**
   * Generate revenue waterfall analysis
   */
  async generateRevenueWaterfall(
    tenantId: string,
    period: { start: Date; end: Date }
  ): Promise<RevenueWaterfall> {
    // Get MRR at start and end of period
    const startingMRR = await this.getMRRAtDate(tenantId, period.start);
    const endingMRR = await this.getMRRAtDate(tenantId, period.end);

    // Get MRR movements during the period
    const movements = await this.getMRRMovements(tenantId, period);

    const newMRR = movements.filter(m => m.type === 'new').reduce((sum, m) => sum + m.amount, 0);
    const expansionMRR = movements.filter(m => m.type === 'expansion').reduce((sum, m) => sum + m.amount, 0);
    const contractionMRR = movements.filter(m => m.type === 'contraction').reduce((sum, m) => sum + Math.abs(m.amount), 0);
    const churnMRR = movements.filter(m => m.type === 'churn').reduce((sum, m) => sum + Math.abs(m.amount), 0);

    const netGrowth = newMRR + expansionMRR - contractionMRR - churnMRR;
    const growthRate = startingMRR > 0 ? (netGrowth / startingMRR) * 100 : 0;

    // Group movements by type for component analysis
    const components = [
      {
        type: 'new' as const,
        amount: newMRR,
        customerCount: movements.filter(m => m.type === 'new').length,
        averageAmount: newMRR / Math.max(movements.filter(m => m.type === 'new').length, 1),
      },
      {
        type: 'expansion' as const,
        amount: expansionMRR,
        customerCount: movements.filter(m => m.type === 'expansion').length,
        averageAmount: expansionMRR / Math.max(movements.filter(m => m.type === 'expansion').length, 1),
      },
      {
        type: 'contraction' as const,
        amount: -contractionMRR,
        customerCount: movements.filter(m => m.type === 'contraction').length,
        averageAmount: -contractionMRR / Math.max(movements.filter(m => m.type === 'contraction').length, 1),
      },
      {
        type: 'churn' as const,
        amount: -churnMRR,
        customerCount: movements.filter(m => m.type === 'churn').length,
        averageAmount: -churnMRR / Math.max(movements.filter(m => m.type === 'churn').length, 1),
      },
    ];

    const waterfall: RevenueWaterfall = {
      period,
      startingMRR,
      newMRR,
      expansionMRR,
      contractionMRR,
      churnMRR,
      endingMRR,
      netGrowth,
      growthRate,
      components,
    };

    logger.info("Revenue waterfall generated", {
      tenantId,
      period: `${period.start.toISOString()} - ${period.end.toISOString()}`,
      startingMRR,
      endingMRR,
      netGrowth,
      growthRate,
    });

    return waterfall;
  }

  /**
   * Generate executive report
   */
  async generateExecutiveReport(
    tenantId: string,
    reportType: ExecutiveReport['reportType'],
    period: { start: Date; end: Date }
  ): Promise<ExecutiveReport> {
    const reportId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Calculate key metrics
    const keyMetrics = await this.calculateFinancialMetrics(tenantId, {
      ...period,
      type: reportType === 'monthly' ? 'monthly' : reportType === 'quarterly' ? 'quarterly' : 'annual',
    });

    // Generate cohort analysis
    const cohortAnalysis = await this.generateCohortAnalysis(tenantId, 6);

    // Generate revenue waterfall
    const revenueWaterfall = await this.generateRevenueWaterfall(tenantId, period);

    // Calculate summary metrics
    const summary = {
      totalRevenue: keyMetrics.revenue.totalRevenue,
      revenueGrowth: keyMetrics.revenue.growth.revenueGrowth,
      mrr: keyMetrics.revenue.mrr,
      mrrGrowth: keyMetrics.revenue.growth.mrrGrowth,
      customerCount: keyMetrics.customers.totalCustomers,
      customerGrowth: await this.calculateCustomerGrowth(tenantId, period),
      churnRate: keyMetrics.subscriptions.churnRate,
      netRevenueRetention: await this.calculateNetRevenueRetention(tenantId, period),
    };

    // Generate insights
    const insights = await this.generateExecutiveInsights(keyMetrics, cohortAnalysis, revenueWaterfall);

    const report: ExecutiveReport = {
      id: reportId,
      tenantId,
      reportType,
      period,
      summary,
      keyMetrics,
      cohortAnalysis,
      revenueWaterfall,
      insights,
      generatedAt: new Date(),
    };

    // Store report
    await this.storeExecutiveReport(report);

    logger.info("Executive report generated", {
      tenantId,
      reportId,
      reportType,
      totalRevenue: summary.totalRevenue,
      mrrGrowth: summary.mrrGrowth,
      insightsCount: insights.length,
    });

    return report;
  }

  /**
   * Generate financial forecast
   */
  async generateFinancialForecast(
    tenantId: string,
    forecastMonths: number = 12,
    methodology: FinancialForecast['methodology'] = 'linear'
  ): Promise<FinancialForecast> {
    const startDate = new Date();
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + forecastMonths);

    // Get historical data for modeling
    const historicalData = await this.getHistoricalFinancialData(tenantId, 12);

    // Generate scenarios
    const scenarios = await this.generateForecastScenarios(historicalData, forecastMonths, methodology);

    // Calculate confidence based on historical variance
    const confidence = this.calculateForecastConfidence(historicalData, methodology);

    // Define assumptions
    const assumptions = [
      {
        parameter: 'Monthly Churn Rate',
        value: historicalData.averageChurnRate,
        rationale: 'Based on 12-month historical average',
      },
      {
        parameter: 'Customer Growth Rate',
        value: historicalData.averageCustomerGrowth,
        rationale: 'Based on recent growth trends',
      },
      {
        parameter: 'ARPU Growth',
        value: historicalData.averageARPUGrowth,
        rationale: 'Based on pricing and expansion trends',
      },
    ];

    // Identify risks
    const risks = [
      {
        risk: 'Economic downturn affecting customer spending',
        probability: 0.3,
        impact: -0.2, // 20% revenue impact
        mitigation: 'Diversify customer base and offer flexible pricing',
      },
      {
        risk: 'Increased competition leading to higher churn',
        probability: 0.4,
        impact: -0.15, // 15% revenue impact
        mitigation: 'Enhance product differentiation and customer success',
      },
      {
        risk: 'Regulatory changes affecting business model',
        probability: 0.2,
        impact: -0.1, // 10% revenue impact
        mitigation: 'Monitor regulatory landscape and adapt compliance',
      },
    ];

    const forecast: FinancialForecast = {
      forecastPeriod: {
        start: startDate,
        end: endDate,
      },
      methodology,
      confidence,
      scenarios,
      assumptions,
      risks,
    };

    logger.info("Financial forecast generated", {
      tenantId,
      forecastMonths,
      methodology,
      confidence,
      scenariosCount: scenarios.length,
    });

    return forecast;
  }

  // Helper methods
  private async getRevenueData(tenantId: string, period: any): Promise<any> {
    // Mock implementation - in production, this would query actual revenue data
    return {
      total: 1500000,
      recurring: 1200000,
      oneTime: 300000,
      recognized: 1400000,
      deferred: 100000,
      mrr: 100000,
    };
  }

  private async getSubscriptionData(tenantId: string, period: any): Promise<any> {
    // Mock implementation
    return {
      total: 1000,
      active: 950,
      new: 50,
      canceled: 25,
      upgrades: 15,
      downgrades: 8,
    };
  }

  private async getCustomerData(tenantId: string, period: any): Promise<any> {
    // Mock implementation
    return {
      total: 800,
      new: 40,
      churned: 20,
    };
  }

  private async calculateMRRGrowth(tenantId: string, period: any): Promise<number> {
    // Mock implementation
    return 0.15; // 15% growth
  }

  private async calculateARRGrowth(tenantId: string, period: any): Promise<number> {
    // Mock implementation
    return 0.18; // 18% growth
  }

  private async calculateRevenueGrowth(tenantId: string, period: any): Promise<number> {
    // Mock implementation
    return 0.20; // 20% growth
  }

  private async calculateCustomerLTV(tenantId: string, period: any): Promise<number> {
    // Mock implementation
    return 5000; // $5,000 LTV
  }

  private async calculateCustomerCAC(tenantId: string, period: any): Promise<number> {
    // Mock implementation
    return 1000; // $1,000 CAC
  }

  private async calculateGrossMargin(tenantId: string, period: any): Promise<number> {
    // Mock implementation
    return 0.85; // 85% gross margin
  }

  private async calculateNetMargin(tenantId: string, period: any): Promise<number> {
    // Mock implementation
    return 0.25; // 25% net margin
  }

  private async getOperatingExpenses(tenantId: string, period: any): Promise<number> {
    // Mock implementation
    return 900000; // $900k operating expenses
  }

  private async calculateEBITDA(tenantId: string, period: any): Promise<number> {
    // Mock implementation
    return 375000; // $375k EBITDA
  }

  private async calculateCashFlow(tenantId: string, period: any): Promise<number> {
    // Mock implementation
    return 300000; // $300k cash flow
  }

  private async calculateBurnRate(tenantId: string, period: any): Promise<number> {
    // Mock implementation
    return 50000; // $50k monthly burn
  }

  private async calculateRunway(tenantId: string, period: any): Promise<number> {
    // Mock implementation
    return 24; // 24 months runway
  }

  private async getCohortCustomers(tenantId: string, cohortDate: Date): Promise<string[]> {
    // Mock implementation
    return Array.from({ length: 50 }, (_, i) => `customer_${i}`);
  }

  private async calculateCohortRevenueByPeriod(
    tenantId: string,
    customers: string[],
    cohortDate: Date,
    periods: number
  ): Promise<CohortAnalysis['revenueByPeriod']> {
    // Mock implementation
    const revenueByPeriod: CohortAnalysis['revenueByPeriod'] = [];
    
    for (let i = 0; i < periods; i++) {
      const retentionRate = Math.max(0.95 - (i * 0.05), 0.3); // Decreasing retention
      const retainedCustomers = Math.floor(customers.length * retentionRate);
      const revenue = retainedCustomers * 100; // $100 per customer
      
      revenueByPeriod.push({
        period: i,
        revenue,
        cumulativeRevenue: revenueByPeriod.reduce((sum, p) => sum + p.revenue, 0) + revenue,
        retainedCustomers,
        retentionRate,
        averageRevenuePerCustomer: retainedCustomers > 0 ? revenue / retainedCustomers : 0,
      });
    }
    
    return revenueByPeriod;
  }

  private calculatePaybackPeriod(revenueByPeriod: CohortAnalysis['revenueByPeriod'], cac: number): number {
    // Mock implementation
    let cumulativeRevenue = 0;
    for (let i = 0; i < revenueByPeriod.length; i++) {
      cumulativeRevenue += revenueByPeriod[i].revenue;
      if (cumulativeRevenue >= cac) {
        return i + 1;
      }
    }
    return revenueByPeriod.length;
  }

  private calculateRevenueRetention(revenueByPeriod: CohortAnalysis['revenueByPeriod'], months: number): number {
    if (months > revenueByPeriod.length || revenueByPeriod[0].revenue === 0) return 0;
    return revenueByPeriod[months - 1].revenue / revenueByPeriod[0].revenue;
  }

  private async getMRRAtDate(tenantId: string, date: Date): Promise<number> {
    // Mock implementation
    return 100000; // $100k MRR
  }

  private async getMRRMovements(tenantId: string, period: { start: Date; end: Date }): Promise<any[]> {
    // Mock implementation
    return [
      { type: 'new', amount: 5000, customerId: 'cust1' },
      { type: 'expansion', amount: 2000, customerId: 'cust2' },
      { type: 'contraction', amount: -1000, customerId: 'cust3' },
      { type: 'churn', amount: -3000, customerId: 'cust4' },
    ];
  }

  private async calculateCustomerGrowth(tenantId: string, period: any): Promise<number> {
    // Mock implementation
    return 0.05; // 5% customer growth
  }

  private async calculateNetRevenueRetention(tenantId: string, period: any): Promise<number> {
    // Mock implementation
    return 1.15; // 115% net revenue retention
  }

  private async generateExecutiveInsights(
    metrics: FinancialMetrics,
    cohorts: CohortAnalysis[],
    waterfall: RevenueWaterfall
  ): Promise<ExecutiveReport['insights']> {
    const insights: ExecutiveReport['insights'] = [];

    // Growth insights
    if (metrics.revenue.growth.mrrGrowth > 0.1) {
      insights.push({
        category: 'growth',
        insight: `Strong MRR growth of ${(metrics.revenue.growth.mrrGrowth * 100).toFixed(1)}% indicates healthy business expansion`,
        impact: 'positive',
        recommendation: 'Continue investing in customer acquisition and expansion',
        priority: 'medium',
      });
    }

    // Retention insights
    if (metrics.subscriptions.churnRate > 0.05) {
      insights.push({
        category: 'retention',
        insight: `Churn rate of ${(metrics.subscriptions.churnRate * 100).toFixed(1)}% is above industry benchmark`,
        impact: 'negative',
        recommendation: 'Implement proactive customer success programs and improve onboarding',
        priority: 'high',
      });
    }

    // Efficiency insights
    const ltv_cac_ratio = metrics.customers.customerLifetimeValue / metrics.customers.customerAcquisitionCost;
    if (ltv_cac_ratio > 3) {
      insights.push({
        category: 'efficiency',
        insight: `Excellent LTV:CAC ratio of ${ltv_cac_ratio.toFixed(1)}:1 indicates efficient customer acquisition`,
        impact: 'positive',
        recommendation: 'Consider increasing marketing spend to accelerate growth',
        priority: 'medium',
      });
    }

    return insights;
  }

  private async storeExecutiveReport(report: ExecutiveReport): Promise<void> {
    await this.db.query(`
      INSERT INTO executive_reports (
        id, tenant_id, report_type, period_start, period_end,
        summary, key_metrics, cohort_analysis, revenue_waterfall,
        insights, generated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
    `, [
      report.id,
      report.tenantId,
      report.reportType,
      report.period.start,
      report.period.end,
      JSON.stringify(report.summary),
      JSON.stringify(report.keyMetrics),
      JSON.stringify(report.cohortAnalysis),
      JSON.stringify(report.revenueWaterfall),
      JSON.stringify(report.insights),
      report.generatedAt,
    ]);
  }

  private async getHistoricalFinancialData(tenantId: string, months: number): Promise<any> {
    // Mock implementation
    return {
      averageChurnRate: 0.03,
      averageCustomerGrowth: 0.05,
      averageARPUGrowth: 0.02,
      revenueVariance: 0.1,
    };
  }

  private async generateForecastScenarios(
    historicalData: any,
    months: number,
    methodology: string
  ): Promise<FinancialForecast['scenarios']> {
    // Mock implementation
    const baseProjections = Array.from({ length: months }, (_, i) => ({
      period: new Date(Date.now() + i * 30 * 24 * 60 * 60 * 1000).toISOString().substring(0, 7),
      revenue: 1500000 * (1 + 0.02) ** i,
      mrr: 100000 * (1 + 0.015) ** i,
      customers: 800 * (1 + 0.01) ** i,
      churnRate: 0.03,
    }));

    return [
      {
        name: 'conservative',
        probability: 0.3,
        projections: baseProjections.map(p => ({
          ...p,
          revenue: p.revenue * 0.9,
          mrr: p.mrr * 0.9,
          customers: p.customers * 0.95,
        })),
      },
      {
        name: 'base',
        probability: 0.5,
        projections: baseProjections,
      },
      {
        name: 'optimistic',
        probability: 0.2,
        projections: baseProjections.map(p => ({
          ...p,
          revenue: p.revenue * 1.2,
          mrr: p.mrr * 1.15,
          customers: p.customers * 1.1,
        })),
      },
    ];
  }

  private calculateForecastConfidence(historicalData: any, methodology: string): number {
    // Mock implementation based on historical variance
    const baseConfidence = methodology === 'ml_model' ? 0.85 : 0.75;
    const varianceAdjustment = Math.max(0, 0.2 - historicalData.revenueVariance);
    return Math.min(baseConfidence + varianceAdjustment, 0.95);
  }
}

export type { FinancialMetrics, CohortAnalysis, ExecutiveReport, FinancialForecast };
