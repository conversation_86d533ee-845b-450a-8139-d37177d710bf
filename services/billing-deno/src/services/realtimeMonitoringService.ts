// Real-time Monitoring and Alerting Service
// Comprehensive monitoring for subscription health, revenue risks, and expansion opportunities

import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { DatabaseService } from "./databaseService.ts";
import { UnifiedRevenueOperationsService } from "./unifiedRevenueOperationsService.ts";
import { AutomatedWorkflowService } from "./automatedWorkflowService.ts";

// Monitoring types and interfaces
interface MonitoringRule {
  id: string;
  tenantId: string;
  name: string;
  type: 'threshold' | 'trend' | 'anomaly' | 'pattern';
  metric: string;
  conditions: {
    operator: '>' | '<' | '=' | '>=' | '<=' | '!=' | 'contains' | 'trend_up' | 'trend_down';
    value?: number | string;
    timeWindow?: number; // minutes
    threshold?: number;
    sensitivity?: 'low' | 'medium' | 'high';
  };
  severity: 'low' | 'medium' | 'high' | 'critical';
  isActive: boolean;
  alertChannels: Array<{
    type: 'email' | 'webhook' | 'slack' | 'dashboard';
    config: Record<string, any>;
  }>;
  suppressionWindow: number; // minutes
  lastTriggered?: Date;
  triggerCount: number;
  createdAt: Date;
}

interface RealtimeMetric {
  id: string;
  tenantId: string;
  subscriptionId?: string;
  customerId?: string;
  metricName: string;
  metricValue: number;
  metricUnit: string;
  timestamp: Date;
  metadata: Record<string, any>;
}

interface AlertInstance {
  id: string;
  ruleId: string;
  tenantId: string;
  subscriptionId?: string;
  customerId?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  metricValue: number;
  threshold: number;
  status: 'active' | 'acknowledged' | 'resolved' | 'suppressed';
  createdAt: Date;
  acknowledgedAt?: Date;
  resolvedAt?: Date;
  acknowledgedBy?: string;
  resolvedBy?: string;
  escalationLevel: number;
  relatedAlerts: string[];
}

interface MonitoringDashboard {
  summary: {
    totalRules: number;
    activeRules: number;
    totalAlerts: number;
    activeAlerts: number;
    criticalAlerts: number;
    averageResolutionTime: number;
  };
  recentAlerts: AlertInstance[];
  metricTrends: Array<{
    metricName: string;
    currentValue: number;
    previousValue: number;
    changePercentage: number;
    trend: 'up' | 'down' | 'stable';
  }>;
  systemHealth: {
    subscriptionHealth: number;
    revenueHealth: number;
    customerHealth: number;
    overallHealth: number;
  };
}

export class RealtimeMonitoringService {
  private db: DatabaseService;
  private revenueOps: UnifiedRevenueOperationsService;
  private workflows: AutomatedWorkflowService;
  private monitoringInterval: number = 30000; // 30 seconds
  private intervalId?: number;

  constructor() {
    this.db = new DatabaseService();
    this.revenueOps = new UnifiedRevenueOperationsService();
    this.workflows = new AutomatedWorkflowService();
  }

  /**
   * Start real-time monitoring
   */
  startMonitoring(): void {
    if (this.intervalId) {
      this.stopMonitoring();
    }

    this.intervalId = setInterval(() => {
      this.runMonitoringCycle().catch(error => {
        logger.error("Monitoring cycle failed", {
          error: (error as Error).message,
        });
      });
    }, this.monitoringInterval);

    logger.info("Real-time monitoring started", {
      interval: this.monitoringInterval,
    });
  }

  /**
   * Stop real-time monitoring
   */
  stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }

    logger.info("Real-time monitoring stopped");
  }

  /**
   * Create monitoring rule
   */
  async createMonitoringRule(
    tenantId: string,
    ruleData: {
      name: string;
      type: MonitoringRule['type'];
      metric: string;
      conditions: MonitoringRule['conditions'];
      severity: MonitoringRule['severity'];
      alertChannels: MonitoringRule['alertChannels'];
      suppressionWindow?: number;
    }
  ): Promise<MonitoringRule> {
    const ruleId = `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date();

    const rule: MonitoringRule = {
      id: ruleId,
      tenantId,
      name: ruleData.name,
      type: ruleData.type,
      metric: ruleData.metric,
      conditions: ruleData.conditions,
      severity: ruleData.severity,
      isActive: true,
      alertChannels: ruleData.alertChannels,
      suppressionWindow: ruleData.suppressionWindow || 60, // 1 hour default
      triggerCount: 0,
      createdAt: now,
    };

    // Store rule in database
    await this.storeMonitoringRule(rule);

    logger.info("Monitoring rule created", {
      tenantId,
      ruleId,
      name: ruleData.name,
      metric: ruleData.metric,
      severity: ruleData.severity,
    });

    return rule;
  }

  /**
   * Record real-time metric
   */
  async recordMetric(
    tenantId: string,
    metricData: {
      subscriptionId?: string;
      customerId?: string;
      metricName: string;
      metricValue: number;
      metricUnit: string;
      metadata?: Record<string, any>;
    }
  ): Promise<RealtimeMetric> {
    const metricId = `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date();

    const metric: RealtimeMetric = {
      id: metricId,
      tenantId,
      subscriptionId: metricData.subscriptionId,
      customerId: metricData.customerId,
      metricName: metricData.metricName,
      metricValue: metricData.metricValue,
      metricUnit: metricData.metricUnit,
      timestamp: now,
      metadata: metricData.metadata || {},
    };

    // Store metric in TimescaleDB
    await this.storeMetric(metric);

    // Check monitoring rules for this metric
    await this.evaluateMetricRules(metric);

    return metric;
  }

  /**
   * Get monitoring dashboard
   */
  async getMonitoringDashboard(tenantId: string): Promise<MonitoringDashboard> {
    const startTime = performance.now();

    try {
      // Get summary statistics
      const summary = await this.getMonitoringSummary(tenantId);

      // Get recent alerts
      const recentAlerts = await this.getRecentAlerts(tenantId, 10);

      // Get metric trends
      const metricTrends = await this.getMetricTrends(tenantId);

      // Calculate system health
      const systemHealth = await this.calculateSystemHealth(tenantId);

      const dashboard: MonitoringDashboard = {
        summary,
        recentAlerts,
        metricTrends,
        systemHealth,
      };

      const queryTime = performance.now() - startTime;

      logger.info("Monitoring dashboard generated", {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        totalAlerts: summary.totalAlerts,
        activeAlerts: summary.activeAlerts,
        overallHealth: systemHealth.overallHealth,
      });

      return dashboard;
    } catch (error) {
      logger.error("Failed to generate monitoring dashboard", {
        tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Acknowledge alert
   */
  async acknowledgeAlert(
    tenantId: string,
    alertId: string,
    acknowledgedBy: string
  ): Promise<AlertInstance> {
    const alert = await this.getAlert(tenantId, alertId);
    if (!alert) {
      throw new Error(`Alert ${alertId} not found`);
    }

    alert.status = 'acknowledged';
    alert.acknowledgedAt = new Date();
    alert.acknowledgedBy = acknowledgedBy;

    await this.updateAlert(alert);

    logger.info("Alert acknowledged", {
      tenantId,
      alertId,
      acknowledgedBy,
    });

    return alert;
  }

  /**
   * Resolve alert
   */
  async resolveAlert(
    tenantId: string,
    alertId: string,
    resolvedBy: string
  ): Promise<AlertInstance> {
    const alert = await this.getAlert(tenantId, alertId);
    if (!alert) {
      throw new Error(`Alert ${alertId} not found`);
    }

    alert.status = 'resolved';
    alert.resolvedAt = new Date();
    alert.resolvedBy = resolvedBy;

    await this.updateAlert(alert);

    logger.info("Alert resolved", {
      tenantId,
      alertId,
      resolvedBy,
      resolutionTime: alert.resolvedAt.getTime() - alert.createdAt.getTime(),
    });

    return alert;
  }

  /**
   * Run monitoring cycle
   */
  private async runMonitoringCycle(): Promise<void> {
    try {
      // Get all active tenants
      const tenants = await this.getActiveTenants();

      for (const tenantId of tenants) {
        try {
          // Collect metrics for tenant
          await this.collectTenantMetrics(tenantId);

          // Evaluate monitoring rules
          await this.evaluateTenantRules(tenantId);

          // Check for anomalies
          await this.detectAnomalies(tenantId);
        } catch (error) {
          logger.error("Failed to process tenant in monitoring cycle", {
            tenantId,
            error: (error as Error).message,
          });
        }
      }
    } catch (error) {
      logger.error("Monitoring cycle failed", {
        error: (error as Error).message,
      });
    }
  }

  /**
   * Collect metrics for tenant
   */
  private async collectTenantMetrics(tenantId: string): Promise<void> {
    try {
      // Get subscription health metrics
      const subscriptionMetrics = await this.collectSubscriptionMetrics(tenantId);

      // Get revenue metrics
      const revenueMetrics = await this.collectRevenueMetrics(tenantId);

      // Get usage metrics
      const usageMetrics = await this.collectUsageMetrics(tenantId);

      // Store all metrics
      const allMetrics = [...subscriptionMetrics, ...revenueMetrics, ...usageMetrics];
      
      for (const metric of allMetrics) {
        await this.recordMetric(tenantId, metric);
      }

      logger.debug("Tenant metrics collected", {
        tenantId,
        metricsCount: allMetrics.length,
      });
    } catch (error) {
      logger.error("Failed to collect tenant metrics", {
        tenantId,
        error: (error as Error).message,
      });
    }
  }

  /**
   * Collect subscription health metrics
   */
  private async collectSubscriptionMetrics(tenantId: string): Promise<any[]> {
    // Get subscription health data
    const alerts = await this.revenueOps.monitorSubscriptionHealth(tenantId);
    
    const metrics = [];

    // Count alerts by severity
    const criticalAlerts = alerts.filter(a => a.severity === 'critical').length;
    const highAlerts = alerts.filter(a => a.severity === 'high').length;
    const mediumAlerts = alerts.filter(a => a.severity === 'medium').length;

    metrics.push(
      {
        metricName: 'critical_alerts',
        metricValue: criticalAlerts,
        metricUnit: 'count',
      },
      {
        metricName: 'high_alerts',
        metricValue: highAlerts,
        metricUnit: 'count',
      },
      {
        metricName: 'medium_alerts',
        metricValue: mediumAlerts,
        metricUnit: 'count',
      }
    );

    return metrics;
  }

  /**
   * Collect revenue metrics
   */
  private async collectRevenueMetrics(tenantId: string): Promise<any[]> {
    // Mock implementation - in production, this would collect actual revenue metrics
    return [
      {
        metricName: 'mrr',
        metricValue: 125000,
        metricUnit: 'usd',
      },
      {
        metricName: 'churn_rate',
        metricValue: 0.03,
        metricUnit: 'percentage',
      },
      {
        metricName: 'expansion_rate',
        metricValue: 0.15,
        metricUnit: 'percentage',
      },
    ];
  }

  /**
   * Collect usage metrics
   */
  private async collectUsageMetrics(tenantId: string): Promise<any[]> {
    // Mock implementation - in production, this would collect actual usage metrics
    return [
      {
        metricName: 'api_calls_per_minute',
        metricValue: 1250,
        metricUnit: 'calls',
      },
      {
        metricName: 'active_subscriptions',
        metricValue: 950,
        metricUnit: 'count',
      },
    ];
  }

  /**
   * Evaluate monitoring rules for metric
   */
  private async evaluateMetricRules(metric: RealtimeMetric): Promise<void> {
    const rules = await this.getActiveRulesForMetric(metric.tenantId, metric.metricName);

    for (const rule of rules) {
      try {
        const isTriggered = await this.evaluateRule(rule, metric);
        
        if (isTriggered) {
          await this.triggerAlert(rule, metric);
        }
      } catch (error) {
        logger.error("Failed to evaluate monitoring rule", {
          ruleId: rule.id,
          metricName: metric.metricName,
          error: (error as Error).message,
        });
      }
    }
  }

  /**
   * Evaluate single monitoring rule
   */
  private async evaluateRule(rule: MonitoringRule, metric: RealtimeMetric): Promise<boolean> {
    const { operator, value, threshold } = rule.conditions;

    switch (operator) {
      case '>':
        return metric.metricValue > (value as number);
      case '<':
        return metric.metricValue < (value as number);
      case '>=':
        return metric.metricValue >= (value as number);
      case '<=':
        return metric.metricValue <= (value as number);
      case '=':
        return metric.metricValue === (value as number);
      case '!=':
        return metric.metricValue !== (value as number);
      case 'trend_up':
        return await this.checkTrendUp(metric, threshold || 0.1);
      case 'trend_down':
        return await this.checkTrendDown(metric, threshold || 0.1);
      default:
        return false;
    }
  }

  /**
   * Trigger alert for rule
   */
  private async triggerAlert(rule: MonitoringRule, metric: RealtimeMetric): Promise<void> {
    // Check suppression window
    if (rule.lastTriggered) {
      const timeSinceLastTrigger = Date.now() - rule.lastTriggered.getTime();
      if (timeSinceLastTrigger < rule.suppressionWindow * 60 * 1000) {
        return; // Still in suppression window
      }
    }

    const alertId = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date();

    const alert: AlertInstance = {
      id: alertId,
      ruleId: rule.id,
      tenantId: rule.tenantId,
      subscriptionId: metric.subscriptionId,
      customerId: metric.customerId,
      severity: rule.severity,
      title: `${rule.name} Alert`,
      message: `${metric.metricName} value ${metric.metricValue} ${metric.metricUnit} triggered ${rule.name}`,
      metricValue: metric.metricValue,
      threshold: rule.conditions.value as number || 0,
      status: 'active',
      createdAt: now,
      escalationLevel: 0,
      relatedAlerts: [],
    };

    // Store alert
    await this.storeAlert(alert);

    // Send notifications
    await this.sendAlertNotifications(alert, rule);

    // Update rule trigger count and last triggered
    await this.updateRuleTrigger(rule.id);

    // Trigger workflows if applicable
    await this.workflows.evaluateAndExecuteWorkflows(
      rule.tenantId,
      'monitoring_alert',
      {
        alertId: alert.id,
        ruleId: rule.id,
        severity: alert.severity,
        metricName: metric.metricName,
        metricValue: metric.metricValue,
        subscriptionId: metric.subscriptionId,
        customerId: metric.customerId,
      }
    );

    logger.info("Alert triggered", {
      alertId,
      ruleId: rule.id,
      tenantId: rule.tenantId,
      severity: rule.severity,
      metricName: metric.metricName,
      metricValue: metric.metricValue,
    });
  }

  // Helper methods
  private async getActiveTenants(): Promise<string[]> {
    const result = await this.db.query(`
      SELECT DISTINCT tenant_id FROM subscriptions WHERE status = 'active'
    `);
    return result.rows.map(row => row.tenant_id);
  }

  private async storeMonitoringRule(rule: MonitoringRule): Promise<void> {
    await this.db.query(`
      INSERT INTO monitoring_rules (
        id, tenant_id, name, type, metric, conditions, severity,
        is_active, alert_channels, suppression_window, trigger_count, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
    `, [
      rule.id,
      rule.tenantId,
      rule.name,
      rule.type,
      rule.metric,
      JSON.stringify(rule.conditions),
      rule.severity,
      rule.isActive,
      JSON.stringify(rule.alertChannels),
      rule.suppressionWindow,
      rule.triggerCount,
      rule.createdAt,
    ]);
  }

  private async storeMetric(metric: RealtimeMetric): Promise<void> {
    await this.db.query(`
      INSERT INTO realtime_metrics (
        id, tenant_id, subscription_id, customer_id, metric_name,
        metric_value, metric_unit, timestamp, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `, [
      metric.id,
      metric.tenantId,
      metric.subscriptionId,
      metric.customerId,
      metric.metricName,
      metric.metricValue,
      metric.metricUnit,
      metric.timestamp,
      JSON.stringify(metric.metadata),
    ]);
  }

  private async storeAlert(alert: AlertInstance): Promise<void> {
    await this.db.query(`
      INSERT INTO alert_instances (
        id, rule_id, tenant_id, subscription_id, customer_id, severity,
        title, message, metric_value, threshold, status, created_at,
        escalation_level, related_alerts
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
    `, [
      alert.id,
      alert.ruleId,
      alert.tenantId,
      alert.subscriptionId,
      alert.customerId,
      alert.severity,
      alert.title,
      alert.message,
      alert.metricValue,
      alert.threshold,
      alert.status,
      alert.createdAt,
      alert.escalationLevel,
      JSON.stringify(alert.relatedAlerts),
    ]);
  }

  private async getActiveRulesForMetric(tenantId: string, metricName: string): Promise<MonitoringRule[]> {
    const result = await this.db.query(`
      SELECT * FROM monitoring_rules 
      WHERE tenant_id = $1 AND metric = $2 AND is_active = true
    `, [tenantId, metricName]);

    return result.rows.map(this.mapRuleRow);
  }

  private async checkTrendUp(metric: RealtimeMetric, threshold: number): Promise<boolean> {
    // Get previous values for trend analysis
    const result = await this.db.query(`
      SELECT metric_value FROM realtime_metrics 
      WHERE tenant_id = $1 AND metric_name = $2 
        AND timestamp < $3
      ORDER BY timestamp DESC 
      LIMIT 5
    `, [metric.tenantId, metric.metricName, metric.timestamp]);

    if (result.rows.length < 2) return false;

    const values = [metric.metricValue, ...result.rows.map(row => row.metric_value)];
    const trend = this.calculateTrend(values);
    
    return trend > threshold;
  }

  private async checkTrendDown(metric: RealtimeMetric, threshold: number): Promise<boolean> {
    // Similar to checkTrendUp but for downward trend
    const result = await this.db.query(`
      SELECT metric_value FROM realtime_metrics 
      WHERE tenant_id = $1 AND metric_name = $2 
        AND timestamp < $3
      ORDER BY timestamp DESC 
      LIMIT 5
    `, [metric.tenantId, metric.metricName, metric.timestamp]);

    if (result.rows.length < 2) return false;

    const values = [metric.metricValue, ...result.rows.map(row => row.metric_value)];
    const trend = this.calculateTrend(values);
    
    return trend < -threshold;
  }

  private calculateTrend(values: number[]): number {
    if (values.length < 2) return 0;
    
    const first = values[values.length - 1];
    const last = values[0];
    
    return (last - first) / first;
  }

  private async sendAlertNotifications(alert: AlertInstance, rule: MonitoringRule): Promise<void> {
    for (const channel of rule.alertChannels) {
      try {
        switch (channel.type) {
          case 'email':
            await this.sendEmailAlert(alert, channel.config);
            break;
          case 'webhook':
            await this.sendWebhookAlert(alert, channel.config);
            break;
          case 'slack':
            await this.sendSlackAlert(alert, channel.config);
            break;
        }
      } catch (error) {
        logger.error("Failed to send alert notification", {
          alertId: alert.id,
          channelType: channel.type,
          error: (error as Error).message,
        });
      }
    }
  }

  private async sendEmailAlert(alert: AlertInstance, config: any): Promise<void> {
    // Mock implementation
    logger.info("Email alert sent", { alertId: alert.id, recipients: config.recipients });
  }

  private async sendWebhookAlert(alert: AlertInstance, config: any): Promise<void> {
    // Mock implementation
    logger.info("Webhook alert sent", { alertId: alert.id, webhookUrl: config.url });
  }

  private async sendSlackAlert(alert: AlertInstance, config: any): Promise<void> {
    // Mock implementation
    logger.info("Slack alert sent", { alertId: alert.id, channel: config.channel });
  }

  private async updateRuleTrigger(ruleId: string): Promise<void> {
    await this.db.query(`
      UPDATE monitoring_rules 
      SET trigger_count = trigger_count + 1, last_triggered = NOW()
      WHERE id = $1
    `, [ruleId]);
  }

  private async getMonitoringSummary(tenantId: string): Promise<MonitoringDashboard['summary']> {
    // Mock implementation
    return {
      totalRules: 15,
      activeRules: 12,
      totalAlerts: 45,
      activeAlerts: 8,
      criticalAlerts: 2,
      averageResolutionTime: 1800000, // 30 minutes in ms
    };
  }

  private async getRecentAlerts(tenantId: string, limit: number): Promise<AlertInstance[]> {
    const result = await this.db.query(`
      SELECT * FROM alert_instances 
      WHERE tenant_id = $1 
      ORDER BY created_at DESC 
      LIMIT $2
    `, [tenantId, limit]);

    return result.rows.map(this.mapAlertRow);
  }

  private async getMetricTrends(tenantId: string): Promise<MonitoringDashboard['metricTrends']> {
    // Mock implementation
    return [
      {
        metricName: 'mrr',
        currentValue: 125000,
        previousValue: 120000,
        changePercentage: 4.17,
        trend: 'up',
      },
      {
        metricName: 'churn_rate',
        currentValue: 0.03,
        previousValue: 0.035,
        changePercentage: -14.29,
        trend: 'down',
      },
    ];
  }

  private async calculateSystemHealth(tenantId: string): Promise<MonitoringDashboard['systemHealth']> {
    // Mock implementation
    return {
      subscriptionHealth: 85,
      revenueHealth: 92,
      customerHealth: 88,
      overallHealth: 88,
    };
  }

  private async getAlert(tenantId: string, alertId: string): Promise<AlertInstance | null> {
    const result = await this.db.query(`
      SELECT * FROM alert_instances 
      WHERE id = $1 AND tenant_id = $2
    `, [alertId, tenantId]);

    return result.rows.length > 0 ? this.mapAlertRow(result.rows[0]) : null;
  }

  private async updateAlert(alert: AlertInstance): Promise<void> {
    await this.db.query(`
      UPDATE alert_instances 
      SET status = $1, acknowledged_at = $2, resolved_at = $3,
          acknowledged_by = $4, resolved_by = $5
      WHERE id = $6
    `, [
      alert.status,
      alert.acknowledgedAt,
      alert.resolvedAt,
      alert.acknowledgedBy,
      alert.resolvedBy,
      alert.id,
    ]);
  }

  private async evaluateTenantRules(tenantId: string): Promise<void> {
    // Implementation for evaluating all rules for a tenant
    logger.debug("Evaluating tenant rules", { tenantId });
  }

  private async detectAnomalies(tenantId: string): Promise<void> {
    // Implementation for anomaly detection
    logger.debug("Detecting anomalies", { tenantId });
  }

  private mapRuleRow(row: any): MonitoringRule {
    return {
      id: row.id,
      tenantId: row.tenant_id,
      name: row.name,
      type: row.type,
      metric: row.metric,
      conditions: JSON.parse(row.conditions),
      severity: row.severity,
      isActive: row.is_active,
      alertChannels: JSON.parse(row.alert_channels),
      suppressionWindow: row.suppression_window,
      lastTriggered: row.last_triggered ? new Date(row.last_triggered) : undefined,
      triggerCount: row.trigger_count,
      createdAt: new Date(row.created_at),
    };
  }

  private mapAlertRow(row: any): AlertInstance {
    return {
      id: row.id,
      ruleId: row.rule_id,
      tenantId: row.tenant_id,
      subscriptionId: row.subscription_id,
      customerId: row.customer_id,
      severity: row.severity,
      title: row.title,
      message: row.message,
      metricValue: row.metric_value,
      threshold: row.threshold,
      status: row.status,
      createdAt: new Date(row.created_at),
      acknowledgedAt: row.acknowledged_at ? new Date(row.acknowledged_at) : undefined,
      resolvedAt: row.resolved_at ? new Date(row.resolved_at) : undefined,
      acknowledgedBy: row.acknowledged_by,
      resolvedBy: row.resolved_by,
      escalationLevel: row.escalation_level,
      relatedAlerts: JSON.parse(row.related_alerts || '[]'),
    };
  }
}

export { RealtimeMonitoringService, type MonitoringRule, type AlertInstance, type MonitoringDashboard };
