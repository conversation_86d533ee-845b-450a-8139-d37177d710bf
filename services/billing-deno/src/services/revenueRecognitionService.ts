// Revenue Recognition Automation Service
// Automated revenue recognition, deferred revenue tracking, and compliance reporting

import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { DatabaseService } from "./databaseService.ts";

// Revenue recognition types and interfaces
interface RevenueTransaction {
  id: string;
  tenantId: string;
  subscriptionId: string;
  customerId: string;
  transactionType: 'subscription' | 'usage' | 'setup_fee' | 'overage' | 'refund' | 'credit';
  amount: number;
  currency: string;
  recognitionMethod: 'immediate' | 'deferred' | 'milestone' | 'percentage_completion';
  recognitionPeriod: {
    start: Date;
    end: Date;
  };
  contractualObligations: string[];
  performanceObligations: Array<{
    id: string;
    description: string;
    allocatedAmount: number;
    deliveryStatus: 'pending' | 'in_progress' | 'delivered';
    deliveryDate?: Date;
  }>;
  createdAt: Date;
  recognizedAt?: Date;
}

interface DeferredRevenue {
  id: string;
  tenantId: string;
  transactionId: string;
  totalAmount: number;
  recognizedAmount: number;
  remainingAmount: number;
  recognitionSchedule: Array<{
    date: Date;
    amount: number;
    status: 'pending' | 'recognized';
    recognizedAt?: Date;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

interface RevenueRecognitionRule {
  id: string;
  tenantId: string;
  name: string;
  conditions: {
    transactionType?: string[];
    amountRange?: { min: number; max: number };
    contractTerms?: string[];
    productTypes?: string[];
  };
  recognitionMethod: 'immediate' | 'deferred' | 'milestone' | 'percentage_completion';
  recognitionPeriod?: {
    type: 'fixed' | 'contract_term' | 'delivery_based';
    duration?: number; // months
  };
  complianceStandard: 'ASC_606' | 'IFRS_15' | 'GAAP' | 'custom';
  isActive: boolean;
  createdAt: Date;
}

interface ComplianceReport {
  id: string;
  tenantId: string;
  reportType: 'monthly' | 'quarterly' | 'annual' | 'custom';
  period: {
    start: Date;
    end: Date;
  };
  metrics: {
    totalRevenue: number;
    recognizedRevenue: number;
    deferredRevenue: number;
    contractLiabilities: number;
    contractAssets: number;
    revenueBySegment: Record<string, number>;
    revenueByProduct: Record<string, number>;
  };
  complianceChecks: Array<{
    rule: string;
    status: 'pass' | 'fail' | 'warning';
    description: string;
    impact?: number;
  }>;
  generatedAt: Date;
}

interface RevenueSchedule {
  transactionId: string;
  schedule: Array<{
    period: string;
    scheduledAmount: number;
    recognizedAmount: number;
    remainingAmount: number;
    recognitionDate: Date;
    status: 'pending' | 'recognized' | 'adjusted';
  }>;
  totalScheduled: number;
  totalRecognized: number;
  totalRemaining: number;
}

export class RevenueRecognitionService {
  private db: DatabaseService;

  constructor() {
    this.db = new DatabaseService();
  }

  /**
   * Process revenue transaction and apply recognition rules
   */
  async processRevenueTransaction(
    tenantId: string,
    transactionData: {
      subscriptionId: string;
      customerId: string;
      transactionType: RevenueTransaction['transactionType'];
      amount: number;
      currency: string;
      contractualObligations: string[];
      performanceObligations: Array<{
        description: string;
        allocatedAmount: number;
      }>;
    }
  ): Promise<RevenueTransaction> {
    const transactionId = `rev_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Apply revenue recognition rules
    const applicableRule = await this.findApplicableRule(tenantId, transactionData);
    
    // Determine recognition method and period
    const recognitionMethod = applicableRule?.recognitionMethod || 'deferred';
    const recognitionPeriod = await this.calculateRecognitionPeriod(
      tenantId,
      transactionData.subscriptionId,
      recognitionMethod,
      applicableRule
    );

    // Create revenue transaction
    const transaction: RevenueTransaction = {
      id: transactionId,
      tenantId,
      subscriptionId: transactionData.subscriptionId,
      customerId: transactionData.customerId,
      transactionType: transactionData.transactionType,
      amount: transactionData.amount,
      currency: transactionData.currency,
      recognitionMethod,
      recognitionPeriod,
      contractualObligations: transactionData.contractualObligations,
      performanceObligations: transactionData.performanceObligations.map((po, index) => ({
        id: `po_${index + 1}`,
        description: po.description,
        allocatedAmount: po.allocatedAmount,
        deliveryStatus: 'pending',
      })),
      createdAt: new Date(),
    };

    // Store transaction
    await this.storeRevenueTransaction(transaction);

    // Create deferred revenue schedule if applicable
    if (recognitionMethod === 'deferred') {
      await this.createDeferredRevenueSchedule(transaction);
    } else if (recognitionMethod === 'immediate') {
      await this.recognizeRevenueImmediately(transaction);
    }

    // Log compliance event
    await this.logComplianceEvent(tenantId, transactionId, 'transaction_created', {
      amount: transactionData.amount,
      recognitionMethod,
      complianceStandard: applicableRule?.complianceStandard || 'ASC_606',
    });

    logger.info("Revenue transaction processed", {
      tenantId,
      transactionId,
      amount: transactionData.amount,
      recognitionMethod,
      transactionType: transactionData.transactionType,
    });

    return transaction;
  }

  /**
   * Recognize deferred revenue based on schedule
   */
  async recognizeDeferredRevenue(tenantId: string, date: Date = new Date()): Promise<{
    processedTransactions: number;
    totalRecognized: number;
    errors: string[];
  }> {
    let processedTransactions = 0;
    let totalRecognized = 0;
    const errors: string[] = [];

    try {
      // Get pending deferred revenue items for the date
      const pendingItems = await this.getPendingDeferredRevenue(tenantId, date);

      for (const item of pendingItems) {
        try {
          const recognizedAmount = await this.processScheduledRecognition(item, date);
          totalRecognized += recognizedAmount;
          processedTransactions++;

          // Log recognition event
          await this.logComplianceEvent(tenantId, item.transactionId, 'revenue_recognized', {
            amount: recognizedAmount,
            recognitionDate: date.toISOString(),
            remainingDeferred: item.remainingAmount - recognizedAmount,
          });
        } catch (error) {
          const errorMessage = `Failed to recognize revenue for transaction ${item.transactionId}: ${(error as Error).message}`;
          errors.push(errorMessage);
          logger.error("Revenue recognition failed", {
            tenantId,
            transactionId: item.transactionId,
            error: errorMessage,
          });
        }
      }

      logger.info("Deferred revenue recognition completed", {
        tenantId,
        processedTransactions,
        totalRecognized,
        errorsCount: errors.length,
      });

      return {
        processedTransactions,
        totalRecognized,
        errors,
      };
    } catch (error) {
      logger.error("Deferred revenue recognition process failed", {
        tenantId,
        error: (error as Error).message,
      });

      return {
        processedTransactions: 0,
        totalRecognized: 0,
        errors: [(error as Error).message],
      };
    }
  }

  /**
   * Generate compliance report
   */
  async generateComplianceReport(
    tenantId: string,
    reportType: ComplianceReport['reportType'],
    period: { start: Date; end: Date }
  ): Promise<ComplianceReport> {
    const reportId = `rpt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Calculate revenue metrics
    const metrics = await this.calculateRevenueMetrics(tenantId, period);

    // Run compliance checks
    const complianceChecks = await this.runComplianceChecks(tenantId, period, metrics);

    const report: ComplianceReport = {
      id: reportId,
      tenantId,
      reportType,
      period,
      metrics,
      complianceChecks,
      generatedAt: new Date(),
    };

    // Store report
    await this.storeComplianceReport(report);

    logger.info("Compliance report generated", {
      tenantId,
      reportId,
      reportType,
      period: `${period.start.toISOString()} - ${period.end.toISOString()}`,
      totalRevenue: metrics.totalRevenue,
      complianceIssues: complianceChecks.filter(c => c.status === 'fail').length,
    });

    return report;
  }

  /**
   * Get revenue schedule for a transaction
   */
  async getRevenueSchedule(tenantId: string, transactionId: string): Promise<RevenueSchedule> {
    const transaction = await this.getRevenueTransaction(tenantId, transactionId);
    if (!transaction) {
      throw new Error(`Transaction ${transactionId} not found`);
    }

    const deferredRevenue = await this.getDeferredRevenue(tenantId, transactionId);
    
    if (!deferredRevenue) {
      // Immediate recognition
      return {
        transactionId,
        schedule: [{
          period: transaction.createdAt.toISOString().substring(0, 7), // YYYY-MM
          scheduledAmount: transaction.amount,
          recognizedAmount: transaction.recognizedAt ? transaction.amount : 0,
          remainingAmount: transaction.recognizedAt ? 0 : transaction.amount,
          recognitionDate: transaction.recognizedAt || transaction.createdAt,
          status: transaction.recognizedAt ? 'recognized' : 'pending',
        }],
        totalScheduled: transaction.amount,
        totalRecognized: transaction.recognizedAt ? transaction.amount : 0,
        totalRemaining: transaction.recognizedAt ? 0 : transaction.amount,
      };
    }

    // Deferred recognition schedule
    const schedule = deferredRevenue.recognitionSchedule.map(item => ({
      period: item.date.toISOString().substring(0, 7), // YYYY-MM
      scheduledAmount: item.amount,
      recognizedAmount: item.status === 'recognized' ? item.amount : 0,
      remainingAmount: item.status === 'recognized' ? 0 : item.amount,
      recognitionDate: item.date,
      status: item.status === 'recognized' ? 'recognized' as const : 'pending' as const,
    }));

    return {
      transactionId,
      schedule,
      totalScheduled: deferredRevenue.totalAmount,
      totalRecognized: deferredRevenue.recognizedAmount,
      totalRemaining: deferredRevenue.remainingAmount,
    };
  }

  /**
   * Adjust revenue recognition for contract modifications
   */
  async adjustRevenueRecognition(
    tenantId: string,
    transactionId: string,
    adjustment: {
      type: 'amount_change' | 'period_change' | 'method_change';
      newAmount?: number;
      newPeriod?: { start: Date; end: Date };
      newMethod?: RevenueTransaction['recognitionMethod'];
      reason: string;
    }
  ): Promise<{
    success: boolean;
    adjustmentId?: string;
    impact: {
      previousRecognized: number;
      newRecognized: number;
      deferredAdjustment: number;
    };
  }> {
    try {
      const transaction = await this.getRevenueTransaction(tenantId, transactionId);
      if (!transaction) {
        throw new Error(`Transaction ${transactionId} not found`);
      }

      const adjustmentId = `adj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Calculate impact
      const previousRecognized = await this.getRecognizedAmount(tenantId, transactionId);
      
      // Apply adjustment based on type
      let newRecognized = previousRecognized;
      let deferredAdjustment = 0;

      if (adjustment.type === 'amount_change' && adjustment.newAmount) {
        const ratio = adjustment.newAmount / transaction.amount;
        newRecognized = previousRecognized * ratio;
        deferredAdjustment = (adjustment.newAmount - newRecognized);
      } else if (adjustment.type === 'period_change' && adjustment.newPeriod) {
        // Recalculate recognition schedule
        const newSchedule = await this.recalculateRecognitionSchedule(
          transaction,
          adjustment.newPeriod
        );
        newRecognized = this.calculateRecognizedToDate(newSchedule, new Date());
        deferredAdjustment = transaction.amount - newRecognized;
      }

      // Store adjustment
      await this.storeRevenueAdjustment(tenantId, adjustmentId, transactionId, adjustment, {
        previousRecognized,
        newRecognized,
        deferredAdjustment,
      });

      // Update deferred revenue schedule
      await this.updateDeferredRevenueSchedule(tenantId, transactionId, adjustment);

      // Log compliance event
      await this.logComplianceEvent(tenantId, transactionId, 'revenue_adjusted', {
        adjustmentId,
        adjustmentType: adjustment.type,
        reason: adjustment.reason,
        impact: { previousRecognized, newRecognized, deferredAdjustment },
      });

      logger.info("Revenue recognition adjusted", {
        tenantId,
        transactionId,
        adjustmentId,
        adjustmentType: adjustment.type,
        impact: { previousRecognized, newRecognized, deferredAdjustment },
      });

      return {
        success: true,
        adjustmentId,
        impact: {
          previousRecognized,
          newRecognized,
          deferredAdjustment,
        },
      };
    } catch (error) {
      logger.error("Revenue recognition adjustment failed", {
        tenantId,
        transactionId,
        error: (error as Error).message,
      });

      return {
        success: false,
        impact: {
          previousRecognized: 0,
          newRecognized: 0,
          deferredAdjustment: 0,
        },
      };
    }
  }

  // Helper methods
  private async findApplicableRule(
    tenantId: string,
    transactionData: any
  ): Promise<RevenueRecognitionRule | null> {
    const result = await this.db.query(`
      SELECT * FROM revenue_recognition_rules 
      WHERE tenant_id = $1 AND is_active = true
      ORDER BY created_at DESC
    `, [tenantId]);

    // Find the first rule that matches the transaction
    for (const row of result.rows) {
      const rule: RevenueRecognitionRule = {
        id: row.id,
        tenantId: row.tenant_id,
        name: row.name,
        conditions: JSON.parse(row.conditions),
        recognitionMethod: row.recognition_method,
        recognitionPeriod: JSON.parse(row.recognition_period || 'null'),
        complianceStandard: row.compliance_standard,
        isActive: row.is_active,
        createdAt: new Date(row.created_at),
      };

      if (this.matchesRuleConditions(rule, transactionData)) {
        return rule;
      }
    }

    return null;
  }

  private matchesRuleConditions(rule: RevenueRecognitionRule, transactionData: any): boolean {
    const conditions = rule.conditions;

    if (conditions.transactionType && !conditions.transactionType.includes(transactionData.transactionType)) {
      return false;
    }

    if (conditions.amountRange) {
      if (transactionData.amount < conditions.amountRange.min || 
          transactionData.amount > conditions.amountRange.max) {
        return false;
      }
    }

    return true;
  }

  private async calculateRecognitionPeriod(
    tenantId: string,
    subscriptionId: string,
    recognitionMethod: string,
    rule?: RevenueRecognitionRule | null
  ): Promise<{ start: Date; end: Date }> {
    const start = new Date();
    let end = new Date(start);

    if (recognitionMethod === 'immediate') {
      return { start, end: start };
    }

    if (rule?.recognitionPeriod) {
      if (rule.recognitionPeriod.type === 'fixed' && rule.recognitionPeriod.duration) {
        end.setMonth(end.getMonth() + rule.recognitionPeriod.duration);
      } else if (rule.recognitionPeriod.type === 'contract_term') {
        // Get subscription term
        const subscription = await this.getSubscriptionTerm(tenantId, subscriptionId);
        end = subscription.termEnd;
      }
    } else {
      // Default to 12 months
      end.setMonth(end.getMonth() + 12);
    }

    return { start, end };
  }

  private async storeRevenueTransaction(transaction: RevenueTransaction): Promise<void> {
    await this.db.query(`
      INSERT INTO revenue_transactions (
        id, tenant_id, subscription_id, customer_id, transaction_type,
        amount, currency, recognition_method, recognition_period_start,
        recognition_period_end, contractual_obligations, performance_obligations,
        created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
    `, [
      transaction.id,
      transaction.tenantId,
      transaction.subscriptionId,
      transaction.customerId,
      transaction.transactionType,
      transaction.amount,
      transaction.currency,
      transaction.recognitionMethod,
      transaction.recognitionPeriod.start,
      transaction.recognitionPeriod.end,
      JSON.stringify(transaction.contractualObligations),
      JSON.stringify(transaction.performanceObligations),
      transaction.createdAt,
    ]);
  }

  private async createDeferredRevenueSchedule(transaction: RevenueTransaction): Promise<void> {
    const schedule = this.generateRecognitionSchedule(transaction);
    
    const deferredRevenue: DeferredRevenue = {
      id: `def_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      tenantId: transaction.tenantId,
      transactionId: transaction.id,
      totalAmount: transaction.amount,
      recognizedAmount: 0,
      remainingAmount: transaction.amount,
      recognitionSchedule: schedule,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await this.db.query(`
      INSERT INTO deferred_revenue (
        id, tenant_id, transaction_id, total_amount, recognized_amount,
        remaining_amount, recognition_schedule, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `, [
      deferredRevenue.id,
      deferredRevenue.tenantId,
      deferredRevenue.transactionId,
      deferredRevenue.totalAmount,
      deferredRevenue.recognizedAmount,
      deferredRevenue.remainingAmount,
      JSON.stringify(deferredRevenue.recognitionSchedule),
      deferredRevenue.createdAt,
      deferredRevenue.updatedAt,
    ]);
  }

  private generateRecognitionSchedule(transaction: RevenueTransaction): DeferredRevenue['recognitionSchedule'] {
    const schedule: DeferredRevenue['recognitionSchedule'] = [];
    const start = transaction.recognitionPeriod.start;
    const end = transaction.recognitionPeriod.end;
    
    // Calculate monthly recognition amounts
    const months = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 30));
    const monthlyAmount = transaction.amount / months;

    for (let i = 0; i < months; i++) {
      const recognitionDate = new Date(start);
      recognitionDate.setMonth(recognitionDate.getMonth() + i);
      
      schedule.push({
        date: recognitionDate,
        amount: i === months - 1 ? transaction.amount - (monthlyAmount * (months - 1)) : monthlyAmount,
        status: 'pending',
      });
    }

    return schedule;
  }

  private async recognizeRevenueImmediately(transaction: RevenueTransaction): Promise<void> {
    await this.db.query(`
      UPDATE revenue_transactions 
      SET recognized_at = $1 
      WHERE id = $2 AND tenant_id = $3
    `, [new Date(), transaction.id, transaction.tenantId]);
  }

  private async getPendingDeferredRevenue(tenantId: string, date: Date): Promise<DeferredRevenue[]> {
    const result = await this.db.query(`
      SELECT * FROM deferred_revenue 
      WHERE tenant_id = $1 AND remaining_amount > 0
    `, [tenantId]);

    return result.rows.map(row => ({
      id: row.id,
      tenantId: row.tenant_id,
      transactionId: row.transaction_id,
      totalAmount: row.total_amount,
      recognizedAmount: row.recognized_amount,
      remainingAmount: row.remaining_amount,
      recognitionSchedule: JSON.parse(row.recognition_schedule),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    })).filter(item => 
      item.recognitionSchedule.some(s => 
        s.status === 'pending' && new Date(s.date) <= date
      )
    );
  }

  private async processScheduledRecognition(item: DeferredRevenue, date: Date): Promise<number> {
    let recognizedAmount = 0;
    
    for (const scheduleItem of item.recognitionSchedule) {
      if (scheduleItem.status === 'pending' && new Date(scheduleItem.date) <= date) {
        scheduleItem.status = 'recognized';
        scheduleItem.recognizedAt = date;
        recognizedAmount += scheduleItem.amount;
      }
    }

    if (recognizedAmount > 0) {
      // Update deferred revenue record
      await this.db.query(`
        UPDATE deferred_revenue 
        SET recognized_amount = recognized_amount + $1,
            remaining_amount = remaining_amount - $1,
            recognition_schedule = $2,
            updated_at = $3
        WHERE id = $4 AND tenant_id = $5
      `, [
        recognizedAmount,
        JSON.stringify(item.recognitionSchedule),
        new Date(),
        item.id,
        item.tenantId,
      ]);
    }

    return recognizedAmount;
  }

  private async calculateRevenueMetrics(tenantId: string, period: { start: Date; end: Date }): Promise<ComplianceReport['metrics']> {
    // Mock implementation - in production, this would calculate actual metrics
    return {
      totalRevenue: 1500000,
      recognizedRevenue: 1200000,
      deferredRevenue: 300000,
      contractLiabilities: 150000,
      contractAssets: 50000,
      revenueBySegment: {
        'Enterprise': 800000,
        'SMB': 400000,
        'Startup': 300000,
      },
      revenueByProduct: {
        'Professional': 900000,
        'Enterprise': 600000,
      },
    };
  }

  private async runComplianceChecks(
    tenantId: string,
    period: { start: Date; end: Date },
    metrics: ComplianceReport['metrics']
  ): Promise<ComplianceReport['complianceChecks']> {
    const checks: ComplianceReport['complianceChecks'] = [];

    // ASC 606 compliance checks
    if (metrics.deferredRevenue / metrics.totalRevenue > 0.3) {
      checks.push({
        rule: 'ASC_606_DEFERRED_RATIO',
        status: 'warning',
        description: 'High deferred revenue ratio may indicate performance obligation delivery issues',
        impact: metrics.deferredRevenue * 0.1,
      });
    }

    // Revenue recognition timing
    if (metrics.recognizedRevenue / metrics.totalRevenue < 0.7) {
      checks.push({
        rule: 'REVENUE_RECOGNITION_TIMING',
        status: 'warning',
        description: 'Low recognized revenue ratio may indicate delayed performance obligation delivery',
      });
    }

    return checks;
  }

  private async storeComplianceReport(report: ComplianceReport): Promise<void> {
    await this.db.query(`
      INSERT INTO compliance_reports (
        id, tenant_id, report_type, period_start, period_end,
        metrics, compliance_checks, generated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `, [
      report.id,
      report.tenantId,
      report.reportType,
      report.period.start,
      report.period.end,
      JSON.stringify(report.metrics),
      JSON.stringify(report.complianceChecks),
      report.generatedAt,
    ]);
  }

  private async logComplianceEvent(
    tenantId: string,
    transactionId: string,
    eventType: string,
    eventData: Record<string, any>
  ): Promise<void> {
    await this.db.query(`
      INSERT INTO compliance_events (
        tenant_id, transaction_id, event_type, event_data, created_at
      ) VALUES ($1, $2, $3, $4, $5)
    `, [
      tenantId,
      transactionId,
      eventType,
      JSON.stringify(eventData),
      new Date(),
    ]);
  }

  // Additional helper methods would be implemented here
  private async getRevenueTransaction(tenantId: string, transactionId: string): Promise<RevenueTransaction | null> {
    // Mock implementation
    return null;
  }

  private async getDeferredRevenue(tenantId: string, transactionId: string): Promise<DeferredRevenue | null> {
    // Mock implementation
    return null;
  }

  private async getRecognizedAmount(tenantId: string, transactionId: string): Promise<number> {
    // Mock implementation
    return 0;
  }

  private async getSubscriptionTerm(tenantId: string, subscriptionId: string): Promise<any> {
    // Mock implementation
    return { termEnd: new Date() };
  }

  private async recalculateRecognitionSchedule(transaction: RevenueTransaction, newPeriod: { start: Date; end: Date }): Promise<any[]> {
    // Mock implementation
    return [];
  }

  private calculateRecognizedToDate(schedule: any[], date: Date): number {
    // Mock implementation
    return 0;
  }

  private async storeRevenueAdjustment(tenantId: string, adjustmentId: string, transactionId: string, adjustment: any, impact: any): Promise<void> {
    // Mock implementation
  }

  private async updateDeferredRevenueSchedule(tenantId: string, transactionId: string, adjustment: any): Promise<void> {
    // Mock implementation
  }
}

export { RevenueRecognitionService, type RevenueTransaction, type DeferredRevenue, type ComplianceReport };
