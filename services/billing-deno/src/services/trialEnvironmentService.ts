// Trial Environment Service
// Automated trial environment provisioning and management for customer onboarding

import { logger } from "../utils/logger.ts";

export interface TrialEnvironment {
  id: string;
  prospectId: string;
  companyName: string;
  contactEmail: string;
  industry: string;
  scenario: string;
  status: 'provisioning' | 'active' | 'expired' | 'converted' | 'cancelled';
  createdAt: Date;
  expiresAt: Date;
  lastAccessedAt?: Date;
  configuration: {
    eventVolume: number;
    features: string[];
    sampleDataSets: string[];
    integrations: string[];
  };
  credentials: {
    tenantId: string;
    apiKey: string;
    dashboardUrl: string;
    demoDataUrl: string;
  };
  metrics: {
    totalSessions: number;
    totalQueries: number;
    averageQueryTime: number;
    featuresExplored: string[];
    timeSpent: number; // minutes
    lastActivity: Date;
  };
  onboardingProgress: {
    currentStep: number;
    totalSteps: number;
    completedSteps: string[];
    nextSteps: string[];
    progressPercentage: number;
  };
}

export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  type: 'tutorial' | 'demo' | 'integration' | 'validation' | 'milestone';
  estimatedTime: number; // minutes
  prerequisites: string[];
  actions: Array<{
    type: 'click' | 'navigate' | 'input' | 'validate' | 'wait';
    target: string;
    description: string;
    validation?: string;
  }>;
  successCriteria: string[];
  helpResources: Array<{
    type: 'video' | 'documentation' | 'support';
    title: string;
    url: string;
  }>;
}

export interface TrialProvisioningRequest {
  companyName: string;
  contactEmail: string;
  contactName: string;
  industry: string;
  monthlyEvents: number;
  currentAnalytics: string;
  useCase: string;
  scenario?: string;
}

export class TrialEnvironmentService {
  private trialEnvironments: Map<string, TrialEnvironment> = new Map();
  private onboardingSteps: Map<string, OnboardingStep[]> = new Map();
  private sampleDataSets: Map<string, any> = new Map();

  constructor() {
    this.initializeOnboardingSteps();
    this.initializeSampleDataSets();
  }

  /**
   * Initialize onboarding steps for different scenarios
   */
  private initializeOnboardingSteps(): void {
    // E-commerce SMB onboarding steps
    this.onboardingSteps.set("ecommerce_smb", [
      {
        id: "welcome",
        title: "Welcome to Your Trial Environment",
        description: "Get familiar with your personalized analytics dashboard",
        type: "tutorial",
        estimatedTime: 5,
        prerequisites: [],
        actions: [
          {
            type: "navigate",
            target: "/dashboard",
            description: "Navigate to your analytics dashboard"
          },
          {
            type: "click",
            target: "#performance-metrics",
            description: "View real-time performance metrics"
          }
        ],
        successCriteria: ["Dashboard accessed", "Performance metrics viewed"],
        helpResources: [
          {
            type: "video",
            title: "Dashboard Overview",
            url: "/help/dashboard-overview"
          }
        ]
      },
      {
        id: "performance_demo",
        title: "Experience Lightning-Fast Performance",
        description: "See our 6-11ms query response times in action",
        type: "demo",
        estimatedTime: 10,
        prerequisites: ["welcome"],
        actions: [
          {
            type: "click",
            target: "#run-performance-test",
            description: "Execute performance benchmark test"
          },
          {
            type: "validate",
            target: "#query-response-time",
            description: "Observe sub-10ms query response times",
            validation: "response_time < 15ms"
          }
        ],
        successCriteria: ["Performance test completed", "Query times under 15ms"],
        helpResources: [
          {
            type: "documentation",
            title: "Performance Benchmarks",
            url: "/docs/performance"
          }
        ]
      },
      {
        id: "sample_data_exploration",
        title: "Explore Your Sample E-commerce Data",
        description: "Navigate through pre-loaded sample data showcasing 24,390 events/sec processing",
        type: "tutorial",
        estimatedTime: 15,
        prerequisites: ["performance_demo"],
        actions: [
          {
            type: "navigate",
            target: "/analytics/cohorts",
            description: "View cohort analysis with sample data"
          },
          {
            type: "click",
            target: "#funnel-analysis",
            description: "Explore conversion funnel insights"
          },
          {
            type: "input",
            target: "#date-range-selector",
            description: "Adjust date range to see real-time updates"
          }
        ],
        successCriteria: ["Cohort analysis viewed", "Funnel analysis explored", "Date range modified"],
        helpResources: [
          {
            type: "video",
            title: "E-commerce Analytics Guide",
            url: "/help/ecommerce-analytics"
          }
        ]
      },
      {
        id: "integration_test",
        title: "Test Integration Capabilities",
        description: "Validate how easily our platform integrates with your existing systems",
        type: "integration",
        estimatedTime: 20,
        prerequisites: ["sample_data_exploration"],
        actions: [
          {
            type: "navigate",
            target: "/integrations",
            description: "Access integration configuration"
          },
          {
            type: "click",
            target: "#test-api-connection",
            description: "Test API connectivity"
          },
          {
            type: "validate",
            target: "#integration-status",
            description: "Verify successful connection",
            validation: "status === 'connected'"
          }
        ],
        successCriteria: ["API connection tested", "Integration validated"],
        helpResources: [
          {
            type: "documentation",
            title: "Integration Guide",
            url: "/docs/integrations"
          }
        ]
      },
      {
        id: "value_realization",
        title: "Calculate Your ROI",
        description: "Use our ROI calculator to see your potential value",
        type: "milestone",
        estimatedTime: 10,
        prerequisites: ["integration_test"],
        actions: [
          {
            type: "navigate",
            target: "/roi-calculator",
            description: "Access ROI calculator"
          },
          {
            type: "input",
            target: "#company-data-form",
            description: "Enter your company information"
          },
          {
            type: "click",
            target: "#calculate-roi",
            description: "Generate ROI projection"
          }
        ],
        successCriteria: ["ROI calculated", "Value proposition understood"],
        helpResources: [
          {
            type: "support",
            title: "Schedule ROI Review",
            url: "/contact/roi-review"
          }
        ]
      }
    ]);

    // Enterprise retail onboarding steps
    this.onboardingSteps.set("enterprise_retail", [
      {
        id: "enterprise_welcome",
        title: "Enterprise Analytics Platform Overview",
        description: "Explore enterprise-grade features and scalability",
        type: "tutorial",
        estimatedTime: 10,
        prerequisites: [],
        actions: [
          {
            type: "navigate",
            target: "/enterprise-dashboard",
            description: "Access enterprise dashboard"
          },
          {
            type: "click",
            target: "#scalability-metrics",
            description: "View scalability and performance metrics"
          }
        ],
        successCriteria: ["Enterprise dashboard accessed", "Scalability metrics viewed"],
        helpResources: [
          {
            type: "video",
            title: "Enterprise Features Overview",
            url: "/help/enterprise-overview"
          }
        ]
      },
      {
        id: "multi_channel_analytics",
        title: "Multi-Channel Analytics Demo",
        description: "Experience unified analytics across all retail channels",
        type: "demo",
        estimatedTime: 20,
        prerequisites: ["enterprise_welcome"],
        actions: [
          {
            type: "navigate",
            target: "/analytics/multi-channel",
            description: "Access multi-channel analytics"
          },
          {
            type: "click",
            target: "#channel-comparison",
            description: "Compare performance across channels"
          },
          {
            type: "validate",
            target: "#unified-view",
            description: "Verify unified data view",
            validation: "channels_count >= 3"
          }
        ],
        successCriteria: ["Multi-channel view accessed", "Channel comparison completed"],
        helpResources: [
          {
            type: "documentation",
            title: "Multi-Channel Analytics",
            url: "/docs/multi-channel"
          }
        ]
      },
      {
        id: "predictive_analytics",
        title: "Explore Predictive Analytics",
        description: "See ML-powered predictions for inventory and demand",
        type: "demo",
        estimatedTime: 25,
        prerequisites: ["multi_channel_analytics"],
        actions: [
          {
            type: "navigate",
            target: "/analytics/predictions",
            description: "Access predictive analytics dashboard"
          },
          {
            type: "click",
            target: "#inventory-predictions",
            description: "View inventory optimization predictions"
          },
          {
            type: "click",
            target: "#demand-forecasting",
            description: "Explore demand forecasting models"
          }
        ],
        successCriteria: ["Predictive analytics explored", "ML models demonstrated"],
        helpResources: [
          {
            type: "video",
            title: "Predictive Analytics Guide",
            url: "/help/predictive-analytics"
          }
        ]
      },
      {
        id: "enterprise_integration",
        title: "Enterprise Integration Validation",
        description: "Test enterprise-grade integration capabilities",
        type: "integration",
        estimatedTime: 30,
        prerequisites: ["predictive_analytics"],
        actions: [
          {
            type: "navigate",
            target: "/enterprise/integrations",
            description: "Access enterprise integration center"
          },
          {
            type: "click",
            target: "#test-enterprise-apis",
            description: "Test enterprise API connections"
          },
          {
            type: "validate",
            target: "#security-validation",
            description: "Verify security compliance",
            validation: "security_score >= 95"
          }
        ],
        successCriteria: ["Enterprise APIs tested", "Security validated"],
        helpResources: [
          {
            type: "documentation",
            title: "Enterprise Integration Guide",
            url: "/docs/enterprise-integrations"
          }
        ]
      },
      {
        id: "enterprise_roi",
        title: "Enterprise ROI Analysis",
        description: "Calculate enterprise-scale value proposition",
        type: "milestone",
        estimatedTime: 15,
        prerequisites: ["enterprise_integration"],
        actions: [
          {
            type: "navigate",
            target: "/enterprise/roi-calculator",
            description: "Access enterprise ROI calculator"
          },
          {
            type: "input",
            target: "#enterprise-metrics-form",
            description: "Enter enterprise metrics and requirements"
          },
          {
            type: "click",
            target: "#generate-enterprise-roi",
            description: "Generate enterprise ROI analysis"
          }
        ],
        successCriteria: ["Enterprise ROI calculated", "Business case developed"],
        helpResources: [
          {
            type: "support",
            title: "Schedule Enterprise Review",
            url: "/contact/enterprise-review"
          }
        ]
      }
    ]);
  }

  /**
   * Initialize sample data sets for different scenarios
   */
  private initializeSampleDataSets(): void {
    // E-commerce sample data
    this.sampleDataSets.set("ecommerce_smb", {
      events: {
        totalEvents: 750000,
        eventTypes: ["page_view", "add_to_cart", "purchase", "checkout_start", "user_signup"],
        timeRange: "30 days",
        processingRate: 24390 // events/sec
      },
      customers: {
        totalCustomers: 15000,
        newCustomers: 2500,
        returningCustomers: 12500,
        averageOrderValue: 85,
        conversionRate: 2.8
      },
      products: {
        totalProducts: 500,
        categories: ["Electronics", "Accessories", "Software", "Hardware"],
        topSellingProducts: [
          { name: "Wireless Headphones", sales: 1250, revenue: 87500 },
          { name: "Smartphone Case", sales: 980, revenue: 29400 },
          { name: "Laptop Stand", sales: 750, revenue: 52500 }
        ]
      },
      performance: {
        averageQueryTime: 8.2,
        systemUptime: 99.97,
        dataAccuracy: 99.95,
        realTimeProcessing: true
      }
    });

    // Enterprise retail sample data
    this.sampleDataSets.set("enterprise_retail", {
      events: {
        totalEvents: 15000000,
        eventTypes: ["store_visit", "online_purchase", "mobile_app_usage", "loyalty_interaction", "inventory_update"],
        timeRange: "30 days",
        processingRate: 24390 // events/sec
      },
      channels: {
        online: { revenue: 2500000, orders: 12500, conversionRate: 3.2 },
        inStore: { revenue: 4200000, transactions: 28000, averageTicket: 150 },
        mobile: { revenue: 1800000, orders: 9000, conversionRate: 4.1 },
        marketplace: { revenue: 950000, orders: 4750, conversionRate: 2.8 }
      },
      inventory: {
        totalSKUs: 25000,
        turnoverRate: 6.2,
        stockoutRate: 2.1,
        predictiveAccuracy: 94.5
      },
      performance: {
        averageQueryTime: 7.8,
        systemUptime: 99.98,
        dataAccuracy: 99.96,
        scalabilityLimit: "10B+ events/month"
      }
    });
  }

  /**
   * Provision new trial environment
   */
  async provisionTrialEnvironment(request: TrialProvisioningRequest): Promise<TrialEnvironment> {
    const startTime = performance.now();
    
    const trialId = `trial_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const tenantId = `tenant_${trialId}`;
    const apiKey = `api_${Math.random().toString(36).substr(2, 32)}`;
    
    // Determine scenario based on industry or explicit request
    const scenario = request.scenario || this.determineScenario(request.industry, request.monthlyEvents);
    
    // Get sample data for scenario
    const sampleData = this.sampleDataSets.get(scenario);
    
    const trialEnvironment: TrialEnvironment = {
      id: trialId,
      prospectId: `prospect_${Date.now()}`,
      companyName: request.companyName,
      contactEmail: request.contactEmail,
      industry: request.industry,
      scenario,
      status: 'provisioning',
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + (14 * 24 * 60 * 60 * 1000)), // 14 days
      configuration: {
        eventVolume: request.monthlyEvents,
        features: this.getScenarioFeatures(scenario),
        sampleDataSets: [scenario],
        integrations: this.getRecommendedIntegrations(request.currentAnalytics)
      },
      credentials: {
        tenantId,
        apiKey,
        dashboardUrl: `/trial/${trialId}/dashboard`,
        demoDataUrl: `/trial/${trialId}/demo-data`
      },
      metrics: {
        totalSessions: 0,
        totalQueries: 0,
        averageQueryTime: 0,
        featuresExplored: [],
        timeSpent: 0,
        lastActivity: new Date()
      },
      onboardingProgress: {
        currentStep: 0,
        totalSteps: this.onboardingSteps.get(scenario)?.length || 5,
        completedSteps: [],
        nextSteps: this.onboardingSteps.get(scenario)?.slice(0, 2).map(step => step.id) || [],
        progressPercentage: 0
      }
    };

    // Simulate provisioning process
    await this.setupTrialInfrastructure(trialEnvironment);
    await this.loadSampleData(trialEnvironment, sampleData);
    
    trialEnvironment.status = 'active';
    this.trialEnvironments.set(trialId, trialEnvironment);

    const provisioningTime = performance.now() - startTime;

    logger.info("Trial environment provisioned", {
      trialId,
      companyName: request.companyName,
      scenario,
      provisioningTime: `${provisioningTime.toFixed(2)}ms`,
      expiresAt: trialEnvironment.expiresAt
    });

    return trialEnvironment;
  }

  /**
   * Get trial environment by ID
   */
  getTrialEnvironment(trialId: string): TrialEnvironment | undefined {
    return this.trialEnvironments.get(trialId);
  }

  /**
   * Update trial environment metrics
   */
  updateTrialMetrics(trialId: string, metrics: Partial<TrialEnvironment['metrics']>): void {
    const trial = this.trialEnvironments.get(trialId);
    if (trial) {
      trial.metrics = { ...trial.metrics, ...metrics };
      trial.lastAccessedAt = new Date();
      this.trialEnvironments.set(trialId, trial);
    }
  }

  /**
   * Update onboarding progress
   */
  updateOnboardingProgress(trialId: string, completedStepId: string): void {
    const trial = this.trialEnvironments.get(trialId);
    if (trial) {
      if (!trial.onboardingProgress.completedSteps.includes(completedStepId)) {
        trial.onboardingProgress.completedSteps.push(completedStepId);
        trial.onboardingProgress.currentStep = trial.onboardingProgress.completedSteps.length;
        trial.onboardingProgress.progressPercentage = 
          (trial.onboardingProgress.completedSteps.length / trial.onboardingProgress.totalSteps) * 100;
        
        // Update next steps
        const allSteps = this.onboardingSteps.get(trial.scenario) || [];
        const nextSteps = allSteps
          .filter(step => !trial.onboardingProgress.completedSteps.includes(step.id))
          .slice(0, 2)
          .map(step => step.id);
        
        trial.onboardingProgress.nextSteps = nextSteps;
        this.trialEnvironments.set(trialId, trial);
      }
    }
  }

  /**
   * Get onboarding steps for scenario
   */
  getOnboardingSteps(scenario: string): OnboardingStep[] {
    return this.onboardingSteps.get(scenario) || [];
  }

  /**
   * Get all active trial environments
   */
  getActiveTrials(): TrialEnvironment[] {
    return Array.from(this.trialEnvironments.values())
      .filter(trial => trial.status === 'active' && trial.expiresAt > new Date());
  }

  /**
   * Determine scenario based on industry and event volume
   */
  private determineScenario(industry: string, monthlyEvents: number): string {
    const normalizedIndustry = industry.toLowerCase();
    
    if (monthlyEvents > 10000000) {
      return "enterprise_retail";
    } else if (normalizedIndustry.includes("ecommerce") || normalizedIndustry.includes("retail")) {
      return "ecommerce_smb";
    } else if (normalizedIndustry.includes("saas") || normalizedIndustry.includes("technology")) {
      return "ecommerce_smb"; // Use ecommerce template for now
    }
    
    return "ecommerce_smb"; // Default scenario
  }

  /**
   * Get features for scenario
   */
  private getScenarioFeatures(scenario: string): string[] {
    const features = {
      ecommerce_smb: [
        "Real-time Analytics",
        "Cohort Analysis",
        "Funnel Analysis",
        "ROI Calculator",
        "Performance Benchmarks",
        "Basic Integrations"
      ],
      enterprise_retail: [
        "Real-time Analytics",
        "Multi-channel Analytics",
        "Predictive Analytics",
        "Advanced Cohort Analysis",
        "Enterprise Integrations",
        "Custom Dashboards",
        "Advanced Security",
        "Scalability Features"
      ]
    };
    
    return features[scenario] || features.ecommerce_smb;
  }

  /**
   * Get recommended integrations based on current analytics
   */
  private getRecommendedIntegrations(currentAnalytics: string): string[] {
    const integrations = {
      "google_analytics": ["Google Analytics Migration", "Google Ads", "Google Tag Manager"],
      "mixpanel": ["Mixpanel Migration", "Event Streaming", "User Segmentation"],
      "adobe_analytics": ["Adobe Migration", "Enterprise SSO", "Advanced Attribution"],
      "none": ["Basic Tracking", "E-commerce Platforms", "Marketing Tools"]
    };
    
    return integrations[currentAnalytics.toLowerCase()] || integrations.none;
  }

  /**
   * Setup trial infrastructure (simulated)
   */
  private async setupTrialInfrastructure(trial: TrialEnvironment): Promise<void> {
    // Simulate infrastructure setup delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    logger.info("Trial infrastructure setup completed", {
      trialId: trial.id,
      tenantId: trial.credentials.tenantId
    });
  }

  /**
   * Load sample data (simulated)
   */
  private async loadSampleData(trial: TrialEnvironment, sampleData: any): Promise<void> {
    // Simulate data loading delay
    await new Promise(resolve => setTimeout(resolve, 200));
    
    logger.info("Sample data loaded", {
      trialId: trial.id,
      scenario: trial.scenario,
      eventCount: sampleData?.events?.totalEvents || 0
    });
  }
}
