// Revenue Intelligence & Forecasting Service
// Predictive revenue modeling, expansion opportunity detection, and automated financial insights

import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { DatabaseService } from "./databaseService.ts";

// Revenue intelligence types and interfaces
interface RevenueIntelligence {
  tenantId: string;
  analysisDate: Date;
  predictions: {
    nextMonthRevenue: {
      amount: number;
      confidence: number;
      factors: Array<{
        factor: string;
        impact: number;
        description: string;
      }>;
    };
    quarterlyRevenue: {
      amount: number;
      confidence: number;
      trend: 'accelerating' | 'stable' | 'decelerating';
    };
    annualRevenue: {
      amount: number;
      confidence: number;
      growthRate: number;
    };
  };
  opportunities: ExpansionOpportunity[];
  risks: RevenueRisk[];
  insights: AutomatedInsight[];
  recommendations: RevenueRecommendation[];
}

interface ExpansionOpportunity {
  id: string;
  customerId: string;
  subscriptionId: string;
  type: 'upsell' | 'cross_sell' | 'usage_expansion' | 'seat_expansion';
  currentValue: number;
  potentialValue: number;
  probability: number;
  timeframe: number; // days
  factors: Array<{
    factor: string;
    score: number;
    description: string;
  }>;
  recommendedActions: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  estimatedEffort: 'low' | 'medium' | 'high';
}

interface RevenueRisk {
  id: string;
  customerId: string;
  subscriptionId: string;
  type: 'churn_risk' | 'downgrade_risk' | 'payment_risk' | 'usage_decline';
  currentValue: number;
  potentialLoss: number;
  probability: number;
  timeframe: number; // days
  indicators: Array<{
    indicator: string;
    severity: 'low' | 'medium' | 'high';
    description: string;
  }>;
  mitigationActions: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
}

interface AutomatedInsight {
  id: string;
  category: 'revenue_trend' | 'customer_behavior' | 'product_performance' | 'market_opportunity';
  insight: string;
  impact: 'positive' | 'negative' | 'neutral';
  confidence: number;
  supportingData: Record<string, any>;
  actionable: boolean;
  recommendations?: string[];
  priority: 'low' | 'medium' | 'high';
}

interface RevenueRecommendation {
  id: string;
  type: 'pricing_optimization' | 'customer_retention' | 'expansion_strategy' | 'product_development';
  title: string;
  description: string;
  expectedImpact: {
    revenue: number;
    probability: number;
    timeframe: number; // days
  };
  implementation: {
    effort: 'low' | 'medium' | 'high';
    resources: string[];
    timeline: number; // days
  };
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'in_progress' | 'completed' | 'dismissed';
}

interface PredictiveModel {
  id: string;
  name: string;
  type: 'revenue_forecast' | 'churn_prediction' | 'expansion_likelihood' | 'pricing_optimization';
  algorithm: 'linear_regression' | 'random_forest' | 'neural_network' | 'time_series';
  features: string[];
  accuracy: number;
  lastTrained: Date;
  version: string;
}

interface RevenueScenario {
  name: string;
  description: string;
  assumptions: Record<string, number>;
  projections: Array<{
    period: string;
    revenue: number;
    confidence: number;
  }>;
  probability: number;
  impact: number;
}

export class RevenueIntelligenceService {
  private db: DatabaseService;
  private models: Map<string, PredictiveModel> = new Map();

  constructor() {
    this.db = new DatabaseService();
    this.initializePredictiveModels();
  }

  /**
   * Generate comprehensive revenue intelligence analysis
   */
  async generateRevenueIntelligence(tenantId: string): Promise<RevenueIntelligence> {
    const analysisDate = new Date();

    // Generate revenue predictions
    const predictions = await this.generateRevenuePredictions(tenantId);

    // Identify expansion opportunities
    const opportunities = await this.identifyExpansionOpportunities(tenantId);

    // Assess revenue risks
    const risks = await this.assessRevenueRisks(tenantId);

    // Generate automated insights
    const insights = await this.generateAutomatedInsights(tenantId);

    // Create recommendations
    const recommendations = await this.generateRevenueRecommendations(
      tenantId,
      predictions,
      opportunities,
      risks,
      insights
    );

    const intelligence: RevenueIntelligence = {
      tenantId,
      analysisDate,
      predictions,
      opportunities,
      risks,
      insights,
      recommendations,
    };

    // Store intelligence for tracking
    await this.storeRevenueIntelligence(intelligence);

    logger.info("Revenue intelligence generated", {
      tenantId,
      opportunitiesCount: opportunities.length,
      risksCount: risks.length,
      insightsCount: insights.length,
      recommendationsCount: recommendations.length,
    });

    return intelligence;
  }

  /**
   * Predict revenue for various timeframes
   */
  async generateRevenuePredictions(tenantId: string): Promise<RevenueIntelligence['predictions']> {
    // Get historical revenue data
    const historicalData = await this.getHistoricalRevenueData(tenantId, 12);
    
    // Get current subscription and usage data
    const currentData = await this.getCurrentRevenueData(tenantId);

    // Apply predictive models
    const nextMonthModel = this.models.get('next_month_revenue');
    const quarterlyModel = this.models.get('quarterly_revenue');
    const annualModel = this.models.get('annual_revenue');

    // Predict next month revenue
    const nextMonthPrediction = await this.predictNextMonthRevenue(
      historicalData,
      currentData,
      nextMonthModel
    );

    // Predict quarterly revenue
    const quarterlyPrediction = await this.predictQuarterlyRevenue(
      historicalData,
      currentData,
      quarterlyModel
    );

    // Predict annual revenue
    const annualPrediction = await this.predictAnnualRevenue(
      historicalData,
      currentData,
      annualModel
    );

    return {
      nextMonthRevenue: nextMonthPrediction,
      quarterlyRevenue: quarterlyPrediction,
      annualRevenue: annualPrediction,
    };
  }

  /**
   * Identify expansion opportunities for customers
   */
  async identifyExpansionOpportunities(tenantId: string): Promise<ExpansionOpportunity[]> {
    const opportunities: ExpansionOpportunity[] = [];

    // Get active subscriptions
    const subscriptions = await this.getActiveSubscriptions(tenantId);

    for (const subscription of subscriptions) {
      // Analyze usage patterns for expansion opportunities
      const usageAnalysis = await this.analyzeCustomerUsage(tenantId, subscription.customerId);
      
      // Check for upsell opportunities
      const upsellOpportunity = await this.assessUpsellOpportunity(subscription, usageAnalysis);
      if (upsellOpportunity) {
        opportunities.push(upsellOpportunity);
      }

      // Check for cross-sell opportunities
      const crossSellOpportunity = await this.assessCrossSellOpportunity(subscription, usageAnalysis);
      if (crossSellOpportunity) {
        opportunities.push(crossSellOpportunity);
      }

      // Check for usage expansion
      const usageExpansion = await this.assessUsageExpansion(subscription, usageAnalysis);
      if (usageExpansion) {
        opportunities.push(usageExpansion);
      }

      // Check for seat expansion
      const seatExpansion = await this.assessSeatExpansion(subscription, usageAnalysis);
      if (seatExpansion) {
        opportunities.push(seatExpansion);
      }
    }

    // Sort by potential value and probability
    opportunities.sort((a, b) => 
      (b.potentialValue * b.probability) - (a.potentialValue * a.probability)
    );

    return opportunities;
  }

  /**
   * Assess revenue risks across customer base
   */
  async assessRevenueRisks(tenantId: string): Promise<RevenueRisk[]> {
    const risks: RevenueRisk[] = [];

    // Get active subscriptions
    const subscriptions = await this.getActiveSubscriptions(tenantId);

    for (const subscription of subscriptions) {
      // Assess churn risk
      const churnRisk = await this.assessChurnRisk(tenantId, subscription);
      if (churnRisk && churnRisk.probability > 0.3) {
        risks.push(churnRisk);
      }

      // Assess downgrade risk
      const downgradeRisk = await this.assessDowngradeRisk(tenantId, subscription);
      if (downgradeRisk && downgradeRisk.probability > 0.2) {
        risks.push(downgradeRisk);
      }

      // Assess payment risk
      const paymentRisk = await this.assessPaymentRisk(tenantId, subscription);
      if (paymentRisk && paymentRisk.probability > 0.1) {
        risks.push(paymentRisk);
      }

      // Assess usage decline risk
      const usageDeclineRisk = await this.assessUsageDeclineRisk(tenantId, subscription);
      if (usageDeclineRisk && usageDeclineRisk.probability > 0.25) {
        risks.push(usageDeclineRisk);
      }
    }

    // Sort by potential loss and probability
    risks.sort((a, b) => 
      (b.potentialLoss * b.probability) - (a.potentialLoss * a.probability)
    );

    return risks;
  }

  /**
   * Generate automated insights from data patterns
   */
  async generateAutomatedInsights(tenantId: string): Promise<AutomatedInsight[]> {
    const insights: AutomatedInsight[] = [];

    // Revenue trend insights
    const revenueTrends = await this.analyzeRevenueTrends(tenantId);
    insights.push(...revenueTrends);

    // Customer behavior insights
    const customerBehavior = await this.analyzeCustomerBehavior(tenantId);
    insights.push(...customerBehavior);

    // Product performance insights
    const productPerformance = await this.analyzeProductPerformance(tenantId);
    insights.push(...productPerformance);

    // Market opportunity insights
    const marketOpportunities = await this.analyzeMarketOpportunities(tenantId);
    insights.push(...marketOpportunities);

    // Filter and prioritize insights
    return insights
      .filter(insight => insight.confidence > 0.7)
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 10); // Top 10 insights
  }

  /**
   * Generate actionable revenue recommendations
   */
  async generateRevenueRecommendations(
    tenantId: string,
    predictions: RevenueIntelligence['predictions'],
    opportunities: ExpansionOpportunity[],
    risks: RevenueRisk[],
    insights: AutomatedInsight[]
  ): Promise<RevenueRecommendation[]> {
    const recommendations: RevenueRecommendation[] = [];

    // Pricing optimization recommendations
    if (predictions.nextMonthRevenue.confidence < 0.8) {
      recommendations.push({
        id: `rec_${Date.now()}_pricing`,
        type: 'pricing_optimization',
        title: 'Optimize Pricing Strategy',
        description: 'Revenue predictions show uncertainty. Consider A/B testing pricing tiers to optimize conversion and expansion.',
        expectedImpact: {
          revenue: predictions.nextMonthRevenue.amount * 0.15,
          probability: 0.7,
          timeframe: 90,
        },
        implementation: {
          effort: 'medium',
          resources: ['Product Team', 'Data Analytics', 'Marketing'],
          timeline: 60,
        },
        priority: 'high',
        status: 'pending',
      });
    }

    // Customer retention recommendations
    const highRiskCustomers = risks.filter(r => r.priority === 'critical' || r.priority === 'high');
    if (highRiskCustomers.length > 0) {
      const totalRisk = highRiskCustomers.reduce((sum, r) => sum + r.potentialLoss, 0);
      
      recommendations.push({
        id: `rec_${Date.now()}_retention`,
        type: 'customer_retention',
        title: 'Implement Proactive Customer Success Program',
        description: `${highRiskCustomers.length} high-risk customers identified. Implement targeted retention campaigns.`,
        expectedImpact: {
          revenue: totalRisk * 0.6, // Prevent 60% of potential churn
          probability: 0.8,
          timeframe: 30,
        },
        implementation: {
          effort: 'high',
          resources: ['Customer Success', 'Sales', 'Product'],
          timeline: 14,
        },
        priority: 'critical',
        status: 'pending',
      });
    }

    // Expansion strategy recommendations
    const highValueOpportunities = opportunities.filter(o => o.priority === 'high' || o.priority === 'critical');
    if (highValueOpportunities.length > 0) {
      const totalOpportunity = highValueOpportunities.reduce((sum, o) => sum + o.potentialValue, 0);
      
      recommendations.push({
        id: `rec_${Date.now()}_expansion`,
        type: 'expansion_strategy',
        title: 'Launch Targeted Expansion Campaign',
        description: `${highValueOpportunities.length} high-value expansion opportunities identified. Launch targeted upsell/cross-sell campaigns.`,
        expectedImpact: {
          revenue: totalOpportunity * 0.4, // Convert 40% of opportunities
          probability: 0.6,
          timeframe: 60,
        },
        implementation: {
          effort: 'medium',
          resources: ['Sales', 'Customer Success', 'Marketing'],
          timeline: 30,
        },
        priority: 'high',
        status: 'pending',
      });
    }

    // Product development recommendations based on insights
    const productInsights = insights.filter(i => i.category === 'product_performance' && i.actionable);
    if (productInsights.length > 0) {
      recommendations.push({
        id: `rec_${Date.now()}_product`,
        type: 'product_development',
        title: 'Enhance Product Features Based on Usage Patterns',
        description: 'Usage analytics reveal opportunities for product improvements that could drive expansion.',
        expectedImpact: {
          revenue: predictions.quarterlyRevenue.amount * 0.1,
          probability: 0.5,
          timeframe: 120,
        },
        implementation: {
          effort: 'high',
          resources: ['Product Team', 'Engineering', 'UX Design'],
          timeline: 90,
        },
        priority: 'medium',
        status: 'pending',
      });
    }

    return recommendations.sort((a, b) => {
      const aScore = a.expectedImpact.revenue * a.expectedImpact.probability;
      const bScore = b.expectedImpact.revenue * b.expectedImpact.probability;
      return bScore - aScore;
    });
  }

  /**
   * Generate revenue scenarios for planning
   */
  async generateRevenueScenarios(
    tenantId: string,
    timeframe: number = 12 // months
  ): Promise<RevenueScenario[]> {
    const baselineData = await this.getCurrentRevenueData(tenantId);
    const historicalData = await this.getHistoricalRevenueData(tenantId, 12);

    const scenarios: RevenueScenario[] = [
      {
        name: 'Conservative Growth',
        description: 'Assumes minimal growth with focus on retention',
        assumptions: {
          monthlyGrowthRate: 0.02,
          churnRate: 0.05,
          expansionRate: 0.1,
        },
        projections: this.calculateScenarioProjections(baselineData, {
          monthlyGrowthRate: 0.02,
          churnRate: 0.05,
          expansionRate: 0.1,
        }, timeframe),
        probability: 0.3,
        impact: baselineData.currentMRR * 12 * 1.2,
      },
      {
        name: 'Base Case',
        description: 'Expected growth based on current trends',
        assumptions: {
          monthlyGrowthRate: 0.05,
          churnRate: 0.03,
          expansionRate: 0.15,
        },
        projections: this.calculateScenarioProjections(baselineData, {
          monthlyGrowthRate: 0.05,
          churnRate: 0.03,
          expansionRate: 0.15,
        }, timeframe),
        probability: 0.5,
        impact: baselineData.currentMRR * 12 * 1.6,
      },
      {
        name: 'Aggressive Growth',
        description: 'Optimistic scenario with strong market expansion',
        assumptions: {
          monthlyGrowthRate: 0.08,
          churnRate: 0.02,
          expansionRate: 0.25,
        },
        projections: this.calculateScenarioProjections(baselineData, {
          monthlyGrowthRate: 0.08,
          churnRate: 0.02,
          expansionRate: 0.25,
        }, timeframe),
        probability: 0.2,
        impact: baselineData.currentMRR * 12 * 2.2,
      },
    ];

    return scenarios;
  }

  // Helper methods
  private initializePredictiveModels(): void {
    // Initialize predictive models
    this.models.set('next_month_revenue', {
      id: 'next_month_revenue',
      name: 'Next Month Revenue Predictor',
      type: 'revenue_forecast',
      algorithm: 'random_forest',
      features: ['historical_revenue', 'subscription_changes', 'usage_trends', 'seasonality'],
      accuracy: 0.85,
      lastTrained: new Date(),
      version: '1.0',
    });

    this.models.set('quarterly_revenue', {
      id: 'quarterly_revenue',
      name: 'Quarterly Revenue Predictor',
      type: 'revenue_forecast',
      algorithm: 'time_series',
      features: ['revenue_trends', 'customer_cohorts', 'market_indicators'],
      accuracy: 0.78,
      lastTrained: new Date(),
      version: '1.0',
    });

    this.models.set('annual_revenue', {
      id: 'annual_revenue',
      name: 'Annual Revenue Predictor',
      type: 'revenue_forecast',
      algorithm: 'neural_network',
      features: ['long_term_trends', 'market_growth', 'competitive_landscape'],
      accuracy: 0.72,
      lastTrained: new Date(),
      version: '1.0',
    });
  }

  private async getHistoricalRevenueData(tenantId: string, months: number): Promise<any> {
    // Mock implementation - in production, this would query actual historical data
    return {
      monthlyRevenue: Array.from({ length: months }, (_, i) => ({
        month: new Date(Date.now() - (months - i) * 30 * 24 * 60 * 60 * 1000),
        revenue: 100000 * (1 + Math.random() * 0.1),
        mrr: 100000 * (1 + i * 0.02),
        customers: 800 + i * 10,
      })),
      trends: {
        growthRate: 0.05,
        seasonality: 0.1,
        volatility: 0.15,
      },
    };
  }

  private async getCurrentRevenueData(tenantId: string): Promise<any> {
    // Mock implementation
    return {
      currentMRR: 120000,
      currentARR: 1440000,
      totalCustomers: 950,
      averageARPU: 126.32,
      churnRate: 0.03,
      expansionRate: 0.15,
    };
  }

  private async predictNextMonthRevenue(historicalData: any, currentData: any, model?: PredictiveModel): Promise<any> {
    // Mock ML prediction
    const baseRevenue = currentData.currentMRR;
    const growthFactor = 1 + (Math.random() * 0.1 - 0.05); // ±5% variance
    const predictedRevenue = baseRevenue * growthFactor;

    return {
      amount: predictedRevenue,
      confidence: 0.85,
      factors: [
        {
          factor: 'Historical Growth Trend',
          impact: 0.4,
          description: 'Consistent 5% monthly growth over past 6 months',
        },
        {
          factor: 'Seasonal Patterns',
          impact: 0.2,
          description: 'Q4 typically shows 10% increase in revenue',
        },
        {
          factor: 'Customer Expansion',
          impact: 0.3,
          description: 'Strong expansion revenue from existing customers',
        },
        {
          factor: 'Market Conditions',
          impact: 0.1,
          description: 'Favorable market conditions for SaaS growth',
        },
      ],
    };
  }

  private async predictQuarterlyRevenue(historicalData: any, currentData: any, model?: PredictiveModel): Promise<any> {
    // Mock quarterly prediction
    const quarterlyRevenue = currentData.currentMRR * 3 * 1.15; // 15% growth over quarter
    
    return {
      amount: quarterlyRevenue,
      confidence: 0.78,
      trend: 'accelerating' as const,
    };
  }

  private async predictAnnualRevenue(historicalData: any, currentData: any, model?: PredictiveModel): Promise<any> {
    // Mock annual prediction
    const annualRevenue = currentData.currentARR * 1.6; // 60% growth
    
    return {
      amount: annualRevenue,
      confidence: 0.72,
      growthRate: 0.6,
    };
  }

  private async getActiveSubscriptions(tenantId: string): Promise<any[]> {
    // Mock implementation
    return Array.from({ length: 50 }, (_, i) => ({
      id: `sub_${i}`,
      customerId: `cust_${i}`,
      planId: 'professional',
      mrr: 1500,
      status: 'active',
    }));
  }

  private async analyzeCustomerUsage(tenantId: string, customerId: string): Promise<any> {
    // Mock usage analysis
    return {
      apiCalls: 75000,
      dataVolume: 500,
      activeUsers: 25,
      featureUsage: {
        analytics: 0.9,
        reporting: 0.7,
        integrations: 0.5,
      },
      trend: 'increasing',
    };
  }

  private async assessUpsellOpportunity(subscription: any, usageAnalysis: any): Promise<ExpansionOpportunity | null> {
    // Mock upsell assessment
    if (usageAnalysis.apiCalls > 80000 && subscription.planId === 'professional') {
      return {
        id: `opp_upsell_${subscription.id}`,
        customerId: subscription.customerId,
        subscriptionId: subscription.id,
        type: 'upsell',
        currentValue: subscription.mrr,
        potentialValue: subscription.mrr * 2,
        probability: 0.7,
        timeframe: 30,
        factors: [
          {
            factor: 'High API Usage',
            score: 0.8,
            description: 'Customer is approaching API limits',
          },
        ],
        recommendedActions: ['Schedule expansion call', 'Present enterprise features'],
        priority: 'high',
        estimatedEffort: 'medium',
      };
    }
    return null;
  }

  private async assessCrossSellOpportunity(subscription: any, usageAnalysis: any): Promise<ExpansionOpportunity | null> {
    // Mock cross-sell assessment
    if (usageAnalysis.featureUsage.integrations < 0.3) {
      return {
        id: `opp_cross_${subscription.id}`,
        customerId: subscription.customerId,
        subscriptionId: subscription.id,
        type: 'cross_sell',
        currentValue: subscription.mrr,
        potentialValue: subscription.mrr + 500,
        probability: 0.5,
        timeframe: 60,
        factors: [
          {
            factor: 'Low Integration Usage',
            score: 0.6,
            description: 'Customer could benefit from additional integrations',
          },
        ],
        recommendedActions: ['Demo integration features', 'Offer integration setup'],
        priority: 'medium',
        estimatedEffort: 'low',
      };
    }
    return null;
  }

  private async assessUsageExpansion(subscription: any, usageAnalysis: any): Promise<ExpansionOpportunity | null> {
    // Mock usage expansion assessment
    return null; // Simplified for brevity
  }

  private async assessSeatExpansion(subscription: any, usageAnalysis: any): Promise<ExpansionOpportunity | null> {
    // Mock seat expansion assessment
    return null; // Simplified for brevity
  }

  private async assessChurnRisk(tenantId: string, subscription: any): Promise<RevenueRisk | null> {
    // Mock churn risk assessment
    const churnProbability = Math.random() * 0.5; // 0-50% churn risk
    
    if (churnProbability > 0.3) {
      return {
        id: `risk_churn_${subscription.id}`,
        customerId: subscription.customerId,
        subscriptionId: subscription.id,
        type: 'churn_risk',
        currentValue: subscription.mrr,
        potentialLoss: subscription.mrr * 12, // Annual loss
        probability: churnProbability,
        timeframe: 90,
        indicators: [
          {
            indicator: 'Decreased Usage',
            severity: 'high',
            description: 'API calls down 40% in last month',
          },
        ],
        mitigationActions: ['Schedule check-in call', 'Offer training session'],
        priority: churnProbability > 0.4 ? 'critical' : 'high',
      };
    }
    return null;
  }

  private async assessDowngradeRisk(tenantId: string, subscription: any): Promise<RevenueRisk | null> {
    // Mock downgrade risk assessment
    return null; // Simplified for brevity
  }

  private async assessPaymentRisk(tenantId: string, subscription: any): Promise<RevenueRisk | null> {
    // Mock payment risk assessment
    return null; // Simplified for brevity
  }

  private async assessUsageDeclineRisk(tenantId: string, subscription: any): Promise<RevenueRisk | null> {
    // Mock usage decline risk assessment
    return null; // Simplified for brevity
  }

  private async analyzeRevenueTrends(tenantId: string): Promise<AutomatedInsight[]> {
    // Mock revenue trend analysis
    return [
      {
        id: `insight_trend_${Date.now()}`,
        category: 'revenue_trend',
        insight: 'MRR growth has accelerated 15% in the last quarter, driven primarily by expansion revenue',
        impact: 'positive',
        confidence: 0.9,
        supportingData: { mrrGrowth: 0.15, expansionContribution: 0.6 },
        actionable: true,
        recommendations: ['Invest more in customer success to drive expansion'],
        priority: 'high',
      },
    ];
  }

  private async analyzeCustomerBehavior(tenantId: string): Promise<AutomatedInsight[]> {
    // Mock customer behavior analysis
    return [];
  }

  private async analyzeProductPerformance(tenantId: string): Promise<AutomatedInsight[]> {
    // Mock product performance analysis
    return [];
  }

  private async analyzeMarketOpportunities(tenantId: string): Promise<AutomatedInsight[]> {
    // Mock market opportunity analysis
    return [];
  }

  private calculateScenarioProjections(baselineData: any, assumptions: any, months: number): any[] {
    // Mock scenario projection calculation
    const projections = [];
    let currentMRR = baselineData.currentMRR;
    
    for (let i = 0; i < months; i++) {
      currentMRR *= (1 + assumptions.monthlyGrowthRate - assumptions.churnRate + assumptions.expansionRate);
      
      projections.push({
        period: new Date(Date.now() + i * 30 * 24 * 60 * 60 * 1000).toISOString().substring(0, 7),
        revenue: currentMRR,
        confidence: Math.max(0.5, 0.9 - (i * 0.02)), // Decreasing confidence over time
      });
    }
    
    return projections;
  }

  private async storeRevenueIntelligence(intelligence: RevenueIntelligence): Promise<void> {
    await this.db.query(`
      INSERT INTO revenue_intelligence (
        tenant_id, analysis_date, predictions, opportunities, risks,
        insights, recommendations
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [
      intelligence.tenantId,
      intelligence.analysisDate,
      JSON.stringify(intelligence.predictions),
      JSON.stringify(intelligence.opportunities),
      JSON.stringify(intelligence.risks),
      JSON.stringify(intelligence.insights),
      JSON.stringify(intelligence.recommendations),
    ]);
  }
}

export { RevenueIntelligenceService, type RevenueIntelligence, type ExpansionOpportunity, type RevenueRisk, type AutomatedInsight };
