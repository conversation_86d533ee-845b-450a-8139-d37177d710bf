import { query, queryOne, transaction } from "../utils/database.ts";
import { logger } from "../utils/logger.ts";
import { SubscriptionService } from "./subscriptionService.ts";

// Enhanced Subscription Management Service with Dynamic Pricing and Revenue Intelligence
// Integrates with Revenue Optimization & Growth Analytics platform

export interface DynamicPricingOptions {
  tenantId: string;
  customerId: string;
  planId: string;
  usageMetrics: {
    apiCalls: number;
    dataVolume: number;
    teamSize: number;
    featureUsage: Record<string, number>;
  };
  marketConditions?: {
    competitorPricing: number;
    demandLevel: 'low' | 'medium' | 'high';
    seasonality: number;
  };
  customerProfile?: {
    lifetimeValue: number;
    churnRisk: number;
    expansionProbability: number;
    paymentReliability: number;
  };
}

export interface DynamicPricingRecommendation {
  recommendedPrice: number;
  originalPrice: number;
  discountPercentage: number;
  pricingStrategy: 'value_based' | 'usage_based' | 'competitive' | 'retention' | 'expansion';
  reasoning: string;
  confidenceScore: number;
  expectedImpact: {
    revenueChange: number;
    churnRiskChange: number;
    conversionProbability: number;
  };
  validUntil: string;
  conditions: string[];
}

export interface UsageBasedBillingConfig {
  basePrice: number;
  usageTiers: Array<{
    from: number;
    to: number;
    pricePerUnit: number;
    description: string;
  }>;
  overage: {
    pricePerUnit: number;
    gracePeriod: number; // days
    notificationThresholds: number[]; // percentages
  };
  billingCycle: 'monthly' | 'quarterly' | 'annually';
  aggregationMethod: 'sum' | 'max' | 'average';
}

export interface TierRecommendation {
  currentTier: string;
  recommendedTier: string;
  reason: 'usage_growth' | 'feature_needs' | 'cost_optimization' | 'value_alignment';
  confidence: number;
  expectedBenefits: {
    costSavings?: number;
    additionalFeatures: string[];
    performanceImprovements: string[];
  };
  migrationPath: {
    steps: string[];
    timeline: string;
    riskFactors: string[];
  };
}

export interface RevenueIntelligenceInsights {
  subscriptionHealth: {
    score: number; // 0-100
    factors: Array<{
      factor: string;
      impact: number;
      trend: 'positive' | 'negative' | 'neutral';
    }>;
  };
  expansionOpportunities: Array<{
    type: 'upgrade' | 'add_on' | 'usage_increase';
    description: string;
    potentialRevenue: number;
    probability: number;
    timeframe: string;
  }>;
  retentionRisks: Array<{
    risk: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    mitigation: string[];
    impact: number;
  }>;
  optimizationRecommendations: Array<{
    area: string;
    recommendation: string;
    expectedImpact: number;
    implementationEffort: 'low' | 'medium' | 'high';
  }>;
}

export class EnhancedSubscriptionService extends SubscriptionService {

  /**
   * Generate dynamic pricing recommendation based on usage and customer profile
   */
  async generateDynamicPricing(options: DynamicPricingOptions): Promise<DynamicPricingRecommendation> {
    try {
      logger.info("Generating dynamic pricing recommendation", {
        tenantId: options.tenantId,
        customerId: options.customerId,
        planId: options.planId,
      });

      // Get current plan pricing
      const currentPlan = await this.getPlanDetails(options.planId);
      const originalPrice = currentPlan.monthlyPrice;

      // Analyze customer usage patterns
      const usageAnalysis = await this.analyzeUsagePatterns(options);

      // Get customer success metrics (mock data for now)
      const customerMetrics = {
        healthScore: 75,
        riskLevel: 'medium',
        engagementScore: 80,
        usageMetrics: {
          featureAdoption: 0.8,
          loginFrequency: 15,
          supportTickets: 2
        }
      };

      // Calculate dynamic pricing
      const pricingCalculation = await this.calculateDynamicPrice({
        originalPrice,
        usageAnalysis,
        customerMetrics: customerMetrics, // Customer metrics object
        marketConditions: options.marketConditions,
      });

      const recommendation: DynamicPricingRecommendation = {
        recommendedPrice: pricingCalculation.price,
        originalPrice,
        discountPercentage: ((originalPrice - pricingCalculation.price) / originalPrice) * 100,
        pricingStrategy: pricingCalculation.strategy,
        reasoning: pricingCalculation.reasoning,
        confidenceScore: pricingCalculation.confidence,
        expectedImpact: {
          revenueChange: pricingCalculation.price - originalPrice,
          churnRiskChange: pricingCalculation.churnImpact,
          conversionProbability: pricingCalculation.conversionProbability,
        },
        validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
        conditions: pricingCalculation.conditions,
      };

      logger.info("Dynamic pricing recommendation generated", {
        tenantId: options.tenantId,
        customerId: options.customerId,
        originalPrice,
        recommendedPrice: recommendation.recommendedPrice,
        strategy: recommendation.pricingStrategy,
      });

      return recommendation;
    } catch (error) {
      logger.error("Failed to generate dynamic pricing recommendation", {
        tenantId: options.tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Optimize usage-based billing configuration
   */
  async optimizeUsageBasedBilling(
    tenantId: string,
    planId: string,
    historicalUsage: Array<{ period: string; usage: number; revenue: number }>
  ): Promise<UsageBasedBillingConfig> {
    try {
      logger.info("Optimizing usage-based billing configuration", {
        tenantId,
        planId,
        periodsAnalyzed: historicalUsage.length,
      });

      // Analyze usage patterns
      const usageStats = this.calculateUsageStatistics(historicalUsage);

      // Get current plan details
      const currentPlan = await this.getPlanDetails(planId);

      // Calculate optimal tiers
      const optimizedTiers = this.calculateOptimalTiers(usageStats, currentPlan.monthlyPrice);

      // Calculate overage pricing
      const overageConfig = this.calculateOverageConfig(usageStats);

      const config: UsageBasedBillingConfig = {
        basePrice: currentPlan.monthlyPrice * 0.6, // 60% base, 40% usage
        usageTiers: optimizedTiers,
        overage: overageConfig,
        billingCycle: 'monthly',
        aggregationMethod: 'sum',
      };

      logger.info("Usage-based billing configuration optimized", {
        tenantId,
        planId,
        basePrice: config.basePrice,
        tiersCount: config.usageTiers.length,
      });

      return config;
    } catch (error) {
      logger.error("Failed to optimize usage-based billing", {
        tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Generate tier recommendation for customer
   */
  async generateTierRecommendation(tenantId: string, customerId: string): Promise<TierRecommendation> {
    try {
      logger.info("Generating tier recommendation", { tenantId, customerId });

      // Get customer's current subscription
      const subscription = await this.getCustomerSubscription(tenantId, customerId);
      
      // Analyze customer usage and needs
      const usageAnalysis = await this.analyzeCustomerUsageNeeds(tenantId, customerId);
      
      // Get customer success metrics (mock data for now)
      const customerMetrics = [{
        healthScore: 75,
        riskLevel: 'medium',
        engagementScore: 80,
        usageMetrics: {
          featureAdoption: 0.8,
          loginFrequency: 15,
          supportTickets: 2
        }
      }];

      // Calculate optimal tier
      const recommendation = await this.calculateOptimalTier(
        subscription.planId,
        usageAnalysis,
        customerMetrics[0]
      );

      logger.info("Tier recommendation generated", {
        tenantId,
        customerId,
        currentTier: recommendation.currentTier,
        recommendedTier: recommendation.recommendedTier,
        reason: recommendation.reason,
      });

      return recommendation;
    } catch (error) {
      logger.error("Failed to generate tier recommendation", {
        tenantId,
        customerId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Get comprehensive revenue intelligence insights for subscription
   */
  async getRevenueIntelligenceInsights(tenantId: string, subscriptionId: string): Promise<RevenueIntelligenceInsights> {
    try {
      logger.info("Generating revenue intelligence insights", { tenantId, subscriptionId });

      // Get subscription details
      const subscription = await this.getSubscription(subscriptionId, tenantId);

      // Get revenue metrics (mock data for now)
      const revenueMetrics = {
        totalRevenue: 50000,
        monthlyGrowthRate: 15.5,
        averageRevenuePerUser: 125,
        monthlyRecurringRevenue: 8500
      };

      // Get customer success data (mock data for now)
      const customerHealth = {
        healthScore: 85,
        riskLevel: 'low',
        engagementScore: 92,
        usageMetrics: {
          featureAdoption: 0.8,
          loginFrequency: 15,
          supportTickets: 2
        }
      };

      // Get expansion opportunities (mock data for now)
      const expansionOpportunities = [
        {
          customerId: subscription.stripeCustomerId,
          opportunityType: 'upgrade',
          potentialRevenue: 500,
          expansionProbability: 0.75,
          timeToExpansion: 30
        }
      ];

      // Get churn predictions (mock data for now)
      const churnPredictions = [
        {
          customerId: subscription.stripeCustomerId,
          churnProbability: 0.25,
          churnRisk: 'medium' as const,
          riskFactors: ['low_usage', 'payment_delays'],
          preventionRecommendations: ['engagement_campaign', 'pricing_review'],
          revenueAtRisk: 1200
        }
      ];

      // Generate comprehensive insights
      const insights: RevenueIntelligenceInsights = {
        subscriptionHealth: {
          score: customerHealth.healthScore || 75,
          factors: [
            {
              factor: "Payment reliability",
              impact: 0.25,
              trend: "positive",
            },
            {
              factor: "Feature adoption",
              impact: 0.20,
              trend: customerHealth.usageMetrics.featureAdoption > 0.7 ? "positive" : "neutral",
            },
            {
              factor: "Engagement level",
              impact: 0.15,
              trend: "positive",
            },
          ],
        },
        expansionOpportunities: expansionOpportunities
          .filter(o => o.customerId === subscription.stripeCustomerId)
          .map(o => ({
            type: o.opportunityType as 'upgrade' | 'add_on' | 'usage_increase',
            description: `${o.opportunityType} opportunity with ${Math.round(o.expansionProbability * 100)}% probability`,
            potentialRevenue: o.potentialRevenue,
            probability: o.expansionProbability,
            timeframe: `${o.timeToExpansion} days`,
          })),
        retentionRisks: churnPredictions
          .filter(p => p.customerId === subscription.stripeCustomerId)
          .map(p => ({
            risk: `Churn probability: ${Math.round(p.churnProbability * 100)}%`,
            severity: p.churnRisk,
            mitigation: p.preventionRecommendations,
            impact: p.revenueAtRisk,
          })),
        optimizationRecommendations: [
          {
            area: "Pricing optimization",
            recommendation: "Consider usage-based pricing model",
            expectedImpact: revenueMetrics.monthlyRecurringRevenue * 0.15,
            implementationEffort: "medium",
          },
          {
            area: "Feature adoption",
            recommendation: "Provide advanced feature training",
            expectedImpact: revenueMetrics.monthlyRecurringRevenue * 0.08,
            implementationEffort: "low",
          },
        ],
      };

      logger.info("Revenue intelligence insights generated", {
        tenantId,
        subscriptionId,
        healthScore: insights.subscriptionHealth.score,
        expansionOpportunities: insights.expansionOpportunities.length,
        retentionRisks: insights.retentionRisks.length,
      });

      return insights;
    } catch (error) {
      logger.error("Failed to generate revenue intelligence insights", {
        tenantId,
        subscriptionId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  // Helper methods for pricing calculations and analysis
  private async getPlanDetails(planId: string): Promise<{ monthlyPrice: number; features: any }> {
    // Simplified plan lookup - would query database in production
    const plans: Record<string, any> = {
      basic: { monthlyPrice: 29, features: {} },
      pro: { monthlyPrice: 99, features: {} },
      enterprise: { monthlyPrice: 299, features: {} },
    };
    
    return plans[planId] || plans.basic;
  }

  private async analyzeUsagePatterns(options: DynamicPricingOptions): Promise<any> {
    // Analyze customer usage patterns
    const usage = options.usageMetrics;
    
    return {
      apiUsageLevel: usage.apiCalls > 10000 ? 'high' : usage.apiCalls > 5000 ? 'medium' : 'low',
      dataVolumeLevel: usage.dataVolume > 1000 ? 'high' : usage.dataVolume > 500 ? 'medium' : 'low',
      teamSizeLevel: usage.teamSize > 10 ? 'large' : usage.teamSize > 5 ? 'medium' : 'small',
      overallUsage: (usage.apiCalls / 10000 + usage.dataVolume / 1000 + usage.teamSize / 10) / 3,
    };
  }

  private async calculateDynamicPrice(params: any): Promise<any> {
    const { originalPrice, usageAnalysis, customerMetrics, marketConditions } = params;
    
    let adjustedPrice = originalPrice;
    let strategy = 'value_based';
    let reasoning = 'Standard pricing';
    let confidence = 0.8;
    let churnImpact = 0;
    let conversionProbability = 0.5;
    let conditions: string[] = [];

    // Usage-based adjustments
    if (usageAnalysis.overallUsage > 0.8) {
      adjustedPrice *= 1.1; // 10% increase for high usage
      strategy = 'usage_based';
      reasoning = 'High usage justifies premium pricing';
      conditions.push('High usage pattern detected');
    } else if (usageAnalysis.overallUsage < 0.3) {
      adjustedPrice *= 0.9; // 10% discount for low usage
      strategy = 'retention';
      reasoning = 'Retention pricing for low usage customer';
      conditions.push('Low usage pattern - retention focus');
    }

    // Customer health adjustments
    if (customerMetrics && customerMetrics.churnProbability > 0.7) {
      adjustedPrice *= 0.85; // 15% discount for high churn risk
      strategy = 'retention';
      reasoning = 'Retention pricing for at-risk customer';
      churnImpact = -0.3; // Reduce churn risk by 30%
      conditions.push('High churn risk - retention pricing applied');
    }

    // Market condition adjustments
    if (marketConditions?.demandLevel === 'high') {
      adjustedPrice *= 1.05; // 5% increase for high demand
      conversionProbability = 0.7;
      conditions.push('High market demand');
    }

    return {
      price: Math.round(adjustedPrice * 100) / 100,
      strategy,
      reasoning,
      confidence,
      churnImpact,
      conversionProbability,
      conditions,
    };
  }

  private calculateUsageStatistics(historicalUsage: Array<{ period: string; usage: number; revenue: number }>): any {
    const usageValues = historicalUsage.map(h => h.usage);
    const revenueValues = historicalUsage.map(h => h.revenue);
    
    return {
      avgUsage: usageValues.reduce((sum, u) => sum + u, 0) / usageValues.length,
      maxUsage: Math.max(...usageValues),
      minUsage: Math.min(...usageValues),
      avgRevenue: revenueValues.reduce((sum, r) => sum + r, 0) / revenueValues.length,
      usageGrowthRate: usageValues.length > 1 ? (usageValues[usageValues.length - 1] - usageValues[0]) / usageValues[0] : 0,
    };
  }

  private calculateOptimalTiers(usageStats: any, basePrice: number): Array<{ from: number; to: number; pricePerUnit: number; description: string }> {
    const avgUsage = usageStats.avgUsage;
    
    return [
      {
        from: 0,
        to: avgUsage * 0.5,
        pricePerUnit: 0.01,
        description: "Base tier - included usage",
      },
      {
        from: avgUsage * 0.5,
        to: avgUsage * 1.5,
        pricePerUnit: 0.008,
        description: "Standard tier - moderate usage",
      },
      {
        from: avgUsage * 1.5,
        to: avgUsage * 3,
        pricePerUnit: 0.006,
        description: "Volume tier - high usage discount",
      },
    ];
  }

  private calculateOverageConfig(usageStats: any): any {
    return {
      pricePerUnit: 0.012,
      gracePeriod: 3,
      notificationThresholds: [80, 90, 100],
    };
  }

  private async getCustomerSubscription(tenantId: string, customerId: string): Promise<any> {
    // Simplified subscription lookup
    return {
      id: "sub_123",
      planId: "pro",
      status: "active",
      stripeCustomerId: customerId,
    };
  }

  private async analyzeCustomerUsageNeeds(tenantId: string, customerId: string): Promise<any> {
    // Analyze customer's usage patterns and needs
    return {
      currentUsage: {
        apiCalls: 8500,
        dataVolume: 750,
        teamSize: 8,
      },
      projectedGrowth: 0.15, // 15% monthly growth
      featureNeeds: ['advanced_analytics', 'custom_integrations'],
      performanceRequirements: 'high',
    };
  }

  private async calculateOptimalTier(currentTier: string, usageAnalysis: any, customerMetrics: any): Promise<TierRecommendation> {
    // Simplified tier recommendation logic
    const tiers = ['basic', 'pro', 'enterprise'];
    const currentIndex = tiers.indexOf(currentTier);
    
    let recommendedTier = currentTier;
    let reason: 'usage_growth' | 'feature_needs' | 'cost_optimization' | 'value_alignment' = 'value_alignment';
    
    if (usageAnalysis.projectedGrowth > 0.2 && currentIndex < 2) {
      recommendedTier = tiers[currentIndex + 1];
      reason = 'usage_growth';
    } else if (usageAnalysis.currentUsage.apiCalls < 5000 && currentIndex > 0) {
      recommendedTier = tiers[currentIndex - 1];
      reason = 'cost_optimization';
    }

    return {
      currentTier,
      recommendedTier,
      reason,
      confidence: 0.85,
      expectedBenefits: {
        costSavings: recommendedTier !== currentTier ? 50 : undefined,
        additionalFeatures: ['Advanced reporting', 'Priority support'],
        performanceImprovements: ['Faster API responses', 'Higher rate limits'],
      },
      migrationPath: {
        steps: ['Review current usage', 'Plan migration', 'Execute upgrade'],
        timeline: '1-2 weeks',
        riskFactors: ['Temporary service interruption', 'Feature learning curve'],
      },
    };
  }
}
