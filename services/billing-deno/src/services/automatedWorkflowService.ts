// Automated Workflow Service
// Comprehensive workflow automation for subscription events, revenue recognition, and customer success

import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { DatabaseService } from "./databaseService.ts";
import { NotificationService } from "./notificationService.ts";

// Workflow types and interfaces
interface WorkflowTrigger {
  type: 'subscription_event' | 'usage_threshold' | 'churn_risk' | 'expansion_opportunity' | 'revenue_milestone' | 'payment_failure';
  conditions: {
    eventType?: string;
    thresholdValue?: number;
    riskLevel?: string;
    opportunityType?: string;
    milestoneAmount?: number;
    failureCount?: number;
    timeWindow?: number; // minutes
  };
}

interface WorkflowAction {
  type: 'email_notification' | 'webhook' | 'subscription_update' | 'billing_action' | 'sales_alert' | 'customer_success_task';
  config: {
    recipients?: string[];
    webhookUrl?: string;
    updateData?: Record<string, any>;
    billingAction?: string;
    alertType?: string;
    taskType?: string;
    template?: string;
    priority?: 'low' | 'medium' | 'high' | 'critical';
  };
  delay?: number; // milliseconds
  retryPolicy?: {
    maxRetries: number;
    backoffMultiplier: number;
    initialDelay: number;
  };
}

interface AutomatedWorkflow {
  id: string;
  tenantId: string;
  name: string;
  description: string;
  trigger: WorkflowTrigger;
  actions: WorkflowAction[];
  isActive: boolean;
  executionCount: number;
  lastExecuted?: Date;
  successRate: number;
  createdAt: Date;
  updatedAt: Date;
}

interface WorkflowExecution {
  id: string;
  workflowId: string;
  tenantId: string;
  subscriptionId?: string;
  customerId?: string;
  triggerData: Record<string, any>;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'retrying';
  startedAt: Date;
  completedAt?: Date;
  actions: Array<{
    actionType: string;
    status: 'pending' | 'completed' | 'failed';
    executedAt?: Date;
    result?: any;
    error?: string;
    retryCount: number;
  }>;
  error?: string;
}

interface WorkflowMetrics {
  totalWorkflows: number;
  activeWorkflows: number;
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  executionsByTrigger: Record<string, number>;
  executionsByAction: Record<string, number>;
  recentExecutions: WorkflowExecution[];
}

export class AutomatedWorkflowService {
  private db: DatabaseService;
  private notifications: NotificationService;
  private executionQueue: Map<string, WorkflowExecution> = new Map();

  constructor() {
    this.db = new DatabaseService();
    this.notifications = new NotificationService();
  }

  /**
   * Create new automated workflow
   */
  async createWorkflow(
    tenantId: string,
    workflowData: {
      name: string;
      description: string;
      trigger: WorkflowTrigger;
      actions: WorkflowAction[];
    }
  ): Promise<AutomatedWorkflow> {
    const workflowId = `wf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date();

    const workflow: AutomatedWorkflow = {
      id: workflowId,
      tenantId,
      name: workflowData.name,
      description: workflowData.description,
      trigger: workflowData.trigger,
      actions: workflowData.actions,
      isActive: true,
      executionCount: 0,
      successRate: 0,
      createdAt: now,
      updatedAt: now,
    };

    // Store workflow in database
    await this.storeWorkflow(workflow);

    logger.info("Automated workflow created", {
      tenantId,
      workflowId,
      name: workflowData.name,
      triggerType: workflowData.trigger.type,
      actionsCount: workflowData.actions.length,
    });

    return workflow;
  }

  /**
   * Evaluate triggers and execute applicable workflows
   */
  async evaluateAndExecuteWorkflows(
    tenantId: string,
    eventType: string,
    eventData: Record<string, any>
  ): Promise<WorkflowExecution[]> {
    try {
      // Get active workflows for tenant
      const workflows = await this.getActiveWorkflows(tenantId);

      // Find applicable workflows
      const applicableWorkflows = workflows.filter(workflow => 
        this.evaluateTrigger(workflow.trigger, eventType, eventData)
      );

      if (applicableWorkflows.length === 0) {
        return [];
      }

      // Execute applicable workflows
      const executions = await Promise.all(
        applicableWorkflows.map(workflow => 
          this.executeWorkflow(workflow, eventData)
        )
      );

      logger.info("Workflows evaluated and executed", {
        tenantId,
        eventType,
        totalWorkflows: workflows.length,
        applicableWorkflows: applicableWorkflows.length,
        executionsStarted: executions.length,
      });

      return executions;
    } catch (error) {
      logger.error("Failed to evaluate and execute workflows", {
        tenantId,
        eventType,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Execute specific workflow
   */
  async executeWorkflow(
    workflow: AutomatedWorkflow,
    triggerData: Record<string, any>
  ): Promise<WorkflowExecution> {
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date();

    const execution: WorkflowExecution = {
      id: executionId,
      workflowId: workflow.id,
      tenantId: workflow.tenantId,
      subscriptionId: triggerData.subscriptionId,
      customerId: triggerData.customerId,
      triggerData,
      status: 'pending',
      startedAt: now,
      actions: workflow.actions.map(action => ({
        actionType: action.type,
        status: 'pending',
        retryCount: 0,
      })),
    };

    // Store execution
    await this.storeExecution(execution);

    // Add to execution queue
    this.executionQueue.set(executionId, execution);

    // Start execution asynchronously
    this.processExecution(execution).catch(error => {
      logger.error("Workflow execution failed", {
        executionId,
        workflowId: workflow.id,
        error: (error as Error).message,
      });
    });

    logger.info("Workflow execution started", {
      executionId,
      workflowId: workflow.id,
      tenantId: workflow.tenantId,
      actionsCount: workflow.actions.length,
    });

    return execution;
  }

  /**
   * Process workflow execution
   */
  private async processExecution(execution: WorkflowExecution): Promise<void> {
    try {
      execution.status = 'running';
      await this.updateExecution(execution);

      const workflow = await this.getWorkflow(execution.workflowId);
      if (!workflow) {
        throw new Error(`Workflow ${execution.workflowId} not found`);
      }

      // Execute actions sequentially
      for (let i = 0; i < workflow.actions.length; i++) {
        const action = workflow.actions[i];
        const executionAction = execution.actions[i];

        try {
          // Apply delay if specified
          if (action.delay && action.delay > 0) {
            await new Promise(resolve => setTimeout(resolve, action.delay));
          }

          // Execute action
          const result = await this.executeAction(action, execution.triggerData);
          
          executionAction.status = 'completed';
          executionAction.executedAt = new Date();
          executionAction.result = result;

          logger.info("Workflow action completed", {
            executionId: execution.id,
            actionType: action.type,
            actionIndex: i,
          });
        } catch (error) {
          executionAction.status = 'failed';
          executionAction.error = (error as Error).message;

          // Retry if policy is defined
          if (action.retryPolicy && executionAction.retryCount < action.retryPolicy.maxRetries) {
            await this.retryAction(action, executionAction, execution);
          } else {
            logger.error("Workflow action failed", {
              executionId: execution.id,
              actionType: action.type,
              actionIndex: i,
              error: (error as Error).message,
            });
          }
        }
      }

      // Check if all actions completed successfully
      const failedActions = execution.actions.filter(a => a.status === 'failed');
      if (failedActions.length === 0) {
        execution.status = 'completed';
      } else {
        execution.status = 'failed';
        execution.error = `${failedActions.length} actions failed`;
      }

      execution.completedAt = new Date();
      await this.updateExecution(execution);

      // Update workflow metrics
      await this.updateWorkflowMetrics(workflow.id, execution.status === 'completed');

      // Remove from queue
      this.executionQueue.delete(execution.id);

      logger.info("Workflow execution completed", {
        executionId: execution.id,
        workflowId: workflow.id,
        status: execution.status,
        duration: execution.completedAt.getTime() - execution.startedAt.getTime(),
      });
    } catch (error) {
      execution.status = 'failed';
      execution.error = (error as Error).message;
      execution.completedAt = new Date();
      
      await this.updateExecution(execution);
      this.executionQueue.delete(execution.id);

      logger.error("Workflow execution failed", {
        executionId: execution.id,
        error: (error as Error).message,
      });
    }
  }

  /**
   * Execute individual action
   */
  private async executeAction(action: WorkflowAction, triggerData: Record<string, any>): Promise<any> {
    switch (action.type) {
      case 'email_notification':
        return await this.executeEmailNotification(action, triggerData);
      
      case 'webhook':
        return await this.executeWebhook(action, triggerData);
      
      case 'subscription_update':
        return await this.executeSubscriptionUpdate(action, triggerData);
      
      case 'billing_action':
        return await this.executeBillingAction(action, triggerData);
      
      case 'sales_alert':
        return await this.executeSalesAlert(action, triggerData);
      
      case 'customer_success_task':
        return await this.executeCustomerSuccessTask(action, triggerData);
      
      default:
        throw new Error(`Unknown action type: ${action.type}`);
    }
  }

  /**
   * Execute email notification action
   */
  private async executeEmailNotification(action: WorkflowAction, triggerData: Record<string, any>): Promise<any> {
    const { recipients, template, priority } = action.config;
    
    if (!recipients || recipients.length === 0) {
      throw new Error('No recipients specified for email notification');
    }

    const emailData = {
      recipients,
      template: template || 'default',
      data: triggerData,
      priority: priority || 'medium',
    };

    // Send email notification
    const result = await this.notifications.sendEmail(emailData);
    
    return {
      emailsSent: recipients.length,
      messageId: result.messageId,
      status: 'sent',
    };
  }

  /**
   * Execute webhook action
   */
  private async executeWebhook(action: WorkflowAction, triggerData: Record<string, any>): Promise<any> {
    const { webhookUrl } = action.config;
    
    if (!webhookUrl) {
      throw new Error('No webhook URL specified');
    }

    const payload = {
      event: 'workflow_action',
      data: triggerData,
      timestamp: new Date().toISOString(),
    };

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Revenue-Operations-Workflow/1.0',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`Webhook failed with status ${response.status}: ${response.statusText}`);
    }

    return {
      status: response.status,
      statusText: response.statusText,
      responseTime: Date.now(),
    };
  }

  /**
   * Execute subscription update action
   */
  private async executeSubscriptionUpdate(action: WorkflowAction, triggerData: Record<string, any>): Promise<any> {
    const { updateData } = action.config;
    const { subscriptionId, tenantId } = triggerData;
    
    if (!subscriptionId || !updateData) {
      throw new Error('Missing subscription ID or update data');
    }

    // Update subscription in database
    await this.db.query(`
      UPDATE subscriptions 
      SET metadata = metadata || $1, updated_at = NOW()
      WHERE id = $2 AND tenant_id = $3
    `, [JSON.stringify(updateData), subscriptionId, tenantId]);

    return {
      subscriptionId,
      updatedFields: Object.keys(updateData),
      updatedAt: new Date(),
    };
  }

  /**
   * Execute billing action
   */
  private async executeBillingAction(action: WorkflowAction, triggerData: Record<string, any>): Promise<any> {
    const { billingAction } = action.config;
    const { subscriptionId, customerId } = triggerData;
    
    if (!billingAction) {
      throw new Error('No billing action specified');
    }

    // Execute billing action based on type
    switch (billingAction) {
      case 'retry_payment':
        return await this.retryPayment(subscriptionId, customerId);
      
      case 'apply_discount':
        return await this.applyDiscount(subscriptionId, action.config);
      
      case 'suspend_subscription':
        return await this.suspendSubscription(subscriptionId);
      
      default:
        throw new Error(`Unknown billing action: ${billingAction}`);
    }
  }

  /**
   * Execute sales alert action
   */
  private async executeSalesAlert(action: WorkflowAction, triggerData: Record<string, any>): Promise<any> {
    const { alertType, priority } = action.config;
    
    const alert = {
      type: alertType || 'general',
      priority: priority || 'medium',
      data: triggerData,
      createdAt: new Date(),
    };

    // Store alert in database
    await this.db.query(`
      INSERT INTO sales_alerts (type, priority, data, created_at)
      VALUES ($1, $2, $3, $4)
    `, [alert.type, alert.priority, JSON.stringify(alert.data), alert.createdAt]);

    return alert;
  }

  /**
   * Execute customer success task action
   */
  private async executeCustomerSuccessTask(action: WorkflowAction, triggerData: Record<string, any>): Promise<any> {
    const { taskType, priority } = action.config;
    const { customerId, subscriptionId } = triggerData;
    
    const task = {
      type: taskType || 'follow_up',
      priority: priority || 'medium',
      customerId,
      subscriptionId,
      data: triggerData,
      status: 'pending',
      createdAt: new Date(),
    };

    // Store task in database
    await this.db.query(`
      INSERT INTO customer_success_tasks (type, priority, customer_id, subscription_id, data, status, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [task.type, task.priority, task.customerId, task.subscriptionId, JSON.stringify(task.data), task.status, task.createdAt]);

    return task;
  }

  /**
   * Evaluate if trigger conditions are met
   */
  private evaluateTrigger(trigger: WorkflowTrigger, eventType: string, eventData: Record<string, any>): boolean {
    switch (trigger.type) {
      case 'subscription_event':
        return trigger.conditions.eventType === eventType;
      
      case 'usage_threshold':
        return eventData.usageValue >= (trigger.conditions.thresholdValue || 0);
      
      case 'churn_risk':
        return eventData.riskLevel === trigger.conditions.riskLevel;
      
      case 'expansion_opportunity':
        return eventData.opportunityType === trigger.conditions.opportunityType;
      
      case 'revenue_milestone':
        return eventData.revenueAmount >= (trigger.conditions.milestoneAmount || 0);
      
      case 'payment_failure':
        return eventData.failureCount >= (trigger.conditions.failureCount || 1);
      
      default:
        return false;
    }
  }

  /**
   * Get workflow metrics
   */
  async getWorkflowMetrics(tenantId: string): Promise<WorkflowMetrics> {
    // Get workflow counts
    const workflowCounts = await this.db.query(`
      SELECT 
        COUNT(*) as total_workflows,
        COUNT(CASE WHEN is_active THEN 1 END) as active_workflows
      FROM automated_workflows 
      WHERE tenant_id = $1
    `, [tenantId]);

    // Get execution counts
    const executionCounts = await this.db.query(`
      SELECT 
        COUNT(*) as total_executions,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_executions,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_executions,
        AVG(EXTRACT(EPOCH FROM (completed_at - started_at)) * 1000) as avg_execution_time
      FROM workflow_executions 
      WHERE tenant_id = $1 AND completed_at IS NOT NULL
    `, [tenantId]);

    // Get recent executions
    const recentExecutions = await this.db.query(`
      SELECT * FROM workflow_executions 
      WHERE tenant_id = $1 
      ORDER BY started_at DESC 
      LIMIT 10
    `, [tenantId]);

    const metrics: WorkflowMetrics = {
      totalWorkflows: parseInt(workflowCounts.rows[0]?.total_workflows || '0'),
      activeWorkflows: parseInt(workflowCounts.rows[0]?.active_workflows || '0'),
      totalExecutions: parseInt(executionCounts.rows[0]?.total_executions || '0'),
      successfulExecutions: parseInt(executionCounts.rows[0]?.successful_executions || '0'),
      failedExecutions: parseInt(executionCounts.rows[0]?.failed_executions || '0'),
      averageExecutionTime: parseFloat(executionCounts.rows[0]?.avg_execution_time || '0'),
      executionsByTrigger: {},
      executionsByAction: {},
      recentExecutions: recentExecutions.rows.map(this.mapExecutionRow),
    };

    return metrics;
  }

  // Helper methods
  private async storeWorkflow(workflow: AutomatedWorkflow): Promise<void> {
    await this.db.query(`
      INSERT INTO automated_workflows (
        id, tenant_id, name, description, trigger, actions, is_active,
        execution_count, success_rate, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
    `, [
      workflow.id,
      workflow.tenantId,
      workflow.name,
      workflow.description,
      JSON.stringify(workflow.trigger),
      JSON.stringify(workflow.actions),
      workflow.isActive,
      workflow.executionCount,
      workflow.successRate,
      workflow.createdAt,
      workflow.updatedAt,
    ]);
  }

  private async getActiveWorkflows(tenantId: string): Promise<AutomatedWorkflow[]> {
    const result = await this.db.query(`
      SELECT * FROM automated_workflows 
      WHERE tenant_id = $1 AND is_active = true
    `, [tenantId]);

    return result.rows.map(this.mapWorkflowRow);
  }

  private async getWorkflow(workflowId: string): Promise<AutomatedWorkflow | null> {
    const result = await this.db.query(`
      SELECT * FROM automated_workflows WHERE id = $1
    `, [workflowId]);

    return result.rows.length > 0 ? this.mapWorkflowRow(result.rows[0]) : null;
  }

  private async storeExecution(execution: WorkflowExecution): Promise<void> {
    await this.db.query(`
      INSERT INTO workflow_executions (
        id, workflow_id, tenant_id, subscription_id, customer_id,
        trigger_data, status, started_at, actions
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `, [
      execution.id,
      execution.workflowId,
      execution.tenantId,
      execution.subscriptionId,
      execution.customerId,
      JSON.stringify(execution.triggerData),
      execution.status,
      execution.startedAt,
      JSON.stringify(execution.actions),
    ]);
  }

  private async updateExecution(execution: WorkflowExecution): Promise<void> {
    await this.db.query(`
      UPDATE workflow_executions 
      SET status = $1, completed_at = $2, actions = $3, error = $4
      WHERE id = $5
    `, [
      execution.status,
      execution.completedAt,
      JSON.stringify(execution.actions),
      execution.error,
      execution.id,
    ]);
  }

  private async updateWorkflowMetrics(workflowId: string, success: boolean): Promise<void> {
    await this.db.query(`
      UPDATE automated_workflows 
      SET execution_count = execution_count + 1,
          success_rate = (
            SELECT COUNT(CASE WHEN status = 'completed' THEN 1 END)::float / COUNT(*)::float * 100
            FROM workflow_executions 
            WHERE workflow_id = $1
          ),
          last_executed = NOW()
      WHERE id = $1
    `, [workflowId]);
  }

  private async retryAction(action: WorkflowAction, executionAction: any, execution: WorkflowExecution): Promise<void> {
    // Implement retry logic with backoff
    const delay = action.retryPolicy!.initialDelay * Math.pow(action.retryPolicy!.backoffMultiplier, executionAction.retryCount);
    
    setTimeout(async () => {
      try {
        executionAction.retryCount++;
        const result = await this.executeAction(action, execution.triggerData);
        executionAction.status = 'completed';
        executionAction.result = result;
        executionAction.executedAt = new Date();
      } catch (error) {
        executionAction.error = (error as Error).message;
        if (executionAction.retryCount >= action.retryPolicy!.maxRetries) {
          executionAction.status = 'failed';
        }
      }
      
      await this.updateExecution(execution);
    }, delay);
  }

  private async retryPayment(subscriptionId: string, customerId: string): Promise<any> {
    // Mock implementation
    return { status: 'retry_scheduled', subscriptionId, customerId };
  }

  private async applyDiscount(subscriptionId: string, config: any): Promise<any> {
    // Mock implementation
    return { status: 'discount_applied', subscriptionId, discount: config.discountAmount };
  }

  private async suspendSubscription(subscriptionId: string): Promise<any> {
    // Mock implementation
    return { status: 'suspended', subscriptionId };
  }

  private mapWorkflowRow(row: any): AutomatedWorkflow {
    return {
      id: row.id,
      tenantId: row.tenant_id,
      name: row.name,
      description: row.description,
      trigger: JSON.parse(row.trigger),
      actions: JSON.parse(row.actions),
      isActive: row.is_active,
      executionCount: row.execution_count,
      lastExecuted: row.last_executed ? new Date(row.last_executed) : undefined,
      successRate: row.success_rate,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }

  private mapExecutionRow(row: any): WorkflowExecution {
    return {
      id: row.id,
      workflowId: row.workflow_id,
      tenantId: row.tenant_id,
      subscriptionId: row.subscription_id,
      customerId: row.customer_id,
      triggerData: JSON.parse(row.trigger_data),
      status: row.status,
      startedAt: new Date(row.started_at),
      completedAt: row.completed_at ? new Date(row.completed_at) : undefined,
      actions: JSON.parse(row.actions),
      error: row.error,
    };
  }
}

export { AutomatedWorkflowService, type AutomatedWorkflow, type WorkflowExecution, type WorkflowMetrics };
