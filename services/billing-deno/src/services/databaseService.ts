import { Pool, PoolClient } from "postgres";
import { query, queryOne, transaction, getPool } from "../utils/database.ts";
import { logger } from "../utils/logger.ts";

/**
 * Database Service - Advanced database operations wrapper
 * Provides sophisticated database functionality for complex billing operations
 * Maintains compatibility with existing service interfaces while leveraging optimized utilities
 */
export class DatabaseService {
  private pool: Pool;

  constructor() {
    this.pool = getPool();
  }

  /**
   * Execute a query with automatic tenant filtering and performance monitoring
   */
  async query<T = unknown>(
    text: string,
    params: unknown[] = [],
    tenantId?: string
  ): Promise<{ rows: T[]; rowCount: number }> {
    try {
      const startTime = Date.now();
      
      // Use the optimized query function from database utils
      const result = await query<T>(text, params, tenantId);
      
      const executionTime = Date.now() - startTime;
      
      // Log slow queries for performance monitoring
      if (executionTime > 1000) {
        logger.warn("Slow query detected", {
          query: text.substring(0, 100),
          executionTime,
          tenantId,
          paramCount: params.length
        });
      }

      // Return in the format expected by existing services
      return {
        rows: result as T[],
        rowCount: Array.isArray(result) ? result.length : 0
      };
    } catch (error) {
      logger.error("Database query failed", {
        error: error instanceof Error ? error.message : String(error),
        query: text.substring(0, 100),
        tenantId,
        paramCount: params.length
      });
      throw error;
    }
  }

  /**
   * Execute a query and return a single row
   */
  async queryOne<T = unknown>(
    text: string,
    params: unknown[] = [],
    tenantId?: string
  ): Promise<T | null> {
    try {
      const result = await queryOne<T>(text, params, tenantId);
      return result;
    } catch (error) {
      logger.error("Database queryOne failed", {
        error: error instanceof Error ? error.message : String(error),
        query: text.substring(0, 100),
        tenantId
      });
      throw error;
    }
  }

  /**
   * Execute a transaction with sophisticated error handling and rollback
   */
  async transaction<T>(
    callback: (client: PoolClient) => Promise<T>,
    tenantId?: string
  ): Promise<T> {
    try {
      const result = await transaction(callback, tenantId);
      return result;
    } catch (error) {
      logger.error("Database transaction failed", {
        error: error instanceof Error ? error.message : String(error),
        tenantId
      });
      throw error;
    }
  }

  /**
   * Advanced bulk insert with conflict resolution
   */
  async bulkInsert<T>(
    tableName: string,
    columns: string[],
    values: unknown[][],
    conflictResolution: 'ignore' | 'update' | 'error' = 'error',
    tenantId?: string
  ): Promise<{ insertedCount: number; updatedCount: number }> {
    const placeholders = values.map((_, index) => 
      `(${columns.map((_, colIndex) => `$${index * columns.length + colIndex + 1}`).join(', ')})`
    ).join(', ');

    const flatValues = values.flat();
    
    let conflictClause = '';
    if (conflictResolution === 'ignore') {
      conflictClause = 'ON CONFLICT DO NOTHING';
    } else if (conflictResolution === 'update') {
      const updateSet = columns.map(col => `${col} = EXCLUDED.${col}`).join(', ');
      conflictClause = `ON CONFLICT DO UPDATE SET ${updateSet}`;
    }

    const queryText = `
      INSERT INTO ${tableName} (${columns.join(', ')})
      VALUES ${placeholders}
      ${conflictClause}
      RETURNING *
    `;

    try {
      const result = await this.query(queryText, flatValues, tenantId);
      return {
        insertedCount: result.rowCount,
        updatedCount: 0 // Would need more sophisticated tracking for actual update count
      };
    } catch (error) {
      logger.error("Bulk insert failed", {
        error: error instanceof Error ? error.message : String(error),
        tableName,
        rowCount: values.length,
        tenantId
      });
      throw error;
    }
  }

  /**
   * Advanced aggregation query with time-series optimization
   */
  async aggregateQuery<T>(
    tableName: string,
    aggregations: Record<string, string>, // e.g., { total: 'SUM(amount)', avg: 'AVG(amount)' }
    groupBy: string[],
    filters: Record<string, unknown>,
    timeRange?: { start: Date; end: Date; timeColumn?: string },
    tenantId?: string
  ): Promise<T[]> {
    const aggregationClauses = Object.entries(aggregations)
      .map(([alias, expr]) => `${expr} as ${alias}`)
      .join(', ');

    const groupByClauses = groupBy.join(', ');
    
    const filterClauses = Object.keys(filters).map((key, index) => 
      `${key} = $${index + 1}`
    );

    let whereClause = '';
    const params: unknown[] = Object.values(filters);
    let paramIndex = params.length;

    if (tenantId) {
      filterClauses.push(`tenant_id = $${++paramIndex}`);
      params.push(tenantId);
    }

    if (timeRange) {
      const timeCol = timeRange.timeColumn || 'created_at';
      filterClauses.push(`${timeCol} >= $${++paramIndex}`);
      filterClauses.push(`${timeCol} <= $${++paramIndex}`);
      params.push(timeRange.start, timeRange.end);
    }

    if (filterClauses.length > 0) {
      whereClause = `WHERE ${filterClauses.join(' AND ')}`;
    }

    const queryText = `
      SELECT ${aggregationClauses}${groupBy.length > 0 ? ', ' + groupByClauses : ''}
      FROM ${tableName}
      ${whereClause}
      ${groupBy.length > 0 ? `GROUP BY ${groupByClauses}` : ''}
      ORDER BY ${groupBy.length > 0 ? groupByClauses : aggregationClauses.split(',')[0]}
    `;

    try {
      const result = await this.query<T>(queryText, params, tenantId);
      return result.rows;
    } catch (error) {
      logger.error("Aggregation query failed", {
        error: error instanceof Error ? error.message : String(error),
        tableName,
        aggregations,
        tenantId
      });
      throw error;
    }
  }

  /**
   * Time-series data query with TimescaleDB optimizations
   */
  async timeSeriesQuery<T>(
    tableName: string,
    timeColumn: string,
    valueColumns: string[],
    timeRange: { start: Date; end: Date },
    interval: string, // e.g., '1 hour', '1 day'
    aggregation: 'avg' | 'sum' | 'count' | 'min' | 'max' = 'avg',
    filters: Record<string, unknown> = {},
    tenantId?: string
  ): Promise<T[]> {
    const valueSelects = valueColumns.map(col => 
      `${aggregation}(${col}) as ${col}_${aggregation}`
    ).join(', ');

    const filterClauses = Object.keys(filters).map((key, index) => 
      `${key} = $${index + 3}`
    );

    let whereClause = `${timeColumn} >= $1 AND ${timeColumn} <= $2`;
    const params: unknown[] = [timeRange.start, timeRange.end];

    if (tenantId) {
      whereClause += ` AND tenant_id = $${params.length + 1}`;
      params.push(tenantId);
    }

    if (filterClauses.length > 0) {
      whereClause += ` AND ${filterClauses.join(' AND ')}`;
      params.push(...Object.values(filters));
    }

    const queryText = `
      SELECT 
        time_bucket('${interval}', ${timeColumn}) as time_bucket,
        ${valueSelects}
      FROM ${tableName}
      WHERE ${whereClause}
      GROUP BY time_bucket
      ORDER BY time_bucket
    `;

    try {
      const result = await this.query<T>(queryText, params, tenantId);
      return result.rows;
    } catch (error) {
      logger.error("Time-series query failed", {
        error: error instanceof Error ? error.message : String(error),
        tableName,
        timeRange,
        interval,
        tenantId
      });
      throw error;
    }
  }

  /**
   * Advanced search with full-text search capabilities
   */
  async searchQuery<T>(
    tableName: string,
    searchColumns: string[],
    searchTerm: string,
    filters: Record<string, unknown> = {},
    limit: number = 50,
    offset: number = 0,
    tenantId?: string
  ): Promise<{ rows: T[]; totalCount: number }> {
    const searchClause = searchColumns.map(col => 
      `${col} ILIKE $1`
    ).join(' OR ');

    const filterClauses = Object.keys(filters).map((key, index) => 
      `${key} = $${index + 2}`
    );

    let whereClause = `(${searchClause})`;
    const params: unknown[] = [`%${searchTerm}%`];

    if (tenantId) {
      whereClause += ` AND tenant_id = $${params.length + 1}`;
      params.push(tenantId);
    }

    if (filterClauses.length > 0) {
      whereClause += ` AND ${filterClauses.join(' AND ')}`;
      params.push(...Object.values(filters));
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM ${tableName} WHERE ${whereClause}`;
    const countResult = await this.query<{ total: number }>(countQuery, params, tenantId);
    const totalCount = countResult.rows[0]?.total || 0;

    // Get paginated results
    const dataQuery = `
      SELECT * FROM ${tableName} 
      WHERE ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;
    params.push(limit, offset);

    try {
      const result = await this.query<T>(dataQuery, params, tenantId);
      return {
        rows: result.rows,
        totalCount
      };
    } catch (error) {
      logger.error("Search query failed", {
        error: error instanceof Error ? error.message : String(error),
        tableName,
        searchTerm,
        tenantId
      });
      throw error;
    }
  }

  /**
   * Health check for database connection
   */
  async healthCheck(): Promise<{ healthy: boolean; latency: number; details: string }> {
    const startTime = Date.now();
    try {
      await this.query("SELECT 1 as health_check");
      const latency = Date.now() - startTime;
      return {
        healthy: true,
        latency,
        details: `Database connection healthy, latency: ${latency}ms`
      };
    } catch (error) {
      const latency = Date.now() - startTime;
      return {
        healthy: false,
        latency,
        details: `Database connection failed: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * Get connection pool statistics
   */
  getPoolStats(): {
    totalConnections: number;
    idleConnections: number;
    activeConnections: number;
  } {
    // Note: This would need to be implemented based on the actual pool implementation
    // For now, return mock data
    return {
      totalConnections: 20,
      idleConnections: 15,
      activeConnections: 5
    };
  }
}
