// Revenue Operations API Routes
// Comprehensive API endpoints for subscription lifecycle, usage billing, revenue recognition, and financial reporting

import { Router } from "https://deno.land/x/oak@v12.6.1/mod.ts";
import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { SubscriptionLifecycleService } from "../services/subscriptionLifecycleService.ts";
import { UsageBasedBillingService } from "../services/usageBasedBillingService.ts";
import { RevenueRecognitionService } from "../services/revenueRecognitionService.ts";
import { FinancialReportingService } from "../services/financialReportingService.ts";
import { RevenueIntelligenceService } from "../services/revenueIntelligenceService.ts";

const router = new Router();

// Initialize services
const subscriptionService = new SubscriptionLifecycleService();
const usageService = new UsageBasedBillingService();
const revenueService = new RevenueRecognitionService();
const reportingService = new FinancialReportingService();
const intelligenceService = new RevenueIntelligenceService();

// Validation schemas
const createSubscriptionSchema = z.object({
  customerId: z.string(),
  planId: z.string(),
  trialDays: z.number().optional(),
  startDate: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

const recordUsageSchema = z.object({
  customerId: z.string(),
  subscriptionId: z.string(),
  metricType: z.enum(['api_calls', 'data_volume', 'active_users', 'storage', 'bandwidth', 'custom']),
  value: z.number(),
  unit: z.string(),
  metadata: z.record(z.any()).optional(),
});

const revenueTransactionSchema = z.object({
  subscriptionId: z.string(),
  customerId: z.string(),
  transactionType: z.enum(['subscription', 'usage', 'setup_fee', 'overage', 'refund', 'credit']),
  amount: z.number(),
  currency: z.string(),
  contractualObligations: z.array(z.string()),
  performanceObligations: z.array(z.object({
    description: z.string(),
    allocatedAmount: z.number(),
  })),
});

// Subscription Lifecycle Management Routes

/**
 * Create new subscription
 */
router.post("/api/revenue-operations/subscriptions", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const body = await ctx.request.body({ type: "json" }).value;
    const data = createSubscriptionSchema.parse(body);

    const subscription = await subscriptionService.createSubscription(
      tenantId,
      data.customerId,
      data.planId,
      {
        trialDays: data.trialDays,
        startDate: data.startDate ? new Date(data.startDate) : undefined,
        metadata: data.metadata,
      }
    );

    ctx.response.status = 201;
    ctx.response.body = {
      success: true,
      data: subscription,
    };

    logger.info("Subscription created via API", {
      tenantId,
      subscriptionId: subscription.id,
      customerId: data.customerId,
    });
  } catch (error) {
    logger.error("Failed to create subscription", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

/**
 * Update subscription status
 */
router.patch("/api/revenue-operations/subscriptions/:subscriptionId/status", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;
    const body = await ctx.request.body({ type: "json" }).value;

    const subscription = await subscriptionService.updateSubscriptionStatus(
      tenantId,
      subscriptionId,
      body.status,
      body.metadata
    );

    ctx.response.body = {
      success: true,
      data: subscription,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

/**
 * Process subscription renewal
 */
router.post("/api/revenue-operations/subscriptions/:subscriptionId/renew", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;

    const result = await subscriptionService.processRenewal(tenantId, subscriptionId);

    ctx.response.body = {
      success: result.success,
      data: result,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

/**
 * Change subscription plan
 */
router.post("/api/revenue-operations/subscriptions/:subscriptionId/change-plan", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;
    const body = await ctx.request.body({ type: "json" }).value;

    const result = await subscriptionService.changeSubscriptionPlan(
      tenantId,
      subscriptionId,
      body.newPlanId,
      {
        prorationBehavior: body.prorationBehavior,
        effectiveDate: body.effectiveDate ? new Date(body.effectiveDate) : undefined,
      }
    );

    ctx.response.body = {
      success: result.success,
      data: result,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

/**
 * Get churn prediction for subscription
 */
router.get("/api/revenue-operations/subscriptions/:subscriptionId/churn-prediction", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;

    const prediction = await subscriptionService.predictChurnRisk(tenantId, subscriptionId);

    ctx.response.body = {
      success: true,
      data: prediction,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

// Usage-Based Billing Routes

/**
 * Record usage metric
 */
router.post("/api/revenue-operations/usage", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const body = await ctx.request.body({ type: "json" }).value;
    const data = recordUsageSchema.parse(body);

    const usage = await usageService.recordUsage(
      tenantId,
      data.customerId,
      data.subscriptionId,
      data.metricType,
      data.value,
      data.unit,
      data.metadata || {}
    );

    ctx.response.status = 201;
    ctx.response.body = {
      success: true,
      data: usage,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

/**
 * Get usage analytics for subscription
 */
router.get("/api/revenue-operations/subscriptions/:subscriptionId/usage-analytics", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;
    const startDate = new Date(ctx.request.url.searchParams.get("startDate") || Date.now() - 30 * 24 * 60 * 60 * 1000);
    const endDate = new Date(ctx.request.url.searchParams.get("endDate") || Date.now());

    const analytics = await usageService.getUsageAnalytics(tenantId, subscriptionId, {
      start: startDate,
      end: endDate,
    });

    ctx.response.body = {
      success: true,
      data: analytics,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

/**
 * Calculate overage billing
 */
router.post("/api/revenue-operations/subscriptions/:subscriptionId/calculate-overage", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;
    const body = await ctx.request.body({ type: "json" }).value;

    const overage = await usageService.calculateOverageBilling(tenantId, subscriptionId, {
      start: new Date(body.startDate),
      end: new Date(body.endDate),
    });

    ctx.response.body = {
      success: true,
      data: overage,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

/**
 * Get tier recommendation
 */
router.get("/api/revenue-operations/subscriptions/:subscriptionId/tier-recommendation", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;

    const recommendation = await usageService.getTierRecommendation(tenantId, subscriptionId);

    ctx.response.body = {
      success: true,
      data: recommendation,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

// Revenue Recognition Routes

/**
 * Process revenue transaction
 */
router.post("/api/revenue-operations/revenue-transactions", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const body = await ctx.request.body({ type: "json" }).value;
    const data = revenueTransactionSchema.parse(body);

    const transaction = await revenueService.processRevenueTransaction(tenantId, data);

    ctx.response.status = 201;
    ctx.response.body = {
      success: true,
      data: transaction,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

/**
 * Recognize deferred revenue
 */
router.post("/api/revenue-operations/recognize-deferred-revenue", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const body = await ctx.request.body({ type: "json" }).value;
    const date = body.date ? new Date(body.date) : new Date();

    const result = await revenueService.recognizeDeferredRevenue(tenantId, date);

    ctx.response.body = {
      success: true,
      data: result,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

/**
 * Generate compliance report
 */
router.post("/api/revenue-operations/compliance-reports", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const body = await ctx.request.body({ type: "json" }).value;

    const report = await revenueService.generateComplianceReport(
      tenantId,
      body.reportType,
      {
        start: new Date(body.startDate),
        end: new Date(body.endDate),
      }
    );

    ctx.response.status = 201;
    ctx.response.body = {
      success: true,
      data: report,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

/**
 * Get revenue schedule
 */
router.get("/api/revenue-operations/transactions/:transactionId/schedule", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const transactionId = ctx.params.transactionId;

    const schedule = await revenueService.getRevenueSchedule(tenantId, transactionId);

    ctx.response.body = {
      success: true,
      data: schedule,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

// Financial Reporting Routes

/**
 * Get financial metrics
 */
router.get("/api/revenue-operations/financial-metrics", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const startDate = new Date(ctx.request.url.searchParams.get("startDate") || Date.now() - 30 * 24 * 60 * 60 * 1000);
    const endDate = new Date(ctx.request.url.searchParams.get("endDate") || Date.now());
    const type = ctx.request.url.searchParams.get("type") || "monthly";

    const metrics = await reportingService.calculateFinancialMetrics(tenantId, {
      start: startDate,
      end: endDate,
      type: type as any,
    });

    ctx.response.body = {
      success: true,
      data: metrics,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

/**
 * Generate cohort analysis
 */
router.get("/api/revenue-operations/cohort-analysis", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const periods = parseInt(ctx.request.url.searchParams.get("periods") || "12");

    const analysis = await reportingService.generateCohortAnalysis(tenantId, periods);

    ctx.response.body = {
      success: true,
      data: analysis,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

/**
 * Generate revenue waterfall
 */
router.get("/api/revenue-operations/revenue-waterfall", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const startDate = new Date(ctx.request.url.searchParams.get("startDate") || Date.now() - 30 * 24 * 60 * 60 * 1000);
    const endDate = new Date(ctx.request.url.searchParams.get("endDate") || Date.now());

    const waterfall = await reportingService.generateRevenueWaterfall(tenantId, {
      start: startDate,
      end: endDate,
    });

    ctx.response.body = {
      success: true,
      data: waterfall,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

/**
 * Generate executive report
 */
router.post("/api/revenue-operations/executive-reports", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const body = await ctx.request.body({ type: "json" }).value;

    const report = await reportingService.generateExecutiveReport(
      tenantId,
      body.reportType,
      {
        start: new Date(body.startDate),
        end: new Date(body.endDate),
      }
    );

    ctx.response.status = 201;
    ctx.response.body = {
      success: true,
      data: report,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

// Revenue Intelligence Routes

/**
 * Generate revenue intelligence
 */
router.get("/api/revenue-operations/revenue-intelligence", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;

    const intelligence = await intelligenceService.generateRevenueIntelligence(tenantId);

    ctx.response.body = {
      success: true,
      data: intelligence,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

/**
 * Generate revenue scenarios
 */
router.get("/api/revenue-operations/revenue-scenarios", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const timeframe = parseInt(ctx.request.url.searchParams.get("timeframe") || "12");

    const scenarios = await intelligenceService.generateRevenueScenarios(tenantId, timeframe);

    ctx.response.body = {
      success: true,
      data: scenarios,
    };
  } catch (error) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: (error as Error).message,
    };
  }
});

// Health check endpoint
router.get("/api/revenue-operations/health", (ctx) => {
  ctx.response.body = {
    success: true,
    message: "Revenue Operations API is healthy",
    timestamp: new Date().toISOString(),
    services: {
      subscriptionLifecycle: "operational",
      usageBasedBilling: "operational",
      revenueRecognition: "operational",
      financialReporting: "operational",
      revenueIntelligence: "operational",
    },
  };
});

export { router as revenueOperationsRouter };
