-- Revenue Operations Database Schema
-- Comprehensive schema for subscription lifecycle, usage billing, revenue recognition, and financial reporting

-- Enable TimescaleDB extension for time-series optimization
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Subscriptions table for lifecycle management
CREATE TABLE IF NOT EXISTS subscriptions (
    id VARCHAR(255) PRIMARY KEY,
    tenant_id VARCHAR(255) NOT NULL,
    customer_id VARCHAR(255) NOT NULL,
    plan_id VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL CHECK (status IN ('active', 'past_due', 'canceled', 'unpaid', 'trialing', 'paused')),
    current_period_start TIMESTAMPTZ NOT NULL,
    current_period_end TIMESTAMPTZ NOT NULL,
    trial_end TIMESTAMPTZ,
    canceled_at TIMESTAMPTZ,
    paused_at TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for subscriptions
CREATE INDEX IF NOT EXISTS idx_subscriptions_tenant_id ON subscriptions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_customer_id ON subscriptions(customer_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_period_end ON subscriptions(current_period_end);

-- Subscription events for lifecycle tracking
CREATE TABLE IF NOT EXISTS subscription_events (
    id VARCHAR(255) PRIMARY KEY,
    subscription_id VARCHAR(255) NOT NULL REFERENCES subscriptions(id),
    tenant_id VARCHAR(255) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB DEFAULT '{}',
    processed_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for subscription events
CREATE INDEX IF NOT EXISTS idx_subscription_events_subscription_id ON subscription_events(subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscription_events_tenant_id ON subscription_events(tenant_id);
CREATE INDEX IF NOT EXISTS idx_subscription_events_type ON subscription_events(event_type);
CREATE INDEX IF NOT EXISTS idx_subscription_events_created_at ON subscription_events(created_at);

-- Usage metrics table (TimescaleDB hypertable)
CREATE TABLE IF NOT EXISTS usage_metrics (
    id VARCHAR(255) PRIMARY KEY,
    tenant_id VARCHAR(255) NOT NULL,
    customer_id VARCHAR(255) NOT NULL,
    subscription_id VARCHAR(255) NOT NULL,
    metric_type VARCHAR(100) NOT NULL,
    value DECIMAL(20,4) NOT NULL,
    unit VARCHAR(50) NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,
    metadata JSONB DEFAULT '{}'
);

-- Convert to hypertable for time-series optimization
SELECT create_hypertable('usage_metrics', 'timestamp', if_not_exists => TRUE);

-- Indexes for usage metrics
CREATE INDEX IF NOT EXISTS idx_usage_metrics_tenant_id ON usage_metrics(tenant_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_usage_metrics_subscription_id ON usage_metrics(subscription_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_usage_metrics_metric_type ON usage_metrics(metric_type, timestamp DESC);

-- Continuous aggregate for hourly usage
CREATE MATERIALIZED VIEW IF NOT EXISTS usage_metrics_hourly
WITH (timescaledb.continuous) AS
SELECT 
    tenant_id,
    subscription_id,
    metric_type,
    time_bucket('1 hour', timestamp) AS hour,
    SUM(value) AS total_value,
    AVG(value) AS avg_value,
    MAX(value) AS max_value,
    COUNT(*) AS count
FROM usage_metrics
GROUP BY tenant_id, subscription_id, metric_type, hour;

-- Continuous aggregate for daily usage
CREATE MATERIALIZED VIEW IF NOT EXISTS usage_metrics_daily
WITH (timescaledb.continuous) AS
SELECT 
    tenant_id,
    subscription_id,
    metric_type,
    time_bucket('1 day', timestamp) AS day,
    SUM(value) AS total_value,
    AVG(value) AS avg_value,
    MAX(value) AS max_value,
    COUNT(*) AS count
FROM usage_metrics
GROUP BY tenant_id, subscription_id, metric_type, day;

-- Overage billing table
CREATE TABLE IF NOT EXISTS overage_billing (
    id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
    subscription_id VARCHAR(255) NOT NULL,
    tenant_id VARCHAR(255) NOT NULL,
    billing_period_start TIMESTAMPTZ NOT NULL,
    billing_period_end TIMESTAMPTZ NOT NULL,
    overages JSONB NOT NULL,
    total_overage_amount DECIMAL(20,4) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for overage billing
CREATE INDEX IF NOT EXISTS idx_overage_billing_subscription_id ON overage_billing(subscription_id);
CREATE INDEX IF NOT EXISTS idx_overage_billing_tenant_id ON overage_billing(tenant_id);
CREATE INDEX IF NOT EXISTS idx_overage_billing_period ON overage_billing(billing_period_start, billing_period_end);

-- Revenue transactions table
CREATE TABLE IF NOT EXISTS revenue_transactions (
    id VARCHAR(255) PRIMARY KEY,
    tenant_id VARCHAR(255) NOT NULL,
    subscription_id VARCHAR(255) NOT NULL,
    customer_id VARCHAR(255) NOT NULL,
    transaction_type VARCHAR(100) NOT NULL,
    amount DECIMAL(20,4) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    recognition_method VARCHAR(100) NOT NULL,
    recognition_period_start TIMESTAMPTZ NOT NULL,
    recognition_period_end TIMESTAMPTZ NOT NULL,
    contractual_obligations JSONB DEFAULT '[]',
    performance_obligations JSONB DEFAULT '[]',
    recognized_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for revenue transactions
CREATE INDEX IF NOT EXISTS idx_revenue_transactions_tenant_id ON revenue_transactions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_revenue_transactions_subscription_id ON revenue_transactions(subscription_id);
CREATE INDEX IF NOT EXISTS idx_revenue_transactions_type ON revenue_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_revenue_transactions_recognition_period ON revenue_transactions(recognition_period_start, recognition_period_end);

-- Deferred revenue table
CREATE TABLE IF NOT EXISTS deferred_revenue (
    id VARCHAR(255) PRIMARY KEY,
    tenant_id VARCHAR(255) NOT NULL,
    transaction_id VARCHAR(255) NOT NULL REFERENCES revenue_transactions(id),
    total_amount DECIMAL(20,4) NOT NULL,
    recognized_amount DECIMAL(20,4) NOT NULL DEFAULT 0,
    remaining_amount DECIMAL(20,4) NOT NULL,
    recognition_schedule JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for deferred revenue
CREATE INDEX IF NOT EXISTS idx_deferred_revenue_tenant_id ON deferred_revenue(tenant_id);
CREATE INDEX IF NOT EXISTS idx_deferred_revenue_transaction_id ON deferred_revenue(transaction_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_deferred_revenue_unique_transaction ON deferred_revenue(transaction_id);

-- Revenue recognition rules table
CREATE TABLE IF NOT EXISTS revenue_recognition_rules (
    id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    conditions JSONB NOT NULL,
    recognition_method VARCHAR(100) NOT NULL,
    recognition_period JSONB,
    compliance_standard VARCHAR(50) NOT NULL DEFAULT 'ASC_606',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for revenue recognition rules
CREATE INDEX IF NOT EXISTS idx_revenue_recognition_rules_tenant_id ON revenue_recognition_rules(tenant_id);
CREATE INDEX IF NOT EXISTS idx_revenue_recognition_rules_active ON revenue_recognition_rules(is_active);

-- Compliance reports table
CREATE TABLE IF NOT EXISTS compliance_reports (
    id VARCHAR(255) PRIMARY KEY,
    tenant_id VARCHAR(255) NOT NULL,
    report_type VARCHAR(100) NOT NULL,
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    metrics JSONB NOT NULL,
    compliance_checks JSONB NOT NULL,
    generated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for compliance reports
CREATE INDEX IF NOT EXISTS idx_compliance_reports_tenant_id ON compliance_reports(tenant_id);
CREATE INDEX IF NOT EXISTS idx_compliance_reports_type ON compliance_reports(report_type);
CREATE INDEX IF NOT EXISTS idx_compliance_reports_period ON compliance_reports(period_start, period_end);

-- Compliance events table
CREATE TABLE IF NOT EXISTS compliance_events (
    id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id VARCHAR(255) NOT NULL,
    transaction_id VARCHAR(255),
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for compliance events
CREATE INDEX IF NOT EXISTS idx_compliance_events_tenant_id ON compliance_events(tenant_id);
CREATE INDEX IF NOT EXISTS idx_compliance_events_transaction_id ON compliance_events(transaction_id);
CREATE INDEX IF NOT EXISTS idx_compliance_events_type ON compliance_events(event_type);
CREATE INDEX IF NOT EXISTS idx_compliance_events_created_at ON compliance_events(created_at);

-- Executive reports table
CREATE TABLE IF NOT EXISTS executive_reports (
    id VARCHAR(255) PRIMARY KEY,
    tenant_id VARCHAR(255) NOT NULL,
    report_type VARCHAR(100) NOT NULL,
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    summary JSONB NOT NULL,
    key_metrics JSONB NOT NULL,
    cohort_analysis JSONB NOT NULL,
    revenue_waterfall JSONB NOT NULL,
    insights JSONB NOT NULL,
    generated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for executive reports
CREATE INDEX IF NOT EXISTS idx_executive_reports_tenant_id ON executive_reports(tenant_id);
CREATE INDEX IF NOT EXISTS idx_executive_reports_type ON executive_reports(report_type);
CREATE INDEX IF NOT EXISTS idx_executive_reports_period ON executive_reports(period_start, period_end);

-- Churn predictions table
CREATE TABLE IF NOT EXISTS churn_predictions (
    subscription_id VARCHAR(255) PRIMARY KEY,
    tenant_id VARCHAR(255) NOT NULL,
    churn_probability DECIMAL(5,4) NOT NULL,
    risk_level VARCHAR(50) NOT NULL,
    factors JSONB NOT NULL,
    recommendations JSONB NOT NULL,
    calculated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for churn predictions
CREATE INDEX IF NOT EXISTS idx_churn_predictions_tenant_id ON churn_predictions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_churn_predictions_risk_level ON churn_predictions(risk_level);
CREATE INDEX IF NOT EXISTS idx_churn_predictions_calculated_at ON churn_predictions(calculated_at);

-- Revenue intelligence table
CREATE TABLE IF NOT EXISTS revenue_intelligence (
    id VARCHAR(255) PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id VARCHAR(255) NOT NULL,
    analysis_date TIMESTAMPTZ NOT NULL,
    predictions JSONB NOT NULL,
    opportunities JSONB NOT NULL,
    risks JSONB NOT NULL,
    insights JSONB NOT NULL,
    recommendations JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for revenue intelligence
CREATE INDEX IF NOT EXISTS idx_revenue_intelligence_tenant_id ON revenue_intelligence(tenant_id);
CREATE INDEX IF NOT EXISTS idx_revenue_intelligence_analysis_date ON revenue_intelligence(analysis_date);

-- Data retention policies for TimescaleDB
SELECT add_retention_policy('usage_metrics', INTERVAL '2 years');
SELECT add_retention_policy('usage_metrics_hourly', INTERVAL '1 year');
SELECT add_retention_policy('usage_metrics_daily', INTERVAL '5 years');

-- Compression policies for TimescaleDB
SELECT add_compression_policy('usage_metrics', INTERVAL '7 days');
SELECT add_compression_policy('usage_metrics_hourly', INTERVAL '30 days');
SELECT add_compression_policy('usage_metrics_daily', INTERVAL '90 days');

-- Continuous aggregate refresh policies
SELECT add_continuous_aggregate_policy('usage_metrics_hourly',
    start_offset => INTERVAL '1 day',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour');

SELECT add_continuous_aggregate_policy('usage_metrics_daily',
    start_offset => INTERVAL '7 days',
    end_offset => INTERVAL '1 day',
    schedule_interval => INTERVAL '1 day');

-- Row Level Security (RLS) for multi-tenant isolation
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE overage_billing ENABLE ROW LEVEL SECURITY;
ALTER TABLE revenue_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE deferred_revenue ENABLE ROW LEVEL SECURITY;
ALTER TABLE revenue_recognition_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE compliance_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE compliance_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE executive_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE churn_predictions ENABLE ROW LEVEL SECURITY;
ALTER TABLE revenue_intelligence ENABLE ROW LEVEL SECURITY;

-- RLS policies for tenant isolation
CREATE POLICY tenant_isolation_subscriptions ON subscriptions
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id', true));

CREATE POLICY tenant_isolation_subscription_events ON subscription_events
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id', true));

CREATE POLICY tenant_isolation_usage_metrics ON usage_metrics
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id', true));

CREATE POLICY tenant_isolation_overage_billing ON overage_billing
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id', true));

CREATE POLICY tenant_isolation_revenue_transactions ON revenue_transactions
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id', true));

CREATE POLICY tenant_isolation_deferred_revenue ON deferred_revenue
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id', true));

CREATE POLICY tenant_isolation_revenue_recognition_rules ON revenue_recognition_rules
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id', true));

CREATE POLICY tenant_isolation_compliance_reports ON compliance_reports
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id', true));

CREATE POLICY tenant_isolation_compliance_events ON compliance_events
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id', true));

CREATE POLICY tenant_isolation_executive_reports ON executive_reports
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id', true));

CREATE POLICY tenant_isolation_churn_predictions ON churn_predictions
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id', true));

CREATE POLICY tenant_isolation_revenue_intelligence ON revenue_intelligence
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id', true));

-- Performance monitoring views
CREATE OR REPLACE VIEW revenue_operations_performance AS
SELECT 
    'subscriptions' as table_name,
    COUNT(*) as row_count,
    pg_size_pretty(pg_total_relation_size('subscriptions')) as size
FROM subscriptions
UNION ALL
SELECT 
    'usage_metrics' as table_name,
    COUNT(*) as row_count,
    pg_size_pretty(pg_total_relation_size('usage_metrics')) as size
FROM usage_metrics
UNION ALL
SELECT 
    'revenue_transactions' as table_name,
    COUNT(*) as row_count,
    pg_size_pretty(pg_total_relation_size('revenue_transactions')) as size
FROM revenue_transactions;

-- Revenue operations summary view
CREATE OR REPLACE VIEW revenue_operations_summary AS
SELECT 
    tenant_id,
    COUNT(DISTINCT s.id) as total_subscriptions,
    COUNT(DISTINCT CASE WHEN s.status = 'active' THEN s.id END) as active_subscriptions,
    COUNT(DISTINCT rt.id) as total_transactions,
    SUM(rt.amount) as total_revenue,
    COUNT(DISTINCT um.subscription_id) as subscriptions_with_usage,
    SUM(um.value) as total_usage_value
FROM subscriptions s
LEFT JOIN revenue_transactions rt ON s.id = rt.subscription_id
LEFT JOIN usage_metrics um ON s.id = um.subscription_id
GROUP BY tenant_id;

-- Comments for documentation
COMMENT ON TABLE subscriptions IS 'Core subscription lifecycle management with status tracking and metadata';
COMMENT ON TABLE usage_metrics IS 'Time-series usage data optimized with TimescaleDB for high-volume ingestion';
COMMENT ON TABLE revenue_transactions IS 'Revenue recognition transactions with performance obligations tracking';
COMMENT ON TABLE deferred_revenue IS 'Deferred revenue schedules for compliance and recognition automation';
COMMENT ON TABLE compliance_reports IS 'Automated compliance reporting for ASC 606, IFRS 15, and GAAP standards';
COMMENT ON TABLE revenue_intelligence IS 'AI-powered revenue insights, predictions, and recommendations';

-- Grant permissions (adjust based on your user roles)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO revenue_operations_user;
-- GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO revenue_operations_user;
