#!/usr/bin/env deno run --allow-read --allow-write

// Fix remaining handlers that still have direct service calls

const filePath = "src/routes/enhancedSubscriptions.ts";
const content = await Deno.readTextFile(filePath);
let lines = content.split('\n');

const serviceNames = [
  'demoEnvironmentService',
  'salesMaterialsService', 
  'trialEnvironmentService'
];

console.log("🔧 Fixing remaining handlers with direct service calls...");

// Find handlers that have direct service calls but no getServices()
for (let i = 0; i < lines.length; i++) {
  const line = lines[i];
  
  // Check if this line has a direct service call
  for (const serviceName of serviceNames) {
    if (line.includes(`${serviceName}.`) && !line.includes(`services.${serviceName}.`)) {
      console.log(`🔍 Found direct service call at line ${i + 1}: ${serviceName}`);
      
      // Find the start of this handler (look backwards for route definition)
      let handlerStart = -1;
      for (let j = i; j >= Math.max(0, i - 20); j--) {
        if (lines[j].includes('.get(') || lines[j].includes('.post(') || 
            lines[j].includes('.put(') || lines[j].includes('.delete(')) {
          handlerStart = j;
          break;
        }
      }
      
      if (handlerStart === -1) continue;
      
      // Find the try block after the handler start
      let tryBlockStart = -1;
      for (let j = handlerStart; j < Math.min(handlerStart + 10, lines.length); j++) {
        if (lines[j].trim().startsWith('try {')) {
          tryBlockStart = j;
          break;
        }
      }
      
      if (tryBlockStart === -1) continue;
      
      // Check if this handler already has getServices()
      let hasGetServices = false;
      for (let j = tryBlockStart; j < Math.min(tryBlockStart + 5, lines.length); j++) {
        if (lines[j].includes('getServices()')) {
          hasGetServices = true;
          break;
        }
      }
      
      // If no getServices(), add it
      if (!hasGetServices) {
        const indent = lines[tryBlockStart].match(/^(\s*)/)?.[1] || '  ';
        const servicesLine = `${indent}  const services = getServices();`;
        lines.splice(tryBlockStart + 1, 0, servicesLine);
        console.log(`   ✅ Added getServices() to handler starting at line ${handlerStart + 1}`);
        
        // Update line numbers since we inserted a line
        i++;
      }
      
      // Now replace the direct service call on the current line
      lines[i] = lines[i].replace(`${serviceName}.`, `services.${serviceName}.`);
      console.log(`   ✅ Updated service call at line ${i + 1}`);
    }
  }
}

// Second pass: fix any remaining direct service calls
console.log("🔄 Second pass: fixing any remaining direct service calls...");
for (let i = 0; i < lines.length; i++) {
  let line = lines[i];
  let updated = false;
  
  for (const serviceName of serviceNames) {
    if (line.includes(`${serviceName}.`) && !line.includes(`services.${serviceName}.`)) {
      line = line.replace(new RegExp(`\\b${serviceName}\\.`, 'g'), `services.${serviceName}.`);
      updated = true;
    }
  }
  
  if (updated) {
    lines[i] = line;
    console.log(`   ✅ Fixed remaining service call at line ${i + 1}`);
  }
}

// Write the updated content
const updatedContent = lines.join('\n');
await Deno.writeTextFile(filePath, updatedContent);

console.log("✅ All remaining handlers fixed!");
