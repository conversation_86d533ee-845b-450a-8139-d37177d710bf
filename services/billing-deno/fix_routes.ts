#!/usr/bin/env deno run --allow-read --allow-write

// <PERSON><PERSON><PERSON> to fix all route handlers to use getServices() function

const filePath = "src/routes/enhancedSubscriptions.ts";

// Read the file
const content = await Deno.readTextFile(filePath);

// Define the service names that need to be replaced
const serviceNames = [
  'enhancedSubscriptionService',
  'unifiedRevenueService', 
  'subscriptionLifecycleService',
  'usageBasedBillingService',
  'revenueIntelligenceService',
  'demoEnvironmentService',
  'salesMaterialsService',
  'trialEnvironmentService'
];

// Split content into lines
let lines = content.split('\n');

// Track which route handlers we've already processed
const processedHandlers = new Set<number>();

// Process each line
for (let i = 0; i < lines.length; i++) {
  const line = lines[i];
  
  // Check if this line contains a route handler definition
  if (line.includes('.post(') || line.includes('.get(') || line.includes('.put(') || line.includes('.delete(')) {
    // Find the opening of the async function
    let j = i;
    while (j < lines.length && !lines[j].includes('async (ctx)')) {
      j++;
    }
    
    if (j < lines.length && !processedHandlers.has(j)) {
      // Found the start of an async handler
      processedHandlers.add(j);
      
      // Look for the try block
      let k = j;
      while (k < lines.length && !lines[k].trim().startsWith('try {')) {
        k++;
      }
      
      if (k < lines.length) {
        // Insert the getServices() call after the try block
        const indent = lines[k].match(/^(\s*)/)?.[1] || '  ';
        const servicesLine = `${indent}  const services = getServices();`;
        
        // Check if services line already exists
        if (!lines[k + 1]?.includes('const services = getServices()')) {
          lines.splice(k + 1, 0, servicesLine);
          
          // Update all service references in this handler
          let handlerEnd = k + 1;
          let braceCount = 0;
          let inHandler = false;
          
          for (let m = k; m < lines.length; m++) {
            const currentLine = lines[m];
            
            // Count braces to find the end of the handler
            for (const char of currentLine) {
              if (char === '{') {
                braceCount++;
                inHandler = true;
              } else if (char === '}') {
                braceCount--;
                if (braceCount === 0 && inHandler) {
                  handlerEnd = m;
                  break;
                }
              }
            }
            
            if (braceCount === 0 && inHandler) {
              handlerEnd = m;
              break;
            }
            
            // Replace service references in this handler
            if (m > k && m <= handlerEnd) {
              for (const serviceName of serviceNames) {
                if (currentLine.includes(`await ${serviceName}.`) || 
                    currentLine.includes(`${serviceName}.`) && !currentLine.includes('services.')) {
                  lines[m] = currentLine.replace(
                    new RegExp(`\\b${serviceName}\\.`, 'g'), 
                    `services.${serviceName}.`
                  );
                }
              }
            }
          }
        }
      }
    }
  }
}

// Write the updated content back to the file
const updatedContent = lines.join('\n');
await Deno.writeTextFile(filePath, updatedContent);

console.log("✅ Fixed all route handlers to use getServices() function");
