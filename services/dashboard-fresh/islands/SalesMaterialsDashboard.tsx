// Sales Materials Dashboard Island
// Comprehensive dashboard for battle cards, case studies, and sales reports

import { useEffect, useState } from "preact/hooks";
import { signal } from "@preact/signals";

// Sales materials state signals
const battleCards = signal([]);
const caseStudies = signal([]);
const selectedBattleCard = signal(null);
const selectedCaseStudy = signal(null);
const generatedReports = signal([]);

interface SalesMaterialsDashboardProps {
  tool: 'battle-cards' | 'case-studies' | 'reports';
}

export default function SalesMaterialsDashboard({ tool }: SalesMaterialsDashboardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCompetitor, setSelectedCompetitor] = useState("google_analytics");
  const [reportType, setReportType] = useState("roi_report");

  useEffect(() => {
    if (tool === 'battle-cards') {
      loadBattleCards();
    } else if (tool === 'case-studies') {
      loadCaseStudies();
    }
  }, [tool]);

  const loadBattleCards = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/demo/battle-cards');
      if (response.ok) {
        const result = await response.json();
        battleCards.value = result.data.battleCards;
        if (result.data.battleCards.length > 0) {
          selectedBattleCard.value = result.data.battleCards[0];
        }
      }
    } catch (error) {
      console.error("Failed to load battle cards:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadCaseStudies = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/demo/case-studies');
      if (response.ok) {
        const result = await response.json();
        caseStudies.value = result.data.caseStudies;
        if (result.data.caseStudies.length > 0) {
          selectedCaseStudy.value = result.data.caseStudies[0];
        }
      }
    } catch (error) {
      console.error("Failed to load case studies:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const generateReport = async (type: string, data?: any) => {
    setIsLoading(true);
    try {
      let endpoint = '';
      let payload = {};

      if (type === 'roi_report') {
        endpoint = '/api/demo/generate-roi-report';
        payload = data || {
          companyName: "Demo Company",
          industry: "ecommerce",
          monthlyEvents: 1000000,
          currentAnalyticsCost: 5000,
          teamSize: 10,
          currentQueryTime: 2500
        };
      } else if (type === 'performance_report') {
        endpoint = '/api/demo/generate-performance-report';
        payload = { competitors: ["google_analytics", "mixpanel", "adobe_analytics"] };
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        const result = await response.json();
        generatedReports.value = [...generatedReports.value, result.data.report];
      }
    } catch (error) {
      console.error("Failed to generate report:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const downloadReport = (report: any) => {
    const blob = new Blob([report.content], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${report.title}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div class="bg-white rounded-lg shadow-lg p-6">
      {/* Battle Cards */}
      {tool === 'battle-cards' && (
        <div>
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold text-gray-900">Competitive Battle Cards</h3>
            <select
              value={selectedCompetitor}
              onChange={(e) => {
                const competitor = (e.target as HTMLSelectElement).value;
                setSelectedCompetitor(competitor);
                const card = battleCards.value.find(bc => bc.competitor.toLowerCase().replace(/\s+/g, '_') === competitor);
                selectedBattleCard.value = card;
              }}
              class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="google_analytics">Google Analytics</option>
              <option value="mixpanel">Mixpanel</option>
              <option value="adobe_analytics">Adobe Analytics</option>
            </select>
          </div>

          {selectedBattleCard.value && (
            <div class="space-y-6">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Competitor Overview */}
                <div class="bg-gray-50 rounded-lg p-4">
                  <h4 class="text-lg font-semibold mb-3 text-gray-900">
                    {selectedBattleCard.value.competitor} Overview
                  </h4>
                  <div class="space-y-3">
                    <div>
                      <h5 class="font-medium text-gray-700">Category</h5>
                      <p class="text-gray-600">{selectedBattleCard.value.category}</p>
                    </div>
                    <div>
                      <h5 class="font-medium text-gray-700">Strengths</h5>
                      <ul class="list-disc list-inside text-gray-600 space-y-1">
                        {selectedBattleCard.value.strengths.map((strength, index) => (
                          <li key={index}>{strength}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h5 class="font-medium text-gray-700">Weaknesses</h5>
                      <ul class="list-disc list-inside text-gray-600 space-y-1">
                        {selectedBattleCard.value.weaknesses.map((weakness, index) => (
                          <li key={index}>{weakness}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Our Advantages */}
                <div class="bg-green-50 rounded-lg p-4">
                  <h4 class="text-lg font-semibold mb-3 text-green-900">Our Advantages</h4>
                  <div class="space-y-3">
                    <div>
                      <h5 class="font-medium text-green-700">Key Differentiators</h5>
                      <ul class="list-disc list-inside text-green-600 space-y-1">
                        {selectedBattleCard.value.keyDifferentiators.map((diff, index) => (
                          <li key={index}>{diff}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h5 class="font-medium text-green-700">Performance Metrics</h5>
                      <div class="grid grid-cols-2 gap-2 mt-2">
                        {Object.entries(selectedBattleCard.value.competitiveMetrics).map(([key, value]) => (
                          <div key={key} class="bg-white p-2 rounded border">
                            <div class="text-sm font-medium text-green-700">
                              {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                            </div>
                            <div class="text-lg font-bold text-green-600">{value}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Objection Handling */}
              <div class="bg-blue-50 rounded-lg p-4">
                <h4 class="text-lg font-semibold mb-3 text-blue-900">Objection Handling</h4>
                <div class="space-y-4">
                  {selectedBattleCard.value.commonObjections.map((objection, index) => (
                    <div key={index} class="bg-white rounded-lg p-4 border border-blue-200">
                      <h5 class="font-medium text-blue-700 mb-2">
                        Objection: "{objection.objection}"
                      </h5>
                      <p class="text-blue-600 mb-3">{objection.response}</p>
                      <div>
                        <h6 class="font-medium text-blue-700 text-sm">Supporting Data:</h6>
                        <ul class="list-disc list-inside text-blue-600 text-sm space-y-1">
                          {objection.supportingData.map((data, dataIndex) => (
                            <li key={dataIndex}>{data}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Winning Messages */}
              <div class="bg-yellow-50 rounded-lg p-4">
                <h4 class="text-lg font-semibold mb-3 text-yellow-900">Winning Messages</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {selectedBattleCard.value.winningMessages.map((message, index) => (
                    <div key={index} class="bg-white p-3 rounded border border-yellow-200">
                      <p class="text-yellow-700 font-medium">💡 {message}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Case Studies */}
      {tool === 'case-studies' && (
        <div>
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold text-gray-900">Customer Case Studies</h3>
            <select
              value={selectedCaseStudy.value?.id || ''}
              onChange={(e) => {
                const caseStudyId = (e.target as HTMLSelectElement).value;
                const caseStudy = caseStudies.value.find(cs => cs.id === caseStudyId);
                selectedCaseStudy.value = caseStudy;
              }}
              class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {caseStudies.value.map(cs => (
                <option key={cs.id} value={cs.id}>{cs.customerName}</option>
              ))}
            </select>
          </div>

          {selectedCaseStudy.value && (
            <div class="space-y-6">
              {/* Customer Overview */}
              <div class="bg-gray-50 rounded-lg p-6">
                <h4 class="text-2xl font-bold text-gray-900 mb-2">
                  {selectedCaseStudy.value.customerName}
                </h4>
                <p class="text-lg text-gray-600 mb-4">{selectedCaseStudy.value.industry}</p>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2">Challenge</h5>
                    <p class="text-gray-600">{selectedCaseStudy.value.challenge}</p>
                  </div>
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2">Solution</h5>
                    <p class="text-gray-600">{selectedCaseStudy.value.solution}</p>
                  </div>
                </div>
              </div>

              {/* Results */}
              <div class="bg-green-50 rounded-lg p-6">
                <h4 class="text-xl font-bold text-green-900 mb-4">Results & Impact</h4>
                
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                  {Object.entries(selectedCaseStudy.value.results.improvements).map(([metric, improvement]) => (
                    <div key={metric} class="bg-white p-4 rounded-lg border border-green-200 text-center">
                      <div class="text-sm font-medium text-green-700 mb-1">
                        {metric.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </div>
                      <div class="text-lg font-bold text-green-600">{improvement}</div>
                    </div>
                  ))}
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="bg-white p-4 rounded-lg border border-green-200">
                    <h5 class="font-semibold text-green-700 mb-2">Revenue Impact</h5>
                    <p class="text-2xl font-bold text-green-600">
                      ${selectedCaseStudy.value.results.revenueImpact.toLocaleString()}
                    </p>
                    <p class="text-sm text-green-600">Annual revenue increase</p>
                  </div>
                  <div class="bg-white p-4 rounded-lg border border-green-200">
                    <h5 class="font-semibold text-green-700 mb-2">Implementation Time</h5>
                    <p class="text-2xl font-bold text-green-600">
                      {selectedCaseStudy.value.results.implementationTime} {selectedCaseStudy.value.results.implementationTime === 1 ? 'hour' : 'hours'}
                    </p>
                    <p class="text-sm text-green-600">Setup and deployment</p>
                  </div>
                </div>
              </div>

              {/* Testimonial */}
              {selectedCaseStudy.value.testimonial && (
                <div class="bg-blue-50 rounded-lg p-6">
                  <h4 class="text-xl font-bold text-blue-900 mb-4">Customer Testimonial</h4>
                  <blockquote class="text-blue-700 italic text-lg">
                    "{selectedCaseStudy.value.testimonial}"
                  </blockquote>
                </div>
              )}

              {/* Key Takeaways */}
              <div class="bg-purple-50 rounded-lg p-6">
                <h4 class="text-xl font-bold text-purple-900 mb-4">Key Takeaways</h4>
                <ul class="list-disc list-inside text-purple-700 space-y-2">
                  {selectedCaseStudy.value.keyTakeaways.map((takeaway, index) => (
                    <li key={index}>{takeaway}</li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Reports */}
      {tool === 'reports' && (
        <div>
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold text-gray-900">Sales Reports & Materials</h3>
            <div class="flex space-x-2">
              <select
                value={reportType}
                onChange={(e) => setReportType((e.target as HTMLSelectElement).value)}
                class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="roi_report">ROI Report</option>
                <option value="performance_report">Performance Report</option>
              </select>
              <button
                onClick={() => generateReport(reportType)}
                disabled={isLoading}
                class="px-4 py-2 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                {isLoading ? 'Generating...' : 'Generate Report'}
              </button>
            </div>
          </div>

          {/* Generated Reports */}
          <div class="space-y-4">
            {generatedReports.value.map((report, index) => (
              <div key={index} class="bg-gray-50 rounded-lg p-4 flex justify-between items-center">
                <div>
                  <h4 class="font-semibold text-gray-900">{report.title}</h4>
                  <p class="text-sm text-gray-600">
                    Generated on {new Date(report.metadata.generatedAt).toLocaleDateString()}
                  </p>
                </div>
                <button
                  onClick={() => downloadReport(report)}
                  class="px-4 py-2 bg-green-600 text-white rounded-md font-medium hover:bg-green-700 transition-colors"
                >
                  Download
                </button>
              </div>
            ))}
          </div>

          {generatedReports.value.length === 0 && (
            <div class="text-center py-12">
              <p class="text-gray-500">No reports generated yet. Click "Generate Report" to create your first report.</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
