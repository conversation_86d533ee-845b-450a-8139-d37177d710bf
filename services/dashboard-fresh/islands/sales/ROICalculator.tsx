// ROI Calculator - Fresh Islands Component
// Interactive ROI calculation tool with industry benchmarks and automated report generation

import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";

interface ROITemplate {
  id: string;
  name: string;
  industry: string;
  description: string;
  variables: Array<{
    name: string;
    label: string;
    type: 'currency' | 'percentage' | 'number';
    defaultValue: number;
  }>;
  calculations: {
    improvementFactor: number;
    implementationCost: number;
    monthlyCost: number;
  };
}

interface IndustryBenchmarks {
  [industry: string]: {
    averageConversionRate?: number;
    averageOrderValue?: number;
    averageCustomerLifetime?: number;
    averageROIImprovement: number;
    timeToValue: string;
    commonChallenges: string[];
  };
}

interface ROICalculatorProps {
  initialROITemplates: ROITemplate[] | null;
  initialIndustryBenchmarks: IndustryBenchmarks | null;
  user: any;
  isOffline?: boolean;
}

export default function ROICalculator({
  initialROITemplates,
  initialIndustryBenchmarks,
  user,
  isOffline = false
}: ROICalculatorProps) {
  // Signals for reactive state management
  const roiTemplates = useSignal(initialROITemplates || []);
  const industryBenchmarks = useSignal(initialIndustryBenchmarks || {});
  const selectedTemplate = useSignal<string | null>(null);
  const inputValues = useSignal<Record<string, number>>({});
  const isLoading = useSignal(false);
  const lastUpdated = useSignal(new Date().toISOString());
  const showResults = useSignal(false);

  // Get current template
  const currentTemplate = useComputed(() => {
    if (!selectedTemplate.value) return null;
    return roiTemplates.value.find(t => t.id === selectedTemplate.value) || null;
  });

  // Calculate ROI results
  const roiResults = useComputed(() => {
    const template = currentTemplate.value;
    if (!template) return null;

    const values = inputValues.value;
    const { improvementFactor, implementationCost, monthlyCost } = template.calculations;

    // Industry-specific calculations
    if (template.industry === 'E-commerce') {
      const monthlyRevenue = values.monthlyRevenue || 0;
      const conversionRate = values.conversionRate || 0;
      const averageOrderValue = values.averageOrderValue || 0;
      const monthlyTraffic = values.monthlyTraffic || 0;
      const currentAnalyticsCost = values.currentAnalyticsCost || 0;

      const improvedConversionRate = conversionRate * improvementFactor;
      const additionalConversions = monthlyTraffic * (improvedConversionRate - conversionRate) / 100;
      const additionalRevenue = additionalConversions * averageOrderValue;
      const annualAdditionalRevenue = additionalRevenue * 12;
      
      const totalFirstYearCost = implementationCost + (monthlyCost * 12);
      const currentAnnualCost = currentAnalyticsCost * 12;
      const netCostIncrease = totalFirstYearCost - currentAnnualCost;
      
      const firstYearROI = ((annualAdditionalRevenue - netCostIncrease) / netCostIncrease) * 100;
      const paybackPeriod = netCostIncrease / additionalRevenue;

      return {
        additionalMonthlyRevenue: additionalRevenue,
        additionalAnnualRevenue: annualAdditionalRevenue,
        firstYearROI,
        paybackPeriod,
        totalFirstYearCost,
        netCostIncrease,
        improvedConversionRate,
        additionalConversions
      };
    } else if (template.industry === 'Technology') {
      const mrr = values.monthlyRecurringRevenue || 0;
      const cac = values.customerAcquisitionCost || 0;
      const churnRate = values.churnRate || 0;
      const customerLifetime = values.averageCustomerLifetime || 0;
      const currentAnalyticsCost = values.currentAnalyticsCost || 0;

      const improvedChurnRate = churnRate * (1 - (improvementFactor - 1));
      const improvedLifetime = customerLifetime * improvementFactor;
      const additionalLTV = (improvedLifetime - customerLifetime) * (mrr / (mrr / cac * customerLifetime));
      const annualAdditionalRevenue = additionalLTV * (mrr / cac) * 12;
      
      const totalFirstYearCost = implementationCost + (monthlyCost * 12);
      const currentAnnualCost = currentAnalyticsCost * 12;
      const netCostIncrease = totalFirstYearCost - currentAnnualCost;
      
      const firstYearROI = ((annualAdditionalRevenue - netCostIncrease) / netCostIncrease) * 100;
      const paybackPeriod = netCostIncrease / (annualAdditionalRevenue / 12);

      return {
        additionalMonthlyRevenue: annualAdditionalRevenue / 12,
        additionalAnnualRevenue: annualAdditionalRevenue,
        firstYearROI,
        paybackPeriod,
        totalFirstYearCost,
        netCostIncrease,
        improvedChurnRate,
        improvedLifetime
      };
    }

    return null;
  });

  // Auto-refresh data every 5 minutes (if not offline)
  useEffect(() => {
    if (isOffline) return;

    const interval = setInterval(async () => {
      if (!isLoading.value) {
        await refreshData();
      }
    }, 300000);

    return () => clearInterval(interval);
  }, [isOffline]);

  const refreshData = async () => {
    if (isOffline) return;
    
    isLoading.value = true;
    try {
      const [templatesData, benchmarksData] = await Promise.all([
        fetch('/api/demo/roi-templates').then(r => r.json()),
        fetch('/api/demo/industry-benchmarks').then(r => r.json()),
      ]);

      roiTemplates.value = templatesData.data || [];
      industryBenchmarks.value = benchmarksData.data || {};
      lastUpdated.value = new Date().toISOString();
    } catch (error) {
      console.error('Failed to refresh ROI calculator data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const selectTemplate = (templateId: string) => {
    selectedTemplate.value = templateId;
    const template = roiTemplates.value.find(t => t.id === templateId);
    if (template) {
      const defaultValues: Record<string, number> = {};
      template.variables.forEach(variable => {
        defaultValues[variable.name] = variable.defaultValue;
      });
      inputValues.value = defaultValues;
    }
    showResults.value = false;
  };

  const updateInputValue = (variableName: string, value: number) => {
    inputValues.value = {
      ...inputValues.value,
      [variableName]: value
    };
  };

  const calculateROI = () => {
    showResults.value = true;
  };

  const generateReport = async () => {
    try {
      const response = await fetch('/api/demo/generate-roi-report', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId: selectedTemplate.value,
          inputValues: inputValues.value,
          results: roiResults.value,
          userId: user.id
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log('ROI report generated:', result);
        // Handle report download or display
      }
    } catch (error) {
      console.error('Failed to generate ROI report:', error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(Math.round(num));
  };

  return (
    <div class="roi-calculator-dashboard space-y-6">
      {/* Dashboard Header */}
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex items-center gap-4">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            ROI Calculator
          </h2>
          {!isOffline && (
            <button
              onClick={refreshData}
              disabled={isLoading.value}
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <svg 
                class={`w-4 h-4 mr-1.5 ${isLoading.value ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          )}
        </div>
      </div>

      {/* Template Selection */}
      {!selectedTemplate.value && (
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Select Industry Template
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {roiTemplates.value.map((template) => (
              <div key={template.id} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-primary-300 dark:hover:border-primary-600 cursor-pointer transition-colors"
                   onClick={() => selectTemplate(template.id)}>
                <h4 class="font-medium text-gray-900 dark:text-white mb-2">
                  {template.name}
                </h4>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {template.description}
                </p>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  Industry: {template.industry}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Calculator Interface */}
      {selectedTemplate.value && currentTemplate.value && (
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Input Form */}
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                {currentTemplate.value.name}
              </h3>
              <button
                onClick={() => {
                  selectedTemplate.value = null;
                  showResults.value = false;
                }}
                class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
              >
                Change Template
              </button>
            </div>
            
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
              {currentTemplate.value.description}
            </p>
            
            <div class="space-y-4">
              {currentTemplate.value.variables.map((variable) => (
                <div key={variable.name}>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {variable.label}
                  </label>
                  <div class="relative">
                    {variable.type === 'currency' && (
                      <span class="absolute left-3 top-2.5 text-gray-500 dark:text-gray-400">$</span>
                    )}
                    <input
                      type="number"
                      value={inputValues.value[variable.name] || variable.defaultValue}
                      onInput={(e) => updateInputValue(variable.name, parseFloat((e.target as HTMLInputElement).value) || 0)}
                      class={`w-full ${variable.type === 'currency' ? 'pl-8' : 'pl-3'} pr-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white`}
                    />
                    {variable.type === 'percentage' && (
                      <span class="absolute right-3 top-2.5 text-gray-500 dark:text-gray-400">%</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            <button
              onClick={calculateROI}
              class="w-full mt-6 inline-flex items-center justify-center px-4 py-2.5 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              Calculate ROI
            </button>
          </div>

          {/* Results */}
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              ROI Analysis Results
            </h3>
            
            {!showResults.value ? (
              <div class="text-center py-8">
                <svg class="w-16 h-16 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                <p class="text-gray-500 dark:text-gray-400">Enter your values and click "Calculate ROI" to see results</p>
              </div>
            ) : roiResults.value ? (
              <div class="space-y-6">
                {/* Key Metrics */}
                <div class="grid grid-cols-2 gap-4">
                  <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                      {formatPercentage(roiResults.value.firstYearROI)}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">First Year ROI</div>
                  </div>
                  
                  <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {roiResults.value.paybackPeriod.toFixed(1)}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Payback (Months)</div>
                  </div>
                </div>

                {/* Revenue Impact */}
                <div>
                  <h4 class="font-medium text-gray-900 dark:text-white mb-3">Revenue Impact</h4>
                  <div class="space-y-2">
                    <div class="flex justify-between">
                      <span class="text-sm text-gray-600 dark:text-gray-400">Additional Monthly Revenue:</span>
                      <span class="font-medium text-gray-900 dark:text-white">
                        {formatCurrency(roiResults.value.additionalMonthlyRevenue)}
                      </span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-sm text-gray-600 dark:text-gray-400">Additional Annual Revenue:</span>
                      <span class="font-medium text-gray-900 dark:text-white">
                        {formatCurrency(roiResults.value.additionalAnnualRevenue)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Cost Analysis */}
                <div>
                  <h4 class="font-medium text-gray-900 dark:text-white mb-3">Cost Analysis</h4>
                  <div class="space-y-2">
                    <div class="flex justify-between">
                      <span class="text-sm text-gray-600 dark:text-gray-400">Total First Year Cost:</span>
                      <span class="font-medium text-gray-900 dark:text-white">
                        {formatCurrency(roiResults.value.totalFirstYearCost)}
                      </span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-sm text-gray-600 dark:text-gray-400">Net Cost Increase:</span>
                      <span class="font-medium text-gray-900 dark:text-white">
                        {formatCurrency(roiResults.value.netCostIncrease)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Performance Improvements */}
                <div>
                  <h4 class="font-medium text-gray-900 dark:text-white mb-3">Performance Improvements</h4>
                  <div class="space-y-2">
                    {currentTemplate.value.industry === 'E-commerce' && (
                      <>
                        <div class="flex justify-between">
                          <span class="text-sm text-gray-600 dark:text-gray-400">Improved Conversion Rate:</span>
                          <span class="font-medium text-gray-900 dark:text-white">
                            {formatPercentage(roiResults.value.improvedConversionRate)}
                          </span>
                        </div>
                        <div class="flex justify-between">
                          <span class="text-sm text-gray-600 dark:text-gray-400">Additional Monthly Conversions:</span>
                          <span class="font-medium text-gray-900 dark:text-white">
                            {formatNumber(roiResults.value.additionalConversions)}
                          </span>
                        </div>
                      </>
                    )}
                    {currentTemplate.value.industry === 'Technology' && (
                      <>
                        <div class="flex justify-between">
                          <span class="text-sm text-gray-600 dark:text-gray-400">Improved Churn Rate:</span>
                          <span class="font-medium text-gray-900 dark:text-white">
                            {formatPercentage(roiResults.value.improvedChurnRate)}
                          </span>
                        </div>
                        <div class="flex justify-between">
                          <span class="text-sm text-gray-600 dark:text-gray-400">Improved Customer Lifetime:</span>
                          <span class="font-medium text-gray-900 dark:text-white">
                            {roiResults.value.improvedLifetime.toFixed(1)} months
                          </span>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                {/* Generate Report Button */}
                <button
                  onClick={generateReport}
                  class="w-full inline-flex items-center justify-center px-4 py-2.5 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Generate Detailed Report
                </button>
              </div>
            ) : (
              <div class="text-center py-8">
                <p class="text-red-500 dark:text-red-400">Unable to calculate ROI with current inputs</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Industry Benchmarks */}
      {selectedTemplate.value && currentTemplate.value && (
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Industry Benchmarks - {currentTemplate.value.industry}
          </h3>
          
          {industryBenchmarks.value[currentTemplate.value.industry] ? (
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatPercentage(industryBenchmarks.value[currentTemplate.value.industry].averageROIImprovement)}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Average ROI Improvement</div>
              </div>
              
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                  {industryBenchmarks.value[currentTemplate.value.industry].timeToValue}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Time to Value</div>
              </div>
              
              {industryBenchmarks.value[currentTemplate.value.industry].averageConversionRate && (
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatPercentage(industryBenchmarks.value[currentTemplate.value.industry].averageConversionRate!)}
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">Industry Avg Conversion</div>
                </div>
              )}
            </div>
          ) : (
            <p class="text-gray-500 dark:text-gray-400">No benchmark data available for this industry</p>
          )}
        </div>
      )}

      {/* Last Updated Timestamp */}
      <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
        Last updated: {new Date(lastUpdated.value).toLocaleString()}
        {isOffline && " (Demo Mode)"}
      </div>
    </div>
  );
}
