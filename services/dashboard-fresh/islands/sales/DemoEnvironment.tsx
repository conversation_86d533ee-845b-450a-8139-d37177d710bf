// Demo Environment - Fresh Islands Component
// Interactive sales demonstrations with performance benchmarks and ROI calculations

import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";

interface DemoScenario {
  id: string;
  name: string;
  description: string;
  industry: string;
  monthlyEvents: number;
  expectedROI: number;
  features: string[];
  demoUrl: string;
  setupTime: string;
  dataPoints: number;
  conversionRate: number;
  status: string;
}

interface DemoMetrics {
  totalDemos: number;
  averageEngagement: number;
  conversionRate: number;
  averageSessionTime: string;
  topPerformingScenario: string;
  recentActivity: Array<{
    timestamp: string;
    action: string;
    scenario: string;
    prospect: string;
    duration?: string;
    value?: string;
  }>;
}

interface DemoEnvironmentProps {
  initialDemoScenarios: DemoScenario[] | null;
  initialDemoMetrics: DemoMetrics | null;
  user: any;
  isOffline?: boolean;
}

export default function DemoEnvironment({
  initialDemoScenarios,
  initialDemoMetrics,
  user,
  isOffline = false
}: DemoEnvironmentProps) {
  // Signals for reactive state management
  const demoScenarios = useSignal(initialDemoScenarios || []);
  const demoMetrics = useSignal(initialDemoMetrics);
  const isLoading = useSignal(false);
  const lastUpdated = useSignal(new Date().toISOString());
  const selectedScenario = useSignal<string | null>(null);
  const showROIModal = useSignal(false);

  // Computed values for dashboard metrics
  const dashboardMetrics = useComputed(() => {
    const metrics = demoMetrics.value;
    const scenarios = demoScenarios.value;
    
    return {
      totalDemos: metrics?.totalDemos || 0,
      averageEngagement: metrics?.averageEngagement || 0,
      conversionRate: metrics?.conversionRate || 0,
      averageSessionTime: metrics?.averageSessionTime || '0:00',
      activeScenarios: scenarios.filter(s => s.status === 'active').length,
      totalDataPoints: scenarios.reduce((sum, s) => sum + s.dataPoints, 0)
    };
  });

  // Auto-refresh data every 60 seconds (if not offline)
  useEffect(() => {
    if (isOffline) return;

    const interval = setInterval(async () => {
      if (!isLoading.value) {
        await refreshData();
      }
    }, 60000);

    return () => clearInterval(interval);
  }, [isOffline]);

  const refreshData = async () => {
    if (isOffline) return;
    
    isLoading.value = true;
    try {
      const [scenariosData, metricsData] = await Promise.all([
        fetch('/api/demo/scenarios').then(r => r.json()),
        fetch('/api/demo/performance-metrics').then(r => r.json()),
      ]);

      demoScenarios.value = scenariosData.data || [];
      demoMetrics.value = metricsData.data;
      lastUpdated.value = new Date().toISOString();
    } catch (error) {
      console.error('Failed to refresh demo environment data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const startDemo = async (scenarioId: string) => {
    try {
      const response = await fetch('/api/demo/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          scenarioId,
          userId: user.id,
          timestamp: new Date().toISOString()
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        // Open demo in new window/tab
        window.open(result.demoUrl, '_blank');
        await refreshData();
      }
    } catch (error) {
      console.error('Failed to start demo:', error);
    }
  };

  const generateROIReport = async (scenario: DemoScenario) => {
    try {
      const response = await fetch('/api/demo/generate-roi-report', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          companyName: 'Prospect Company',
          industry: scenario.industry,
          monthlyEvents: scenario.monthlyEvents,
          scenario: scenario.id
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log('ROI report generated:', result);
        showROIModal.value = true;
      }
    } catch (error) {
      console.error('Failed to generate ROI report:', error);
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  return (
    <div class="demo-environment-dashboard space-y-6">
      {/* Dashboard Header with Controls */}
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex items-center gap-4">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Demo Environment Dashboard
          </h2>
          {!isOffline && (
            <button
              onClick={refreshData}
              disabled={isLoading.value}
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <svg 
                class={`w-4 h-4 mr-1.5 ${isLoading.value ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          )}
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Total Demos</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.totalDemos}
              </p>
            </div>
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Engagement</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.averageEngagement.toFixed(1)}%
              </p>
            </div>
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Conversion</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.conversionRate.toFixed(1)}%
              </p>
            </div>
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Session Time</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.averageSessionTime}
              </p>
            </div>
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Active Scenarios</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.activeScenarios}
              </p>
            </div>
            <div class="w-8 h-8 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Data Points</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {formatNumber(dashboardMetrics.value.totalDataPoints)}
              </p>
            </div>
            <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Demo Scenarios and Recent Activity Grid */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Demo Scenarios */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Demo Scenarios
            </h3>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {demoScenarios.value.length} scenarios
            </span>
          </div>

          {isLoading.value ? (
            <div class="flex items-center justify-center h-48">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : demoScenarios.value.length === 0 ? (
            <div class="text-center py-8">
              <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
              </svg>
              <p class="text-gray-500 dark:text-gray-400">No demo scenarios available</p>
            </div>
          ) : (
            <div class="space-y-4 max-h-96 overflow-y-auto">
              {demoScenarios.value.map((scenario: DemoScenario) => (
                <div key={scenario.id} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900 dark:text-white">
                      {scenario.name}
                    </h4>
                    <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(scenario.status)}`}>
                      {scenario.status}
                    </span>
                  </div>
                  
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    {scenario.description}
                  </p>
                  
                  <div class="grid grid-cols-2 gap-4 text-sm mb-3">
                    <div>
                      <span class="text-gray-600 dark:text-gray-400">Industry:</span>
                      <p class="font-medium text-gray-900 dark:text-white">{scenario.industry}</p>
                    </div>
                    <div>
                      <span class="text-gray-600 dark:text-gray-400">Expected ROI:</span>
                      <p class="font-medium text-green-600 dark:text-green-400">{scenario.expectedROI}%</p>
                    </div>
                    <div>
                      <span class="text-gray-600 dark:text-gray-400">Setup Time:</span>
                      <p class="font-medium text-gray-900 dark:text-white">{scenario.setupTime}</p>
                    </div>
                    <div>
                      <span class="text-gray-600 dark:text-gray-400">Data Points:</span>
                      <p class="font-medium text-gray-900 dark:text-white">{formatNumber(scenario.dataPoints)}</p>
                    </div>
                  </div>
                  
                  <div class="mb-3">
                    <span class="text-sm text-gray-600 dark:text-gray-400 mb-2 block">Features:</span>
                    <div class="flex flex-wrap gap-1">
                      {scenario.features.map((feature, index) => (
                        <span key={index} class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div class="flex gap-2">
                    <button 
                      onClick={() => startDemo(scenario.id)}
                      class="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                    >
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M19 10a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Start Demo
                    </button>
                    <button 
                      onClick={() => generateROIReport(scenario)}
                      class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
                    >
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                      ROI
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Recent Activity */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Recent Demo Activity
            </h3>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {demoMetrics.value?.recentActivity?.length || 0} activities
            </span>
          </div>

          {isLoading.value ? (
            <div class="flex items-center justify-center h-48">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : !demoMetrics.value?.recentActivity?.length ? (
            <div class="text-center py-8">
              <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p class="text-gray-500 dark:text-gray-400">No recent activity</p>
            </div>
          ) : (
            <div class="space-y-4 max-h-96 overflow-y-auto">
              {demoMetrics.value.recentActivity.map((activity, index) => (
                <div key={index} class="flex items-start gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div class="flex-shrink-0 w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  
                  <div class="flex-1">
                    <div class="flex items-center justify-between mb-2">
                      <h4 class="font-medium text-gray-900 dark:text-white">
                        {activity.action}
                      </h4>
                      <span class="text-xs text-gray-500 dark:text-gray-400">
                        {formatTimestamp(activity.timestamp)}
                      </span>
                    </div>
                    
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">
                      {activity.prospect} • {activity.scenario}
                    </p>
                    
                    {(activity.duration || activity.value) && (
                      <p class="text-xs text-gray-500 dark:text-gray-400">
                        {activity.duration && `Duration: ${activity.duration}`}
                        {activity.value && `Value: ${activity.value}`}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Last Updated Timestamp */}
      <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
        Last updated: {new Date(lastUpdated.value).toLocaleString()}
        {isOffline && " (Demo Mode)"}
      </div>
    </div>
  );
}
