// Sales Tools Overview - Fresh Islands Component
// Comprehensive sales enablement tools and demo environment interface

import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";

interface DemoScenario {
  id: string;
  name: string;
  description: string;
  industry: string;
  monthlyEvents: number;
  expectedROI: number;
  features: string[];
}

interface BattleCard {
  competitor: string;
  strengths: string[];
  weaknesses: string[];
  ourAdvantages: string[];
  talkingPoints: string[];
}

interface CaseStudy {
  id: string;
  companyName: string;
  industry: string;
  challenge: string;
  solution: string;
  results: {
    conversionIncrease: number;
    revenueGrowth: number;
    timeToInsight: string;
  };
}

interface SalesToolsOverviewProps {
  initialDemoScenarios: DemoScenario[] | null;
  initialBattleCards: BattleCard[] | null;
  initialCaseStudies: CaseStudy[] | null;
  user: any;
  isOffline?: boolean;
}

export default function SalesToolsOverview({
  initialDemoScenarios,
  initialBattleCards,
  initialCaseStudies,
  user,
  isOffline = false
}: SalesToolsOverviewProps) {
  // Signals for reactive state management
  const demoScenarios = useSignal(initialDemoScenarios || []);
  const battleCards = useSignal(initialBattleCards || []);
  const caseStudies = useSignal(initialCaseStudies || []);
  const isLoading = useSignal(false);
  const lastUpdated = useSignal(new Date().toISOString());
  const selectedTab = useSignal('demo');
  const selectedScenario = useSignal<string | null>(null);

  // Computed values for dashboard metrics
  const dashboardMetrics = useComputed(() => {
    return {
      totalScenarios: demoScenarios.value.length,
      totalBattleCards: battleCards.value.length,
      totalCaseStudies: caseStudies.value.length,
      averageROI: demoScenarios.value.length > 0 ? 
        demoScenarios.value.reduce((sum, scenario) => sum + scenario.expectedROI, 0) / demoScenarios.value.length : 0
    };
  });

  // Auto-refresh data every 60 seconds (if not offline)
  useEffect(() => {
    if (isOffline) return;

    const interval = setInterval(async () => {
      if (!isLoading.value) {
        await refreshData();
      }
    }, 60000);

    return () => clearInterval(interval);
  }, [isOffline]);

  const refreshData = async () => {
    if (isOffline) return;
    
    isLoading.value = true;
    try {
      const [scenariosData, battleCardsData, caseStudiesData] = await Promise.all([
        fetch('/api/demo/scenarios').then(r => r.json()),
        fetch('/api/demo/battle-cards').then(r => r.json()),
        fetch('/api/demo/case-studies').then(r => r.json()),
      ]);

      demoScenarios.value = scenariosData.data || [];
      battleCards.value = battleCardsData.data || [];
      caseStudies.value = caseStudiesData.data || [];
      lastUpdated.value = new Date().toISOString();
    } catch (error) {
      console.error('Failed to refresh sales tools data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const generateROIReport = async (scenario: DemoScenario) => {
    try {
      const response = await fetch('/api/demo/generate-roi-report', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          companyName: 'Prospect Company',
          industry: scenario.industry,
          monthlyEvents: scenario.monthlyEvents,
          scenario: scenario.id
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        // Handle ROI report generation success
        console.log('ROI report generated:', result);
      }
    } catch (error) {
      console.error('Failed to generate ROI report:', error);
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  return (
    <div class="sales-tools-dashboard space-y-6">
      {/* Dashboard Header with Controls */}
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex items-center gap-4">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Sales Tools Dashboard
          </h2>
          {!isOffline && (
            <button
              onClick={refreshData}
              disabled={isLoading.value}
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <svg 
                class={`w-4 h-4 mr-1.5 ${isLoading.value ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          )}
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Demo Scenarios</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.totalScenarios}
              </p>
            </div>
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Battle Cards</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.totalBattleCards}
              </p>
            </div>
            <div class="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Case Studies</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.totalCaseStudies}
              </p>
            </div>
            <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Average ROI</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.averageROI.toFixed(0)}%
              </p>
            </div>
            <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="-mb-px flex space-x-8">
          {[
            { id: 'demo', label: 'Demo Scenarios', icon: 'M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z' },
            { id: 'battle-cards', label: 'Battle Cards', icon: 'M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z' },
            { id: 'case-studies', label: 'Case Studies', icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' },
            { id: 'roi-calculator', label: 'ROI Calculator', icon: 'M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => selectedTab.value = tab.id}
              class={`${
                selectedTab.value === tab.id
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={tab.icon} />
              </svg>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div class="tab-content">
        {selectedTab.value === 'demo' && (
          <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {demoScenarios.value.map((scenario) => (
              <div key={scenario.id} class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {scenario.name}
                  </h3>
                  <span class="text-sm text-gray-500 dark:text-gray-400">
                    {scenario.industry}
                  </span>
                </div>
                
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  {scenario.description}
                </p>
                
                <div class="space-y-2 mb-4">
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600 dark:text-gray-400">Monthly Events:</span>
                    <span class="font-medium text-gray-900 dark:text-white">
                      {formatNumber(scenario.monthlyEvents)}
                    </span>
                  </div>
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600 dark:text-gray-400">Expected ROI:</span>
                    <span class="font-medium text-green-600 dark:text-green-400">
                      {scenario.expectedROI}%
                    </span>
                  </div>
                </div>
                
                <div class="mb-4">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Features:</h4>
                  <div class="flex flex-wrap gap-1">
                    {scenario.features.map((feature, index) => (
                      <span key={index} class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div class="flex gap-2">
                  <button 
                    onClick={() => generateROIReport(scenario)}
                    class="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                  >
                    Generate ROI
                  </button>
                  <button class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                    Demo
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {selectedTab.value === 'battle-cards' && (
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {battleCards.value.map((card, index) => (
              <div key={index} class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  vs {card.competitor}
                </h3>
                
                <div class="space-y-4">
                  <div>
                    <h4 class="text-sm font-medium text-red-600 dark:text-red-400 mb-2">Their Strengths:</h4>
                    <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                      {card.strengths.map((strength, i) => (
                        <li key={i} class="flex items-start">
                          <span class="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                          {strength}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h4 class="text-sm font-medium text-yellow-600 dark:text-yellow-400 mb-2">Their Weaknesses:</h4>
                    <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                      {card.weaknesses.map((weakness, i) => (
                        <li key={i} class="flex items-start">
                          <span class="w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                          {weakness}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h4 class="text-sm font-medium text-green-600 dark:text-green-400 mb-2">Our Advantages:</h4>
                    <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                      {card.ourAdvantages.map((advantage, i) => (
                        <li key={i} class="flex items-start">
                          <span class="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                          {advantage}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {selectedTab.value === 'case-studies' && (
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {caseStudies.value.map((study) => (
              <div key={study.id} class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {study.companyName}
                  </h3>
                  <span class="text-sm text-gray-500 dark:text-gray-400">
                    {study.industry}
                  </span>
                </div>
                
                <div class="space-y-4">
                  <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Challenge:</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{study.challenge}</p>
                  </div>
                  
                  <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Solution:</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{study.solution}</p>
                  </div>
                  
                  <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Results:</h4>
                    <div class="grid grid-cols-3 gap-4 text-center">
                      <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
                        <div class="text-lg font-bold text-green-600 dark:text-green-400">
                          +{study.results.conversionIncrease}%
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Conversion</div>
                      </div>
                      <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                        <div class="text-lg font-bold text-blue-600 dark:text-blue-400">
                          +{study.results.revenueGrowth}%
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Revenue</div>
                      </div>
                      <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
                        <div class="text-lg font-bold text-purple-600 dark:text-purple-400">
                          {study.results.timeToInsight}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Faster</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {selectedTab.value === 'roi-calculator' && (
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              ROI Calculator
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
              Calculate potential ROI for prospects based on their business metrics.
            </p>
            <div class="text-center py-8">
              <svg class="w-16 h-16 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              <p class="text-gray-500 dark:text-gray-400">ROI Calculator interface coming soon</p>
            </div>
          </div>
        )}
      </div>

      {/* Last Updated Timestamp */}
      <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
        Last updated: {new Date(lastUpdated.value).toLocaleString()}
        {isOffline && " (Demo Mode)"}
      </div>
    </div>
  );
}
