// Sales Materials - Fresh Islands Component
// Comprehensive sales enablement materials portal with case studies and templates

import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";

interface CaseStudy {
  id: string;
  companyName: string;
  industry: string;
  challenge: string;
  solution: string;
  results: {
    conversionIncrease: number;
    revenueGrowth: number;
    timeToInsight: string;
  };
  testimonial: string;
  downloadUrl: string;
  createdAt: string;
  tags: string[];
}

interface SalesCollateral {
  id: string;
  title: string;
  description: string;
  type: string;
  format: string;
  pages: number;
  downloadUrl: string;
  lastUpdated: string;
  category: string;
}

interface Template {
  id: string;
  name: string;
  description: string;
  type: string;
  category: string;
  content: string;
  lastUpdated: string;
}

interface SalesMaterialsProps {
  initialCaseStudies: CaseStudy[] | null;
  initialSalesCollateral: SalesCollateral[] | null;
  initialTemplates: Template[] | null;
  user: any;
  isOffline?: boolean;
}

export default function SalesMaterials({
  initialCaseStudies,
  initialSalesCollateral,
  initialTemplates,
  user,
  isOffline = false
}: SalesMaterialsProps) {
  // Signals for reactive state management
  const caseStudies = useSignal(initialCaseStudies || []);
  const salesCollateral = useSignal(initialSalesCollateral || []);
  const templates = useSignal(initialTemplates || []);
  const isLoading = useSignal(false);
  const lastUpdated = useSignal(new Date().toISOString());
  const selectedTab = useSignal('case-studies');
  const searchQuery = useSignal('');
  const selectedCategory = useSignal('all');

  // Computed values for dashboard metrics
  const dashboardMetrics = useComputed(() => {
    return {
      totalCaseStudies: caseStudies.value.length,
      totalCollateral: salesCollateral.value.length,
      totalTemplates: templates.value.length,
      recentlyUpdated: [...salesCollateral.value, ...templates.value]
        .filter(item => {
          const updatedDate = new Date(item.lastUpdated);
          const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          return updatedDate >= weekAgo;
        }).length
    };
  });

  // Filtered content based on search and category
  const filteredCaseStudies = useComputed(() => {
    let studies = caseStudies.value;
    
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      studies = studies.filter(study => 
        study.companyName.toLowerCase().includes(query) ||
        study.industry.toLowerCase().includes(query) ||
        study.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    if (selectedCategory.value !== 'all') {
      studies = studies.filter(study => study.industry.toLowerCase() === selectedCategory.value);
    }
    
    return studies;
  });

  const filteredCollateral = useComputed(() => {
    let collateral = salesCollateral.value;
    
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      collateral = collateral.filter(item => 
        item.title.toLowerCase().includes(query) ||
        item.description.toLowerCase().includes(query) ||
        item.category.toLowerCase().includes(query)
      );
    }
    
    if (selectedCategory.value !== 'all') {
      collateral = collateral.filter(item => item.category === selectedCategory.value);
    }
    
    return collateral;
  });

  const filteredTemplates = useComputed(() => {
    let templateList = templates.value;
    
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      templateList = templateList.filter(template => 
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.category.toLowerCase().includes(query)
      );
    }
    
    if (selectedCategory.value !== 'all') {
      templateList = templateList.filter(template => template.category === selectedCategory.value);
    }
    
    return templateList;
  });

  // Auto-refresh data every 5 minutes (if not offline)
  useEffect(() => {
    if (isOffline) return;

    const interval = setInterval(async () => {
      if (!isLoading.value) {
        await refreshData();
      }
    }, 300000);

    return () => clearInterval(interval);
  }, [isOffline]);

  const refreshData = async () => {
    if (isOffline) return;
    
    isLoading.value = true;
    try {
      const [caseStudiesData, collateralData, templatesData] = await Promise.all([
        fetch('/api/demo/case-studies').then(r => r.json()),
        fetch('/api/demo/sales-collateral').then(r => r.json()),
        fetch('/api/demo/templates').then(r => r.json()),
      ]);

      caseStudies.value = caseStudiesData.data || [];
      salesCollateral.value = collateralData.data || [];
      templates.value = templatesData.data || [];
      lastUpdated.value = new Date().toISOString();
    } catch (error) {
      console.error('Failed to refresh sales materials data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const downloadMaterial = async (downloadUrl: string, title: string) => {
    try {
      // In a real implementation, this would handle the download
      console.log(`Downloading: ${title} from ${downloadUrl}`);
      // window.open(downloadUrl, '_blank');
    } catch (error) {
      console.error('Failed to download material:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'presentation':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'worksheet':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'guide':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'document':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'call_script':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400';
      case 'email_sequence':
        return 'bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  return (
    <div class="sales-materials-dashboard space-y-6">
      {/* Dashboard Header with Controls */}
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex items-center gap-4">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Sales Materials Portal
          </h2>
          {!isOffline && (
            <button
              onClick={refreshData}
              disabled={isLoading.value}
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <svg 
                class={`w-4 h-4 mr-1.5 ${isLoading.value ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          )}
        </div>

        {/* Search and Filter */}
        <div class="flex items-center gap-4">
          <div class="relative">
            <input
              type="text"
              placeholder="Search materials..."
              value={searchQuery.value}
              onInput={(e) => searchQuery.value = (e.target as HTMLInputElement).value}
              class="w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            />
            <svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          
          <select
            value={selectedCategory.value}
            onChange={(e) => selectedCategory.value = (e.target as HTMLSelectElement).value}
            class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="all">All Categories</option>
            <option value="product">Product</option>
            <option value="financial">Financial</option>
            <option value="technical">Technical</option>
            <option value="sales_process">Sales Process</option>
            <option value="proposals">Proposals</option>
            <option value="nurturing">Nurturing</option>
          </select>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Case Studies</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.totalCaseStudies}
              </p>
            </div>
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Sales Collateral</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.totalCollateral}
              </p>
            </div>
            <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Templates</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.totalTemplates}
              </p>
            </div>
            <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Recently Updated</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.recentlyUpdated}
              </p>
            </div>
            <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="-mb-px flex space-x-8">
          {[
            { id: 'case-studies', label: 'Case Studies', icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' },
            { id: 'collateral', label: 'Sales Collateral', icon: 'M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2' },
            { id: 'templates', label: 'Templates', icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => selectedTab.value = tab.id}
              class={`${
                selectedTab.value === tab.id
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={tab.icon} />
              </svg>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div class="tab-content">
        {selectedTab.value === 'case-studies' && (
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredCaseStudies.value.map((study) => (
              <div key={study.id} class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {study.companyName}
                  </h3>
                  <span class="text-sm text-gray-500 dark:text-gray-400">
                    {study.industry}
                  </span>
                </div>
                
                <div class="space-y-4">
                  <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Challenge:</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{study.challenge}</p>
                  </div>
                  
                  <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Solution:</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{study.solution}</p>
                  </div>
                  
                  <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Results:</h4>
                    <div class="grid grid-cols-3 gap-4 text-center">
                      <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
                        <div class="text-lg font-bold text-green-600 dark:text-green-400">
                          +{study.results.conversionIncrease}%
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Conversion</div>
                      </div>
                      <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                        <div class="text-lg font-bold text-blue-600 dark:text-blue-400">
                          +{study.results.revenueGrowth}%
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Revenue</div>
                      </div>
                      <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
                        <div class="text-lg font-bold text-purple-600 dark:text-purple-400">
                          {study.results.timeToInsight}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Faster</div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                    <p class="text-sm italic text-gray-600 dark:text-gray-400">
                      "{study.testimonial}"
                    </p>
                  </div>
                  
                  <div class="flex items-center justify-between">
                    <div class="flex flex-wrap gap-1">
                      {study.tags.map((tag, index) => (
                        <span key={index} class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                          {tag}
                        </span>
                      ))}
                    </div>
                    
                    <button 
                      onClick={() => downloadMaterial(study.downloadUrl, study.companyName)}
                      class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                    >
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Download
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {selectedTab.value === 'collateral' && (
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCollateral.value.map((item) => (
              <div key={item.id} class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {item.title}
                  </h3>
                  <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(item.type)}`}>
                    {item.type}
                  </span>
                </div>
                
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  {item.description}
                </p>
                
                <div class="grid grid-cols-2 gap-4 text-sm mb-4">
                  <div>
                    <span class="text-gray-600 dark:text-gray-400">Format:</span>
                    <p class="font-medium text-gray-900 dark:text-white">{item.format}</p>
                  </div>
                  <div>
                    <span class="text-gray-600 dark:text-gray-400">Pages:</span>
                    <p class="font-medium text-gray-900 dark:text-white">{item.pages}</p>
                  </div>
                </div>
                
                <div class="flex items-center justify-between">
                  <span class="text-xs text-gray-500 dark:text-gray-400">
                    Updated: {formatDate(item.lastUpdated)}
                  </span>
                  
                  <button 
                    onClick={() => downloadMaterial(item.downloadUrl, item.title)}
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Download
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {selectedTab.value === 'templates' && (
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {filteredTemplates.value.map((template) => (
              <div key={template.id} class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {template.name}
                  </h3>
                  <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(template.type)}`}>
                    {template.type.replace('_', ' ')}
                  </span>
                </div>
                
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  {template.description}
                </p>
                
                <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 mb-4">
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    {template.content}
                  </p>
                </div>
                
                <div class="flex items-center justify-between">
                  <span class="text-xs text-gray-500 dark:text-gray-400">
                    Updated: {formatDate(template.lastUpdated)}
                  </span>
                  
                  <div class="flex gap-2">
                    <button class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      Preview
                    </button>
                    <button class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                      </svg>
                      Use Template
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Last Updated Timestamp */}
      <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
        Last updated: {new Date(lastUpdated.value).toLocaleString()}
        {isOffline && " (Demo Mode)"}
      </div>
    </div>
  );
}
