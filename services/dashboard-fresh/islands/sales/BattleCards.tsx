// Battle Cards - Fresh Islands Component
// Comprehensive competitive intelligence and positioning strategies

import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";

interface BattleCard {
  id: string;
  competitor: string;
  category: string;
  marketShare: number;
  strengths: string[];
  weaknesses: string[];
  ourAdvantages: string[];
  talkingPoints: string[];
  objectionHandling: Array<{
    objection: string;
    response: string;
  }>;
  winRate: number;
  lastUpdated: string;
}

interface CompetitiveIntel {
  marketTrends: Array<{
    trend: string;
    impact: string;
    description: string;
    ourPosition: string;
  }>;
  competitorUpdates: Array<{
    competitor: string;
    update: string;
    date: string;
    impact: string;
  }>;
}

interface BattleCardsProps {
  initialBattleCards: BattleCard[] | null;
  initialCompetitiveIntel: CompetitiveIntel | null;
  user: any;
  isOffline?: boolean;
}

export default function BattleCards({
  initialBattleCards,
  initialCompetitiveIntel,
  user,
  isOffline = false
}: BattleCardsProps) {
  // Signals for reactive state management
  const battleCards = useSignal(initialBattleCards || []);
  const competitiveIntel = useSignal(initialCompetitiveIntel);
  const isLoading = useSignal(false);
  const lastUpdated = useSignal(new Date().toISOString());
  const selectedCard = useSignal<string | null>(null);
  const searchQuery = useSignal('');
  const selectedCategory = useSignal('all');

  // Computed values for dashboard metrics
  const dashboardMetrics = useComputed(() => {
    const cards = battleCards.value;
    return {
      totalCompetitors: cards.length,
      averageWinRate: cards.length > 0 ? cards.reduce((sum, card) => sum + card.winRate, 0) / cards.length : 0,
      highWinRateCompetitors: cards.filter(card => card.winRate >= 70).length,
      recentlyUpdated: cards.filter(card => {
        const updatedDate = new Date(card.lastUpdated);
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        return updatedDate >= weekAgo;
      }).length
    };
  });

  // Filtered battle cards based on search and category
  const filteredBattleCards = useComputed(() => {
    let cards = battleCards.value;
    
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      cards = cards.filter(card => 
        card.competitor.toLowerCase().includes(query) ||
        card.category.toLowerCase().includes(query)
      );
    }
    
    if (selectedCategory.value !== 'all') {
      cards = cards.filter(card => card.category.toLowerCase().includes(selectedCategory.value));
    }
    
    return cards.sort((a, b) => b.winRate - a.winRate);
  });

  // Auto-refresh data every 5 minutes (if not offline)
  useEffect(() => {
    if (isOffline) return;

    const interval = setInterval(async () => {
      if (!isLoading.value) {
        await refreshData();
      }
    }, 300000);

    return () => clearInterval(interval);
  }, [isOffline]);

  const refreshData = async () => {
    if (isOffline) return;
    
    isLoading.value = true;
    try {
      const [cardsData, intelData] = await Promise.all([
        fetch('/api/demo/battle-cards').then(r => r.json()),
        fetch('/api/demo/competitive-intelligence').then(r => r.json()),
      ]);

      battleCards.value = cardsData.data || [];
      competitiveIntel.value = intelData.data;
      lastUpdated.value = new Date().toISOString();
    } catch (error) {
      console.error('Failed to refresh battle cards data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getWinRateColor = (winRate: number) => {
    if (winRate >= 70) return 'text-green-600 dark:text-green-400';
    if (winRate >= 50) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getMarketShareColor = (marketShare: number) => {
    if (marketShare >= 50) return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    if (marketShare >= 20) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
  };

  const getImpactColor = (impact: string) => {
    switch (impact.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  return (
    <div class="battle-cards-dashboard space-y-6">
      {/* Dashboard Header with Controls */}
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex items-center gap-4">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Competitive Battle Cards
          </h2>
          {!isOffline && (
            <button
              onClick={refreshData}
              disabled={isLoading.value}
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <svg 
                class={`w-4 h-4 mr-1.5 ${isLoading.value ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          )}
        </div>

        {/* Search and Filter */}
        <div class="flex items-center gap-4">
          <div class="relative">
            <input
              type="text"
              placeholder="Search competitors..."
              value={searchQuery.value}
              onInput={(e) => searchQuery.value = (e.target as HTMLInputElement).value}
              class="w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            />
            <svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          
          <select
            value={selectedCategory.value}
            onChange={(e) => selectedCategory.value = (e.target as HTMLSelectElement).value}
            class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="all">All Categories</option>
            <option value="web analytics">Web Analytics</option>
            <option value="enterprise analytics">Enterprise Analytics</option>
            <option value="product analytics">Product Analytics</option>
          </select>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Competitors</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.totalCompetitors}
              </p>
            </div>
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Average Win Rate</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.averageWinRate.toFixed(1)}%
              </p>
            </div>
            <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">High Win Rate</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.highWinRateCompetitors}
              </p>
            </div>
            <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Recently Updated</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.recentlyUpdated}
              </p>
            </div>
            <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Battle Cards and Competitive Intelligence Grid */}
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Battle Cards List */}
        <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Battle Cards
            </h3>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {filteredBattleCards.value.length} competitors
            </span>
          </div>

          {isLoading.value ? (
            <div class="flex items-center justify-center h-48">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : filteredBattleCards.value.length === 0 ? (
            <div class="text-center py-8">
              <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              <p class="text-gray-500 dark:text-gray-400">No battle cards found</p>
            </div>
          ) : (
            <div class="space-y-4 max-h-96 overflow-y-auto">
              {filteredBattleCards.value.map((card: BattleCard) => (
                <div key={card.id} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900 dark:text-white">
                      vs {card.competitor}
                    </h4>
                    <div class="flex items-center gap-2">
                      <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getMarketShareColor(card.marketShare)}`}>
                        {card.marketShare}% market
                      </span>
                      <span class={`text-sm font-medium ${getWinRateColor(card.winRate)}`}>
                        {card.winRate}% win rate
                      </span>
                    </div>
                  </div>
                  
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    {card.category}
                  </p>
                  
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <h5 class="font-medium text-red-600 dark:text-red-400 mb-2">Their Strengths</h5>
                      <ul class="space-y-1">
                        {card.strengths.slice(0, 2).map((strength, index) => (
                          <li key={index} class="flex items-start">
                            <span class="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                            <span class="text-gray-600 dark:text-gray-400">{strength}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h5 class="font-medium text-yellow-600 dark:text-yellow-400 mb-2">Their Weaknesses</h5>
                      <ul class="space-y-1">
                        {card.weaknesses.slice(0, 2).map((weakness, index) => (
                          <li key={index} class="flex items-start">
                            <span class="w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                            <span class="text-gray-600 dark:text-gray-400">{weakness}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h5 class="font-medium text-green-600 dark:text-green-400 mb-2">Our Advantages</h5>
                      <ul class="space-y-1">
                        {card.ourAdvantages.slice(0, 2).map((advantage, index) => (
                          <li key={index} class="flex items-start">
                            <span class="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                            <span class="text-gray-600 dark:text-gray-400">{advantage}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  
                  <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                      <span class="text-xs text-gray-500 dark:text-gray-400">
                        Updated: {formatDate(card.lastUpdated)}
                      </span>
                      <button 
                        onClick={() => selectedCard.value = selectedCard.value === card.id ? null : card.id}
                        class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300"
                      >
                        {selectedCard.value === card.id ? 'Hide Details' : 'View Details'}
                      </button>
                    </div>
                  </div>
                  
                  {selectedCard.value === card.id && (
                    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 space-y-4">
                      <div>
                        <h5 class="font-medium text-gray-900 dark:text-white mb-2">Key Talking Points</h5>
                        <ul class="space-y-1">
                          {card.talkingPoints.map((point, index) => (
                            <li key={index} class="flex items-start">
                              <span class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                              <span class="text-sm text-gray-600 dark:text-gray-400">{point}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h5 class="font-medium text-gray-900 dark:text-white mb-2">Objection Handling</h5>
                        <div class="space-y-2">
                          {card.objectionHandling.map((objection, index) => (
                            <div key={index} class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                              <p class="text-sm font-medium text-gray-900 dark:text-white mb-1">
                                "{objection.objection}"
                              </p>
                              <p class="text-sm text-gray-600 dark:text-gray-400">
                                {objection.response}
                              </p>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Competitive Intelligence */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Competitive Intelligence
          </h3>
          
          {competitiveIntel.value ? (
            <div class="space-y-6">
              {/* Market Trends */}
              <div>
                <h4 class="font-medium text-gray-900 dark:text-white mb-3">Market Trends</h4>
                <div class="space-y-3">
                  {competitiveIntel.value.marketTrends.map((trend, index) => (
                    <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                      <div class="flex items-center justify-between mb-2">
                        <h5 class="text-sm font-medium text-gray-900 dark:text-white">
                          {trend.trend}
                        </h5>
                        <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getImpactColor(trend.impact)}`}>
                          {trend.impact}
                        </span>
                      </div>
                      <p class="text-xs text-gray-600 dark:text-gray-400 mb-2">
                        {trend.description}
                      </p>
                      <p class="text-xs text-green-600 dark:text-green-400">
                        Our Position: {trend.ourPosition}
                      </p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Competitor Updates */}
              <div>
                <h4 class="font-medium text-gray-900 dark:text-white mb-3">Recent Updates</h4>
                <div class="space-y-3">
                  {competitiveIntel.value.competitorUpdates.map((update, index) => (
                    <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                      <div class="flex items-center justify-between mb-2">
                        <h5 class="text-sm font-medium text-gray-900 dark:text-white">
                          {update.competitor}
                        </h5>
                        <span class="text-xs text-gray-500 dark:text-gray-400">
                          {formatDate(update.date)}
                        </span>
                      </div>
                      <p class="text-xs text-gray-600 dark:text-gray-400 mb-2">
                        {update.update}
                      </p>
                      <p class="text-xs text-blue-600 dark:text-blue-400">
                        Impact: {update.impact}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div class="text-center py-8">
              <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p class="text-gray-500 dark:text-gray-400">No competitive intelligence available</p>
            </div>
          )}
        </div>
      </div>

      {/* Last Updated Timestamp */}
      <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
        Last updated: {new Date(lastUpdated.value).toLocaleString()}
        {isOffline && " (Demo Mode)"}
      </div>
    </div>
  );
}
