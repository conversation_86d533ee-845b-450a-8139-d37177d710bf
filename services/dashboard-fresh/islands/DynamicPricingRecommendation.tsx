import { useEffect, useState } from "preact/hooks";
import { signal } from "@preact/signals";
import { revenueAnalyticsApi, formatCurrency, formatPercentage } from "../utils/revenueAnalyticsApi.ts";

// Dynamic Pricing Recommendation Island Component
// Provides real-time pricing optimization recommendations with usage analysis

interface DynamicPricingRequest {
  customerId: string;
  planId: string;
  usageMetrics: {
    apiCalls: number;
    dataVolume: number;
    teamSize: number;
    featureUsage: Record<string, number>;
  };
  marketConditions?: {
    competitorPricing: number;
    demandLevel: 'low' | 'medium' | 'high';
    seasonality: number;
  };
  customerProfile?: {
    lifetimeValue: number;
    churnRisk: number;
    expansionProbability: number;
    paymentReliability: number;
  };
}

interface DynamicPricingRecommendation {
  recommendedPrice: number;
  originalPrice: number;
  discountPercentage: number;
  pricingStrategy: string;
  reasoning: string;
  confidenceScore: number;
  expectedImpact: {
    revenueChange: number;
    churnRiskChange: number;
    conversionProbability: number;
  };
  validUntil: string;
  conditions: string[];
}

// Global signals for real-time updates
const pricingRecommendationSignal = signal<DynamicPricingRecommendation | null>(null);
const isLoadingSignal = signal<boolean>(false);
const errorSignal = signal<string | null>(null);

export default function DynamicPricingRecommendation() {
  const [selectedCustomer, setSelectedCustomer] = useState<string>("");
  const [selectedPlan, setSelectedPlan] = useState<string>("pro");
  const [usageMetrics, setUsageMetrics] = useState({
    apiCalls: 5000,
    dataVolume: 500,
    teamSize: 5,
    featureUsage: {},
  });
  const [includeMarketConditions, setIncludeMarketConditions] = useState(false);
  const [marketConditions, setMarketConditions] = useState({
    competitorPricing: 99,
    demandLevel: 'medium' as 'low' | 'medium' | 'high',
    seasonality: 1.0,
  });

  // Generate pricing recommendation
  const generateRecommendation = async () => {
    if (!selectedCustomer) {
      errorSignal.value = "Please select a customer";
      return;
    }

    try {
      isLoadingSignal.value = true;
      errorSignal.value = null;

      const request: DynamicPricingRequest = {
        customerId: selectedCustomer,
        planId: selectedPlan,
        usageMetrics,
      };

      if (includeMarketConditions) {
        request.marketConditions = marketConditions;
      }

      const response = await revenueAnalyticsApi.generateDynamicPricing(request);
      pricingRecommendationSignal.value = response.recommendation;
    } catch (error) {
      console.error('Failed to generate pricing recommendation:', error);
      errorSignal.value = error instanceof Error ? error.message : 'Unknown error occurred';
    } finally {
      isLoadingSignal.value = false;
    }
  };

  // Auto-refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      if (selectedCustomer && pricingRecommendationSignal.value) {
        generateRecommendation();
      }
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [selectedCustomer]);

  const recommendation = pricingRecommendationSignal.value;
  const isLoading = isLoadingSignal.value;
  const error = errorSignal.value;

  return (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
          Dynamic Pricing Recommendation
        </h2>
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-500 dark:text-gray-400">
            AI-Powered Pricing Optimization
          </span>
          <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        </div>
      </div>

      {/* Configuration Form */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Customer & Plan Selection */}
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Customer ID
            </label>
            <input
              type="text"
              value={selectedCustomer}
              onChange={(e) => setSelectedCustomer(e.currentTarget.value)}
              placeholder="Enter customer ID"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Current Plan
            </label>
            <select
              value={selectedPlan}
              onChange={(e) => setSelectedPlan(e.currentTarget.value)}
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
            >
              <option value="basic">Basic ($29/month)</option>
              <option value="pro">Professional ($99/month)</option>
              <option value="enterprise">Enterprise ($299/month)</option>
            </select>
          </div>
        </div>

        {/* Usage Metrics */}
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              API Calls (30 days)
            </label>
            <input
              type="number"
              value={usageMetrics.apiCalls}
              onChange={(e) => setUsageMetrics(prev => ({ ...prev, apiCalls: parseInt(e.currentTarget.value) || 0 }))}
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Data Volume (GB)
              </label>
              <input
                type="number"
                value={usageMetrics.dataVolume}
                onChange={(e) => setUsageMetrics(prev => ({ ...prev, dataVolume: parseInt(e.currentTarget.value) || 0 }))}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Team Size
              </label>
              <input
                type="number"
                value={usageMetrics.teamSize}
                onChange={(e) => setUsageMetrics(prev => ({ ...prev, teamSize: parseInt(e.currentTarget.value) || 0 }))}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Market Conditions Toggle */}
      <div class="mb-6">
        <label class="flex items-center">
          <input
            type="checkbox"
            checked={includeMarketConditions}
            onChange={(e) => setIncludeMarketConditions(e.currentTarget.checked)}
            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
            Include market conditions analysis
          </span>
        </label>

        {includeMarketConditions && (
          <div class="mt-4 grid grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Competitor Pricing
              </label>
              <input
                type="number"
                value={marketConditions.competitorPricing}
                onChange={(e) => setMarketConditions(prev => ({ ...prev, competitorPricing: parseFloat(e.currentTarget.value) || 0 }))}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Demand Level
              </label>
              <select
                value={marketConditions.demandLevel}
                onChange={(e) => setMarketConditions(prev => ({ ...prev, demandLevel: e.currentTarget.value as 'low' | 'medium' | 'high' }))}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Seasonality Factor
              </label>
              <input
                type="number"
                step="0.1"
                value={marketConditions.seasonality}
                onChange={(e) => setMarketConditions(prev => ({ ...prev, seasonality: parseFloat(e.currentTarget.value) || 1.0 }))}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        )}
      </div>

      {/* Generate Button */}
      <div class="mb-6">
        <button
          onClick={generateRecommendation}
          disabled={isLoading || !selectedCustomer}
          class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-3 px-4 rounded-md transition-colors flex items-center justify-center gap-2"
        >
          {isLoading ? (
            <>
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Generating Recommendation...
            </>
          ) : (
            <>
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
              Generate Pricing Recommendation
            </>
          )}
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div class="mb-6 p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-red-800 dark:text-red-200 text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* Recommendation Display */}
      {recommendation && (
        <div class="space-y-6">
          {/* Pricing Summary */}
          <div class="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 rounded-lg p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="text-center">
                <div class="text-sm text-blue-600 dark:text-blue-300 font-medium">Current Price</div>
                <div class="text-2xl font-bold text-blue-900 dark:text-blue-100">
                  {formatCurrency(recommendation.originalPrice)}
                </div>
              </div>

              <div class="text-center">
                <div class="text-sm text-green-600 dark:text-green-300 font-medium">Recommended Price</div>
                <div class="text-3xl font-bold text-green-900 dark:text-green-100">
                  {formatCurrency(recommendation.recommendedPrice)}
                </div>
                <div class={`text-sm font-medium ${
                  recommendation.discountPercentage > 0 
                    ? 'text-green-600 dark:text-green-400' 
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {recommendation.discountPercentage > 0 ? '↓' : '↑'} {formatPercentage(Math.abs(recommendation.discountPercentage) / 100)}
                </div>
              </div>

              <div class="text-center">
                <div class="text-sm text-purple-600 dark:text-purple-300 font-medium">Confidence Score</div>
                <div class="text-2xl font-bold text-purple-900 dark:text-purple-100">
                  {formatPercentage(recommendation.confidenceScore)}
                </div>
                <div class="text-sm text-purple-600 dark:text-purple-400">
                  {recommendation.pricingStrategy.replace('_', ' ')}
                </div>
              </div>
            </div>
          </div>

          {/* Reasoning & Impact */}
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Reasoning</h3>
              <p class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                {recommendation.reasoning}
              </p>
              
              {recommendation.conditions.length > 0 && (
                <div class="mt-4">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Conditions</h4>
                  <ul class="space-y-1">
                    {recommendation.conditions.map((condition, index) => (
                      <li key={index} class="text-xs text-gray-600 dark:text-gray-400 flex items-center">
                        <span class="w-1 h-1 bg-blue-500 rounded-full mr-2"></span>
                        {condition}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Expected Impact</h3>
              <div class="space-y-3">
                <div class="flex justify-between items-center">
                  <span class="text-sm text-gray-600 dark:text-gray-400">Revenue Change</span>
                  <span class={`text-sm font-medium ${
                    recommendation.expectedImpact.revenueChange >= 0 
                      ? 'text-green-600 dark:text-green-400' 
                      : 'text-red-600 dark:text-red-400'
                  }`}>
                    {recommendation.expectedImpact.revenueChange >= 0 ? '+' : ''}{formatCurrency(recommendation.expectedImpact.revenueChange)}
                  </span>
                </div>

                <div class="flex justify-between items-center">
                  <span class="text-sm text-gray-600 dark:text-gray-400">Churn Risk Change</span>
                  <span class={`text-sm font-medium ${
                    recommendation.expectedImpact.churnRiskChange <= 0 
                      ? 'text-green-600 dark:text-green-400' 
                      : 'text-red-600 dark:text-red-400'
                  }`}>
                    {recommendation.expectedImpact.churnRiskChange > 0 ? '+' : ''}{formatPercentage(recommendation.expectedImpact.churnRiskChange)}
                  </span>
                </div>

                <div class="flex justify-between items-center">
                  <span class="text-sm text-gray-600 dark:text-gray-400">Conversion Probability</span>
                  <span class="text-sm font-medium text-blue-600 dark:text-blue-400">
                    {formatPercentage(recommendation.expectedImpact.conversionProbability)}
                  </span>
                </div>
              </div>

              <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  Valid until: {new Date(recommendation.validUntil).toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div class="flex gap-4">
            <button class="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
              Apply Recommendation
            </button>
            <button class="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
              Schedule Review
            </button>
            <button 
              onClick={generateRecommendation}
              class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Refresh
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
