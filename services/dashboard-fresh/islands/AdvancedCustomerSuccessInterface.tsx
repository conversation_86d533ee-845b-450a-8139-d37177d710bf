import { useEffect, useState, useRef } from "preact/hooks";
import { signal } from "@preact/signals";
import * as d3 from "d3";
import { revenueAnalyticsApi, formatCurrency, formatPercentage, getHealthScoreColor, getChurnRiskColor } from "../utils/revenueAnalyticsApi.ts";

// Advanced Customer Success Interface Island Component
// ML-powered customer success interface with churn prediction, health scoring, and expansion opportunity management

interface MLChurnPrediction {
  customerId: string;
  churnProbability: number;
  churnRisk: 'low' | 'medium' | 'high' | 'critical';
  predictedChurnDate: string;
  confidenceScore: number;
  modelVersion: string;
  featureImportance: Record<string, number>;
  keyRiskFactors: string[];
  preventionRecommendations: string[];
  revenueAtRisk: number;
  interventionPriority: number;
  similarCustomerOutcomes: {
    churned: number;
    retained: number;
    expanded: number;
  };
}

interface CustomerHealthData {
  customerId: string;
  healthScore: number;
  riskLevel: string;
  churnProbability: number;
  expansionProbability: number;
  lastActivity: string;
  engagementTrend: string;
  usageMetrics: {
    featureAdoption: number;
    apiUsage: number;
    loginFrequency: number;
    supportTickets: number;
  };
  revenueMetrics: {
    currentMrr: number;
    lifetimeValue: number;
    paymentHistory: string;
  };
  predictiveInsights: {
    expansionOpportunity: string;
    recommendedActions: string[];
  };
}

interface AdvancedExpansionOpportunity {
  customerId: string;
  opportunityType: 'upgrade' | 'add_on' | 'usage_increase' | 'feature_unlock' | 'team_expansion';
  expansionProbability: number;
  potentialRevenue: number;
  recommendedPlan?: string;
  recommendedFeatures: string[];
  timeToExpansion: number;
  confidenceScore: number;
  triggerEvents: string[];
  nextBestAction: string;
  competitiveRisk: number;
  implementationComplexity: 'low' | 'medium' | 'high';
  expectedROI: number;
  similarCustomerSuccessRate: number;
}

// Global signals for real-time updates
const churnPredictionsSignal = signal<MLChurnPrediction[]>([]);
const customerHealthSignal = signal<CustomerHealthData[]>([]);
const expansionOpportunitiesSignal = signal<AdvancedExpansionOpportunity[]>([]);
const isLoadingSignal = signal<boolean>(true);
const errorSignal = signal<string | null>(null);

export default function AdvancedCustomerSuccessInterface() {
  const [selectedView, setSelectedView] = useState<'overview' | 'churn' | 'health' | 'expansion'>('overview');
  const [riskThreshold, setRiskThreshold] = useState(0.5);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);

  // D3.js chart refs
  const healthDistributionRef = useRef<SVGSVGElement>(null);
  const churnRiskRef = useRef<SVGSVGElement>(null);
  const expansionPotentialRef = useRef<SVGSVGElement>(null);

  // Fetch customer success data
  const fetchCustomerSuccessData = async () => {
    try {
      isLoadingSignal.value = true;
      errorSignal.value = null;

      const [churnData, healthData, expansionData] = await Promise.all([
        revenueAnalyticsApi.getMLChurnPredictions(riskThreshold),
        revenueAnalyticsApi.getCustomerHealthScores(),
        revenueAnalyticsApi.getAdvancedExpansionOpportunities(),
      ]);

      churnPredictionsSignal.value = churnData.data || [];
      customerHealthSignal.value = healthData.data || [];
      expansionOpportunitiesSignal.value = expansionData.data || [];
    } catch (error) {
      console.error('Failed to fetch customer success data:', error);
      errorSignal.value = error instanceof Error ? error.message : 'Unknown error occurred';
    } finally {
      isLoadingSignal.value = false;
    }
  };

  // Auto-refresh functionality
  useEffect(() => {
    fetchCustomerSuccessData();

    let intervalId: number | undefined;
    
    if (autoRefresh) {
      intervalId = setInterval(fetchCustomerSuccessData, 60000); // 1 minute
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [riskThreshold, autoRefresh]);

  // Create health distribution chart
  useEffect(() => {
    if (!healthDistributionRef.current || customerHealthSignal.value.length === 0) return;

    const svg = d3.select(healthDistributionRef.current);
    svg.selectAll("*").remove();

    const width = 300;
    const height = 200;
    const margin = { top: 20, right: 20, bottom: 40, left: 40 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left}, ${margin.top})`);

    // Group customers by health score ranges
    const healthRanges = [
      { range: "0-20", min: 0, max: 20, color: "#EF4444" },
      { range: "21-40", min: 21, max: 40, color: "#F97316" },
      { range: "41-60", min: 41, max: 60, color: "#EAB308" },
      { range: "61-80", min: 61, max: 80, color: "#22C55E" },
      { range: "81-100", min: 81, max: 100, color: "#10B981" },
    ];

    const data = healthRanges.map(range => ({
      range: range.range,
      count: customerHealthSignal.value.filter(c => 
        c.healthScore >= range.min && c.healthScore <= range.max
      ).length,
      color: range.color,
    }));

    const xScale = d3.scaleBand()
      .domain(data.map(d => d.range))
      .range([0, chartWidth])
      .padding(0.2);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.count) || 0])
      .range([chartHeight, 0]);

    // Bars
    g.selectAll(".bar")
      .data(data)
      .enter()
      .append("rect")
      .attr("class", "bar")
      .attr("x", d => xScale(d.range) || 0)
      .attr("y", chartHeight)
      .attr("width", xScale.bandwidth())
      .attr("height", 0)
      .attr("fill", d => d.color)
      .attr("rx", 4)
      .transition()
      .duration(800)
      .attr("y", d => yScale(d.count))
      .attr("height", d => chartHeight - yScale(d.count));

    // Value labels
    g.selectAll(".label")
      .data(data)
      .enter()
      .append("text")
      .attr("class", "label")
      .attr("x", d => (xScale(d.range) || 0) + xScale.bandwidth() / 2)
      .attr("y", d => yScale(d.count) - 5)
      .attr("text-anchor", "middle")
      .attr("font-size", "12px")
      .attr("font-weight", "600")
      .attr("fill", "#374151")
      .text(d => d.count);

    // X-axis
    g.append("g")
      .attr("transform", `translate(0, ${chartHeight})`)
      .call(d3.axisBottom(xScale))
      .selectAll("text")
      .attr("font-size", "11px")
      .attr("fill", "#6B7280");

    // Y-axis
    g.append("g")
      .call(d3.axisLeft(yScale).ticks(5))
      .selectAll("text")
      .attr("font-size", "11px")
      .attr("fill", "#6B7280");

    // Remove axis lines
    g.selectAll(".domain").remove();
    g.selectAll(".tick line").attr("stroke", "#E5E7EB");

  }, [customerHealthSignal.value]);

  const churnPredictions = churnPredictionsSignal.value;
  const customerHealth = customerHealthSignal.value;
  const expansionOpportunities = expansionOpportunitiesSignal.value;
  const isLoading = isLoadingSignal.value;
  const error = errorSignal.value;

  if (isLoading && churnPredictions.length === 0) {
    return (
      <div class="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p class="text-gray-600 dark:text-gray-400">Loading Customer Success Analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div class="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div class="text-center max-w-md">
          <div class="bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded mb-4">
            <strong class="font-bold">Error: </strong>
            <span class="block sm:inline">{error}</span>
          </div>
          <button
            onClick={fetchCustomerSuccessData}
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      {/* Header */}
      <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
              Advanced Customer Success
            </h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">
              ML-powered customer success with predictive analytics and intervention recommendations
            </p>
          </div>
          
          {/* Controls */}
          <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-4">
            {/* Risk Threshold */}
            <div class="flex items-center gap-2">
              <label class="text-sm text-gray-700 dark:text-gray-300">Risk Threshold:</label>
              <select
                value={riskThreshold}
                onChange={(e) => setRiskThreshold(parseFloat(e.currentTarget.value))}
                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
              >
                <option value={0.3}>30%</option>
                <option value={0.5}>50%</option>
                <option value={0.7}>70%</option>
              </select>
            </div>

            {/* Auto-refresh */}
            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              class={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                autoRefresh
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
              }`}
            >
              Auto-refresh: {autoRefresh ? 'ON' : 'OFF'}
            </button>
            
            <button
              onClick={fetchCustomerSuccessData}
              disabled={isLoading}
              class="px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-md text-sm font-medium transition-colors"
            >
              {isLoading ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div class="mb-8">
        <nav class="flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: '📊' },
            { id: 'churn', label: 'Churn Predictions', icon: '⚠️' },
            { id: 'health', label: 'Health Scores', icon: '💚' },
            { id: 'expansion', label: 'Expansion Opportunities', icon: '📈' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setSelectedView(tab.id as any)}
              class={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                selectedView === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <span>{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Overview Tab */}
      {selectedView === 'overview' && (
        <div class="space-y-6">
          {/* Summary Cards */}
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Customers</p>
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">
                    {customerHealth.length}
                  </p>
                </div>
                <div class="text-blue-600 dark:text-blue-400">
                  <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-400">At Risk Customers</p>
                  <p class="text-2xl font-bold text-red-600 dark:text-red-400">
                    {churnPredictions.length}
                  </p>
                </div>
                <div class="text-red-600 dark:text-red-400">
                  <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                  </svg>
                </div>
              </div>
              <div class="mt-2 text-sm text-red-600 dark:text-red-400">
                {formatCurrency(churnPredictions.reduce((sum, p) => sum + p.revenueAtRisk, 0))} at risk
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Expansion Opportunities</p>
                  <p class="text-2xl font-bold text-green-600 dark:text-green-400">
                    {expansionOpportunities.length}
                  </p>
                </div>
                <div class="text-green-600 dark:text-green-400">
                  <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                  </svg>
                </div>
              </div>
              <div class="mt-2 text-sm text-green-600 dark:text-green-400">
                {formatCurrency(expansionOpportunities.reduce((sum, o) => sum + o.potentialRevenue, 0))} potential
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Health Score</p>
                  <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {Math.round(customerHealth.reduce((sum, c) => sum + c.healthScore, 0) / customerHealth.length || 0)}
                  </p>
                </div>
                <div class="text-blue-600 dark:text-blue-400">
                  <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Health Distribution Chart */}
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Customer Health Distribution</h3>
            <svg ref={healthDistributionRef} class="w-full"></svg>
          </div>
        </div>
      )}

      {/* Churn Predictions Tab */}
      {selectedView === 'churn' && (
        <div class="space-y-6">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white">ML Churn Predictions</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                Customers with churn probability ≥ {formatPercentage(riskThreshold)}
              </p>
            </div>
            
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Customer
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Churn Risk
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Revenue at Risk
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Key Risk Factors
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {churnPredictions.slice(0, 10).map((prediction) => (
                    <tr key={prediction.customerId} class="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                          {prediction.customerId}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                          Confidence: {formatPercentage(prediction.confidenceScore)}
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                          <span class={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getChurnRiskColor(prediction.churnRisk)}`}>
                            {prediction.churnRisk}
                          </span>
                          <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">
                            {formatPercentage(prediction.churnProbability)}
                          </span>
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {formatCurrency(prediction.revenueAtRisk)}
                      </td>
                      <td class="px-6 py-4">
                        <div class="text-sm text-gray-900 dark:text-white">
                          {prediction.keyRiskFactors.slice(0, 2).map((factor, index) => (
                            <div key={index} class="flex items-center mb-1">
                              <span class="w-1 h-1 bg-red-500 rounded-full mr-2"></span>
                              {factor}
                            </div>
                          ))}
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">
                          Intervene
                        </button>
                        <button class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300">
                          Details
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Health Scores Tab */}
      {selectedView === 'health' && (
        <div class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {customerHealth.slice(0, 9).map((customer) => (
              <div key={customer.customerId} class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between mb-4">
                  <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                    {customer.customerId}
                  </h4>
                  <span class={`text-2xl font-bold ${getHealthScoreColor(customer.healthScore)}`}>
                    {customer.healthScore}
                  </span>
                </div>
                
                <div class="space-y-3">
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600 dark:text-gray-400">Feature Adoption</span>
                    <span class="font-medium">{formatPercentage(customer.usageMetrics.featureAdoption)}</span>
                  </div>
                  
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600 dark:text-gray-400">API Usage</span>
                    <span class="font-medium">{customer.usageMetrics.apiUsage.toLocaleString()}</span>
                  </div>
                  
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600 dark:text-gray-400">MRR</span>
                    <span class="font-medium">{formatCurrency(customer.revenueMetrics.currentMrr)}</span>
                  </div>
                  
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600 dark:text-gray-400">Churn Risk</span>
                    <span class={`font-medium ${getChurnRiskColor(customer.riskLevel)}`}>
                      {formatPercentage(customer.churnProbability)}
                    </span>
                  </div>
                </div>

                <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Recommended Actions:</p>
                  <ul class="space-y-1">
                    {customer.predictiveInsights.recommendedActions.slice(0, 2).map((action, index) => (
                      <li key={index} class="text-xs text-gray-700 dark:text-gray-300 flex items-center">
                        <span class="w-1 h-1 bg-blue-500 rounded-full mr-2"></span>
                        {action}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Expansion Opportunities Tab */}
      {selectedView === 'expansion' && (
        <div class="space-y-6">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {expansionOpportunities.slice(0, 6).map((opportunity) => (
              <div key={opportunity.customerId} class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between mb-4">
                  <div>
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                      {opportunity.customerId}
                    </h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400 capitalize">
                      {opportunity.opportunityType.replace('_', ' ')} opportunity
                    </p>
                  </div>
                  <div class="text-right">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                      {formatCurrency(opportunity.potentialRevenue)}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                      {formatPercentage(opportunity.expansionProbability)} probability
                    </div>
                  </div>
                </div>

                <div class="space-y-3">
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600 dark:text-gray-400">Time to Expansion</span>
                    <span class="font-medium">{opportunity.timeToExpansion} days</span>
                  </div>
                  
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600 dark:text-gray-400">Expected ROI</span>
                    <span class="font-medium text-green-600 dark:text-green-400">
                      {formatPercentage(opportunity.expectedROI)}
                    </span>
                  </div>
                  
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600 dark:text-gray-400">Implementation</span>
                    <span class={`font-medium ${
                      opportunity.implementationComplexity === 'low' ? 'text-green-600 dark:text-green-400' :
                      opportunity.implementationComplexity === 'medium' ? 'text-yellow-600 dark:text-yellow-400' :
                      'text-red-600 dark:text-red-400'
                    }`}>
                      {opportunity.implementationComplexity}
                    </span>
                  </div>
                </div>

                <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Recommended Features:</p>
                  <div class="flex flex-wrap gap-1">
                    {opportunity.recommendedFeatures.slice(0, 3).map((feature, index) => (
                      <span key={index} class="px-2 py-1 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded">
                        {feature}
                      </span>
                    ))}
                  </div>
                  
                  <div class="mt-3">
                    <button class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors text-sm">
                      {opportunity.nextBestAction}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Real-time Status Indicator */}
      <div class="fixed bottom-4 right-4">
        <div class={`flex items-center gap-2 px-3 py-2 rounded-full text-sm font-medium ${
          autoRefresh && !isLoading
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            : isLoading
            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
            : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
        }`}>
          <div class={`w-2 h-2 rounded-full ${
            autoRefresh && !isLoading
              ? 'bg-green-500 animate-pulse'
              : isLoading
              ? 'bg-blue-500 animate-spin'
              : 'bg-gray-500'
          }`}></div>
          {isLoading ? 'Updating...' : autoRefresh ? 'Live' : 'Paused'}
        </div>
      </div>
    </div>
  );
}
