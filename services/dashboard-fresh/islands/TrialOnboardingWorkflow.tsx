// Trial Onboarding Workflow Island
// Self-service onboarding experience with guided tutorials and progress tracking

import { useEffect, useState, useRef } from "preact/hooks";
import { signal } from "@preact/signals";

// Onboarding state signals
const currentTrial = signal(null);
const onboardingSteps = signal([]);
const currentStep = signal(0);
const completedSteps = signal([]);
const isLoading = signal(false);

interface TrialOnboardingWorkflowProps {
  trialId: string;
  scenario: string;
}

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  type: 'tutorial' | 'demo' | 'integration' | 'validation' | 'milestone';
  estimatedTime: number;
  prerequisites: string[];
  actions: Array<{
    type: 'click' | 'navigate' | 'input' | 'validate' | 'wait';
    target: string;
    description: string;
    validation?: string;
  }>;
  successCriteria: string[];
  helpResources: Array<{
    type: 'video' | 'documentation' | 'support';
    title: string;
    url: string;
  }>;
}

export default function TrialOnboardingWorkflow({ trialId, scenario }: TrialOnboardingWorkflowProps) {
  const [showStepDetails, setShowStepDetails] = useState(false);
  const [stepProgress, setStepProgress] = useState(0);
  const [timeSpent, setTimeSpent] = useState(0);
  const [startTime, setStartTime] = useState<number | null>(null);

  useEffect(() => {
    loadTrialData();
    loadOnboardingSteps();
    
    // Start timing
    setStartTime(Date.now());
    
    // Track time spent
    const interval = setInterval(() => {
      if (startTime) {
        setTimeSpent(Math.floor((Date.now() - startTime) / 1000 / 60)); // minutes
      }
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [trialId, scenario]);

  const loadTrialData = async () => {
    try {
      const response = await fetch(`/api/enhanced-subscriptions/trial/${trialId}`);
      if (response.ok) {
        const result = await response.json();
        currentTrial.value = result.data.trialEnvironment;
        currentStep.value = result.data.trialEnvironment.onboardingProgress.currentStep;
        completedSteps.value = result.data.trialEnvironment.onboardingProgress.completedSteps;
      }
    } catch (error) {
      console.error("Failed to load trial data:", error);
    }
  };

  const loadOnboardingSteps = async () => {
    try {
      const response = await fetch(`/api/enhanced-subscriptions/trial/onboarding-steps/${scenario}`);
      if (response.ok) {
        const result = await response.json();
        onboardingSteps.value = result.data.onboardingSteps;
      }
    } catch (error) {
      console.error("Failed to load onboarding steps:", error);
    }
  };

  const completeStep = async (stepId: string) => {
    isLoading.value = true;
    try {
      const response = await fetch(`/api/enhanced-subscriptions/trial/${trialId}/onboarding`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ completedStepId: stepId }),
      });

      if (response.ok) {
        completedSteps.value = [...completedSteps.value, stepId];
        
        // Update metrics
        await updateTrialMetrics({
          timeSpent: timeSpent,
          featuresExplored: [...(currentTrial.value?.metrics?.featuresExplored || []), stepId],
          lastActivity: new Date()
        });

        // Move to next step
        const nextStepIndex = onboardingSteps.value.findIndex(step => step.id === stepId) + 1;
        if (nextStepIndex < onboardingSteps.value.length) {
          currentStep.value = nextStepIndex;
        }
      }
    } catch (error) {
      console.error("Failed to complete step:", error);
    } finally {
      isLoading.value = false;
    }
  };

  const updateTrialMetrics = async (metrics: any) => {
    try {
      await fetch(`/api/enhanced-subscriptions/trial/${trialId}/metrics`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(metrics),
      });
    } catch (error) {
      console.error("Failed to update trial metrics:", error);
    }
  };

  const getCurrentStepData = (): OnboardingStep | null => {
    return onboardingSteps.value[currentStep.value] || null;
  };

  const getProgressPercentage = (): number => {
    if (onboardingSteps.value.length === 0) return 0;
    return Math.round((completedSteps.value.length / onboardingSteps.value.length) * 100);
  };

  const isStepCompleted = (stepId: string): boolean => {
    return completedSteps.value.includes(stepId);
  };

  const canAccessStep = (step: OnboardingStep): boolean => {
    return step.prerequisites.every(prereq => completedSteps.value.includes(prereq));
  };

  const getStepIcon = (step: OnboardingStep): string => {
    if (isStepCompleted(step.id)) return "✅";
    if (!canAccessStep(step)) return "🔒";
    
    switch (step.type) {
      case 'tutorial': return "📚";
      case 'demo': return "🎯";
      case 'integration': return "🔗";
      case 'validation': return "✔️";
      case 'milestone': return "🏆";
      default: return "📋";
    }
  };

  const currentStepData = getCurrentStepData();

  return (
    <div class="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div class="flex justify-between items-center mb-6">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">Welcome to Your Trial</h2>
          <p class="text-gray-600">
            {currentTrial.value?.companyName} - {currentTrial.value?.scenario.replace('_', ' ').toUpperCase()}
          </p>
        </div>
        <div class="text-right">
          <div class="text-sm text-gray-500">Progress</div>
          <div class="text-2xl font-bold text-blue-600">{getProgressPercentage()}%</div>
          <div class="text-sm text-gray-500">{timeSpent} min spent</div>
        </div>
      </div>

      {/* Progress Bar */}
      <div class="mb-8">
        <div class="flex justify-between text-sm text-gray-600 mb-2">
          <span>Onboarding Progress</span>
          <span>{completedSteps.value.length} of {onboardingSteps.value.length} steps completed</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-3">
          <div 
            class="bg-blue-600 h-3 rounded-full transition-all duration-300"
            style={{ width: `${getProgressPercentage()}%` }}
          ></div>
        </div>
      </div>

      {/* Current Step */}
      {currentStepData && (
        <div class="bg-blue-50 rounded-lg p-6 mb-6">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center mb-2">
                <span class="text-2xl mr-3">{getStepIcon(currentStepData)}</span>
                <h3 class="text-xl font-bold text-blue-900">{currentStepData.title}</h3>
                <span class="ml-3 px-2 py-1 bg-blue-200 text-blue-800 text-xs rounded-full">
                  {currentStepData.estimatedTime} min
                </span>
              </div>
              <p class="text-blue-700 mb-4">{currentStepData.description}</p>
              
              {/* Actions */}
              <div class="space-y-3">
                {currentStepData.actions.map((action, index) => (
                  <div key={index} class="flex items-center p-3 bg-white rounded border border-blue-200">
                    <div class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                      {index + 1}
                    </div>
                    <div class="flex-1">
                      <div class="font-medium text-gray-900">{action.description}</div>
                      <div class="text-sm text-gray-600">
                        {action.type.toUpperCase()}: {action.target}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Success Criteria */}
              <div class="mt-4">
                <h4 class="font-medium text-blue-900 mb-2">Success Criteria:</h4>
                <ul class="list-disc list-inside text-blue-700 space-y-1">
                  {currentStepData.successCriteria.map((criteria, index) => (
                    <li key={index}>{criteria}</li>
                  ))}
                </ul>
              </div>
            </div>
            
            <div class="ml-6">
              <button
                onClick={() => completeStep(currentStepData.id)}
                disabled={isLoading.value}
                class="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                {isLoading.value ? 'Completing...' : 'Mark Complete'}
              </button>
            </div>
          </div>

          {/* Help Resources */}
          {currentStepData.helpResources.length > 0 && (
            <div class="mt-6 pt-4 border-t border-blue-200">
              <h4 class="font-medium text-blue-900 mb-3">Need Help?</h4>
              <div class="flex flex-wrap gap-2">
                {currentStepData.helpResources.map((resource, index) => (
                  <a
                    key={index}
                    href={resource.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    class="inline-flex items-center px-3 py-1 bg-blue-200 text-blue-800 rounded-full text-sm hover:bg-blue-300 transition-colors"
                  >
                    {resource.type === 'video' && '🎥'}
                    {resource.type === 'documentation' && '📖'}
                    {resource.type === 'support' && '💬'}
                    <span class="ml-1">{resource.title}</span>
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* All Steps Overview */}
      <div class="space-y-4">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-bold text-gray-900">All Onboarding Steps</h3>
          <button
            onClick={() => setShowStepDetails(!showStepDetails)}
            class="text-blue-600 hover:text-blue-700 font-medium"
          >
            {showStepDetails ? 'Hide Details' : 'Show Details'}
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {onboardingSteps.value.map((step, index) => (
            <div
              key={step.id}
              class={`p-4 rounded-lg border-2 transition-all ${
                isStepCompleted(step.id)
                  ? 'border-green-200 bg-green-50'
                  : canAccessStep(step)
                  ? 'border-blue-200 bg-blue-50'
                  : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div class="flex items-center justify-between mb-2">
                <span class="text-2xl">{getStepIcon(step)}</span>
                <span class="text-sm text-gray-500">{step.estimatedTime} min</span>
              </div>
              
              <h4 class="font-medium text-gray-900 mb-1">{step.title}</h4>
              <p class="text-sm text-gray-600 mb-3">{step.description}</p>
              
              <div class="flex items-center justify-between">
                <span class={`px-2 py-1 rounded-full text-xs font-medium ${
                  step.type === 'milestone' ? 'bg-purple-100 text-purple-800' :
                  step.type === 'demo' ? 'bg-blue-100 text-blue-800' :
                  step.type === 'integration' ? 'bg-green-100 text-green-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {step.type}
                </span>
                
                {isStepCompleted(step.id) && (
                  <span class="text-green-600 font-medium text-sm">Completed</span>
                )}
                {!canAccessStep(step) && (
                  <span class="text-gray-400 font-medium text-sm">Locked</span>
                )}
              </div>

              {showStepDetails && (
                <div class="mt-3 pt-3 border-t border-gray-200">
                  <div class="text-sm">
                    <div class="font-medium text-gray-700 mb-1">Actions:</div>
                    <ul class="list-disc list-inside text-gray-600 space-y-1">
                      {step.actions.slice(0, 2).map((action, actionIndex) => (
                        <li key={actionIndex}>{action.description}</li>
                      ))}
                      {step.actions.length > 2 && (
                        <li class="text-gray-500">+{step.actions.length - 2} more...</li>
                      )}
                    </ul>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Trial Information */}
      {currentTrial.value && (
        <div class="mt-8 p-4 bg-gray-50 rounded-lg">
          <h4 class="font-medium text-gray-900 mb-3">Trial Information</h4>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div class="text-gray-500">Trial ID</div>
              <div class="font-medium">{currentTrial.value.id}</div>
            </div>
            <div>
              <div class="text-gray-500">Scenario</div>
              <div class="font-medium">{currentTrial.value.scenario.replace('_', ' ')}</div>
            </div>
            <div>
              <div class="text-gray-500">Expires</div>
              <div class="font-medium">
                {new Date(currentTrial.value.expiresAt).toLocaleDateString()}
              </div>
            </div>
            <div>
              <div class="text-gray-500">Status</div>
              <div class={`font-medium ${
                currentTrial.value.status === 'active' ? 'text-green-600' : 'text-gray-600'
              }`}>
                {currentTrial.value.status.toUpperCase()}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
