// Customer Success Dashboard Island
// Comprehensive health scoring and success metrics tracking for trial environments

import { useEffect, useState, useRef } from "preact/hooks";
import { signal } from "@preact/signals";
import * as d3 from "https://cdn.skypack.dev/d3@7";

// Customer success state signals
const healthScore = signal(null);
const successMilestones = signal([]);
const journeyEvents = signal([]);
const activeTrials = signal([]);

interface CustomerSuccessDashboardProps {
  trialId?: string;
  mode?: 'single' | 'overview';
}

export default function CustomerSuccessDashboard({ trialId, mode = 'single' }: CustomerSuccessDashboardProps) {
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 seconds
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');
  const intervalRef = useRef<number>();

  // D3 chart refs
  const healthScoreChartRef = useRef<SVGSVGElement>(null);
  const milestonesChartRef = useRef<SVGSVGElement>(null);
  const riskDistributionRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (mode === 'single' && trialId) {
      loadHealthScore();
    } else if (mode === 'overview') {
      loadActiveTrialsOverview();
    }

    // Set up auto-refresh
    intervalRef.current = setInterval(() => {
      if (mode === 'single' && trialId) {
        loadHealthScore();
      } else if (mode === 'overview') {
        loadActiveTrialsOverview();
      }
    }, refreshInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [trialId, mode, refreshInterval]);

  useEffect(() => {
    if (mode === 'single' && healthScore.value) {
      renderHealthScoreChart();
      renderMilestonesChart();
    } else if (mode === 'overview' && activeTrials.value.length > 0) {
      renderRiskDistributionChart();
    }
  }, [healthScore.value, successMilestones.value, activeTrials.value, mode]);

  const loadHealthScore = async () => {
    if (!trialId) return;
    
    try {
      const response = await fetch(`/api/enhanced-subscriptions/trial/${trialId}/health-score`);
      if (response.ok) {
        const result = await response.json();
        healthScore.value = result.data.healthScore;
        successMilestones.value = result.data.successMilestones;
        journeyEvents.value = result.data.journeyEvents;
      }
    } catch (error) {
      console.error("Failed to load health score:", error);
    }
  };

  const loadActiveTrialsOverview = async () => {
    try {
      const response = await fetch('/api/enhanced-subscriptions/trial/active-overview');
      if (response.ok) {
        const result = await response.json();
        activeTrials.value = result.data.activeTrials;
      }
    } catch (error) {
      console.error("Failed to load active trials overview:", error);
    }
  };

  const renderHealthScoreChart = () => {
    if (!healthScoreChartRef.current || !healthScore.value) return;

    const svg = d3.select(healthScoreChartRef.current);
    svg.selectAll("*").remove();

    const width = 300;
    const height = 300;
    const radius = Math.min(width, height) / 2 - 20;

    const g = svg.append("g")
      .attr("transform", `translate(${width / 2},${height / 2})`);

    // Create arc for health score
    const arc = d3.arc()
      .innerRadius(radius * 0.6)
      .outerRadius(radius)
      .startAngle(0)
      .endAngle((healthScore.value.overallScore / 100) * 2 * Math.PI);

    // Background arc
    g.append("path")
      .attr("d", d3.arc()
        .innerRadius(radius * 0.6)
        .outerRadius(radius)
        .startAngle(0)
        .endAngle(2 * Math.PI)
      )
      .attr("fill", "#e5e7eb");

    // Health score arc
    g.append("path")
      .attr("d", arc)
      .attr("fill", getHealthScoreColor(healthScore.value.overallScore));

    // Center text
    g.append("text")
      .attr("text-anchor", "middle")
      .attr("dy", "-0.5em")
      .style("font-size", "36px")
      .style("font-weight", "bold")
      .style("fill", getHealthScoreColor(healthScore.value.overallScore))
      .text(healthScore.value.overallScore);

    g.append("text")
      .attr("text-anchor", "middle")
      .attr("dy", "1em")
      .style("font-size", "14px")
      .style("fill", "#6b7280")
      .text("Health Score");

    g.append("text")
      .attr("text-anchor", "middle")
      .attr("dy", "2.5em")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .style("fill", getRiskLevelColor(healthScore.value.riskLevel))
      .text(healthScore.value.riskLevel.toUpperCase());
  };

  const renderMilestonesChart = () => {
    if (!milestonesChartRef.current || !successMilestones.value) return;

    const svg = d3.select(milestonesChartRef.current);
    svg.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 120 };
    const width = 500 - margin.left - margin.right;
    const height = 300 - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const milestones = successMilestones.value.slice(0, 6); // Show top 6 milestones

    const xScale = d3.scaleLinear()
      .domain([0, 100])
      .range([0, width]);

    const yScale = d3.scaleBand()
      .domain(milestones.map(m => m.name))
      .range([0, height])
      .padding(0.1);

    // Add bars
    g.selectAll(".bar")
      .data(milestones)
      .enter().append("rect")
      .attr("class", "bar")
      .attr("x", 0)
      .attr("y", d => yScale(d.name))
      .attr("width", d => xScale(d.achievedAt ? 100 : 0))
      .attr("height", yScale.bandwidth())
      .attr("fill", d => d.achievedAt ? "#10b981" : "#e5e7eb")
      .attr("rx", 4);

    // Add milestone icons
    g.selectAll(".icon")
      .data(milestones)
      .enter().append("text")
      .attr("class", "icon")
      .attr("x", -15)
      .attr("y", d => yScale(d.name) + yScale.bandwidth() / 2)
      .attr("dy", "0.35em")
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .text(d => d.achievedAt ? "✅" : "⏳");

    // Add labels
    g.selectAll(".label")
      .data(milestones)
      .enter().append("text")
      .attr("class", "label")
      .attr("x", -25)
      .attr("y", d => yScale(d.name) + yScale.bandwidth() / 2)
      .attr("dy", "0.35em")
      .attr("text-anchor", "end")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .text(d => d.name);

    // Add achievement dates
    g.selectAll(".date")
      .data(milestones.filter(m => m.achievedAt))
      .enter().append("text")
      .attr("class", "date")
      .attr("x", width + 5)
      .attr("y", d => yScale(d.name) + yScale.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("font-size", "10px")
      .style("fill", "#6b7280")
      .text(d => new Date(d.achievedAt).toLocaleDateString());
  };

  const renderRiskDistributionChart = () => {
    if (!riskDistributionRef.current || !activeTrials.value) return;

    const svg = d3.select(riskDistributionRef.current);
    svg.selectAll("*").remove();

    const width = 400;
    const height = 300;
    const radius = Math.min(width, height) / 2 - 20;

    const g = svg.append("g")
      .attr("transform", `translate(${width / 2},${height / 2})`);

    // Count trials by risk level
    const riskCounts = activeTrials.value.reduce((acc, trial) => {
      const riskLevel = trial.healthScore?.riskLevel || 'unknown';
      acc[riskLevel] = (acc[riskLevel] || 0) + 1;
      return acc;
    }, {});

    const data = Object.entries(riskCounts).map(([risk, count]) => ({
      risk,
      count,
      color: getRiskLevelColor(risk)
    }));

    const pie = d3.pie()
      .value(d => d.count)
      .sort(null);

    const arc = d3.arc()
      .innerRadius(0)
      .outerRadius(radius);

    const arcs = g.selectAll(".arc")
      .data(pie(data))
      .enter().append("g")
      .attr("class", "arc");

    arcs.append("path")
      .attr("d", arc)
      .attr("fill", d => d.data.color);

    arcs.append("text")
      .attr("transform", d => `translate(${arc.centroid(d)})`)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .style("fill", "white")
      .text(d => d.data.count);

    // Add legend
    const legend = svg.append("g")
      .attr("transform", `translate(${width - 100}, 20)`);

    data.forEach((d, i) => {
      const legendRow = legend.append("g")
        .attr("transform", `translate(0, ${i * 20})`);

      legendRow.append("rect")
        .attr("width", 12)
        .attr("height", 12)
        .attr("fill", d.color);

      legendRow.append("text")
        .attr("x", 16)
        .attr("y", 6)
        .attr("dy", "0.35em")
        .style("font-size", "12px")
        .text(`${d.risk} (${d.count})`);
    });
  };

  const getHealthScoreColor = (score: number): string => {
    if (score >= 80) return "#10b981"; // green
    if (score >= 60) return "#f59e0b"; // yellow
    if (score >= 40) return "#f97316"; // orange
    return "#ef4444"; // red
  };

  const getRiskLevelColor = (riskLevel: string): string => {
    switch (riskLevel) {
      case 'low': return "#10b981";
      case 'medium': return "#f59e0b";
      case 'high': return "#f97316";
      case 'critical': return "#ef4444";
      default: return "#6b7280";
    }
  };

  const formatTimeAgo = (date: string): string => {
    const now = new Date();
    const eventDate = new Date(date);
    const diffMs = now.getTime() - eventDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  return (
    <div class="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900">
          {mode === 'single' ? 'Customer Success Metrics' : 'Active Trials Overview'}
        </h2>
        <div class="flex items-center space-x-4">
          <select
            value={refreshInterval}
            onChange={(e) => setRefreshInterval(parseInt((e.target as HTMLSelectElement).value))}
            class="px-3 py-1 border border-gray-300 rounded-md text-sm"
          >
            <option value={10000}>10s refresh</option>
            <option value={30000}>30s refresh</option>
            <option value={60000}>1m refresh</option>
            <option value={300000}>5m refresh</option>
          </select>
          <div class="flex items-center text-sm text-gray-500">
            <div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
            Live Updates
          </div>
        </div>
      </div>

      {/* Single Trial Mode */}
      {mode === 'single' && healthScore.value && (
        <div class="space-y-6">
          {/* Health Score Overview */}
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-1">
              <h3 class="text-lg font-semibold mb-4">Overall Health Score</h3>
              <svg ref={healthScoreChartRef} width="300" height="300"></svg>
            </div>
            
            <div class="lg:col-span-2">
              <h3 class="text-lg font-semibold mb-4">Score Components</h3>
              <div class="grid grid-cols-2 gap-4">
                {Object.entries(healthScore.value.scoreComponents).map(([component, score]) => (
                  <div key={component} class="bg-gray-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600 mb-1">
                      {component.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </div>
                    <div class="flex items-center">
                      <div class="text-2xl font-bold text-gray-900 mr-2">{score}</div>
                      <div class="flex-1 bg-gray-200 rounded-full h-2">
                        <div 
                          class="h-2 rounded-full transition-all duration-300"
                          style={{ 
                            width: `${score}%`,
                            backgroundColor: getHealthScoreColor(score)
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Success Milestones */}
          <div>
            <h3 class="text-lg font-semibold mb-4">Success Milestones</h3>
            <svg ref={milestonesChartRef} width="500" height="300"></svg>
          </div>

          {/* Intervention Triggers */}
          {healthScore.value.interventionTriggers.length > 0 && (
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 class="font-semibold text-yellow-800 mb-2">⚠️ Intervention Triggers</h4>
              <ul class="list-disc list-inside text-yellow-700 space-y-1">
                {healthScore.value.interventionTriggers.map((trigger, index) => (
                  <li key={index}>{trigger}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Recommendations */}
          {healthScore.value.recommendations.length > 0 && (
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 class="font-semibold text-blue-800 mb-2">💡 Recommendations</h4>
              <ul class="list-disc list-inside text-blue-700 space-y-1">
                {healthScore.value.recommendations.map((recommendation, index) => (
                  <li key={index}>{recommendation}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Recent Journey Events */}
          <div>
            <h3 class="text-lg font-semibold mb-4">Recent Activity</h3>
            <div class="space-y-3">
              {journeyEvents.value.slice(0, 5).map((event, index) => (
                <div key={index} class="flex items-center p-3 bg-gray-50 rounded-lg">
                  <div class={`w-3 h-3 rounded-full mr-3 ${
                    event.impact === 'positive' ? 'bg-green-500' :
                    event.impact === 'negative' ? 'bg-red-500' : 'bg-gray-400'
                  }`}></div>
                  <div class="flex-1">
                    <div class="font-medium text-gray-900">{event.eventType.replace(/_/g, ' ')}</div>
                    <div class="text-sm text-gray-600">{formatTimeAgo(event.timestamp)}</div>
                  </div>
                  {event.scoreChange && (
                    <div class={`text-sm font-medium ${
                      event.scoreChange > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {event.scoreChange > 0 ? '+' : ''}{event.scoreChange}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Overview Mode */}
      {mode === 'overview' && activeTrials.value.length > 0 && (
        <div class="space-y-6">
          {/* Summary Stats */}
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-blue-50 p-4 rounded-lg text-center">
              <div class="text-2xl font-bold text-blue-600">{activeTrials.value.length}</div>
              <div class="text-sm text-blue-800">Active Trials</div>
            </div>
            <div class="bg-green-50 p-4 rounded-lg text-center">
              <div class="text-2xl font-bold text-green-600">
                {activeTrials.value.filter(t => t.healthScore?.riskLevel === 'low').length}
              </div>
              <div class="text-sm text-green-800">Low Risk</div>
            </div>
            <div class="bg-yellow-50 p-4 rounded-lg text-center">
              <div class="text-2xl font-bold text-yellow-600">
                {activeTrials.value.filter(t => t.healthScore?.riskLevel === 'medium').length}
              </div>
              <div class="text-sm text-yellow-800">Medium Risk</div>
            </div>
            <div class="bg-red-50 p-4 rounded-lg text-center">
              <div class="text-2xl font-bold text-red-600">
                {activeTrials.value.filter(t => ['high', 'critical'].includes(t.healthScore?.riskLevel)).length}
              </div>
              <div class="text-sm text-red-800">High Risk</div>
            </div>
          </div>

          {/* Risk Distribution */}
          <div>
            <h3 class="text-lg font-semibold mb-4">Risk Distribution</h3>
            <svg ref={riskDistributionRef} width="400" height="300"></svg>
          </div>

          {/* Active Trials List */}
          <div>
            <h3 class="text-lg font-semibold mb-4">Active Trials</h3>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Industry</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Health Score</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Risk Level</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Progress</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expires</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  {activeTrials.value.slice(0, 10).map((trial, index) => (
                    <tr key={index} class="hover:bg-gray-50">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">{trial.companyName}</div>
                        <div class="text-sm text-gray-500">{trial.trialId}</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {trial.industry}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">
                          {trial.healthScore?.overallScore || 'N/A'}
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          trial.healthScore?.riskLevel === 'low' ? 'bg-green-100 text-green-800' :
                          trial.healthScore?.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          trial.healthScore?.riskLevel === 'high' ? 'bg-orange-100 text-orange-800' :
                          trial.healthScore?.riskLevel === 'critical' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {trial.healthScore?.riskLevel || 'unknown'}
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                          {trial.onboardingProgress.progressPercentage}%
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                          <div 
                            class="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${trial.onboardingProgress.progressPercentage}%` }}
                          ></div>
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(trial.expiresAt).toLocaleDateString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
