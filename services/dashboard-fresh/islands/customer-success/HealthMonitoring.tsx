// Health Monitoring - Fresh Islands Component
// Real-time customer health monitoring, risk assessment, and intervention tracking

import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";

interface HealthAlert {
  id: string;
  type: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  message: string;
  subscriptionId: string;
  timestamp: string;
  customerId: string;
  companyName: string;
}

interface HealthScore {
  subscriptionId: string;
  companyName: string;
  overallScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  factors: Array<{
    name: string;
    score: number;
    weight: number;
    impact: string;
  }>;
  interventionTriggers: string[];
  lastUpdated: string;
}

interface HealthMonitoringProps {
  initialHealthMonitoring: any;
  initialSubscriptionHealth: any;
  user: any;
  isOffline?: boolean;
}

export default function HealthMonitoring({
  initialHealthMonitoring,
  initialSubscriptionHealth,
  user,
  isOffline = false
}: HealthMonitoringProps) {
  // Signals for reactive state management
  const healthMonitoring = useSignal(initialHealthMonitoring);
  const subscriptionHealth = useSignal(initialSubscriptionHealth);
  const isLoading = useSignal(false);
  const lastUpdated = useSignal(new Date().toISOString());
  const selectedCustomer = useSignal<string | null>(null);
  const filterSeverity = useSignal<string>('all');

  // Computed values for dashboard metrics
  const dashboardMetrics = useComputed(() => {
    const health = subscriptionHealth.value;
    const alerts = healthMonitoring.value;
    
    return {
      averageHealthScore: health?.summary?.averageScore || 0,
      totalCustomers: health?.summary?.totalCustomers || 0,
      highRiskCustomers: health?.summary?.highRisk || 0,
      activeAlerts: alerts?.summary?.total || 0,
      criticalAlerts: alerts?.summary?.critical || 0,
      interventionsNeeded: health?.healthScores?.filter((score: HealthScore) => 
        score.interventionTriggers.length > 0).length || 0
    };
  });

  // Filtered alerts based on severity
  const filteredAlerts = useComputed(() => {
    const alerts = healthMonitoring.value?.alerts || [];
    if (filterSeverity.value === 'all') return alerts;
    return alerts.filter((alert: HealthAlert) => alert.severity === filterSeverity.value);
  });

  // Auto-refresh data every 30 seconds (if not offline)
  useEffect(() => {
    if (isOffline) return;

    const interval = setInterval(async () => {
      if (!isLoading.value) {
        await refreshData();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [isOffline]);

  const refreshData = async () => {
    if (isOffline) return;
    
    isLoading.value = true;
    try {
      const [healthData, subscriptionData] = await Promise.all([
        fetch('/api/billing/health-monitoring').then(r => r.json()),
        fetch('/api/billing/subscription-health-overview').then(r => r.json()),
      ]);

      healthMonitoring.value = healthData.data;
      subscriptionHealth.value = subscriptionData.data;
      lastUpdated.value = new Date().toISOString();
    } catch (error) {
      console.error('Failed to refresh health monitoring data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const dismissAlert = async (alertId: string) => {
    try {
      const response = await fetch(`/api/billing/health-monitoring/alerts/${alertId}/dismiss`, {
        method: 'POST'
      });
      
      if (response.ok) {
        await refreshData();
      }
    } catch (error) {
      console.error('Failed to dismiss alert:', error);
    }
  };

  const triggerIntervention = async (subscriptionId: string, interventionType: string) => {
    try {
      const response = await fetch('/api/billing/customer-success/intervention', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          subscriptionId,
          interventionType,
          triggeredBy: user.id
        })
      });
      
      if (response.ok) {
        console.log('Intervention triggered successfully');
        await refreshData();
      }
    } catch (error) {
      console.error('Failed to trigger intervention:', error);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800';
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800';
    }
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
        return 'text-red-600 dark:text-red-400';
      case 'high':
        return 'text-orange-600 dark:text-orange-400';
      case 'medium':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'low':
        return 'text-green-600 dark:text-green-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400';
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  return (
    <div class="health-monitoring-dashboard space-y-6">
      {/* Dashboard Header with Controls */}
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex items-center gap-4">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Health Monitoring Dashboard
          </h2>
          {!isOffline && (
            <button
              onClick={refreshData}
              disabled={isLoading.value}
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <svg 
                class={`w-4 h-4 mr-1.5 ${isLoading.value ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          )}
        </div>

        {/* Alert Filter */}
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-600 dark:text-gray-400">Filter alerts:</span>
          <select
            value={filterSeverity.value}
            onChange={(e) => filterSeverity.value = (e.target as HTMLSelectElement).value}
            class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1.5 text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="all">All Severities</option>
            <option value="critical">Critical</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Avg Health Score</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.averageHealthScore.toFixed(1)}
              </p>
            </div>
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Total Customers</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.totalCustomers}
              </p>
            </div>
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">High Risk</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.highRiskCustomers}
              </p>
            </div>
            <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Active Alerts</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.activeAlerts}
              </p>
            </div>
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.343 12.344l1.414 1.414L9 10.414V9a3 3 0 016 0v1.414l3.243 3.243 1.414-1.414L18 10.586V9a5 5 0 00-10 0v1.586l-3.657 2.758z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Critical Alerts</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.criticalAlerts}
              </p>
            </div>
            <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Interventions</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.interventionsNeeded}
              </p>
            </div>
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Health Alerts and Customer Health Scores Grid */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Health Alerts */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Health Alerts
            </h3>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {filteredAlerts.value.length} alerts
            </span>
          </div>

          {isLoading.value ? (
            <div class="flex items-center justify-center h-48">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : filteredAlerts.value.length === 0 ? (
            <div class="text-center py-8">
              <svg class="w-12 h-12 text-green-400 dark:text-green-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p class="text-gray-500 dark:text-gray-400">No alerts for selected filter</p>
            </div>
          ) : (
            <div class="space-y-4 max-h-96 overflow-y-auto">
              {filteredAlerts.value.map((alert: HealthAlert) => (
                <div key={alert.id} class={`border rounded-lg p-4 ${getSeverityColor(alert.severity)}`}>
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="flex items-center gap-2 mb-2">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {alert.companyName}
                        </h4>
                        <span class="text-xs text-gray-500 dark:text-gray-400">
                          {alert.type.replace('_', ' ')}
                        </span>
                      </div>
                      
                      <p class="text-sm text-gray-700 dark:text-gray-300 mb-2">
                        {alert.message}
                      </p>
                      
                      <p class="text-xs text-gray-500 dark:text-gray-400">
                        {formatTimestamp(alert.timestamp)}
                      </p>
                    </div>

                    <div class="flex items-center gap-1 ml-4">
                      <button
                        onClick={() => triggerIntervention(alert.subscriptionId, alert.type)}
                        class="p-1.5 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20"
                        title="Trigger Intervention"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </button>
                      
                      <button
                        onClick={() => dismissAlert(alert.id)}
                        class="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600"
                        title="Dismiss Alert"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Customer Health Scores */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Customer Health Scores
            </h3>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {subscriptionHealth.value?.healthScores?.length || 0} customers
            </span>
          </div>

          {isLoading.value ? (
            <div class="flex items-center justify-center h-48">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : !subscriptionHealth.value?.healthScores?.length ? (
            <div class="text-center py-8">
              <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <p class="text-gray-500 dark:text-gray-400">No customer health data available</p>
            </div>
          ) : (
            <div class="space-y-4 max-h-96 overflow-y-auto">
              {subscriptionHealth.value.healthScores.map((score: HealthScore) => (
                <div key={score.subscriptionId} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900 dark:text-white">
                      {score.companyName}
                    </h4>
                    <div class="flex items-center gap-2">
                      <span class={`text-lg font-bold ${getHealthScoreColor(score.overallScore)}`}>
                        {score.overallScore}
                      </span>
                      <span class={`text-sm font-medium ${getRiskLevelColor(score.riskLevel)}`}>
                        {score.riskLevel}
                      </span>
                    </div>
                  </div>
                  
                  <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-3">
                    <div 
                      class={`h-2 rounded-full transition-all duration-300 ${
                        score.overallScore >= 80 ? 'bg-green-500' :
                        score.overallScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${score.overallScore}%` }}
                    ></div>
                  </div>
                  
                  {score.interventionTriggers.length > 0 && (
                    <div class="flex items-center gap-2 mb-2">
                      <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      <span class="text-sm text-orange-600 dark:text-orange-400 font-medium">
                        Intervention needed: {score.interventionTriggers.join(', ')}
                      </span>
                    </div>
                  )}
                  
                  <div class="grid grid-cols-2 gap-2 text-xs">
                    {score.factors.slice(0, 4).map((factor, index) => (
                      <div key={index} class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">{factor.name}:</span>
                        <span class={`font-medium ${getHealthScoreColor(factor.score)}`}>
                          {factor.score}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Last Updated Timestamp */}
      <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
        Last updated: {new Date(lastUpdated.value).toLocaleString()}
        {isOffline && " (Demo Mode)"}
      </div>
    </div>
  );
}
