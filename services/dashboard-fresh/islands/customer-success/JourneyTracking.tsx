// Journey Tracking - Fresh Islands Component
// Customer journey events, milestones, and success progression tracking

import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";

interface JourneyEvent {
  id: string;
  customerId: string;
  companyName: string;
  eventType: string;
  eventName: string;
  timestamp: string;
  metadata: Record<string, any>;
}

interface Milestone {
  id: string;
  name: string;
  description: string;
  category: 'onboarding' | 'activation' | 'value_realization' | 'expansion';
  targetTimeframe: string;
  completionRate: number;
  averageTime: string;
  impactOnSuccess: 'low' | 'medium' | 'high' | 'critical';
}

interface JourneyTrackingProps {
  initialJourneyEvents: JourneyEvent[] | null;
  initialMilestones: Milestone[] | null;
  user: any;
  isOffline?: boolean;
}

export default function JourneyTracking({
  initialJourneyEvents,
  initialMilestones,
  user,
  isOffline = false
}: JourneyTrackingProps) {
  // Signals for reactive state management
  const journeyEvents = useSignal(initialJourneyEvents || []);
  const milestones = useSignal(initialMilestones || []);
  const isLoading = useSignal(false);
  const lastUpdated = useSignal(new Date().toISOString());
  const selectedCustomer = useSignal<string | null>(null);
  const selectedTimeframe = useSignal('30d');
  const selectedCategory = useSignal<string>('all');

  // Computed values for dashboard metrics
  const dashboardMetrics = useComputed(() => {
    const events = journeyEvents.value;
    const milestoneData = milestones.value;
    
    const uniqueCustomers = new Set(events.map(event => event.customerId)).size;
    const totalEvents = events.length;
    const avgCompletionRate = milestoneData.length > 0 ? 
      milestoneData.reduce((sum, milestone) => sum + milestone.completionRate, 0) / milestoneData.length : 0;
    
    const recentEvents = events.filter(event => {
      const eventDate = new Date(event.timestamp);
      const cutoffDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      return eventDate >= cutoffDate;
    }).length;

    return {
      uniqueCustomers,
      totalEvents,
      avgCompletionRate,
      recentEvents,
      criticalMilestones: milestoneData.filter(m => m.impactOnSuccess === 'critical').length,
      conversionEvents: events.filter(e => e.eventType === 'subscription_converted').length
    };
  });

  // Filtered events based on customer and timeframe
  const filteredEvents = useComputed(() => {
    let events = journeyEvents.value;
    
    if (selectedCustomer.value) {
      events = events.filter(event => event.customerId === selectedCustomer.value);
    }
    
    if (selectedTimeframe.value !== 'all') {
      const days = selectedTimeframe.value === '7d' ? 7 : selectedTimeframe.value === '30d' ? 30 : 90;
      const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      events = events.filter(event => new Date(event.timestamp) >= cutoffDate);
    }
    
    return events.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  });

  // Filtered milestones based on category
  const filteredMilestones = useComputed(() => {
    if (selectedCategory.value === 'all') return milestones.value;
    return milestones.value.filter(milestone => milestone.category === selectedCategory.value);
  });

  // Auto-refresh data every 60 seconds (if not offline)
  useEffect(() => {
    if (isOffline) return;

    const interval = setInterval(async () => {
      if (!isLoading.value) {
        await refreshData();
      }
    }, 60000);

    return () => clearInterval(interval);
  }, [isOffline]);

  const refreshData = async () => {
    if (isOffline) return;
    
    isLoading.value = true;
    try {
      const [eventsData, milestonesData] = await Promise.all([
        fetch('/api/billing/customer-journey/events').then(r => r.json()),
        fetch('/api/billing/customer-journey/milestones').then(r => r.json()),
      ]);

      journeyEvents.value = eventsData.data || [];
      milestones.value = milestonesData.data || [];
      lastUpdated.value = new Date().toISOString();
    } catch (error) {
      console.error('Failed to refresh journey tracking data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const getEventTypeColor = (eventType: string) => {
    switch (eventType) {
      case 'trial_started':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'integration_completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'first_link_created':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'milestone_reached':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'subscription_converted':
        return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'onboarding':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'activation':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'value_realization':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'expansion':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'critical':
        return 'text-red-600 dark:text-red-400';
      case 'high':
        return 'text-orange-600 dark:text-orange-400';
      case 'medium':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'low':
        return 'text-green-600 dark:text-green-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'trial_started':
        return 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z';
      case 'integration_completed':
        return 'M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V5a1 1 0 011-1h3a1 1 0 001-1V4z';
      case 'first_link_created':
        return 'M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1';
      case 'milestone_reached':
        return 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z';
      case 'subscription_converted':
        return 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1';
      default:
        return 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
    }
  };

  return (
    <div class="journey-tracking-dashboard space-y-6">
      {/* Dashboard Header with Controls */}
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex items-center gap-4">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Journey Tracking Dashboard
          </h2>
          {!isOffline && (
            <button
              onClick={refreshData}
              disabled={isLoading.value}
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <svg 
                class={`w-4 h-4 mr-1.5 ${isLoading.value ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          )}
        </div>

        {/* Filters */}
        <div class="flex items-center gap-4">
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-600 dark:text-gray-400">Timeframe:</span>
            <select
              value={selectedTimeframe.value}
              onChange={(e) => selectedTimeframe.value = (e.target as HTMLSelectElement).value}
              class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1.5 text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="all">All time</option>
            </select>
          </div>
          
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-600 dark:text-gray-400">Category:</span>
            <select
              value={selectedCategory.value}
              onChange={(e) => selectedCategory.value = (e.target as HTMLSelectElement).value}
              class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1.5 text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            >
              <option value="all">All Categories</option>
              <option value="onboarding">Onboarding</option>
              <option value="activation">Activation</option>
              <option value="value_realization">Value Realization</option>
              <option value="expansion">Expansion</option>
            </select>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Active Customers</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.uniqueCustomers}
              </p>
            </div>
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Total Events</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.totalEvents}
              </p>
            </div>
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Completion Rate</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.avgCompletionRate.toFixed(1)}%
              </p>
            </div>
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Recent Events</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.recentEvents}
              </p>
            </div>
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Critical Milestones</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.criticalMilestones}
              </p>
            </div>
            <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Conversions</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.conversionEvents}
              </p>
            </div>
            <div class="w-8 h-8 bg-emerald-100 dark:bg-emerald-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-emerald-600 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Journey Events and Milestones Grid */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Journey Events Timeline */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Journey Events
            </h3>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {filteredEvents.value.length} events
            </span>
          </div>

          {isLoading.value ? (
            <div class="flex items-center justify-center h-48">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : filteredEvents.value.length === 0 ? (
            <div class="text-center py-8">
              <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
              </svg>
              <p class="text-gray-500 dark:text-gray-400">No journey events found</p>
            </div>
          ) : (
            <div class="space-y-4 max-h-96 overflow-y-auto">
              {filteredEvents.value.map((event: JourneyEvent) => (
                <div key={event.id} class="flex items-start gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div class="flex-shrink-0 w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={getEventIcon(event.eventType)} />
                    </svg>
                  </div>
                  
                  <div class="flex-1">
                    <div class="flex items-center justify-between mb-2">
                      <h4 class="font-medium text-gray-900 dark:text-white">
                        {event.eventName}
                      </h4>
                      <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getEventTypeColor(event.eventType)}`}>
                        {event.eventType.replace('_', ' ')}
                      </span>
                    </div>
                    
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {event.companyName}
                    </p>
                    
                    <p class="text-xs text-gray-500 dark:text-gray-400 mb-2">
                      {formatTimestamp(event.timestamp)}
                    </p>
                    
                    {Object.keys(event.metadata).length > 0 && (
                      <div class="text-xs text-gray-500 dark:text-gray-400">
                        {Object.entries(event.metadata).slice(0, 2).map(([key, value]) => (
                          <span key={key} class="mr-3">
                            {key}: {String(value)}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Milestone Performance */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Milestone Performance
            </h3>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {filteredMilestones.value.length} milestones
            </span>
          </div>

          {isLoading.value ? (
            <div class="flex items-center justify-center h-48">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : filteredMilestones.value.length === 0 ? (
            <div class="text-center py-8">
              <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p class="text-gray-500 dark:text-gray-400">No milestones found</p>
            </div>
          ) : (
            <div class="space-y-4 max-h-96 overflow-y-auto">
              {filteredMilestones.value.map((milestone: Milestone) => (
                <div key={milestone.id} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900 dark:text-white">
                      {milestone.name}
                    </h4>
                    <div class="flex items-center gap-2">
                      <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(milestone.category)}`}>
                        {milestone.category.replace('_', ' ')}
                      </span>
                      <span class={`text-sm font-medium ${getImpactColor(milestone.impactOnSuccess)}`}>
                        {milestone.impactOnSuccess}
                      </span>
                    </div>
                  </div>
                  
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    {milestone.description}
                  </p>
                  
                  <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span class="text-gray-600 dark:text-gray-400">Completion Rate:</span>
                      <div class="flex items-center gap-2 mt-1">
                        <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div 
                            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${milestone.completionRate}%` }}
                          ></div>
                        </div>
                        <span class="font-medium text-gray-900 dark:text-white">
                          {milestone.completionRate}%
                        </span>
                      </div>
                    </div>
                    
                    <div>
                      <span class="text-gray-600 dark:text-gray-400">Target Time:</span>
                      <p class="font-medium text-gray-900 dark:text-white mt-1">
                        {milestone.targetTimeframe}
                      </p>
                      <p class="text-xs text-gray-500 dark:text-gray-400">
                        Avg: {milestone.averageTime}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Last Updated Timestamp */}
      <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
        Last updated: {new Date(lastUpdated.value).toLocaleString()}
        {isOffline && " (Demo Mode)"}
      </div>
    </div>
  );
}
