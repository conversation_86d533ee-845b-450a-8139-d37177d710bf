// Customer Success Overview - Fresh Islands Component
// Comprehensive customer success monitoring and trial management interface

import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";

interface ActiveTrial {
  trialId: string;
  companyName: string;
  industry: string;
  scenario: string;
  status: string;
  createdAt: string;
  expiresAt: string;
  onboardingProgress: {
    completedSteps: number;
    totalSteps: number;
    currentStep: string;
  };
  healthScore: {
    overallScore: number;
    riskLevel: string;
    interventionTriggers: string[];
  };
}

interface CustomerSuccessOverviewProps {
  initialActiveTrials: any;
  initialHealthMonitoring: any;
  user: any;
  isOffline?: boolean;
}

export default function CustomerSuccessOverview({
  initialActiveTrials,
  initialHealthMonitoring,
  user,
  isOffline = false
}: CustomerSuccessOverviewProps) {
  // Signals for reactive state management
  const activeTrials = useSignal(initialActiveTrials);
  const healthMonitoring = useSignal(initialHealthMonitoring);
  const isLoading = useSignal(false);
  const lastUpdated = useSignal(new Date().toISOString());
  const selectedTab = useSignal('overview');

  // Computed values for dashboard metrics
  const dashboardMetrics = useComputed(() => {
    const trials = activeTrials.value;
    return {
      totalActiveTrials: trials?.summary?.totalActiveTrials || trials?.activeTrials?.length || 0,
      averageHealthScore: trials?.summary?.averageHealthScore || 85,
      highRiskTrials: trials?.summary?.highRiskTrials || 0,
      completionRate: trials?.activeTrials ? 
        (trials.activeTrials.reduce((sum: number, trial: ActiveTrial) => 
          sum + (trial.onboardingProgress.completedSteps / trial.onboardingProgress.totalSteps), 0) / trials.activeTrials.length * 100) : 75
    };
  });

  // Auto-refresh data every 30 seconds (if not offline)
  useEffect(() => {
    if (isOffline) return;

    const interval = setInterval(async () => {
      if (!isLoading.value) {
        await refreshData();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [isOffline]);

  const refreshData = async () => {
    if (isOffline) return;
    
    isLoading.value = true;
    try {
      const [trialsData, healthData] = await Promise.all([
        fetch('/api/billing/trial/active-overview').then(r => r.json()),
        fetch('/api/billing/health-monitoring').then(r => r.json()),
      ]);

      activeTrials.value = trialsData.data;
      healthMonitoring.value = healthData.data;
      lastUpdated.value = new Date().toISOString();
    } catch (error) {
      console.error('Failed to refresh customer success data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400';
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'expired':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  return (
    <div class="customer-success-dashboard space-y-6">
      {/* Dashboard Header with Controls */}
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex items-center gap-4">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Customer Success Overview
          </h2>
          {!isOffline && (
            <button
              onClick={refreshData}
              disabled={isLoading.value}
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <svg 
                class={`w-4 h-4 mr-1.5 ${isLoading.value ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          )}
        </div>

        <div class="flex items-center gap-2">
          <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Provision Trial
          </button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Trials</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.totalActiveTrials}
              </p>
            </div>
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Health Score</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.averageHealthScore.toFixed(1)}
              </p>
            </div>
            <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">High Risk Trials</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.highRiskTrials}
              </p>
            </div>
            <div class="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Completion Rate</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.completionRate.toFixed(1)}%
              </p>
            </div>
            <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Active Trials Table */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            Active Trials
          </h3>
          <span class="text-sm text-gray-600 dark:text-gray-400">
            {activeTrials.value?.activeTrials?.length || 0} trials
          </span>
        </div>

        {isLoading.value ? (
          <div class="flex items-center justify-center h-48">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : !activeTrials.value?.activeTrials?.length ? (
          <div class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p class="text-gray-500 dark:text-gray-400">No active trials</p>
          </div>
        ) : (
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Company
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Progress
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Health Score
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Expires
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {activeTrials.value.activeTrials.map((trial: ActiveTrial) => (
                  <tr key={trial.trialId} class="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                          {trial.companyName}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                          {trial.industry} • {trial.scenario}
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="flex-1">
                          <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                            <span>{trial.onboardingProgress.completedSteps}/{trial.onboardingProgress.totalSteps} steps</span>
                            <span>{Math.round((trial.onboardingProgress.completedSteps / trial.onboardingProgress.totalSteps) * 100)}%</span>
                          </div>
                          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div 
                              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${(trial.onboardingProgress.completedSteps / trial.onboardingProgress.totalSteps) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <span class={`text-sm font-medium ${getHealthScoreColor(trial.healthScore.overallScore)}`}>
                          {trial.healthScore.overallScore}
                        </span>
                        <span class={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRiskLevelColor(trial.healthScore.riskLevel)}`}>
                          {trial.healthScore.riskLevel}
                        </span>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(trial.status)}`}>
                        {trial.status}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(trial.expiresAt)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3">
                        View
                      </button>
                      <button class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300">
                        Extend
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Last Updated Timestamp */}
      <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
        Last updated: {new Date(lastUpdated.value).toLocaleString()}
        {isOffline && " (Demo Mode)"}
      </div>
    </div>
  );
}
