// Advanced Customer Success Dashboard - Fresh Islands Component
// Customer journey visualization, health monitoring, and predictive success analytics

import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";

interface HealthAlert {
  id: string;
  type: string;
  severity: string;
  subscriptionId: string;
  customerId: string;
  message: string;
  data: Record<string, any>;
  createdAt: string;
  acknowledged: boolean;
}

interface HealthMonitoringData {
  alerts: HealthAlert[];
  summary: {
    critical: number;
    high: number;
    medium: number;
    low: number;
    total: number;
  };
}

interface CustomerJourneyData {
  customers: Array<{
    customerId: string;
    companyName: string;
    currentStage: string;
    healthScore: number;
    riskLevel: string;
    milestones: Array<{
      id: string;
      name: string;
      status: string;
      completedAt?: string;
      dueDate?: string;
      progress: number;
    }>;
    interventions: Array<{
      type: string;
      scheduledDate: string;
      status: string;
      outcome?: string;
    }>;
    journey: Array<{
      stage: string;
      enteredAt: string;
      exitedAt?: string;
      duration?: number;
    }>;
    predictedOutcome: {
      successProbability: number;
      churnRisk: number;
      expansionPotential: number;
      timeToValue: number;
    };
  }>;
  stages: Array<{
    name: string;
    description: string;
    averageDuration: number;
    conversionRate: number;
    dropoffRate: number;
  }>;
  insights: {
    averageJourneyDuration: number;
    successRate: number;
    commonDropoffPoints: string[];
    topSuccessFactors: string[];
  };
}

interface SuccessMetrics {
  overview: {
    totalCustomers: number;
    healthyCustomers: number;
    atRiskCustomers: number;
    averageHealthScore: number;
    successRate: number;
    interventionSuccessRate: number;
    timeToValue: number;
    customerSatisfaction: number;
  };
  trends: {
    healthScoreTrend: Array<{
      period: string;
      score: number;
    }>;
    successRateTrend: Array<{
      period: string;
      rate: number;
    }>;
    interventionTrend: Array<{
      period: string;
      interventions: number;
      successRate: number;
    }>;
  };
  predictions: {
    nextMonthRisk: {
      atRiskCustomers: number;
      churnProbability: number;
      revenueAtRisk: number;
    };
    successOpportunities: Array<{
      customerId: string;
      opportunity: string;
      probability: number;
      impact: number;
    }>;
  };
}

interface AdvancedCustomerSuccessDashboardProps {
  initialHealthMonitoring: HealthMonitoringData | null;
  initialCustomerJourney: CustomerJourneyData | null;
  initialSuccessMetrics: SuccessMetrics | null;
  user: any;
  isOffline?: boolean;
}

export default function AdvancedCustomerSuccessDashboard({
  initialHealthMonitoring,
  initialCustomerJourney,
  initialSuccessMetrics,
  user,
  isOffline = false
}: AdvancedCustomerSuccessDashboardProps) {
  // Signals for reactive state management
  const healthMonitoring = useSignal(initialHealthMonitoring);
  const customerJourney = useSignal(initialCustomerJourney);
  const successMetrics = useSignal(initialSuccessMetrics);
  const isLoading = useSignal(false);
  const lastUpdated = useSignal(new Date().toISOString());
  const selectedTab = useSignal('overview');
  const selectedCustomer = useSignal<string | null>(null);

  // Computed values for dashboard metrics
  const dashboardMetrics = useComputed(() => {
    const health = healthMonitoring.value;
    const journey = customerJourney.value;
    const metrics = successMetrics.value;
    
    return {
      totalCustomers: metrics?.overview.totalCustomers || 0,
      healthyCustomers: metrics?.overview.healthyCustomers || 0,
      atRiskCustomers: metrics?.overview.atRiskCustomers || 0,
      averageHealthScore: metrics?.overview.averageHealthScore || 0,
      successRate: metrics?.overview.successRate || 0,
      interventionSuccessRate: metrics?.overview.interventionSuccessRate || 0,
      timeToValue: metrics?.overview.timeToValue || 0,
      customerSatisfaction: metrics?.overview.customerSatisfaction || 0,
      criticalAlerts: health?.summary.critical || 0,
      highAlerts: health?.summary.high || 0,
      totalJourneyStages: journey?.stages.length || 0
    };
  });

  // Auto-refresh data every 45 seconds (if not offline)
  useEffect(() => {
    if (isOffline) return;

    const interval = setInterval(async () => {
      if (!isLoading.value) {
        await refreshData();
      }
    }, 45000);

    return () => clearInterval(interval);
  }, [isOffline]);

  const refreshData = async () => {
    if (isOffline) return;
    
    isLoading.value = true;
    try {
      const [healthData, journeyData, metricsData] = await Promise.all([
        fetch('/api/billing/health-monitoring').then(r => r.json()),
        fetch('/api/billing/customer-journey/all').then(r => r.json()),
        fetch('/api/billing/customer-success-metrics').then(r => r.json()),
      ]);

      healthMonitoring.value = healthData.data;
      customerJourney.value = journeyData.data;
      successMetrics.value = metricsData.data;
      lastUpdated.value = new Date().toISOString();
    } catch (error) {
      console.error('Failed to refresh customer success data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400';
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
    if (score >= 40) return 'text-orange-600 dark:text-orange-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStageColor = (stage: string) => {
    const colors = [
      'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
      'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
      'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400',
      'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    ];
    return colors[stage.length % colors.length];
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div class="advanced-customer-success-dashboard space-y-6">
      {/* Dashboard Header with Controls */}
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex items-center gap-4">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Advanced Customer Success Dashboard
          </h2>
          {!isOffline && (
            <button
              type="button"
              onClick={refreshData}
              disabled={isLoading.value}
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <svg 
                class={`w-4 h-4 mr-1.5 ${isLoading.value ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          )}
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Total Customers</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.totalCustomers}
              </p>
            </div>
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Healthy Customers</p>
              <p class="text-xl font-bold text-green-600 dark:text-green-400">
                {dashboardMetrics.value.healthyCustomers}
              </p>
            </div>
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">At Risk</p>
              <p class="text-xl font-bold text-red-600 dark:text-red-400">
                {dashboardMetrics.value.atRiskCustomers}
              </p>
            </div>
            <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Avg Health Score</p>
              <p class={`text-xl font-bold ${getHealthScoreColor(dashboardMetrics.value.averageHealthScore)}`}>
                {dashboardMetrics.value.averageHealthScore.toFixed(1)}
              </p>
            </div>
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Success Rate</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {formatPercentage(dashboardMetrics.value.successRate)}
              </p>
            </div>
            <div class="w-8 h-8 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Intervention Success</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {formatPercentage(dashboardMetrics.value.interventionSuccessRate)}
              </p>
            </div>
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Time to Value</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.timeToValue.toFixed(1)}d
              </p>
            </div>
            <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">CSAT Score</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.customerSatisfaction.toFixed(1)}/10
              </p>
            </div>
            <div class="w-8 h-8 bg-pink-100 dark:bg-pink-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-pink-600 dark:text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Last Updated Timestamp */}
      <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
        Last updated: {new Date(lastUpdated.value).toLocaleString()}
        {isOffline && " (Demo Mode)"}
      </div>
    </div>
  );
}
