// Usage Billing Dashboard - Fresh Islands Component
// Real-time usage analytics, tier recommendations, and billing optimization

import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";

interface UsageCustomer {
  customerId: string;
  subscriptionId: string;
  companyName: string;
  currentTier: string;
  monthlyUsage: number;
  tierLimit: number;
  usagePercentage: number;
  overage: number;
  cost: number;
  usageCost: number;
  totalCost: number;
  trend: string;
  efficiency: number;
  recommendation: {
    type: string;
    suggestedTier: string;
    reasoning: string;
    potentialSavings: number;
    potentialCost: number;
  };
  usageHistory: Array<{
    date: string;
    usage: number;
  }>;
}

interface UsageAnalytics {
  summary: {
    totalCustomers: number;
    totalUsage: number;
    averageUsagePerCustomer: number;
    usageGrowthRate: number;
    overageCustomers: number;
    underutilizedCustomers: number;
    optimalUsageCustomers: number;
    totalRevenue: number;
    usageBasedRevenue: number;
  };
  customers: UsageCustomer[];
  insights: {
    usagePatterns: Array<{
      pattern: string;
      description: string;
      impact: string;
      actionable: boolean;
    }>;
    optimizationOpportunities: Array<{
      type: string;
      title: string;
      description: string;
      potentialSavings: number;
      effort: string;
      customers: number;
    }>;
    revenueImpact: {
      currentMonthlyRevenue: number;
      potentialAdditionalRevenue: number;
      optimizationSavings: number;
      netRevenueImpact: number;
      confidenceLevel: number;
    };
  };
  trends: {
    usageGrowth: Array<{
      period: string;
      usage: number;
      customers: number;
    }>;
    revenueGrowth: Array<{
      period: string;
      revenue: number;
      usageBased: number;
    }>;
    tierDistribution: Array<{
      tier: string;
      customers: number;
      revenue: number;
      avgUsage: number;
    }>;
  };
}

interface BillingOptimization {
  recommendations: Array<{
    id: string;
    type: string;
    priority: string;
    title: string;
    description: string;
    impact: {
      revenueIncrease: number;
      costReduction: number;
      customerSatisfaction: number;
      churnReduction: number;
    };
    implementation: {
      effort: string;
      timeframe: string;
      resources: string[];
    };
    affectedCustomers: number;
  }>;
  automationOpportunities: Array<{
    process: string;
    description: string;
    complexity: string;
    expectedSavings: number;
    implementationTime: string;
  }>;
}

interface TierRecommendation {
  customerId: string;
  currentTier: string;
  recommendedTier: string;
  confidence: number;
  reasoning: string[];
  impact: {
    costChange: number;
    usageHeadroom: number | string;
    featureBenefits: string[];
  };
  timeline: string;
}

interface UsageBillingDashboardProps {
  initialUsageAnalytics: UsageAnalytics | null;
  initialBillingOptimization: BillingOptimization | null;
  initialTierRecommendations: TierRecommendation[] | null;
  user: any;
  isOffline?: boolean;
}

export default function UsageBillingDashboard({
  initialUsageAnalytics,
  initialBillingOptimization,
  initialTierRecommendations,
  user,
  isOffline = false
}: UsageBillingDashboardProps) {
  // Signals for reactive state management
  const usageAnalytics = useSignal(initialUsageAnalytics);
  const billingOptimization = useSignal(initialBillingOptimization);
  const tierRecommendations = useSignal(initialTierRecommendations || []);
  const isLoading = useSignal(false);
  const lastUpdated = useSignal(new Date().toISOString());
  const selectedTab = useSignal('overview');
  const selectedCustomer = useSignal<string | null>(null);

  // Computed values for dashboard metrics
  const dashboardMetrics = useComputed(() => {
    const analytics = usageAnalytics.value;
    const optimization = billingOptimization.value;
    
    return {
      totalCustomers: analytics?.summary.totalCustomers || 0,
      totalUsage: analytics?.summary.totalUsage || 0,
      averageUsage: analytics?.summary.averageUsagePerCustomer || 0,
      usageGrowthRate: analytics?.summary.usageGrowthRate || 0,
      overageCustomers: analytics?.summary.overageCustomers || 0,
      underutilizedCustomers: analytics?.summary.underutilizedCustomers || 0,
      totalRevenue: analytics?.summary.totalRevenue || 0,
      usageBasedRevenue: analytics?.summary.usageBasedRevenue || 0,
      potentialSavings: optimization?.recommendations.reduce((sum, rec) => sum + rec.impact.revenueIncrease + rec.impact.costReduction, 0) || 0,
      totalRecommendations: tierRecommendations.value.length
    };
  });

  // Auto-refresh data every 30 seconds (if not offline)
  useEffect(() => {
    if (isOffline) return;

    const interval = setInterval(async () => {
      if (!isLoading.value) {
        await refreshData();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [isOffline]);

  const refreshData = async () => {
    if (isOffline) return;
    
    isLoading.value = true;
    try {
      const [analyticsData, optimizationData, recommendationsData] = await Promise.all([
        fetch('/api/billing/usage-analytics/all').then(r => r.json()),
        fetch('/api/billing/optimize-usage-billing').then(r => r.json()),
        fetch('/api/billing/tier-recommendations').then(r => r.json()),
      ]);

      usageAnalytics.value = analyticsData.data;
      billingOptimization.value = optimizationData.data;
      tierRecommendations.value = recommendationsData.data || [];
      lastUpdated.value = new Date().toISOString();
    } catch (error) {
      console.error('Failed to refresh usage billing data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage > 100) return 'text-red-600 dark:text-red-400';
    if (percentage > 80) return 'text-orange-600 dark:text-orange-400';
    if (percentage > 60) return 'text-yellow-600 dark:text-yellow-400';
    if (percentage < 30) return 'text-blue-600 dark:text-blue-400';
    return 'text-green-600 dark:text-green-400';
  };

  const getUsageBarColor = (percentage: number) => {
    if (percentage > 100) return 'bg-red-500';
    if (percentage > 80) return 'bg-orange-500';
    if (percentage > 60) return 'bg-yellow-500';
    if (percentage < 30) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'increasing':
        return 'text-green-600 dark:text-green-400';
      case 'decreasing':
        return 'text-red-600 dark:text-red-400';
      case 'stable':
        return 'text-gray-600 dark:text-gray-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getEffortColor = (effort: string) => {
    switch (effort) {
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  return (
    <div class="usage-billing-dashboard space-y-6">
      {/* Dashboard Header with Controls */}
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex items-center gap-4">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Usage-Based Billing Dashboard
          </h2>
          {!isOffline && (
            <button
              type="button"
              onClick={refreshData}
              disabled={isLoading.value}
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <svg 
                class={`w-4 h-4 mr-1.5 ${isLoading.value ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          )}
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Total Usage</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {formatNumber(dashboardMetrics.value.totalUsage)}
              </p>
            </div>
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Usage Growth</p>
              <p class="text-xl font-bold text-green-600 dark:text-green-400">
                +{formatPercentage(dashboardMetrics.value.usageGrowthRate)}
              </p>
            </div>
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Overage Customers</p>
              <p class="text-xl font-bold text-red-600 dark:text-red-400">
                {dashboardMetrics.value.overageCustomers}
              </p>
            </div>
            <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Underutilized</p>
              <p class="text-xl font-bold text-yellow-600 dark:text-yellow-400">
                {dashboardMetrics.value.underutilizedCustomers}
              </p>
            </div>
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Usage Revenue</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(dashboardMetrics.value.usageBasedRevenue)}
              </p>
            </div>
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Potential Savings</p>
              <p class="text-xl font-bold text-green-600 dark:text-green-400">
                {formatCurrency(dashboardMetrics.value.potentialSavings)}
              </p>
            </div>
            <div class="w-8 h-8 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
            { id: 'customers', label: 'Customer Analysis', icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' },
            { id: 'recommendations', label: 'Tier Recommendations', icon: 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z' },
            { id: 'optimization', label: 'Billing Optimization', icon: 'M13 10V3L4 14h7v7l9-11h-7z' },
            { id: 'trends', label: 'Usage Trends', icon: 'M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z' },
          ].map((tab) => (
            <button
              key={tab.id}
              type="button"
              onClick={() => selectedTab.value = tab.id}
              class={`${
                selectedTab.value === tab.id
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={tab.icon} />
              </svg>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div class="tab-content">
        {selectedTab.value === 'overview' && (
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Usage Patterns */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Usage Patterns
              </h3>
              {usageAnalytics.value?.insights.usagePatterns.length ? (
                <div class="space-y-3">
                  {usageAnalytics.value.insights.usagePatterns.map((pattern, index) => (
                    <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                      <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {pattern.pattern}
                        </h4>
                        {pattern.actionable && (
                          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                            Actionable
                          </span>
                        )}
                      </div>
                      <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {pattern.description}
                      </p>
                      <p class="text-sm text-green-600 dark:text-green-400">
                        Impact: {pattern.impact}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No usage patterns available</p>
                </div>
              )}
            </div>

            {/* Optimization Opportunities */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Optimization Opportunities
              </h3>
              {usageAnalytics.value?.insights.optimizationOpportunities.length ? (
                <div class="space-y-3">
                  {usageAnalytics.value.insights.optimizationOpportunities.map((opportunity, index) => (
                    <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                      <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {opportunity.title}
                        </h4>
                        <span class="text-sm font-medium text-green-600 dark:text-green-400">
                          {formatCurrency(opportunity.potentialSavings)}
                        </span>
                      </div>
                      <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {opportunity.description}
                      </p>
                      <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">
                          {opportunity.customers} customers
                        </span>
                        <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getEffortColor(opportunity.effort)}`}>
                          {opportunity.effort} effort
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No optimization opportunities available</p>
                </div>
              )}
            </div>

            {/* Revenue Impact */}
            <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Revenue Impact Analysis
              </h3>
              {usageAnalytics.value?.insights.revenueImpact ? (
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900 dark:text-white">
                      {formatCurrency(usageAnalytics.value.insights.revenueImpact.currentMonthlyRevenue)}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Current Revenue</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                      +{formatCurrency(usageAnalytics.value.insights.revenueImpact.potentialAdditionalRevenue)}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Additional Revenue</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {formatCurrency(usageAnalytics.value.insights.revenueImpact.optimizationSavings)}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Optimization Savings</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                      {formatPercentage(usageAnalytics.value.insights.revenueImpact.confidenceLevel)}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Confidence Level</div>
                  </div>
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No revenue impact data available</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Additional tab content will be added in the next section */}
      </div>

      {/* Tab Navigation */}
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
            { id: 'customers', label: 'Customer Analysis', icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' },
            { id: 'recommendations', label: 'Tier Recommendations', icon: 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z' },
            { id: 'optimization', label: 'Billing Optimization', icon: 'M13 10V3L4 14h7v7l9-11h-7z' },
            { id: 'trends', label: 'Usage Trends', icon: 'M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z' },
          ].map((tab) => (
            <button
              key={tab.id}
              type="button"
              onClick={() => selectedTab.value = tab.id}
              class={`${
                selectedTab.value === tab.id
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={tab.icon} />
              </svg>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div class="tab-content">
        {selectedTab.value === 'overview' && (
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Usage Patterns */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Usage Patterns
              </h3>
              {usageAnalytics.value?.insights.usagePatterns.length ? (
                <div class="space-y-3">
                  {usageAnalytics.value.insights.usagePatterns.map((pattern, index) => (
                    <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                      <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {pattern.pattern}
                        </h4>
                        {pattern.actionable && (
                          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                            Actionable
                          </span>
                        )}
                      </div>
                      <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {pattern.description}
                      </p>
                      <p class="text-sm text-green-600 dark:text-green-400">
                        Impact: {pattern.impact}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No usage patterns available</p>
                </div>
              )}
            </div>

            {/* Optimization Opportunities */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Optimization Opportunities
              </h3>
              {usageAnalytics.value?.insights.optimizationOpportunities.length ? (
                <div class="space-y-3">
                  {usageAnalytics.value.insights.optimizationOpportunities.map((opportunity, index) => (
                    <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                      <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {opportunity.title}
                        </h4>
                        <span class="text-sm font-medium text-green-600 dark:text-green-400">
                          {formatCurrency(opportunity.potentialSavings)}
                        </span>
                      </div>
                      <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {opportunity.description}
                      </p>
                      <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">
                          {opportunity.customers} customers
                        </span>
                        <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getEffortColor(opportunity.effort)}`}>
                          {opportunity.effort} effort
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No optimization opportunities available</p>
                </div>
              )}
            </div>

            {/* Revenue Impact */}
            <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Revenue Impact Analysis
              </h3>
              {usageAnalytics.value?.insights.revenueImpact ? (
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900 dark:text-white">
                      {formatCurrency(usageAnalytics.value.insights.revenueImpact.currentMonthlyRevenue)}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Current Revenue</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                      +{formatCurrency(usageAnalytics.value.insights.revenueImpact.potentialAdditionalRevenue)}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Additional Revenue</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {formatCurrency(usageAnalytics.value.insights.revenueImpact.optimizationSavings)}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Optimization Savings</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                      {formatPercentage(usageAnalytics.value.insights.revenueImpact.confidenceLevel)}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Confidence Level</div>
                  </div>
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No revenue impact data available</p>
                </div>
              )}
            </div>
          </div>
        )}

        {selectedTab.value === 'customers' && (
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                Customer Usage Analysis
              </h3>
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {usageAnalytics.value?.customers.length || 0} customers
              </span>
            </div>

            {usageAnalytics.value?.customers.length ? (
              <div class="space-y-4 max-h-96 overflow-y-auto">
                {usageAnalytics.value.customers.map((customer) => (
                  <div key={customer.customerId} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                      <div class="flex items-center gap-3">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {customer.companyName}
                        </h4>
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                          {customer.currentTier}
                        </span>
                      </div>
                      <div class="text-right">
                        <div class="text-lg font-bold text-gray-900 dark:text-white">
                          {formatCurrency(customer.totalCost)}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                          Total Cost
                        </div>
                      </div>
                    </div>

                    <div class="mb-3">
                      <div class="flex items-center justify-between text-sm mb-1">
                        <span>Usage: {formatNumber(customer.monthlyUsage)} / {formatNumber(customer.tierLimit)}</span>
                        <span class={getUsageColor(customer.usagePercentage)}>
                          {formatPercentage(customer.usagePercentage)}
                        </span>
                      </div>
                      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          class={`h-2 rounded-full ${getUsageBarColor(customer.usagePercentage)}`}
                          style={`width: ${Math.min(customer.usagePercentage, 100)}%`}
                        ></div>
                      </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-3">
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Trend:</span>
                        <p class={`font-medium ${getTrendColor(customer.trend)}`}>
                          {customer.trend}
                        </p>
                      </div>
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Efficiency:</span>
                        <p class="font-medium text-gray-900 dark:text-white">
                          {formatPercentage(customer.efficiency * 100)}
                        </p>
                      </div>
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Overage:</span>
                        <p class={`font-medium ${customer.overage > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                          {customer.overage > 0 ? `+${formatNumber(customer.overage)}` : 'None'}
                        </p>
                      </div>
                    </div>

                    {customer.recommendation && (
                      <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                        <h5 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                          Recommendation: {customer.recommendation.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </h5>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {customer.recommendation.reasoning}
                        </p>
                        <div class="flex items-center justify-between text-sm">
                          <span class="text-gray-600 dark:text-gray-400">
                            Suggested: {customer.recommendation.suggestedTier}
                          </span>
                          <span class={`font-medium ${
                            customer.recommendation.potentialSavings > 0 ? 'text-green-600 dark:text-green-400' :
                            customer.recommendation.potentialCost > 0 ? 'text-orange-600 dark:text-orange-400' :
                            'text-gray-600 dark:text-gray-400'
                          }`}>
                            {customer.recommendation.potentialSavings > 0 ?
                              `-${formatCurrency(customer.recommendation.potentialSavings)}` :
                              customer.recommendation.potentialCost > 0 ?
                              `+${formatCurrency(customer.recommendation.potentialCost)}` :
                              'No cost change'
                            }
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div class="text-center py-8">
                <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <p class="text-gray-500 dark:text-gray-400">No customer data available</p>
              </div>
            )}
          </div>
        )}

        {selectedTab.value === 'recommendations' && (
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                Tier Recommendations
              </h3>
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {tierRecommendations.value.length} recommendations
              </span>
            </div>

            {tierRecommendations.value.length ? (
              <div class="space-y-4">
                {tierRecommendations.value.map((recommendation) => (
                  <div key={recommendation.customerId} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                      <div class="flex items-center gap-3">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          Customer: {recommendation.customerId}
                        </h4>
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                          {recommendation.currentTier} → {recommendation.recommendedTier}
                        </span>
                      </div>
                      <div class="text-right">
                        <div class="text-lg font-bold text-gray-900 dark:text-white">
                          {formatPercentage(recommendation.confidence)}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">confidence</div>
                      </div>
                    </div>

                    <div class="mb-3">
                      <h5 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Reasoning:</h5>
                      <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                        {recommendation.reasoning.map((reason, index) => (
                          <li key={index} class="flex items-start gap-2">
                            <span class="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                            {reason}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-3">
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Cost Impact:</span>
                        <p class={`font-medium ${
                          recommendation.impact.costChange > 0 ? 'text-red-600 dark:text-red-400' :
                          recommendation.impact.costChange < 0 ? 'text-green-600 dark:text-green-400' :
                          'text-gray-600 dark:text-gray-400'
                        }`}>
                          {recommendation.impact.costChange > 0 ? '+' : ''}{formatCurrency(recommendation.impact.costChange)}
                        </p>
                      </div>
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Usage Headroom:</span>
                        <p class="font-medium text-gray-900 dark:text-white">
                          {typeof recommendation.impact.usageHeadroom === 'number' ?
                            formatNumber(recommendation.impact.usageHeadroom) :
                            recommendation.impact.usageHeadroom
                          }
                        </p>
                      </div>
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Timeline:</span>
                        <p class="font-medium text-gray-900 dark:text-white">
                          {recommendation.timeline}
                        </p>
                      </div>
                    </div>

                    <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                      <h5 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Feature Benefits:
                      </h5>
                      <div class="flex flex-wrap gap-1">
                        {recommendation.impact.featureBenefits.map((benefit, index) => (
                          <span key={index} class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                            {benefit}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div class="text-center py-8">
                <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                <p class="text-gray-500 dark:text-gray-400">No tier recommendations available</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Last Updated Timestamp */}
      <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
        Last updated: {new Date(lastUpdated.value).toLocaleString()}
        {isOffline && " (Demo Mode)"}
      </div>
    </div>
  );
}
