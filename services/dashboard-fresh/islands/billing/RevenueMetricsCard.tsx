// Revenue Metrics Card Component
// D3.js visualization for revenue metrics and trends

import { useEffect, useRef } from "preact/hooks";
import * as d3 from "d3";

interface RevenueMetrics {
  totalMRR: number;
  totalSubscriptions: number;
  averageHealthScore: number;
  churnRate: number;
  expansionRevenue: number;
  growthRate: number;
}

interface RevenueMetricsCardProps {
  metrics: RevenueMetrics;
  timeRange: string;
  isLoading: boolean;
}

export default function RevenueMetricsCard({ metrics, timeRange, isLoading }: RevenueMetricsCardProps) {
  const chartRef = useRef<SVGSVGElement>(null);

  // Generate sample trend data based on metrics
  const generateTrendData = () => {
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365;
    const data = [];
    const baseRevenue = metrics.totalMRR;
    
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - (days - i));
      
      // Generate realistic revenue trend with some randomness
      const trend = (metrics.growthRate / 100) / days;
      const noise = (Math.random() - 0.5) * 0.02; // ±1% noise
      const dayRevenue = baseRevenue * (1 - trend * (days - i)) * (1 + noise);
      
      data.push({
        date: date,
        revenue: Math.max(0, dayRevenue),
        subscriptions: Math.floor(metrics.totalSubscriptions * (dayRevenue / baseRevenue)),
      });
    }
    
    return data;
  };

  // Create revenue trend chart
  useEffect(() => {
    if (!chartRef.current || isLoading) return;

    const svg = d3.select(chartRef.current);
    svg.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 60 };
    const width = 400 - margin.left - margin.right;
    const height = 200 - margin.bottom - margin.top;

    const g = svg
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const data = generateTrendData();

    // Scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(data, d => d.date) as [Date, Date])
      .range([0, width]);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.revenue) as number])
      .range([height, 0]);

    // Line generator
    const line = d3.line<any>()
      .x(d => xScale(d.date))
      .y(d => yScale(d.revenue))
      .curve(d3.curveMonotoneX);

    // Add gradient
    const gradient = svg.append("defs")
      .append("linearGradient")
      .attr("id", "revenue-gradient")
      .attr("gradientUnits", "userSpaceOnUse")
      .attr("x1", 0).attr("y1", height)
      .attr("x2", 0).attr("y2", 0);

    gradient.append("stop")
      .attr("offset", "0%")
      .attr("stop-color", "#3B82F6")
      .attr("stop-opacity", 0.1);

    gradient.append("stop")
      .attr("offset", "100%")
      .attr("stop-color", "#3B82F6")
      .attr("stop-opacity", 0.8);

    // Add area
    const area = d3.area<any>()
      .x(d => xScale(d.date))
      .y0(height)
      .y1(d => yScale(d.revenue))
      .curve(d3.curveMonotoneX);

    g.append("path")
      .datum(data)
      .attr("fill", "url(#revenue-gradient)")
      .attr("d", area);

    // Add line
    g.append("path")
      .datum(data)
      .attr("fill", "none")
      .attr("stroke", "#3B82F6")
      .attr("stroke-width", 2)
      .attr("d", line);

    // Add dots
    g.selectAll(".dot")
      .data(data.filter((_, i) => i % Math.ceil(data.length / 10) === 0))
      .enter().append("circle")
      .attr("class", "dot")
      .attr("cx", d => xScale(d.date))
      .attr("cy", d => yScale(d.revenue))
      .attr("r", 3)
      .attr("fill", "#3B82F6");

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale).ticks(5).tickFormat(d3.timeFormat("%m/%d")));

    g.append("g")
      .call(d3.axisLeft(yScale).ticks(5).tickFormat(d => `$${(d / 1000).toFixed(0)}k`));

    // Add axis labels
    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("y", 0 - margin.left)
      .attr("x", 0 - (height / 2))
      .attr("dy", "1em")
      .style("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", "#6B7280")
      .text("Revenue ($)");

  }, [metrics, timeRange, isLoading]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Revenue Trends
        </h3>
        <div class="flex items-center gap-2">
          <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
          <span class="text-sm text-gray-600 dark:text-gray-400">MRR</span>
        </div>
      </div>

      {isLoading ? (
        <div class="flex items-center justify-center h-48">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <>
          {/* Chart */}
          <div class="mb-4">
            <svg ref={chartRef}></svg>
          </div>

          {/* Key Metrics Summary */}
          <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">Current MRR</p>
              <p class="text-lg font-semibold text-gray-900 dark:text-white">
                {formatCurrency(metrics.totalMRR)}
              </p>
            </div>
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400">Growth Rate</p>
              <p class={`text-lg font-semibold ${metrics.growthRate >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {metrics.growthRate >= 0 ? '+' : ''}{metrics.growthRate.toFixed(1)}%
              </p>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
