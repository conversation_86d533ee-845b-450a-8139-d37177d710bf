// Enhanced Billing Dashboard - Fresh Islands Component
// Comprehensive subscription management and revenue intelligence interface

import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";
import RevenueMetricsCard from "./RevenueMetricsCard.tsx";
import SubscriptionOverview from "./SubscriptionOverview.tsx";
import RevenueIntelligencePanel from "./RevenueIntelligencePanel.tsx";
import HealthMonitoringPanel from "./HealthMonitoringPanel.tsx";

interface BillingDashboardProps {
  initialRevenueOps: any;
  initialRevenueIntelligence: any;
  initialHealthMonitoring: any;
  user: any;
  isOffline?: boolean;
}

export default function BillingDashboard({
  initialRevenueOps,
  initialRevenueIntelligence,
  initialHealthMonitoring,
  user,
  isOffline = false
}: BillingDashboardProps) {
  // Signals for reactive state management
  const revenueOps = useSignal(initialRevenueOps);
  const revenueIntelligence = useSignal(initialRevenueIntelligence);
  const healthMonitoring = useSignal(initialHealthMonitoring);
  const isLoading = useSignal(false);
  const lastUpdated = useSignal(new Date().toISOString());
  const selectedTimeRange = useSignal('30d');
  const activeTab = useSignal('overview');

  // Computed values for dashboard metrics
  const dashboardMetrics = useComputed(() => {
    const ops = revenueOps.value;
    return {
      totalMRR: ops?.summary?.totalMRR || 0,
      totalSubscriptions: ops?.summary?.totalSubscriptions || 0,
      averageHealthScore: ops?.summary?.averageHealthScore || 0,
      churnRate: ops?.summary?.churnRate || 0,
      expansionRevenue: ops?.summary?.expansionRevenue || 0,
      growthRate: ((ops?.summary?.totalMRR || 0) / 100000 - 1) * 100, // Mock calculation
    };
  });

  const alertsSummary = useComputed(() => {
    const health = healthMonitoring.value;
    return {
      critical: health?.summary?.critical || 0,
      high: health?.summary?.high || 0,
      medium: health?.summary?.medium || 0,
      low: health?.summary?.low || 0,
      total: health?.summary?.total || 0,
    };
  });

  // Auto-refresh data every 30 seconds (if not offline)
  useEffect(() => {
    if (isOffline) return;

    const interval = setInterval(async () => {
      if (!isLoading.value) {
        await refreshData();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [isOffline]);

  const refreshData = async () => {
    if (isOffline) return;
    
    isLoading.value = true;
    try {
      const [revenueOpsData, revenueIntelData, healthData] = await Promise.all([
        fetch('/api/billing/revenue-operations-dashboard').then(r => r.json()),
        fetch('/api/billing/revenue-intelligence').then(r => r.json()),
        fetch('/api/billing/health-monitoring').then(r => r.json()),
      ]);

      revenueOps.value = revenueOpsData.data;
      revenueIntelligence.value = revenueIntelData.data.intelligence;
      healthMonitoring.value = healthData.data;
      lastUpdated.value = new Date().toISOString();
    } catch (error) {
      console.error('Failed to refresh billing data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const handleTimeRangeChange = (range: string) => {
    selectedTimeRange.value = range;
    // Trigger data refresh with new time range
    refreshData();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  return (
    <div class="billing-dashboard space-y-6">
      {/* Dashboard Header with Controls */}
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex items-center gap-4">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Revenue Operations Dashboard
          </h2>
          {!isOffline && (
            <button
              onClick={refreshData}
              disabled={isLoading.value}
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <svg 
                class={`w-4 h-4 mr-1.5 ${isLoading.value ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          )}
        </div>

        {/* Time Range Selector */}
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-600 dark:text-gray-400">Time Range:</span>
          <select
            value={selectedTimeRange.value}
            onChange={(e) => handleTimeRangeChange((e.target as HTMLSelectElement).value)}
            class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1.5 text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Monthly Recurring Revenue</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(dashboardMetrics.value.totalMRR)}
              </p>
            </div>
            <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
          <div class="mt-4 flex items-center">
            <span class="text-sm text-green-600 dark:text-green-400 font-medium">
              +{formatPercentage(dashboardMetrics.value.growthRate)}
            </span>
            <span class="text-sm text-gray-500 dark:text-gray-400 ml-2">vs last period</span>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Subscriptions</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.totalSubscriptions.toLocaleString()}
              </p>
            </div>
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
          </div>
          <div class="mt-4 flex items-center">
            <span class="text-sm text-blue-600 dark:text-blue-400 font-medium">
              {dashboardMetrics.value.averageHealthScore.toFixed(1)} avg health
            </span>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Churn Rate</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {formatPercentage(dashboardMetrics.value.churnRate)}
              </p>
            </div>
            <div class="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
              </svg>
            </div>
          </div>
          <div class="mt-4 flex items-center">
            <span class="text-sm text-gray-500 dark:text-gray-400">
              {alertsSummary.value.total} active alerts
            </span>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Expansion Revenue</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(dashboardMetrics.value.expansionRevenue)}
              </p>
            </div>
            <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12" />
              </svg>
            </div>
          </div>
          <div class="mt-4 flex items-center">
            <span class="text-sm text-purple-600 dark:text-purple-400 font-medium">
              +{formatPercentage(15.2)} growth
            </span>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
            { id: 'intelligence', label: 'Revenue Intelligence', icon: 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z' },
            { id: 'health', label: 'Health Monitoring', icon: 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => activeTab.value = tab.id}
              class={`${
                activeTab.value === tab.id
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={tab.icon} />
              </svg>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div class="tab-content">
        {activeTab.value === 'overview' && (
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <RevenueMetricsCard 
              metrics={dashboardMetrics.value}
              timeRange={selectedTimeRange.value}
              isLoading={isLoading.value}
            />
            <SubscriptionOverview 
              subscriptions={revenueOps.value?.subscriptions || []}
              isLoading={isLoading.value}
            />
          </div>
        )}

        {activeTab.value === 'intelligence' && (
          <RevenueIntelligencePanel 
            intelligence={revenueIntelligence.value}
            isLoading={isLoading.value}
          />
        )}

        {activeTab.value === 'health' && (
          <HealthMonitoringPanel 
            healthData={healthMonitoring.value}
            alertsSummary={alertsSummary.value}
            isLoading={isLoading.value}
          />
        )}
      </div>

      {/* Last Updated Timestamp */}
      <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
        Last updated: {new Date(lastUpdated.value).toLocaleString()}
        {isOffline && " (Demo Mode)"}
      </div>
    </div>
  );
}
