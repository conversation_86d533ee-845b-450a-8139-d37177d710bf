// Revenue Operations Dashboard - Fresh Islands Component
// Comprehensive subscription management, health monitoring, and revenue optimization

import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";

interface RevenueOperationsSummary {
  totalSubscriptions: number;
  totalMRR: number;
  totalARR: number;
  averageHealthScore: number;
  churnRate: number;
  expansionRate: number;
  totalExpansionOpportunities: number;
  totalRisks: number;
  potentialExpansionRevenue: number;
  atRiskRevenue: number;
}

interface SubscriptionData {
  subscription: {
    id: string;
    customerId: string;
    planId: string;
    status: string;
    mrr: number;
    tier: string;
    createdAt: string;
  };
  healthScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  expansionOpportunities: Array<{
    type: string;
    description: string;
    potentialRevenue: number;
    confidence: number;
    priority: string;
  }>;
  usageAnalytics: {
    currentUsage: number;
    trend: string;
    efficiency: number;
  };
  churnPrediction: {
    churnProbability: number;
    factors: string[];
    riskLevel: string;
  };
}

interface RevenueOperationsData {
  summary: RevenueOperationsSummary;
  subscriptions: SubscriptionData[];
  insights: {
    topExpansionOpportunities: Array<{
      customerId: string;
      opportunity: string;
      potentialRevenue: number;
      confidence: number;
      timeframe: string;
    }>;
    criticalRisks: Array<{
      customerId: string;
      risk: string;
      impact: number;
      probability: number;
      urgency: string;
    }>;
    revenueRecommendations: Array<{
      type: string;
      description: string;
      expectedImpact: number;
      effort: string;
    }>;
    performanceAlerts: Array<{
      type: string;
      message: string;
      severity: string;
      actionRequired: boolean;
    }>;
  };
  trends: {
    mrrGrowth: number[];
    churnTrend: number[];
    expansionTrend: number[];
    healthScoreTrend: number[];
  };
}

interface HealthMonitoringData {
  alerts: Array<{
    id: string;
    type: string;
    severity: string;
    subscriptionId: string;
    customerId: string;
    message: string;
    data: Record<string, any>;
    createdAt: string;
    acknowledged: boolean;
  }>;
  summary: {
    critical: number;
    high: number;
    medium: number;
    low: number;
    total: number;
  };
}

interface RevenueIntelligenceData {
  opportunities: Array<{
    type: string;
    description: string;
    potentialRevenue: number;
    confidence: number;
    priority: string;
    customerId: string;
    timeframe: string;
  }>;
  risks: Array<{
    type: string;
    description: string;
    impact: number;
    probability: number;
    severity: string;
    customerId: string;
    mitigationActions: string[];
  }>;
  insights: Array<{
    category: string;
    title: string;
    description: string;
    actionable: boolean;
    impact: string;
  }>;
  recommendations: Array<{
    type: string;
    title: string;
    description: string;
    expectedImpact: number;
    effort: string;
    priority: string;
  }>;
  predictions: {
    nextMonthRevenue: {
      amount: number;
      confidence: number;
      factors: string[];
    };
    quarterlyForecast: {
      q1: number;
      q2: number;
      q3: number;
      q4: number;
      confidence: number;
    };
    churnRisk: {
      probability: number;
      factors: string[];
      atRiskRevenue: number;
    };
  };
}

interface RevenueOperationsDashboardProps {
  initialRevenueOps: RevenueOperationsData | null;
  initialHealthMonitoring: HealthMonitoringData | null;
  initialRevenueIntelligence: RevenueIntelligenceData | null;
  user: any;
  isOffline?: boolean;
}

export default function RevenueOperationsDashboard({
  initialRevenueOps,
  initialHealthMonitoring,
  initialRevenueIntelligence,
  user,
  isOffline = false
}: RevenueOperationsDashboardProps) {
  // Signals for reactive state management
  const revenueOps = useSignal(initialRevenueOps);
  const healthMonitoring = useSignal(initialHealthMonitoring);
  const revenueIntelligence = useSignal(initialRevenueIntelligence);
  const isLoading = useSignal(false);
  const lastUpdated = useSignal(new Date().toISOString());
  const selectedTab = useSignal('overview');
  const selectedSubscription = useSignal<string | null>(null);

  // Computed values for dashboard metrics
  const dashboardMetrics = useComputed(() => {
    const ops = revenueOps.value;
    const health = healthMonitoring.value;
    const intelligence = revenueIntelligence.value;
    
    return {
      totalMRR: ops?.summary.totalMRR || 0,
      totalARR: ops?.summary.totalARR || 0,
      healthScore: ops?.summary.averageHealthScore || 0,
      churnRate: ops?.summary.churnRate || 0,
      expansionRate: ops?.summary.expansionRate || 0,
      criticalAlerts: health?.summary.critical || 0,
      highAlerts: health?.summary.high || 0,
      totalOpportunities: intelligence?.opportunities.length || 0,
      totalRisks: intelligence?.risks.length || 0,
      potentialRevenue: ops?.summary.potentialExpansionRevenue || 0,
      atRiskRevenue: ops?.summary.atRiskRevenue || 0
    };
  });

  // Auto-refresh data every 30 seconds (if not offline)
  useEffect(() => {
    if (isOffline) return;

    const interval = setInterval(async () => {
      if (!isLoading.value) {
        await refreshData();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [isOffline]);

  const refreshData = async () => {
    if (isOffline) return;
    
    isLoading.value = true;
    try {
      const [revenueOpsData, healthData, intelligenceData] = await Promise.all([
        fetch('/api/billing/revenue-operations-dashboard').then(r => r.json()),
        fetch('/api/billing/health-monitoring').then(r => r.json()),
        fetch('/api/billing/revenue-intelligence').then(r => r.json()),
      ]);

      revenueOps.value = revenueOpsData.data;
      healthMonitoring.value = healthData.data;
      revenueIntelligence.value = intelligenceData.data;
      lastUpdated.value = new Date().toISOString();
    } catch (error) {
      console.error('Failed to refresh revenue operations data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400';
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
    if (score >= 40) return 'text-orange-600 dark:text-orange-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <div class="revenue-operations-dashboard space-y-6">
      {/* Dashboard Header with Controls */}
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex items-center gap-4">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Revenue Operations Dashboard
          </h2>
          {!isOffline && (
            <button
              onClick={refreshData}
              disabled={isLoading.value}
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <svg 
                class={`w-4 h-4 mr-1.5 ${isLoading.value ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          )}
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Monthly Recurring Revenue</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(dashboardMetrics.value.totalMRR)}
              </p>
            </div>
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Annual Recurring Revenue</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(dashboardMetrics.value.totalARR)}
              </p>
            </div>
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Avg Health Score</p>
              <p class={`text-xl font-bold ${getHealthScoreColor(dashboardMetrics.value.healthScore)}`}>
                {dashboardMetrics.value.healthScore.toFixed(1)}
              </p>
            </div>
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Churn Rate</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {formatPercentage(dashboardMetrics.value.churnRate)}
              </p>
            </div>
            <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Expansion Rate</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {formatPercentage(dashboardMetrics.value.expansionRate)}
              </p>
            </div>
            <div class="w-8 h-8 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Critical Alerts</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.criticalAlerts + dashboardMetrics.value.highAlerts}
              </p>
            </div>
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
            { id: 'subscriptions', label: 'Subscriptions', icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' },
            { id: 'health', label: 'Health Monitoring', icon: 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z' },
            { id: 'intelligence', label: 'Revenue Intelligence', icon: 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z' },
          ].map((tab) => (
            <button
              key={tab.id}
              type="button"
              onClick={() => selectedTab.value = tab.id}
              class={`${
                selectedTab.value === tab.id
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={tab.icon} />
              </svg>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div class="tab-content">
        {selectedTab.value === 'overview' && (
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Revenue Trends */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Revenue Trends
              </h3>
              {revenueOps.value?.trends ? (
                <div class="space-y-4">
                  <div class="grid grid-cols-2 gap-4">
                    <div class="text-center">
                      <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        +{formatPercentage(((revenueOps.value.trends.mrrGrowth[4] - revenueOps.value.trends.mrrGrowth[0]) / revenueOps.value.trends.mrrGrowth[0]) * 100)}
                      </div>
                      <div class="text-sm text-gray-600 dark:text-gray-400">MRR Growth</div>
                    </div>
                    <div class="text-center">
                      <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        +{formatPercentage(revenueOps.value.trends.expansionTrend[4] - revenueOps.value.trends.expansionTrend[0])}
                      </div>
                      <div class="text-sm text-gray-600 dark:text-gray-400">Expansion Rate</div>
                    </div>
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">
                    Showing 5-month trend data
                  </div>
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No trend data available</p>
                </div>
              )}
            </div>

            {/* Top Opportunities */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Top Expansion Opportunities
              </h3>
              {revenueOps.value?.insights.topExpansionOpportunities.length ? (
                <div class="space-y-3">
                  {revenueOps.value.insights.topExpansionOpportunities.slice(0, 3).map((opportunity, index) => (
                    <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                      <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {opportunity.opportunity}
                        </h4>
                        <span class="text-sm font-medium text-green-600 dark:text-green-400">
                          {formatCurrency(opportunity.potentialRevenue)}
                        </span>
                      </div>
                      <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">
                          Customer: {opportunity.customerId}
                        </span>
                        <span class="text-gray-600 dark:text-gray-400">
                          {opportunity.timeframe}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No opportunities available</p>
                </div>
              )}
            </div>

            {/* Critical Risks */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Critical Risks
              </h3>
              {revenueOps.value?.insights.criticalRisks.length ? (
                <div class="space-y-3">
                  {revenueOps.value.insights.criticalRisks.map((risk, index) => (
                    <div key={index} class="border border-red-200 dark:border-red-700 rounded-lg p-3 bg-red-50 dark:bg-red-900/10">
                      <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {risk.risk}
                        </h4>
                        <span class="text-sm font-medium text-red-600 dark:text-red-400">
                          {formatCurrency(risk.impact)}
                        </span>
                      </div>
                      <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">
                          Customer: {risk.customerId}
                        </span>
                        <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          risk.urgency === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                          'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                        }`}>
                          {risk.urgency} urgency
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No critical risks identified</p>
                </div>
              )}
            </div>

            {/* Revenue Recommendations */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Revenue Recommendations
              </h3>
              {revenueOps.value?.insights.revenueRecommendations.length ? (
                <div class="space-y-3">
                  {revenueOps.value.insights.revenueRecommendations.map((recommendation, index) => (
                    <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                      <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {recommendation.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </h4>
                        <span class="text-sm font-medium text-green-600 dark:text-green-400">
                          +{formatCurrency(recommendation.expectedImpact)}
                        </span>
                      </div>
                      <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {recommendation.description}
                      </p>
                      <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        recommendation.effort === 'low' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                        recommendation.effort === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                        'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                      }`}>
                        {recommendation.effort} effort
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No recommendations available</p>
                </div>
              )}
            </div>
          </div>
        )}

        {selectedTab.value === 'subscriptions' && (
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                Subscription Management
              </h3>
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {revenueOps.value?.subscriptions.length || 0} subscriptions
              </span>
            </div>

            {revenueOps.value?.subscriptions.length ? (
              <div class="space-y-4 max-h-96 overflow-y-auto">
                {revenueOps.value.subscriptions.map((sub) => (
                  <div key={sub.subscription.id} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                      <div class="flex items-center gap-3">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {sub.subscription.tier} Plan
                        </h4>
                        <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(sub.riskLevel)}`}>
                          {sub.riskLevel} risk
                        </span>
                      </div>
                      <div class="text-right">
                        <div class="text-lg font-bold text-gray-900 dark:text-white">
                          {formatCurrency(sub.subscription.mrr)}/mo
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                          Customer: {sub.subscription.customerId}
                        </div>
                      </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Health Score:</span>
                        <p class={`font-medium ${getHealthScoreColor(sub.healthScore)}`}>
                          {sub.healthScore}/100
                        </p>
                      </div>
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Usage Trend:</span>
                        <p class={`font-medium ${
                          sub.usageAnalytics.trend === 'increasing' ? 'text-green-600 dark:text-green-400' :
                          sub.usageAnalytics.trend === 'decreasing' ? 'text-red-600 dark:text-red-400' :
                          'text-gray-600 dark:text-gray-400'
                        }`}>
                          {sub.usageAnalytics.trend}
                        </p>
                      </div>
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Churn Risk:</span>
                        <p class={`font-medium ${
                          sub.churnPrediction.churnProbability < 0.2 ? 'text-green-600 dark:text-green-400' :
                          sub.churnPrediction.churnProbability < 0.4 ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-red-600 dark:text-red-400'
                        }`}>
                          {formatPercentage(sub.churnPrediction.churnProbability * 100)}
                        </p>
                      </div>
                    </div>

                    {sub.expansionOpportunities.length > 0 && (
                      <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                        <h5 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                          Expansion Opportunities:
                        </h5>
                        <div class="space-y-1">
                          {sub.expansionOpportunities.map((opp, index) => (
                            <div key={index} class="flex items-center justify-between text-sm">
                              <span class="text-gray-600 dark:text-gray-400">{opp.description}</span>
                              <span class="font-medium text-green-600 dark:text-green-400">
                                +{formatCurrency(opp.potentialRevenue)}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div class="text-center py-8">
                <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <p class="text-gray-500 dark:text-gray-400">No subscriptions found</p>
              </div>
            )}
          </div>
        )}

        {selectedTab.value === 'health' && (
          <div class="space-y-6">
            {/* Health Monitoring Summary */}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Critical Alerts</p>
                    <p class="text-2xl font-bold text-red-600 dark:text-red-400">
                      {healthMonitoring.value?.summary.critical || 0}
                    </p>
                  </div>
                  <div class="w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                </div>
              </div>

              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">High Priority</p>
                    <p class="text-2xl font-bold text-orange-600 dark:text-orange-400">
                      {healthMonitoring.value?.summary.high || 0}
                    </p>
                  </div>
                  <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                    </svg>
                  </div>
                </div>
              </div>

              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Medium Priority</p>
                    <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                      {healthMonitoring.value?.summary.medium || 0}
                    </p>
                  </div>
                  <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              </div>

              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Alerts</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">
                      {healthMonitoring.value?.summary.total || 0}
                    </p>
                  </div>
                  <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-5a7.5 7.5 0 015.5-7.21" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Active Alerts */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Active Health Alerts
              </h3>

              {healthMonitoring.value?.alerts.length ? (
                <div class="space-y-4">
                  {healthMonitoring.value.alerts.map((alert) => (
                    <div key={alert.id} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center gap-3">
                          <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                            {alert.severity}
                          </span>
                          <span class="text-sm text-gray-600 dark:text-gray-400">
                            {alert.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </span>
                        </div>
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                          {formatTimestamp(alert.createdAt)}
                        </span>
                      </div>

                      <p class="text-gray-900 dark:text-white mb-2">
                        {alert.message}
                      </p>

                      <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">
                          Customer: {alert.customerId} | Subscription: {alert.subscriptionId}
                        </span>
                        {!alert.acknowledged && (
                          <button
                            type="button"
                            class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300"
                          >
                            Acknowledge
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p class="text-gray-500 dark:text-gray-400">No active alerts</p>
                </div>
              )}
            </div>
          </div>
        )}

        {selectedTab.value === 'intelligence' && (
          <div class="space-y-6">
            {/* Revenue Predictions */}
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Revenue Predictions
                </h3>
                {revenueIntelligence.value?.predictions ? (
                  <div class="space-y-4">
                    <div class="text-center">
                      <div class="text-3xl font-bold text-green-600 dark:text-green-400">
                        {formatCurrency(revenueIntelligence.value.predictions.nextMonthRevenue.amount)}
                      </div>
                      <div class="text-sm text-gray-600 dark:text-gray-400">
                        Next Month Revenue ({formatPercentage(revenueIntelligence.value.predictions.nextMonthRevenue.confidence * 100)} confidence)
                      </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4 text-center">
                      <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                        <div class="text-lg font-bold text-blue-600 dark:text-blue-400">
                          {formatCurrency(revenueIntelligence.value.predictions.quarterlyForecast.q1)}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Q1 Forecast</div>
                      </div>
                      <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
                        <div class="text-lg font-bold text-purple-600 dark:text-purple-400">
                          {formatPercentage(revenueIntelligence.value.predictions.churnRisk.probability * 100)}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Churn Risk</div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div class="text-center py-8">
                    <p class="text-gray-500 dark:text-gray-400">No prediction data available</p>
                  </div>
                )}
              </div>

              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Revenue Opportunities
                </h3>
                {revenueIntelligence.value?.opportunities.length ? (
                  <div class="space-y-3">
                    {revenueIntelligence.value.opportunities.slice(0, 3).map((opportunity, index) => (
                      <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                        <div class="flex items-center justify-between mb-2">
                          <h4 class="font-medium text-gray-900 dark:text-white">
                            {opportunity.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </h4>
                          <span class="text-sm font-medium text-green-600 dark:text-green-400">
                            {formatCurrency(opportunity.potentialRevenue)}
                          </span>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {opportunity.description}
                        </p>
                        <div class="flex items-center justify-between text-xs">
                          <span class="text-gray-500 dark:text-gray-400">
                            Customer: {opportunity.customerId}
                          </span>
                          <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            opportunity.priority === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                            opportunity.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                            'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          }`}>
                            {opportunity.priority}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div class="text-center py-8">
                    <p class="text-gray-500 dark:text-gray-400">No opportunities identified</p>
                  </div>
                )}
              </div>
            </div>

            {/* Revenue Risks */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Revenue Risks
              </h3>
              {revenueIntelligence.value?.risks.length ? (
                <div class="space-y-4">
                  {revenueIntelligence.value.risks.map((risk, index) => (
                    <div key={index} class="border border-red-200 dark:border-red-700 rounded-lg p-4 bg-red-50 dark:bg-red-900/10">
                      <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center gap-3">
                          <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(risk.severity)}`}>
                            {risk.severity}
                          </span>
                          <h4 class="font-medium text-gray-900 dark:text-white">
                            {risk.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </h4>
                        </div>
                        <span class="text-sm font-medium text-red-600 dark:text-red-400">
                          -{formatCurrency(risk.impact)}
                        </span>
                      </div>

                      <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        {risk.description}
                      </p>

                      <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">
                          Customer: {risk.customerId} | Probability: {formatPercentage(risk.probability * 100)}
                        </span>
                        <div class="flex gap-1">
                          {risk.mitigationActions.map((action, actionIndex) => (
                            <span key={actionIndex} class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                              {action.replace('_', ' ')}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p class="text-gray-500 dark:text-gray-400">No revenue risks identified</p>
                </div>
              )}
            </div>

            {/* Strategic Recommendations */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Strategic Recommendations
              </h3>
              {revenueIntelligence.value?.recommendations.length ? (
                <div class="space-y-4">
                  {revenueIntelligence.value.recommendations.map((recommendation, index) => (
                    <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center gap-3">
                          <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            recommendation.priority === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                            recommendation.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                            'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          }`}>
                            {recommendation.priority}
                          </span>
                          <h4 class="font-medium text-gray-900 dark:text-white">
                            {recommendation.title}
                          </h4>
                        </div>
                        <span class="text-sm font-medium text-green-600 dark:text-green-400">
                          +{formatCurrency(recommendation.expectedImpact)}
                        </span>
                      </div>

                      <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        {recommendation.description}
                      </p>

                      <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">
                          Type: {recommendation.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                        <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          recommendation.effort === 'low' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                          recommendation.effort === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                          'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                        }`}>
                          {recommendation.effort} effort
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No recommendations available</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Last Updated Timestamp */}
      <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
        Last updated: {new Date(lastUpdated.value).toLocaleString()}
        {isOffline && " (Demo Mode)"}
      </div>
    </div>
  );
}
