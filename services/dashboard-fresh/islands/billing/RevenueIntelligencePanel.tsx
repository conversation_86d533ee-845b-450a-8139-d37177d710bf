// Revenue Intelligence Panel Component
// Displays AI-powered revenue insights, opportunities, and predictions

interface RevenueIntelligence {
  opportunities: Array<{
    type: string;
    description: string;
    potentialRevenue: number;
    confidence: number;
    priority: 'high' | 'medium' | 'low';
  }>;
  risks: Array<{
    type: string;
    description: string;
    impact: number;
    probability: number;
    severity: 'critical' | 'high' | 'medium' | 'low';
  }>;
  insights: Array<{
    category: string;
    title: string;
    description: string;
    actionable: boolean;
  }>;
  predictions: {
    nextMonthRevenue: {
      amount: number;
      confidence: number;
    };
    churnRisk: {
      probability: number;
      factors: string[];
    };
  };
}

interface RevenueIntelligencePanelProps {
  intelligence: RevenueIntelligence;
  isLoading: boolean;
}

export default function RevenueIntelligencePanel({ intelligence, isLoading }: RevenueIntelligencePanelProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  if (isLoading) {
    return (
      <div class="flex items-center justify-center h-96">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div class="space-y-6">
      {/* Revenue Predictions */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Revenue Predictions
          </h3>
          
          <div class="space-y-4">
            <div class="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div>
                <p class="text-sm text-blue-600 dark:text-blue-400 font-medium">Next Month Revenue</p>
                <p class="text-2xl font-bold text-blue-900 dark:text-blue-100">
                  {formatCurrency(intelligence?.predictions?.nextMonthRevenue?.amount || 0)}
                </p>
              </div>
              <div class="text-right">
                <p class="text-sm text-blue-600 dark:text-blue-400">Confidence</p>
                <p class="text-lg font-semibold text-blue-900 dark:text-blue-100">
                  {formatPercentage(intelligence?.predictions?.nextMonthRevenue?.confidence || 0)}
                </p>
              </div>
            </div>

            <div class="flex items-center justify-between p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <div>
                <p class="text-sm text-orange-600 dark:text-orange-400 font-medium">Churn Risk</p>
                <p class="text-2xl font-bold text-orange-900 dark:text-orange-100">
                  {formatPercentage(intelligence?.predictions?.churnRisk?.probability || 0)}
                </p>
              </div>
              <div class="text-right">
                <p class="text-sm text-orange-600 dark:text-orange-400">Risk Factors</p>
                <p class="text-lg font-semibold text-orange-900 dark:text-orange-100">
                  {intelligence?.predictions?.churnRisk?.factors?.length || 0}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Key Insights */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Key Insights
          </h3>
          
          <div class="space-y-3">
            {intelligence?.insights?.slice(0, 3).map((insight, index) => (
              <div key={index} class="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <div class="flex-1">
                  <h4 class="font-medium text-gray-900 dark:text-white text-sm">
                    {insight.title}
                  </h4>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {insight.description}
                  </p>
                  {insight.actionable && (
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 mt-2">
                      Actionable
                    </span>
                  )}
                </div>
              </div>
            )) || (
              <p class="text-gray-500 dark:text-gray-400 text-sm">No insights available</p>
            )}
          </div>
        </div>
      </div>

      {/* Opportunities and Risks */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Opportunities */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Revenue Opportunities
            </h3>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {intelligence?.opportunities?.length || 0} identified
            </span>
          </div>

          <div class="space-y-3">
            {intelligence?.opportunities?.map((opportunity, index) => (
              <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                  <h4 class="font-medium text-gray-900 dark:text-white text-sm">
                    {opportunity.type.replace('_', ' ').toUpperCase()}
                  </h4>
                  <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(opportunity.priority)}`}>
                    {opportunity.priority}
                  </span>
                </div>
                
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {opportunity.description}
                </p>
                
                <div class="flex items-center justify-between">
                  <span class="text-sm font-medium text-green-600 dark:text-green-400">
                    {formatCurrency(opportunity.potentialRevenue)} potential
                  </span>
                  <span class="text-sm text-gray-500 dark:text-gray-400">
                    {formatPercentage(opportunity.confidence)} confidence
                  </span>
                </div>
              </div>
            )) || (
              <p class="text-gray-500 dark:text-gray-400 text-sm">No opportunities identified</p>
            )}
          </div>
        </div>

        {/* Revenue Risks */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Revenue Risks
            </h3>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {intelligence?.risks?.length || 0} identified
            </span>
          </div>

          <div class="space-y-3">
            {intelligence?.risks?.map((risk, index) => (
              <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                  <h4 class="font-medium text-gray-900 dark:text-white text-sm">
                    {risk.type.replace('_', ' ').toUpperCase()}
                  </h4>
                  <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(risk.severity)}`}>
                    {risk.severity}
                  </span>
                </div>
                
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {risk.description}
                </p>
                
                <div class="flex items-center justify-between">
                  <span class="text-sm font-medium text-red-600 dark:text-red-400">
                    {formatCurrency(risk.impact)} at risk
                  </span>
                  <span class="text-sm text-gray-500 dark:text-gray-400">
                    {formatPercentage(risk.probability)} probability
                  </span>
                </div>
              </div>
            )) || (
              <p class="text-gray-500 dark:text-gray-400 text-sm">No risks identified</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
