// Revenue Intelligence Dashboard - Fresh Islands Component
// Advanced revenue forecasting, churn prediction, and strategic opportunity analysis

import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";

interface RevenueIntelligenceData {
  opportunities: Array<{
    type: string;
    description: string;
    potentialRevenue: number;
    confidence: number;
    priority: string;
    customerId: string;
    timeframe: string;
    factors: string[];
  }>;
  risks: Array<{
    type: string;
    description: string;
    impact: number;
    probability: number;
    severity: string;
    customerId: string;
    mitigationActions: string[];
    factors: string[];
  }>;
  insights: Array<{
    category: string;
    title: string;
    description: string;
    actionable: boolean;
    impact: string;
    confidence: number;
  }>;
  recommendations: Array<{
    type: string;
    title: string;
    description: string;
    expectedImpact: number;
    effort: string;
    priority: string;
    timeframe: string;
    confidence: number;
  }>;
  predictions: {
    nextMonthRevenue: {
      amount: number;
      confidence: number;
      factors: string[];
      breakdown: {
        existing: number;
        expansion: number;
        new: number;
      };
    };
    quarterlyForecast: {
      q1: number;
      q2: number;
      q3: number;
      q4: number;
      confidence: number;
      assumptions: string[];
    };
    churnRisk: {
      probability: number;
      factors: string[];
      atRiskRevenue: number;
      timeline: string;
    };
    expansionForecast: {
      probability: number;
      potentialRevenue: number;
      timeline: string;
      topOpportunities: number;
    };
  };
  marketAnalysis: {
    competitivePosition: string;
    marketTrends: Array<{
      trend: string;
      impact: string;
      confidence: number;
      timeframe: string;
    }>;
    opportunityScore: number;
    riskScore: number;
  };
}

interface ChurnPrediction {
  customerId: string;
  subscriptionId: string;
  churnProbability: number;
  riskLevel: string;
  factors: Array<{
    factor: string;
    weight: number;
    impact: string;
  }>;
  recommendations: string[];
  timeline: string;
  confidence: number;
}

interface RevenueForecasts {
  monthly: Array<{
    month: string;
    revenue: number;
    confidence: number;
  }>;
  scenarios: {
    conservative: {
      q1: number;
      q2: number;
      q3: number;
      q4: number;
      confidence: number;
    };
    optimistic: {
      q1: number;
      q2: number;
      q3: number;
      q4: number;
      confidence: number;
    };
    pessimistic: {
      q1: number;
      q2: number;
      q3: number;
      q4: number;
      confidence: number;
    };
  };
  drivers: Array<{
    driver: string;
    impact: number;
    trend: string;
  }>;
}

interface RevenueIntelligenceDashboardProps {
  initialRevenueIntelligence: RevenueIntelligenceData | null;
  initialChurnPredictions: ChurnPrediction[] | null;
  initialRevenueForecasts: RevenueForecasts | null;
  user: any;
  isOffline?: boolean;
}

export default function RevenueIntelligenceDashboard({
  initialRevenueIntelligence,
  initialChurnPredictions,
  initialRevenueForecasts,
  user,
  isOffline = false
}: RevenueIntelligenceDashboardProps) {
  // Signals for reactive state management
  const revenueIntelligence = useSignal(initialRevenueIntelligence);
  const churnPredictions = useSignal(initialChurnPredictions || []);
  const revenueForecasts = useSignal(initialRevenueForecasts);
  const isLoading = useSignal(false);
  const lastUpdated = useSignal(new Date().toISOString());
  const selectedTab = useSignal('predictions');
  const selectedScenario = useSignal('conservative');

  // Computed values for dashboard metrics
  const dashboardMetrics = useComputed(() => {
    const intelligence = revenueIntelligence.value;
    const predictions = churnPredictions.value;
    const forecasts = revenueForecasts.value;
    
    return {
      totalOpportunities: intelligence?.opportunities.length || 0,
      totalOpportunityValue: intelligence?.opportunities.reduce((sum, opp) => sum + opp.potentialRevenue, 0) || 0,
      totalRisks: intelligence?.risks.length || 0,
      totalRiskValue: intelligence?.risks.reduce((sum, risk) => sum + risk.impact, 0) || 0,
      highRiskCustomers: predictions.filter(p => p.riskLevel === 'high' || p.churnProbability > 0.5).length,
      nextMonthRevenue: intelligence?.predictions.nextMonthRevenue.amount || 0,
      churnRiskRevenue: intelligence?.predictions.churnRisk.atRiskRevenue || 0,
      expansionPotential: intelligence?.predictions.expansionForecast.potentialRevenue || 0,
      opportunityScore: intelligence?.marketAnalysis.opportunityScore || 0,
      riskScore: intelligence?.marketAnalysis.riskScore || 0
    };
  });

  // Auto-refresh data every 60 seconds (if not offline)
  useEffect(() => {
    if (isOffline) return;

    const interval = setInterval(async () => {
      if (!isLoading.value) {
        await refreshData();
      }
    }, 60000);

    return () => clearInterval(interval);
  }, [isOffline]);

  const refreshData = async () => {
    if (isOffline) return;
    
    isLoading.value = true;
    try {
      const [intelligenceData, churnData, forecastsData] = await Promise.all([
        fetch('/api/billing/revenue-intelligence').then(r => r.json()),
        fetch('/api/billing/churn-prediction/all').then(r => r.json()),
        fetch('/api/billing/revenue-forecasts').then(r => r.json()),
      ]);

      revenueIntelligence.value = intelligenceData.data;
      churnPredictions.value = churnData.data || [];
      revenueForecasts.value = forecastsData.data;
      lastUpdated.value = new Date().toISOString();
    } catch (error) {
      console.error('Failed to refresh revenue intelligence data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'positive':
        return 'text-green-600 dark:text-green-400';
      case 'negative':
        return 'text-red-600 dark:text-red-400';
      case 'neutral':
        return 'text-gray-600 dark:text-gray-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  return (
    <div class="revenue-intelligence-dashboard space-y-6">
      {/* Dashboard Header with Controls */}
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex items-center gap-4">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Revenue Intelligence Dashboard
          </h2>
          {!isOffline && (
            <button
              type="button"
              onClick={refreshData}
              disabled={isLoading.value}
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <svg 
                class={`w-4 h-4 mr-1.5 ${isLoading.value ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          )}
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Next Month Revenue</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(dashboardMetrics.value.nextMonthRevenue)}
              </p>
            </div>
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Expansion Potential</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(dashboardMetrics.value.expansionPotential)}
              </p>
            </div>
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">At-Risk Revenue</p>
              <p class="text-xl font-bold text-red-600 dark:text-red-400">
                {formatCurrency(dashboardMetrics.value.churnRiskRevenue)}
              </p>
            </div>
            <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Opportunity Score</p>
              <p class="text-xl font-bold text-purple-600 dark:text-purple-400">
                {dashboardMetrics.value.opportunityScore.toFixed(1)}
              </p>
            </div>
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">High Risk Customers</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.highRiskCustomers}
              </p>
            </div>
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="-mb-px flex space-x-8">
          {[
            { id: 'predictions', label: 'Revenue Predictions', icon: 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6' },
            { id: 'opportunities', label: 'Opportunities', icon: 'M7 11l5-5m0 0l5 5m-5-5v12' },
            { id: 'risks', label: 'Risk Analysis', icon: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z' },
            { id: 'churn', label: 'Churn Predictions', icon: 'M13 17h8m0 0V9m0 8l-8-8-4 4-6-6' },
            { id: 'insights', label: 'Strategic Insights', icon: 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z' },
          ].map((tab) => (
            <button
              key={tab.id}
              type="button"
              onClick={() => selectedTab.value = tab.id}
              class={`${
                selectedTab.value === tab.id
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={tab.icon} />
              </svg>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div class="tab-content">
        {selectedTab.value === 'predictions' && (
          <div class="space-y-6">
            {/* Revenue Predictions Overview */}
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Next Month Revenue Breakdown
                </h3>
                {revenueIntelligence.value?.predictions ? (
                  <div class="space-y-4">
                    <div class="text-center">
                      <div class="text-3xl font-bold text-green-600 dark:text-green-400">
                        {formatCurrency(revenueIntelligence.value.predictions.nextMonthRevenue.amount)}
                      </div>
                      <div class="text-sm text-gray-600 dark:text-gray-400">
                        {formatPercentage(revenueIntelligence.value.predictions.nextMonthRevenue.confidence)} confidence
                      </div>
                    </div>

                    <div class="grid grid-cols-3 gap-4 text-center">
                      <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                        <div class="text-lg font-bold text-blue-600 dark:text-blue-400">
                          {formatCurrency(revenueIntelligence.value.predictions.nextMonthRevenue.breakdown.existing)}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Existing</div>
                      </div>
                      <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
                        <div class="text-lg font-bold text-green-600 dark:text-green-400">
                          {formatCurrency(revenueIntelligence.value.predictions.nextMonthRevenue.breakdown.expansion)}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Expansion</div>
                      </div>
                      <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
                        <div class="text-lg font-bold text-purple-600 dark:text-purple-400">
                          {formatCurrency(revenueIntelligence.value.predictions.nextMonthRevenue.breakdown.new)}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">New</div>
                      </div>
                    </div>

                    <div class="mt-4">
                      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Key Factors:</h4>
                      <div class="flex flex-wrap gap-1">
                        {revenueIntelligence.value.predictions.nextMonthRevenue.factors.map((factor, index) => (
                          <span key={index} class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                            {factor.replace('_', ' ')}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div class="text-center py-8">
                    <p class="text-gray-500 dark:text-gray-400">No prediction data available</p>
                  </div>
                )}
              </div>

              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Quarterly Forecast
                </h3>
                {revenueIntelligence.value?.predictions && revenueForecasts.value ? (
                  <div class="space-y-4">
                    <div class="flex items-center justify-between mb-4">
                      <span class="text-sm text-gray-600 dark:text-gray-400">Scenario:</span>
                      <select
                        value={selectedScenario.value}
                        onChange={(e) => selectedScenario.value = (e.target as HTMLSelectElement).value}
                        class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1 text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                      >
                        <option value="conservative">Conservative</option>
                        <option value="optimistic">Optimistic</option>
                        <option value="pessimistic">Pessimistic</option>
                      </select>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                      <div class="text-center">
                        <div class="text-lg font-bold text-gray-900 dark:text-white">
                          {formatCurrency(revenueForecasts.value.scenarios[selectedScenario.value as keyof typeof revenueForecasts.value.scenarios].q1)}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Q1</div>
                      </div>
                      <div class="text-center">
                        <div class="text-lg font-bold text-gray-900 dark:text-white">
                          {formatCurrency(revenueForecasts.value.scenarios[selectedScenario.value as keyof typeof revenueForecasts.value.scenarios].q2)}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Q2</div>
                      </div>
                      <div class="text-center">
                        <div class="text-lg font-bold text-gray-900 dark:text-white">
                          {formatCurrency(revenueForecasts.value.scenarios[selectedScenario.value as keyof typeof revenueForecasts.value.scenarios].q3)}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Q3</div>
                      </div>
                      <div class="text-center">
                        <div class="text-lg font-bold text-gray-900 dark:text-white">
                          {formatCurrency(revenueForecasts.value.scenarios[selectedScenario.value as keyof typeof revenueForecasts.value.scenarios].q4)}
                        </div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">Q4</div>
                      </div>
                    </div>

                    <div class="text-center text-sm text-gray-600 dark:text-gray-400">
                      {formatPercentage(revenueForecasts.value.scenarios[selectedScenario.value as keyof typeof revenueForecasts.value.scenarios].confidence)} confidence
                    </div>
                  </div>
                ) : (
                  <div class="text-center py-8">
                    <p class="text-gray-500 dark:text-gray-400">No forecast data available</p>
                  </div>
                )}
              </div>
            </div>

            {/* Revenue Drivers */}
            {revenueForecasts.value?.drivers && (
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Revenue Drivers
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {revenueForecasts.value.drivers.map((driver, index) => (
                    <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <h4 class="font-medium text-gray-900 dark:text-white mb-2">
                        {driver.driver}
                      </h4>
                      <div class="flex items-center justify-between">
                        <span class="text-lg font-bold text-gray-900 dark:text-white">
                          {formatPercentage(driver.impact)}
                        </span>
                        <span class={`text-sm font-medium ${getImpactColor(driver.trend)}`}>
                          {driver.trend}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {selectedTab.value === 'opportunities' && (
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                Revenue Opportunities
              </h3>
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {revenueIntelligence.value?.opportunities.length || 0} opportunities
              </span>
            </div>

            {revenueIntelligence.value?.opportunities.length ? (
              <div class="space-y-4">
                {revenueIntelligence.value.opportunities.map((opportunity, index) => (
                  <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                      <div class="flex items-center gap-3">
                        <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(opportunity.priority)}`}>
                          {opportunity.priority}
                        </span>
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {opportunity.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </h4>
                      </div>
                      <span class="text-lg font-bold text-green-600 dark:text-green-400">
                        {formatCurrency(opportunity.potentialRevenue)}
                      </span>
                    </div>

                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      {opportunity.description}
                    </p>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-3">
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Customer:</span>
                        <p class="font-medium text-gray-900 dark:text-white">{opportunity.customerId}</p>
                      </div>
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Timeframe:</span>
                        <p class="font-medium text-gray-900 dark:text-white">{opportunity.timeframe}</p>
                      </div>
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Confidence:</span>
                        <p class="font-medium text-gray-900 dark:text-white">{formatPercentage(opportunity.confidence)}</p>
                      </div>
                    </div>

                    <div>
                      <span class="text-sm text-gray-600 dark:text-gray-400 mb-2 block">Key Factors:</span>
                      <div class="flex flex-wrap gap-1">
                        {opportunity.factors.map((factor, factorIndex) => (
                          <span key={factorIndex} class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                            {factor.replace('_', ' ')}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div class="text-center py-8">
                <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12" />
                </svg>
                <p class="text-gray-500 dark:text-gray-400">No opportunities identified</p>
              </div>
            )}
          </div>
        )}

        {selectedTab.value === 'risks' && (
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                Revenue Risk Analysis
              </h3>
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {revenueIntelligence.value?.risks.length || 0} risks identified
              </span>
            </div>

            {revenueIntelligence.value?.risks.length ? (
              <div class="space-y-4">
                {revenueIntelligence.value.risks.map((risk, index) => (
                  <div key={index} class="border border-red-200 dark:border-red-700 rounded-lg p-4 bg-red-50 dark:bg-red-900/10">
                    <div class="flex items-center justify-between mb-3">
                      <div class="flex items-center gap-3">
                        <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(risk.severity)}`}>
                          {risk.severity}
                        </span>
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {risk.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </h4>
                      </div>
                      <span class="text-lg font-bold text-red-600 dark:text-red-400">
                        -{formatCurrency(risk.impact)}
                      </span>
                    </div>

                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      {risk.description}
                    </p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-3">
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Customer:</span>
                        <p class="font-medium text-gray-900 dark:text-white">{risk.customerId}</p>
                      </div>
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Probability:</span>
                        <p class="font-medium text-red-600 dark:text-red-400">{formatPercentage(risk.probability)}</p>
                      </div>
                    </div>

                    <div class="mb-3">
                      <span class="text-sm text-gray-600 dark:text-gray-400 mb-2 block">Risk Factors:</span>
                      <div class="flex flex-wrap gap-1">
                        {risk.factors.map((factor, factorIndex) => (
                          <span key={factorIndex} class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                            {factor.replace('_', ' ')}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div>
                      <span class="text-sm text-gray-600 dark:text-gray-400 mb-2 block">Mitigation Actions:</span>
                      <div class="flex flex-wrap gap-1">
                        {risk.mitigationActions.map((action, actionIndex) => (
                          <span key={actionIndex} class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                            {action.replace('_', ' ')}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div class="text-center py-8">
                <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p class="text-gray-500 dark:text-gray-400">No revenue risks identified</p>
              </div>
            )}
          </div>
        )}

        {selectedTab.value === 'churn' && (
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                Churn Predictions
              </h3>
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {churnPredictions.value.length} customers analyzed
              </span>
            </div>

            {churnPredictions.value.length ? (
              <div class="space-y-4">
                {churnPredictions.value.map((prediction) => (
                  <div key={prediction.subscriptionId} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                      <div class="flex items-center gap-3">
                        <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(prediction.riskLevel)}`}>
                          {prediction.riskLevel} risk
                        </span>
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          Customer: {prediction.customerId}
                        </h4>
                      </div>
                      <span class="text-lg font-bold text-red-600 dark:text-red-400">
                        {formatPercentage(prediction.churnProbability)}
                      </span>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-3">
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Timeline:</span>
                        <p class="font-medium text-gray-900 dark:text-white">{prediction.timeline}</p>
                      </div>
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Confidence:</span>
                        <p class="font-medium text-gray-900 dark:text-white">{formatPercentage(prediction.confidence)}</p>
                      </div>
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Subscription:</span>
                        <p class="font-medium text-gray-900 dark:text-white">{prediction.subscriptionId}</p>
                      </div>
                    </div>

                    <div class="mb-3">
                      <span class="text-sm text-gray-600 dark:text-gray-400 mb-2 block">Risk Factors:</span>
                      <div class="space-y-1">
                        {prediction.factors.map((factor, factorIndex) => (
                          <div key={factorIndex} class="flex items-center justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">
                              {factor.factor.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </span>
                            <div class="flex items-center gap-2">
                              <span class={`font-medium ${factor.impact === 'positive' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                                {factor.impact}
                              </span>
                              <span class="text-gray-500 dark:text-gray-400">
                                ({formatPercentage(factor.weight)})
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <span class="text-sm text-gray-600 dark:text-gray-400 mb-2 block">Recommendations:</span>
                      <div class="flex flex-wrap gap-1">
                        {prediction.recommendations.map((recommendation, recIndex) => (
                          <span key={recIndex} class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                            {recommendation}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div class="text-center py-8">
                <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p class="text-gray-500 dark:text-gray-400">No churn predictions available</p>
              </div>
            )}
          </div>
        )}

        {selectedTab.value === 'insights' && (
          <div class="space-y-6">
            {/* Strategic Insights */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Strategic Insights
              </h3>
              {revenueIntelligence.value?.insights.length ? (
                <div class="space-y-4">
                  {revenueIntelligence.value.insights.map((insight, index) => (
                    <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center gap-3">
                          <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            insight.impact === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                            insight.impact === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                            'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          }`}>
                            {insight.impact} impact
                          </span>
                          <span class="text-sm text-gray-600 dark:text-gray-400">
                            {insight.category}
                          </span>
                        </div>
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                          {formatPercentage(insight.confidence)} confidence
                        </span>
                      </div>

                      <h4 class="font-medium text-gray-900 dark:text-white mb-2">
                        {insight.title}
                      </h4>

                      <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {insight.description}
                      </p>

                      {insight.actionable && (
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                          Actionable
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No insights available</p>
                </div>
              )}
            </div>

            {/* Strategic Recommendations */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Strategic Recommendations
              </h3>
              {revenueIntelligence.value?.recommendations.length ? (
                <div class="space-y-4">
                  {revenueIntelligence.value.recommendations.map((recommendation, index) => (
                    <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center gap-3">
                          <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(recommendation.priority)}`}>
                            {recommendation.priority}
                          </span>
                          <h4 class="font-medium text-gray-900 dark:text-white">
                            {recommendation.title}
                          </h4>
                        </div>
                        <span class="text-lg font-bold text-green-600 dark:text-green-400">
                          +{formatCurrency(recommendation.expectedImpact)}
                        </span>
                      </div>

                      <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        {recommendation.description}
                      </p>

                      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span class="text-gray-600 dark:text-gray-400">Type:</span>
                          <p class="font-medium text-gray-900 dark:text-white">
                            {recommendation.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </p>
                        </div>
                        <div>
                          <span class="text-gray-600 dark:text-gray-400">Timeframe:</span>
                          <p class="font-medium text-gray-900 dark:text-white">{recommendation.timeframe}</p>
                        </div>
                        <div>
                          <span class="text-gray-600 dark:text-gray-400">Effort:</span>
                          <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            recommendation.effort === 'low' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                            recommendation.effort === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                            'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                          }`}>
                            {recommendation.effort}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No recommendations available</p>
                </div>
              )}
            </div>

            {/* Market Analysis */}
            {revenueIntelligence.value?.marketAnalysis && (
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Market Analysis
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">Market Position</h4>
                    <div class="space-y-2">
                      <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Competitive Position:</span>
                        <span class="font-medium text-gray-900 dark:text-white">
                          {revenueIntelligence.value.marketAnalysis.competitivePosition}
                        </span>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Opportunity Score:</span>
                        <span class="font-medium text-green-600 dark:text-green-400">
                          {revenueIntelligence.value.marketAnalysis.opportunityScore}/10
                        </span>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Risk Score:</span>
                        <span class="font-medium text-red-600 dark:text-red-400">
                          {revenueIntelligence.value.marketAnalysis.riskScore}/10
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">Market Trends</h4>
                    <div class="space-y-2">
                      {revenueIntelligence.value.marketAnalysis.marketTrends.map((trend, index) => (
                        <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                          <div class="flex items-center justify-between mb-1">
                            <span class="text-sm font-medium text-gray-900 dark:text-white">
                              {trend.trend}
                            </span>
                            <span class={`text-xs font-medium ${getImpactColor(trend.impact)}`}>
                              {trend.impact}
                            </span>
                          </div>
                          <div class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
                            <span>{trend.timeframe}</span>
                            <span>{formatPercentage(trend.confidence)} confidence</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Last Updated Timestamp */}
      <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
        Last updated: {new Date(lastUpdated.value).toLocaleString()}
        {isOffline && " (Demo Mode)"}
      </div>
    </div>
  );
}
