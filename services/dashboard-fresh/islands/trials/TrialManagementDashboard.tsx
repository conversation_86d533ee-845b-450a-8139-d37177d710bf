// Trial Management Dashboard - Fresh Islands Component
// Comprehensive trial analytics, onboarding progress tracking, and automated workflow management

import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";

interface TrialData {
  id: string;
  customerId: string;
  companyName: string;
  email: string;
  planTier: string;
  startDate: string;
  endDate: string;
  daysRemaining: number;
  healthScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  onboardingProgress: {
    totalSteps: number;
    completedSteps: number;
    currentStep: string;
    completionRate: number;
  };
  usageMetrics: {
    loginCount: number;
    featuresUsed: number;
    dataPointsProcessed: number;
    apiCalls: number;
    lastActivity: string;
  };
  conversionPrediction: {
    probability: number;
    factors: string[];
    confidence: number;
  };
  interventions: Array<{
    type: string;
    scheduledDate: string;
    status: string;
  }>;
}

interface ActiveTrialsData {
  summary: {
    totalActiveTrials: number;
    averageHealthScore: number;
    conversionRate: number;
    averageTrialLength: number;
    onboardingCompletionRate: number;
    atRiskTrials: number;
    highValueTrials: number;
    expiringThisWeek: number;
  };
  trials: TrialData[];
  insights: {
    topConversionFactors: Array<{
      factor: string;
      impact: number;
      description: string;
    }>;
    riskIndicators: Array<{
      indicator: string;
      threshold: string;
      impact: string;
    }>;
    recommendations: Array<{
      type: string;
      title: string;
      description: string;
      expectedImpact: string;
      effort: string;
    }>;
  };
}

interface TrialMetrics {
  conversionTrends: Array<{
    period: string;
    conversions: number;
    trials: number;
    rate: number;
  }>;
  onboardingMetrics: {
    averageCompletionTime: string;
    dropoffPoints: Array<{
      step: string;
      dropoffRate: number;
    }>;
    completionRateByTier: Record<string, number>;
  };
  usagePatterns: {
    averageSessionDuration: string;
    peakUsageHours: string[];
    mostUsedFeatures: Array<{
      feature: string;
      usage: number;
    }>;
  };
  performanceMetrics: {
    averageTimeToValue: string;
    customerSatisfactionScore: number;
    supportTicketsPerTrial: number;
    featureAdoptionRate: number;
  };
}

interface TrialWorkflows {
  activeWorkflows: Array<{
    id: string;
    name: string;
    description: string;
    triggers: string[];
    actions: string[];
    status: string;
    performance: {
      triggered: number;
      completed: number;
      successRate: number;
    };
  }>;
  workflowTemplates: Array<{
    id: string;
    name: string;
    description: string;
    steps: number;
    estimatedDuration: string;
    conversionRate: number;
  }>;
}

interface TrialManagementDashboardProps {
  initialActiveTrials: ActiveTrialsData | null;
  initialTrialMetrics: TrialMetrics | null;
  initialTrialWorkflows: TrialWorkflows | null;
  user: any;
  isOffline?: boolean;
}

export default function TrialManagementDashboard({
  initialActiveTrials,
  initialTrialMetrics,
  initialTrialWorkflows,
  user,
  isOffline = false
}: TrialManagementDashboardProps) {
  // Signals for reactive state management
  const activeTrials = useSignal(initialActiveTrials);
  const trialMetrics = useSignal(initialTrialMetrics);
  const trialWorkflows = useSignal(initialTrialWorkflows);
  const isLoading = useSignal(false);
  const lastUpdated = useSignal(new Date().toISOString());
  const selectedTab = useSignal('overview');
  const selectedTrial = useSignal<string | null>(null);

  // Computed values for dashboard metrics
  const dashboardMetrics = useComputed(() => {
    const trials = activeTrials.value;
    const metrics = trialMetrics.value;
    
    return {
      totalActiveTrials: trials?.summary.totalActiveTrials || 0,
      averageHealthScore: trials?.summary.averageHealthScore || 0,
      conversionRate: trials?.summary.conversionRate || 0,
      onboardingCompletionRate: trials?.summary.onboardingCompletionRate || 0,
      atRiskTrials: trials?.summary.atRiskTrials || 0,
      highValueTrials: trials?.summary.highValueTrials || 0,
      expiringThisWeek: trials?.summary.expiringThisWeek || 0,
      averageTimeToValue: metrics?.performanceMetrics.averageTimeToValue || '0 days'
    };
  });

  // Auto-refresh data every 45 seconds (if not offline)
  useEffect(() => {
    if (isOffline) return;

    const interval = setInterval(async () => {
      if (!isLoading.value) {
        await refreshData();
      }
    }, 45000);

    return () => clearInterval(interval);
  }, [isOffline]);

  const refreshData = async () => {
    if (isOffline) return;
    
    isLoading.value = true;
    try {
      const [trialsData, metricsData, workflowsData] = await Promise.all([
        fetch('/api/billing/trial/active-overview').then(r => r.json()),
        fetch('/api/billing/trial/metrics').then(r => r.json()),
        fetch('/api/billing/trial/workflows').then(r => r.json()),
      ]);

      activeTrials.value = trialsData.data;
      trialMetrics.value = metricsData.data;
      trialWorkflows.value = workflowsData.data;
      lastUpdated.value = new Date().toISOString();
    } catch (error) {
      console.error('Failed to refresh trial management data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400';
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
    if (score >= 40) return 'text-orange-600 dark:text-orange-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getProgressColor = (rate: number) => {
    if (rate >= 80) return 'bg-green-500';
    if (rate >= 60) return 'bg-yellow-500';
    if (rate >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getDaysRemainingColor = (days: number) => {
    if (days <= 3) return 'text-red-600 dark:text-red-400';
    if (days <= 7) return 'text-orange-600 dark:text-orange-400';
    if (days <= 14) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-green-600 dark:text-green-400';
  };

  return (
    <div class="trial-management-dashboard space-y-6">
      {/* Dashboard Header with Controls */}
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex items-center gap-4">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Trial Management Dashboard
          </h2>
          {!isOffline && (
            <button
              type="button"
              onClick={refreshData}
              disabled={isLoading.value}
              class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <svg 
                class={`w-4 h-4 mr-1.5 ${isLoading.value ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          )}
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Active Trials</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.totalActiveTrials}
              </p>
            </div>
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Avg Health Score</p>
              <p class={`text-xl font-bold ${getHealthScoreColor(dashboardMetrics.value.averageHealthScore)}`}>
                {dashboardMetrics.value.averageHealthScore.toFixed(1)}
              </p>
            </div>
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Conversion Rate</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {formatPercentage(dashboardMetrics.value.conversionRate)}
              </p>
            </div>
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Onboarding Rate</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {formatPercentage(dashboardMetrics.value.onboardingCompletionRate)}
              </p>
            </div>
            <div class="w-8 h-8 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">At Risk</p>
              <p class="text-xl font-bold text-red-600 dark:text-red-400">
                {dashboardMetrics.value.atRiskTrials}
              </p>
            </div>
            <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">High Value</p>
              <p class="text-xl font-bold text-green-600 dark:text-green-400">
                {dashboardMetrics.value.highValueTrials}
              </p>
            </div>
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Expiring This Week</p>
              <p class="text-xl font-bold text-orange-600 dark:text-orange-400">
                {dashboardMetrics.value.expiringThisWeek}
              </p>
            </div>
            <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-xs font-medium text-gray-600 dark:text-gray-400">Time to Value</p>
              <p class="text-xl font-bold text-gray-900 dark:text-white">
                {dashboardMetrics.value.averageTimeToValue}
              </p>
            </div>
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
            { id: 'trials', label: 'Active Trials', icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' },
            { id: 'analytics', label: 'Analytics', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
            { id: 'workflows', label: 'Workflows', icon: 'M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z' },
          ].map((tab) => (
            <button
              key={tab.id}
              type="button"
              onClick={() => selectedTab.value = tab.id}
              class={`${
                selectedTab.value === tab.id
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={tab.icon} />
              </svg>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div class="tab-content">
        {selectedTab.value === 'overview' && (
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Conversion Insights */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Top Conversion Factors
              </h3>
              {activeTrials.value?.insights.topConversionFactors.length ? (
                <div class="space-y-3">
                  {activeTrials.value.insights.topConversionFactors.map((factor, index) => (
                    <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                      <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {factor.factor}
                        </h4>
                        <span class="text-sm font-medium text-green-600 dark:text-green-400">
                          +{formatPercentage(factor.impact * 100)} impact
                        </span>
                      </div>
                      <p class="text-sm text-gray-600 dark:text-gray-400">
                        {factor.description}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No conversion factors available</p>
                </div>
              )}
            </div>

            {/* Risk Indicators */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Risk Indicators
              </h3>
              {activeTrials.value?.insights.riskIndicators.length ? (
                <div class="space-y-3">
                  {activeTrials.value.insights.riskIndicators.map((indicator, index) => (
                    <div key={index} class="border border-red-200 dark:border-red-700 rounded-lg p-3 bg-red-50 dark:bg-red-900/10">
                      <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {indicator.indicator}
                        </h4>
                        <span class="text-sm text-red-600 dark:text-red-400">
                          {indicator.threshold}
                        </span>
                      </div>
                      <p class="text-sm text-gray-600 dark:text-gray-400">
                        Impact: {indicator.impact}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No risk indicators available</p>
                </div>
              )}
            </div>

            {/* Recommendations */}
            <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Strategic Recommendations
              </h3>
              {activeTrials.value?.insights.recommendations.length ? (
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {activeTrials.value.insights.recommendations.map((recommendation, index) => (
                    <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div class="flex items-center justify-between mb-3">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {recommendation.title}
                        </h4>
                        <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          recommendation.effort === 'low' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                          recommendation.effort === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                          'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                        }`}>
                          {recommendation.effort}
                        </span>
                      </div>
                      <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        {recommendation.description}
                      </p>
                      <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">
                          Type: {recommendation.type.replace('_', ' ')}
                        </span>
                        <span class="font-medium text-green-600 dark:text-green-400">
                          {recommendation.expectedImpact}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No recommendations available</p>
                </div>
              )}
            </div>
          </div>
        )}

        {selectedTab.value === 'trials' && (
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                Active Trials Management
              </h3>
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {activeTrials.value?.trials.length || 0} active trials
              </span>
            </div>

            {activeTrials.value?.trials.length ? (
              <div class="space-y-4 max-h-96 overflow-y-auto">
                {activeTrials.value.trials.map((trial) => (
                  <div key={trial.id} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                      <div class="flex items-center gap-3">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {trial.companyName}
                        </h4>
                        <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(trial.riskLevel)}`}>
                          {trial.riskLevel} risk
                        </span>
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                          {trial.planTier}
                        </span>
                      </div>
                      <div class="text-right">
                        <div class={`text-lg font-bold ${getDaysRemainingColor(trial.daysRemaining)}`}>
                          {trial.daysRemaining} days left
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                          Health: <span class={getHealthScoreColor(trial.healthScore)}>{trial.healthScore}</span>
                        </div>
                      </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-3">
                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Onboarding Progress:</span>
                        <div class="mt-1">
                          <div class="flex items-center justify-between text-xs mb-1">
                            <span>{trial.onboardingProgress.completedSteps}/{trial.onboardingProgress.totalSteps} steps</span>
                            <span>{formatPercentage(trial.onboardingProgress.completionRate)}</span>
                          </div>
                          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div
                              class={`h-2 rounded-full ${getProgressColor(trial.onboardingProgress.completionRate)}`}
                              style={`width: ${trial.onboardingProgress.completionRate}%`}
                            ></div>
                          </div>
                          <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Current: {trial.onboardingProgress.currentStep.replace('_', ' ')}
                          </div>
                        </div>
                      </div>

                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Usage Metrics:</span>
                        <div class="mt-1 space-y-1">
                          <div class="flex justify-between text-xs">
                            <span>Logins:</span>
                            <span class="font-medium">{trial.usageMetrics.loginCount}</span>
                          </div>
                          <div class="flex justify-between text-xs">
                            <span>Features Used:</span>
                            <span class="font-medium">{trial.usageMetrics.featuresUsed}</span>
                          </div>
                          <div class="flex justify-between text-xs">
                            <span>API Calls:</span>
                            <span class="font-medium">{formatNumber(trial.usageMetrics.apiCalls)}</span>
                          </div>
                          <div class="text-xs text-gray-500 dark:text-gray-400">
                            Last active: {formatDate(trial.usageMetrics.lastActivity)}
                          </div>
                        </div>
                      </div>

                      <div>
                        <span class="text-gray-600 dark:text-gray-400">Conversion Prediction:</span>
                        <div class="mt-1">
                          <div class="text-lg font-bold text-gray-900 dark:text-white">
                            {formatPercentage(trial.conversionPrediction.probability * 100)}
                          </div>
                          <div class="text-xs text-gray-500 dark:text-gray-400">
                            {formatPercentage(trial.conversionPrediction.confidence * 100)} confidence
                          </div>
                          <div class="flex flex-wrap gap-1 mt-2">
                            {trial.conversionPrediction.factors.slice(0, 2).map((factor, index) => (
                              <span key={index} class="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                                {factor.replace('_', ' ')}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>

                    {trial.interventions.length > 0 && (
                      <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                        <h5 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                          Scheduled Interventions:
                        </h5>
                        <div class="space-y-1">
                          {trial.interventions.map((intervention, index) => (
                            <div key={index} class="flex items-center justify-between text-sm">
                              <span class="text-gray-600 dark:text-gray-400">
                                {intervention.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </span>
                              <div class="flex items-center gap-2">
                                <span class="text-gray-500 dark:text-gray-400">
                                  {formatDate(intervention.scheduledDate)}
                                </span>
                                <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                  intervention.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                                  intervention.status === 'scheduled' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                                  'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                                }`}>
                                  {intervention.status}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div class="text-center py-8">
                <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <p class="text-gray-500 dark:text-gray-400">No active trials found</p>
              </div>
            )}
          </div>
        )}

        {selectedTab.value === 'analytics' && (
          <div class="space-y-6">
            {/* Conversion Trends */}
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Conversion Trends
                </h3>
                {trialMetrics.value?.conversionTrends.length ? (
                  <div class="space-y-4">
                    <div class="grid grid-cols-4 gap-4">
                      {trialMetrics.value.conversionTrends.map((trend, index) => (
                        <div key={index} class="text-center">
                          <div class="text-lg font-bold text-gray-900 dark:text-white">
                            {formatPercentage(trend.rate)}
                          </div>
                          <div class="text-sm text-gray-600 dark:text-gray-400">{trend.period}</div>
                          <div class="text-xs text-gray-500 dark:text-gray-400">
                            {trend.conversions}/{trend.trials}
                          </div>
                        </div>
                      ))}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400 text-center">
                      Weekly conversion rate trends
                    </div>
                  </div>
                ) : (
                  <div class="text-center py-8">
                    <p class="text-gray-500 dark:text-gray-400">No conversion trend data available</p>
                  </div>
                )}
              </div>

              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Onboarding Metrics
                </h3>
                {trialMetrics.value?.onboardingMetrics ? (
                  <div class="space-y-4">
                    <div class="text-center">
                      <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {trialMetrics.value.onboardingMetrics.averageCompletionTime}
                      </div>
                      <div class="text-sm text-gray-600 dark:text-gray-400">Average Completion Time</div>
                    </div>

                    <div>
                      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Completion Rate by Tier:</h4>
                      <div class="space-y-2">
                        {Object.entries(trialMetrics.value.onboardingMetrics.completionRateByTier).map(([tier, rate]) => (
                          <div key={tier} class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{tier}:</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">
                              {formatPercentage(rate)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div class="text-center py-8">
                    <p class="text-gray-500 dark:text-gray-400">No onboarding metrics available</p>
                  </div>
                )}
              </div>
            </div>

            {/* Onboarding Dropoff Points */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Onboarding Dropoff Analysis
              </h3>
              {trialMetrics.value?.onboardingMetrics.dropoffPoints.length ? (
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {trialMetrics.value.onboardingMetrics.dropoffPoints.map((point, index) => (
                    <div key={index} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <h4 class="font-medium text-gray-900 dark:text-white mb-2">
                        {point.step}
                      </h4>
                      <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                        {formatPercentage(point.dropoffRate)}
                      </div>
                      <div class="text-sm text-gray-600 dark:text-gray-400">dropoff rate</div>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <p class="text-gray-500 dark:text-gray-400">No dropoff data available</p>
                </div>
              )}
            </div>

            {/* Usage Patterns and Performance Metrics */}
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Usage Patterns
                </h3>
                {trialMetrics.value?.usagePatterns ? (
                  <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4 text-center">
                      <div>
                        <div class="text-lg font-bold text-gray-900 dark:text-white">
                          {trialMetrics.value.usagePatterns.averageSessionDuration}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Avg Session</div>
                      </div>
                      <div>
                        <div class="text-lg font-bold text-gray-900 dark:text-white">
                          {trialMetrics.value.usagePatterns.peakUsageHours.length}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Peak Hours</div>
                      </div>
                    </div>

                    <div>
                      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Peak Usage Times:</h4>
                      <div class="flex flex-wrap gap-1">
                        {trialMetrics.value.usagePatterns.peakUsageHours.map((hour, index) => (
                          <span key={index} class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                            {hour}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Most Used Features:</h4>
                      <div class="space-y-2">
                        {trialMetrics.value.usagePatterns.mostUsedFeatures.map((feature, index) => (
                          <div key={index} class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{feature.feature}:</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">
                              {formatPercentage(feature.usage)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div class="text-center py-8">
                    <p class="text-gray-500 dark:text-gray-400">No usage pattern data available</p>
                  </div>
                )}
              </div>

              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Performance Metrics
                </h3>
                {trialMetrics.value?.performanceMetrics ? (
                  <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                      <div class="text-center">
                        <div class="text-lg font-bold text-green-600 dark:text-green-400">
                          {trialMetrics.value.performanceMetrics.averageTimeToValue}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Time to Value</div>
                      </div>
                      <div class="text-center">
                        <div class="text-lg font-bold text-blue-600 dark:text-blue-400">
                          {trialMetrics.value.performanceMetrics.customerSatisfactionScore}/10
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">CSAT Score</div>
                      </div>
                      <div class="text-center">
                        <div class="text-lg font-bold text-purple-600 dark:text-purple-400">
                          {trialMetrics.value.performanceMetrics.supportTicketsPerTrial}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Tickets/Trial</div>
                      </div>
                      <div class="text-center">
                        <div class="text-lg font-bold text-indigo-600 dark:text-indigo-400">
                          {formatPercentage(trialMetrics.value.performanceMetrics.featureAdoptionRate)}
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Feature Adoption</div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div class="text-center py-8">
                    <p class="text-gray-500 dark:text-gray-400">No performance metrics available</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {selectedTab.value === 'workflows' && (
          <div class="space-y-6">
            {/* Active Workflows */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                  Active Workflows
                </h3>
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {trialWorkflows.value?.activeWorkflows.length || 0} active workflows
                </span>
              </div>

              {trialWorkflows.value?.activeWorkflows.length ? (
                <div class="space-y-4">
                  {trialWorkflows.value.activeWorkflows.map((workflow) => (
                    <div key={workflow.id} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center gap-3">
                          <h4 class="font-medium text-gray-900 dark:text-white">
                            {workflow.name}
                          </h4>
                          <span class={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            workflow.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                            'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                          }`}>
                            {workflow.status}
                          </span>
                        </div>
                        <div class="text-right">
                          <div class="text-lg font-bold text-gray-900 dark:text-white">
                            {formatPercentage(workflow.performance.successRate)}
                          </div>
                          <div class="text-sm text-gray-600 dark:text-gray-400">success rate</div>
                        </div>
                      </div>

                      <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        {workflow.description}
                      </p>

                      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-3">
                        <div>
                          <span class="text-gray-600 dark:text-gray-400">Triggers:</span>
                          <div class="mt-1 flex flex-wrap gap-1">
                            {workflow.triggers.map((trigger, index) => (
                              <span key={index} class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                                {trigger.replace('_', ' ')}
                              </span>
                            ))}
                          </div>
                        </div>

                        <div>
                          <span class="text-gray-600 dark:text-gray-400">Actions:</span>
                          <div class="mt-1 flex flex-wrap gap-1">
                            {workflow.actions.map((action, index) => (
                              <span key={index} class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                                {action.replace('_', ' ')}
                              </span>
                            ))}
                          </div>
                        </div>

                        <div>
                          <span class="text-gray-600 dark:text-gray-400">Performance:</span>
                          <div class="mt-1 space-y-1">
                            <div class="flex justify-between text-xs">
                              <span>Triggered:</span>
                              <span class="font-medium">{workflow.performance.triggered}</span>
                            </div>
                            <div class="flex justify-between text-xs">
                              <span>Completed:</span>
                              <span class="font-medium">{workflow.performance.completed}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                  </svg>
                  <p class="text-gray-500 dark:text-gray-400">No active workflows found</p>
                </div>
              )}
            </div>

            {/* Workflow Templates */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6">
              <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                  Workflow Templates
                </h3>
                <span class="text-sm text-gray-600 dark:text-gray-400">
                  {trialWorkflows.value?.workflowTemplates.length || 0} templates available
                </span>
              </div>

              {trialWorkflows.value?.workflowTemplates.length ? (
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {trialWorkflows.value.workflowTemplates.map((template) => (
                    <div key={template.id} class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div class="flex items-center justify-between mb-3">
                        <h4 class="font-medium text-gray-900 dark:text-white">
                          {template.name}
                        </h4>
                        <span class="text-sm font-medium text-green-600 dark:text-green-400">
                          {formatPercentage(template.conversionRate)} conversion
                        </span>
                      </div>

                      <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        {template.description}
                      </p>

                      <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span class="text-gray-600 dark:text-gray-400">Steps:</span>
                          <p class="font-medium text-gray-900 dark:text-white">{template.steps}</p>
                        </div>
                        <div>
                          <span class="text-gray-600 dark:text-gray-400">Duration:</span>
                          <p class="font-medium text-gray-900 dark:text-white">{template.estimatedDuration}</p>
                        </div>
                      </div>

                      <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                        <button
                          type="button"
                          class="w-full text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 font-medium"
                        >
                          Use Template
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div class="text-center py-8">
                  <svg class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <p class="text-gray-500 dark:text-gray-400">No workflow templates available</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Last Updated Timestamp */}
      <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
        Last updated: {new Date(lastUpdated.value).toLocaleString()}
        {isOffline && " (Demo Mode)"}
      </div>
    </div>
  );
}
