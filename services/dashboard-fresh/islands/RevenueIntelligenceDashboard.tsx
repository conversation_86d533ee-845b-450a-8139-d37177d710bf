import { useEffect, useState } from "preact/hooks";
import { signal } from "@preact/signals";
import { RevenueMetricsCard } from "../components/RevenueMetricsCard.tsx";
import { CustomerHealthOverview } from "../components/CustomerHealthOverview.tsx";
import { ChurnRiskAlert } from "../components/ChurnRiskAlert.tsx";
import { ExpansionOpportunities } from "../components/ExpansionOpportunities.tsx";
import { RevenueForecasting } from "../components/RevenueForecasting.tsx";
import { PricingRecommendations } from "../components/PricingRecommendations.tsx";

// Revenue Intelligence Dashboard Island Component
// Implements comprehensive revenue optimization dashboard with real-time analytics

interface RevenueMetrics {
  totalRevenue: number;
  recurringRevenue: number;
  oneTimeRevenue: number;
  averageRevenuePerUser: number;
  monthlyRecurringRevenue: number;
  annualRecurringRevenue: number;
  revenueGrowthRate: number;
  customerLifetimeValue: number;
  revenuePerCustomer: number;
  conversionRate: number;
  churnRate: number;
  expansionRevenue: number;
  contractionRevenue: number;
  netRevenueRetention: number;
}

interface CustomerHealthSummary {
  averageScore: number;
  totalCustomers: number;
  riskDistribution: Record<string, number>;
}

interface ChurnRiskSummary {
  totalAtRisk: number;
  revenueAtRisk: number;
  riskDistribution: Record<string, number>;
  criticalCustomers: number;
  averageChurnProbability: number;
}

interface ExpansionSummary {
  totalOpportunities: number;
  potentialRevenue: number;
  highConfidenceOpportunities: number;
}

interface DashboardData {
  revenue: RevenueMetrics;
  customerHealth: CustomerHealthSummary;
  churnRisk: ChurnRiskSummary;
  expansion: ExpansionSummary;
}

// Global signals for real-time updates
const dashboardData = signal<DashboardData | null>(null);
const isLoading = signal<boolean>(true);
const error = signal<string | null>(null);
const lastUpdated = signal<string>("");

export default function RevenueIntelligenceDashboard() {
  const [timeRange, setTimeRange] = useState("30d");
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 seconds

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await fetch(`/api/analytics/revenue-analytics/dashboard?timeRange=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        dashboardData.value = result.data;
        lastUpdated.value = new Date().toLocaleTimeString();
      } else {
        throw new Error(result.error || 'Failed to fetch dashboard data');
      }
    } catch (err) {
      console.error('Dashboard data fetch error:', err);
      error.value = err instanceof Error ? err.message : 'Unknown error occurred';
    } finally {
      isLoading.value = false;
    }
  };

  // Auto-refresh functionality
  useEffect(() => {
    fetchDashboardData();

    let intervalId: number | undefined;
    
    if (autoRefresh) {
      intervalId = setInterval(fetchDashboardData, refreshInterval);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [timeRange, autoRefresh, refreshInterval]);

  // Handle time range change
  const handleTimeRangeChange = (newTimeRange: string) => {
    setTimeRange(newTimeRange);
  };

  // Handle refresh settings
  const handleRefreshToggle = () => {
    setAutoRefresh(!autoRefresh);
  };

  const handleRefreshIntervalChange = (interval: number) => {
    setRefreshInterval(interval);
  };

  // Manual refresh
  const handleManualRefresh = () => {
    fetchDashboardData();
  };

  if (isLoading.value && !dashboardData.value) {
    return (
      <div class="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p class="text-gray-600 dark:text-gray-400">Loading Revenue Intelligence Dashboard...</p>
        </div>
      </div>
    );
  }

  if (error.value) {
    return (
      <div class="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div class="text-center max-w-md">
          <div class="bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded mb-4">
            <strong class="font-bold">Error: </strong>
            <span class="block sm:inline">{error.value}</span>
          </div>
          <button
            onClick={handleManualRefresh}
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const data = dashboardData.value;
  if (!data) return null;

  return (
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      {/* Header */}
      <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
              Revenue Intelligence Dashboard
            </h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">
              Comprehensive revenue optimization and growth analytics
            </p>
          </div>
          
          {/* Controls */}
          <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-4">
            {/* Time Range Selector */}
            <select
              value={timeRange}
              onChange={(e) => handleTimeRangeChange(e.currentTarget.value)}
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>

            {/* Auto-refresh Controls */}
            <div class="flex items-center gap-2">
              <button
                onClick={handleRefreshToggle}
                class={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  autoRefresh
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
                }`}
              >
                Auto-refresh: {autoRefresh ? 'ON' : 'OFF'}
              </button>
              
              <button
                onClick={handleManualRefresh}
                disabled={isLoading.value}
                class="px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-md text-sm font-medium transition-colors"
              >
                {isLoading.value ? 'Refreshing...' : 'Refresh'}
              </button>
            </div>
          </div>
        </div>

        {/* Last Updated */}
        {lastUpdated.value && (
          <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Last updated: {lastUpdated.value}
          </div>
        )}
      </div>

      {/* Main Dashboard Grid */}
      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
        {/* Revenue Metrics Overview */}
        <div class="xl:col-span-2">
          <RevenueMetricsCard 
            metrics={data.revenue}
            timeRange={timeRange}
            isLoading={isLoading.value}
          />
        </div>

        {/* Customer Health Overview */}
        <div>
          <CustomerHealthOverview 
            healthData={data.customerHealth}
            isLoading={isLoading.value}
          />
        </div>
      </div>

      {/* Secondary Metrics Grid */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Churn Risk Alerts */}
        <ChurnRiskAlert 
          churnData={data.churnRisk}
          isLoading={isLoading.value}
        />

        {/* Expansion Opportunities */}
        <ExpansionOpportunities 
          expansionData={data.expansion}
          isLoading={isLoading.value}
        />
      </div>

      {/* Advanced Analytics Grid */}
      <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Revenue Forecasting */}
        <RevenueForecasting 
          timeRange={timeRange}
          isLoading={isLoading.value}
        />

        {/* Pricing Recommendations */}
        <PricingRecommendations 
          isLoading={isLoading.value}
        />
      </div>

      {/* Real-time Status Indicator */}
      <div class="fixed bottom-4 right-4">
        <div class={`flex items-center gap-2 px-3 py-2 rounded-full text-sm font-medium ${
          autoRefresh && !isLoading.value
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            : isLoading.value
            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
            : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
        }`}>
          <div class={`w-2 h-2 rounded-full ${
            autoRefresh && !isLoading.value
              ? 'bg-green-500 animate-pulse'
              : isLoading.value
              ? 'bg-blue-500 animate-spin'
              : 'bg-gray-500'
          }`}></div>
          {isLoading.value ? 'Updating...' : autoRefresh ? 'Live' : 'Paused'}
        </div>
      </div>
    </div>
  );
}
