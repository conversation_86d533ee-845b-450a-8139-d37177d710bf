// Revenue Operations Dashboard Island
// Comprehensive dashboard combining subscription management with revenue intelligence and financial reporting

import { useEffect, useState, useRef } from "preact/hooks";
import { signal } from "@preact/signals";
import * as d3 from "https://cdn.skypack.dev/d3@7";

// Dashboard state signals
const dashboardData = signal(null);
const selectedSubscription = signal(null);
const alertsData = signal([]);
const isLoading = signal(true);
const lastUpdated = signal(new Date());

interface RevenueOperationsDashboardProps {
  tenantId: string;
  initialData?: any;
}

interface DashboardSummary {
  totalSubscriptions: number;
  totalMRR: number;
  totalARR: number;
  averageHealthScore: number;
  churnRate: number;
  expansionRate: number;
  totalExpansionOpportunities: number;
  totalRisks: number;
  potentialExpansionRevenue: number;
  atRiskRevenue: number;
}

interface UnifiedSubscriptionData {
  subscription: any;
  usageAnalytics: any;
  churnPrediction: any;
  expansionOpportunities: any[];
  revenueIntelligence: any;
  financialMetrics: any;
  healthScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

export default function RevenueOperationsDashboard({ tenantId, initialData }: RevenueOperationsDashboardProps) {
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 seconds
  const [autoRefresh, setAutoRefresh] = useState(true);
  const intervalRef = useRef<number>();

  // D3 chart refs
  const mrrTrendRef = useRef<SVGSVGElement>(null);
  const healthScoreRef = useRef<SVGSVGElement>(null);
  const riskDistributionRef = useRef<SVGSVGElement>(null);
  const expansionOpportunitiesRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (initialData) {
      dashboardData.value = initialData;
      isLoading.value = false;
    } else {
      loadDashboardData();
    }

    if (autoRefresh) {
      startAutoRefresh();
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [tenantId, autoRefresh, refreshInterval]);

  useEffect(() => {
    if (dashboardData.value) {
      renderCharts();
    }
  }, [dashboardData.value]);

  const loadDashboardData = async () => {
    try {
      isLoading.value = true;
      
      // Load dashboard data
      const dashboardResponse = await fetch(`/api/enhanced-subscriptions/revenue-operations-dashboard`, {
        headers: { 'X-Tenant-ID': tenantId },
      });
      const dashboardResult = await dashboardResponse.json();

      // Load health monitoring alerts
      const alertsResponse = await fetch(`/api/enhanced-subscriptions/health-monitoring`, {
        headers: { 'X-Tenant-ID': tenantId },
      });
      const alertsResult = await alertsResponse.json();

      if (dashboardResult.success && alertsResult.success) {
        dashboardData.value = dashboardResult.data.dashboard;
        alertsData.value = alertsResult.data.alerts;
        lastUpdated.value = new Date();
      } else {
        console.error('Failed to load dashboard data:', dashboardResult.error || alertsResult.error);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const startAutoRefresh = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    intervalRef.current = setInterval(() => {
      loadDashboardData();
    }, refreshInterval);
  };

  const renderCharts = () => {
    if (!dashboardData.value) return;

    renderMRRTrend();
    renderHealthScoreDistribution();
    renderRiskDistribution();
    renderExpansionOpportunities();
  };

  const renderMRRTrend = () => {
    if (!mrrTrendRef.current || !dashboardData.value.trends) return;

    const svg = d3.select(mrrTrendRef.current);
    svg.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 70 };
    const width = 400 - margin.left - margin.right;
    const height = 200 - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const data = dashboardData.value.trends.mrrGrowth.map((value: number, index: number) => ({
      month: index,
      value,
    }));

    const xScale = d3.scaleLinear()
      .domain([0, data.length - 1])
      .range([0, width]);

    const yScale = d3.scaleLinear()
      .domain(d3.extent(data, d => d.value) as [number, number])
      .range([height, 0]);

    const line = d3.line<any>()
      .x(d => xScale(d.month))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale).tickFormat(d => `M${d + 1}`));

    g.append("g")
      .call(d3.axisLeft(yScale).tickFormat(d => `$${(d / 1000).toFixed(0)}K`));

    // Add line
    g.append("path")
      .datum(data)
      .attr("fill", "none")
      .attr("stroke", "#3b82f6")
      .attr("stroke-width", 2)
      .attr("d", line);

    // Add dots
    g.selectAll(".dot")
      .data(data)
      .enter().append("circle")
      .attr("class", "dot")
      .attr("cx", d => xScale(d.month))
      .attr("cy", d => yScale(d.value))
      .attr("r", 4)
      .attr("fill", "#3b82f6");
  };

  const renderHealthScoreDistribution = () => {
    if (!healthScoreRef.current || !dashboardData.value.subscriptions) return;

    const svg = d3.select(healthScoreRef.current);
    svg.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 40 };
    const width = 300 - margin.left - margin.right;
    const height = 200 - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Group subscriptions by health score ranges
    const healthRanges = [
      { range: "90-100", min: 90, max: 100, color: "#10b981" },
      { range: "70-89", min: 70, max: 89, color: "#f59e0b" },
      { range: "50-69", min: 50, max: 69, color: "#f97316" },
      { range: "0-49", min: 0, max: 49, color: "#ef4444" },
    ];

    const data = healthRanges.map(range => ({
      range: range.range,
      count: dashboardData.value.subscriptions.filter((sub: UnifiedSubscriptionData) => 
        sub.healthScore >= range.min && sub.healthScore <= range.max
      ).length,
      color: range.color,
    }));

    const xScale = d3.scaleBand()
      .domain(data.map(d => d.range))
      .range([0, width])
      .padding(0.1);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.count) as number])
      .range([height, 0]);

    // Add bars
    g.selectAll(".bar")
      .data(data)
      .enter().append("rect")
      .attr("class", "bar")
      .attr("x", d => xScale(d.range)!)
      .attr("width", xScale.bandwidth())
      .attr("y", d => yScale(d.count))
      .attr("height", d => height - yScale(d.count))
      .attr("fill", d => d.color);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale));

    g.append("g")
      .call(d3.axisLeft(yScale));
  };

  const renderRiskDistribution = () => {
    if (!riskDistributionRef.current || !dashboardData.value.subscriptions) return;

    const svg = d3.select(riskDistributionRef.current);
    svg.selectAll("*").remove();

    const width = 200;
    const height = 200;
    const radius = Math.min(width, height) / 2;

    const g = svg.append("g")
      .attr("transform", `translate(${width / 2},${height / 2})`);

    const riskLevels = ['low', 'medium', 'high', 'critical'];
    const colors = ['#10b981', '#f59e0b', '#f97316', '#ef4444'];

    const data = riskLevels.map((level, index) => ({
      level,
      count: dashboardData.value.subscriptions.filter((sub: UnifiedSubscriptionData) => sub.riskLevel === level).length,
      color: colors[index],
    }));

    const pie = d3.pie<any>().value(d => d.count);
    const arc = d3.arc<any>().innerRadius(0).outerRadius(radius - 10);

    const arcs = g.selectAll(".arc")
      .data(pie(data))
      .enter().append("g")
      .attr("class", "arc");

    arcs.append("path")
      .attr("d", arc)
      .attr("fill", d => d.data.color);

    arcs.append("text")
      .attr("transform", d => `translate(${arc.centroid(d)})`)
      .attr("dy", "0.35em")
      .style("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", "white")
      .text(d => d.data.count > 0 ? d.data.count : '');
  };

  const renderExpansionOpportunities = () => {
    if (!expansionOpportunitiesRef.current || !dashboardData.value.subscriptions) return;

    const svg = d3.select(expansionOpportunitiesRef.current);
    svg.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 70 };
    const width = 400 - margin.left - margin.right;
    const height = 200 - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Get top 10 expansion opportunities
    const opportunities = dashboardData.value.subscriptions
      .flatMap((sub: UnifiedSubscriptionData) => 
        sub.expansionOpportunities.map(opp => ({
          ...opp,
          subscriptionId: sub.subscription.id,
          expectedValue: opp.potentialValue * opp.probability,
        }))
      )
      .sort((a, b) => b.expectedValue - a.expectedValue)
      .slice(0, 10);

    if (opportunities.length === 0) return;

    const xScale = d3.scaleLinear()
      .domain([0, d3.max(opportunities, d => d.expectedValue) as number])
      .range([0, width]);

    const yScale = d3.scaleBand()
      .domain(opportunities.map((_, i) => i.toString()))
      .range([0, height])
      .padding(0.1);

    // Add bars
    g.selectAll(".bar")
      .data(opportunities)
      .enter().append("rect")
      .attr("class", "bar")
      .attr("x", 0)
      .attr("y", (_, i) => yScale(i.toString())!)
      .attr("width", d => xScale(d.expectedValue))
      .attr("height", yScale.bandwidth())
      .attr("fill", "#3b82f6");

    // Add value labels
    g.selectAll(".label")
      .data(opportunities)
      .enter().append("text")
      .attr("class", "label")
      .attr("x", d => xScale(d.expectedValue) + 5)
      .attr("y", (_, i) => yScale(i.toString())! + yScale.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("font-size", "10px")
      .text(d => `$${(d.expectedValue / 1000).toFixed(1)}K`);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale).tickFormat(d => `$${(d / 1000).toFixed(0)}K`));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getAlertSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-blue-600 bg-blue-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (isLoading.value) {
    return (
      <div class="flex items-center justify-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span class="ml-3 text-gray-600">Loading revenue operations dashboard...</span>
      </div>
    );
  }

  if (!dashboardData.value) {
    return (
      <div class="text-center py-12">
        <p class="text-gray-500">No dashboard data available</p>
        <button
          onClick={loadDashboardData}
          class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  const summary: DashboardSummary = dashboardData.value.summary;

  return (
    <div class="space-y-6">
      {/* Header */}
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            Revenue Operations Dashboard
          </h1>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            Last updated: {lastUpdated.value.toLocaleTimeString()}
          </p>
        </div>
        <div class="flex items-center space-x-4">
          <label class="flex items-center">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh((e.target as HTMLInputElement).checked)}
              class="mr-2"
            />
            <span class="text-sm text-gray-600 dark:text-gray-300">Auto-refresh</span>
          </label>
          <button
            onClick={loadDashboardData}
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total MRR</p>
              <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                {formatCurrency(summary.totalMRR)}
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
              <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Health Score</p>
              <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                {summary.averageHealthScore.toFixed(1)}
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
              <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">At Risk Revenue</p>
              <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                {formatCurrency(summary.atRiskRevenue)}
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
              <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Expansion Revenue</p>
              <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                {formatCurrency(summary.potentialExpansionRevenue)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">MRR Trend</h3>
          <svg ref={mrrTrendRef} width="400" height="200"></svg>
        </div>

        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Health Score Distribution</h3>
          <svg ref={healthScoreRef} width="300" height="200"></svg>
        </div>

        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Risk Distribution</h3>
          <svg ref={riskDistributionRef} width="200" height="200"></svg>
        </div>

        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Top Expansion Opportunities</h3>
          <svg ref={expansionOpportunitiesRef} width="400" height="200"></svg>
        </div>
      </div>

      {/* Alerts Section */}
      {alertsData.value && alertsData.value.length > 0 && (
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Real-time Alerts ({alertsData.value.length})
          </h3>
          <div class="space-y-3">
            {alertsData.value.slice(0, 5).map((alert: any, index: number) => (
              <div key={index} class="flex items-center justify-between p-3 border rounded-lg">
                <div class="flex items-center space-x-3">
                  <span class={`px-2 py-1 text-xs font-medium rounded-full ${getAlertSeverityColor(alert.severity)}`}>
                    {alert.severity}
                  </span>
                  <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{alert.message}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                      Subscription: {alert.subscriptionId}
                    </p>
                  </div>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  {new Date(alert.createdAt).toLocaleTimeString()}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Subscriptions Table */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            Subscription Overview ({summary.totalSubscriptions})
          </h3>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Subscription
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Health Score
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Risk Level
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  MRR
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Opportunities
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {dashboardData.value.subscriptions.slice(0, 10).map((sub: UnifiedSubscriptionData, index: number) => (
                <tr key={index} class="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                      {sub.subscription.id}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      {sub.subscription.customerId}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">
                      {sub.healthScore.toFixed(1)}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class={`px-2 py-1 text-xs font-medium rounded-full ${getRiskLevelColor(sub.riskLevel)}`}>
                      {sub.riskLevel}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {formatCurrency(sub.subscription.mrr)}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {sub.expansionOpportunities.length}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => selectedSubscription.value = sub}
                      class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
