// Enhanced ROI Calculator Island
// Interactive ROI calculator with industry-specific templates and real-time calculations

import { useEffect, useState, useRef } from "preact/hooks";
import { signal } from "@preact/signals";
import * as d3 from "https://cdn.skypack.dev/d3@7";

// ROI Calculator state signals
const roiData = signal(null);
const industryTemplates = signal([]);
const performanceBenchmarks = signal([]);
const selectedIndustry = signal("ecommerce");
const calculationResults = signal(null);

interface EnhancedROICalculatorProps {
  initialData?: any;
  mode?: 'interactive' | 'presentation' | 'report';
}

interface CustomerInputs {
  companyName: string;
  industry: string;
  monthlyEvents: number;
  currentAnalyticsCost: number;
  teamSize: number;
  currentQueryTime: number;
  painPoints: string[];
  goals: string[];
}

export default function EnhancedROICalculator({ initialData, mode = 'interactive' }: EnhancedROICalculatorProps) {
  const [customerInputs, setCustomerInputs] = useState<CustomerInputs>({
    companyName: "",
    industry: "ecommerce",
    monthlyEvents: 1000000,
    currentAnalyticsCost: 5000,
    teamSize: 10,
    currentQueryTime: 2500,
    painPoints: [],
    goals: []
  });

  const [isCalculating, setIsCalculating] = useState(false);
  const [showReport, setShowReport] = useState(false);

  // D3 chart refs
  const roiChartRef = useRef<SVGSVGElement>(null);
  const comparisonChartRef = useRef<SVGSVGElement>(null);
  const savingsBreakdownRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    loadIndustryTemplates();
    loadPerformanceBenchmarks();
    if (initialData) {
      roiData.value = initialData;
    }
  }, []);

  useEffect(() => {
    if (calculationResults.value) {
      renderCharts();
    }
  }, [calculationResults.value]);

  const loadIndustryTemplates = async () => {
    try {
      const response = await fetch('/api/demo/industry-templates');
      if (response.ok) {
        const result = await response.json();
        industryTemplates.value = result.data.templates;
      }
    } catch (error) {
      console.error("Failed to load industry templates:", error);
    }
  };

  const loadPerformanceBenchmarks = async () => {
    try {
      const response = await fetch('/api/demo/performance-benchmarks');
      if (response.ok) {
        const result = await response.json();
        performanceBenchmarks.value = result.data.benchmarks;
      }
    } catch (error) {
      console.error("Failed to load performance benchmarks:", error);
    }
  };

  const calculateROI = async () => {
    setIsCalculating(true);
    try {
      const response = await fetch('/api/demo/roi-calculator', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customerInputs),
      });

      if (response.ok) {
        const result = await response.json();
        calculationResults.value = result.data;
      }
    } catch (error) {
      console.error("Failed to calculate ROI:", error);
    } finally {
      setIsCalculating(false);
    }
  };

  const renderCharts = () => {
    renderROIChart();
    renderComparisonChart();
    renderSavingsBreakdown();
  };

  const renderROIChart = () => {
    if (!roiChartRef.current || !calculationResults.value) return;

    const svg = d3.select(roiChartRef.current);
    svg.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 70 };
    const width = 400 - margin.left - margin.right;
    const height = 300 - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const data = [
      { category: "Current Cost", value: customerInputs.currentAnalyticsCost, color: "#ef4444" },
      { category: "Our Cost", value: Math.min(customerInputs.currentAnalyticsCost * 0.4, 4999), color: "#f59e0b" },
      { category: "Monthly Value", value: calculationResults.value.revenueImpact.summary.totalMonthlyValue, color: "#10b981" }
    ];

    const xScale = d3.scaleBand()
      .domain(data.map(d => d.category))
      .range([0, width])
      .padding(0.1);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.value) as number])
      .range([height, 0]);

    // Add bars
    g.selectAll(".bar")
      .data(data)
      .enter().append("rect")
      .attr("class", "bar")
      .attr("x", d => xScale(d.category)!)
      .attr("width", xScale.bandwidth())
      .attr("y", d => yScale(d.value))
      .attr("height", d => height - yScale(d.value))
      .attr("fill", d => d.color)
      .attr("rx", 4);

    // Add value labels
    g.selectAll(".label")
      .data(data)
      .enter().append("text")
      .attr("class", "label")
      .attr("x", d => xScale(d.category)! + xScale.bandwidth() / 2)
      .attr("y", d => yScale(d.value) - 5)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .text(d => `$${(d.value / 1000).toFixed(0)}K`);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale));

    g.append("g")
      .call(d3.axisLeft(yScale).tickFormat(d => `$${(d / 1000).toFixed(0)}K`));
  };

  const renderComparisonChart = () => {
    if (!comparisonChartRef.current || !performanceBenchmarks.value) return;

    const svg = d3.select(comparisonChartRef.current);
    svg.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 100 };
    const width = 500 - margin.left - margin.right;
    const height = 250 - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const competitors = performanceBenchmarks.value.filter(b => b.competitor !== "Our Platform");
    const ourPlatform = performanceBenchmarks.value.find(b => b.competitor === "Our Platform");

    if (!ourPlatform) return;

    const data = competitors.map(comp => ({
      name: comp.competitor,
      queryTime: comp.metrics.queryResponseTime,
      improvement: ((comp.metrics.queryResponseTime - ourPlatform.metrics.queryResponseTime) / comp.metrics.queryResponseTime * 100).toFixed(1)
    }));

    const xScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.queryTime) as number])
      .range([0, width]);

    const yScale = d3.scaleBand()
      .domain(data.map(d => d.name))
      .range([0, height])
      .padding(0.1);

    // Add bars
    g.selectAll(".bar")
      .data(data)
      .enter().append("rect")
      .attr("class", "bar")
      .attr("x", 0)
      .attr("y", d => yScale(d.name)!)
      .attr("width", d => xScale(d.queryTime))
      .attr("height", yScale.bandwidth())
      .attr("fill", "#ef4444")
      .attr("rx", 4);

    // Add our platform bar
    g.append("rect")
      .attr("x", 0)
      .attr("y", height + 10)
      .attr("width", xScale(ourPlatform.metrics.queryResponseTime))
      .attr("height", yScale.bandwidth())
      .attr("fill", "#10b981")
      .attr("rx", 4);

    // Add improvement labels
    g.selectAll(".improvement")
      .data(data)
      .enter().append("text")
      .attr("class", "improvement")
      .attr("x", d => xScale(d.queryTime) + 5)
      .attr("y", d => yScale(d.name)! + yScale.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("font-size", "11px")
      .style("font-weight", "bold")
      .style("fill", "#059669")
      .text(d => `${d.improvement}% faster`);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale).tickFormat(d => `${d}ms`));

    g.append("g")
      .call(d3.axisLeft(yScale));

    // Add our platform label
    g.append("text")
      .attr("x", -90)
      .attr("y", height + 10 + yScale.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .text("Our Platform");
  };

  const renderSavingsBreakdown = () => {
    if (!savingsBreakdownRef.current || !calculationResults.value) return;

    const svg = d3.select(savingsBreakdownRef.current);
    svg.selectAll("*").remove();

    const width = 300;
    const height = 300;
    const radius = Math.min(width, height) / 2 - 20;

    const g = svg.append("g")
      .attr("transform", `translate(${width / 2},${height / 2})`);

    const savings = calculationResults.value.revenueImpact.projectedSavings;
    const revenues = calculationResults.value.revenueImpact.revenueGains;

    const data = [
      { category: "Cost Savings", value: Object.values(savings).reduce((a, b) => a + b, 0), color: "#3b82f6" },
      { category: "Revenue Gains", value: Object.values(revenues).reduce((a, b) => a + b, 0), color: "#10b981" }
    ];

    const pie = d3.pie<any>()
      .value(d => d.value)
      .sort(null);

    const arc = d3.arc<any>()
      .innerRadius(radius * 0.4)
      .outerRadius(radius);

    const arcs = g.selectAll(".arc")
      .data(pie(data))
      .enter().append("g")
      .attr("class", "arc");

    arcs.append("path")
      .attr("d", arc)
      .attr("fill", d => d.data.color);

    arcs.append("text")
      .attr("transform", d => `translate(${arc.centroid(d)})`)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .style("fill", "white")
      .text(d => `$${(d.data.value / 1000).toFixed(0)}K`);
  };

  const generateReport = () => {
    setShowReport(true);
  };

  return (
    <div class="bg-white rounded-lg shadow-lg p-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900">Enhanced ROI Calculator</h2>
        <div class="flex space-x-2">
          <button
            onClick={calculateROI}
            disabled={isCalculating}
            class="px-4 py-2 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            {isCalculating ? 'Calculating...' : 'Calculate ROI'}
          </button>
          {calculationResults.value && (
            <button
              onClick={generateReport}
              class="px-4 py-2 bg-green-600 text-white rounded-md font-medium hover:bg-green-700 transition-colors"
            >
              Generate Report
            </button>
          )}
        </div>
      </div>

      {/* Input Form */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">Company Information</h3>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
            <input
              type="text"
              value={customerInputs.companyName}
              onChange={(e) => setCustomerInputs({...customerInputs, companyName: (e.target as HTMLInputElement).value})}
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter company name"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Industry</label>
            <select
              value={customerInputs.industry}
              onChange={(e) => setCustomerInputs({...customerInputs, industry: (e.target as HTMLSelectElement).value})}
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="ecommerce">E-commerce & Retail</option>
              <option value="saas">SaaS & Technology</option>
              <option value="enterprise_retail">Enterprise Retail</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Monthly Events</label>
            <input
              type="number"
              value={customerInputs.monthlyEvents}
              onChange={(e) => setCustomerInputs({...customerInputs, monthlyEvents: parseInt((e.target as HTMLInputElement).value) || 0})}
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="1000000"
            />
          </div>
        </div>

        <div class="space-y-4">
          <h3 class="text-lg font-semibold">Current Analytics Setup</h3>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Current Analytics Cost ($/month)</label>
            <input
              type="number"
              value={customerInputs.currentAnalyticsCost}
              onChange={(e) => setCustomerInputs({...customerInputs, currentAnalyticsCost: parseInt((e.target as HTMLInputElement).value) || 0})}
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="5000"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Team Size</label>
            <input
              type="number"
              value={customerInputs.teamSize}
              onChange={(e) => setCustomerInputs({...customerInputs, teamSize: parseInt((e.target as HTMLInputElement).value) || 0})}
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="10"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Current Query Time (ms)</label>
            <input
              type="number"
              value={customerInputs.currentQueryTime}
              onChange={(e) => setCustomerInputs({...customerInputs, currentQueryTime: parseInt((e.target as HTMLInputElement).value) || 0})}
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="2500"
            />
          </div>
        </div>
      </div>

      {/* Results */}
      {calculationResults.value && (
        <div class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-blue-50 p-4 rounded-lg text-center">
              <div class="text-2xl font-bold text-blue-600">
                ${calculationResults.value.revenueImpact.summary.totalMonthlyValue.toLocaleString()}
              </div>
              <div class="text-sm text-blue-800">Monthly Value</div>
            </div>
            <div class="bg-green-50 p-4 rounded-lg text-center">
              <div class="text-2xl font-bold text-green-600">
                {calculationResults.value.revenueImpact.summary.roiPercentage}%
              </div>
              <div class="text-sm text-green-800">ROI</div>
            </div>
            <div class="bg-purple-50 p-4 rounded-lg text-center">
              <div class="text-2xl font-bold text-purple-600">
                {calculationResults.value.revenueImpact.summary.paybackPeriod}
              </div>
              <div class="text-sm text-purple-800">Months Payback</div>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="bg-gray-50 rounded-lg p-4">
              <h4 class="text-lg font-semibold mb-4">ROI Breakdown</h4>
              <svg ref={roiChartRef} width="400" height="300"></svg>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-4">
              <h4 class="text-lg font-semibold mb-4">Performance Comparison</h4>
              <svg ref={comparisonChartRef} width="500" height="250"></svg>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-4">
              <h4 class="text-lg font-semibold mb-4">Value Distribution</h4>
              <svg ref={savingsBreakdownRef} width="300" height="300"></svg>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
