import { useEffect, useState } from "preact/hooks";
import { signal } from "@preact/signals";
import { revenueAnalyticsApi, formatCurrency } from "../utils/revenueAnalyticsApi.ts";

// Tier Recommendation Widget Island Component
// Provides intelligent plan tier recommendations based on usage patterns and customer profile

interface TierRecommendation {
  currentTier: string;
  recommendedTier: string;
  reason: 'usage_growth' | 'feature_needs' | 'cost_optimization' | 'value_alignment';
  confidence: number;
  expectedBenefits: {
    costSavings?: number;
    additionalFeatures: string[];
    performanceImprovements: string[];
  };
  migrationPath: {
    steps: string[];
    timeline: string;
    riskFactors: string[];
  };
}

// Global signals for real-time updates
const tierRecommendationSignal = signal<TierRecommendation | null>(null);
const isLoadingSignal = signal<boolean>(false);
const errorSignal = signal<string | null>(null);

const tierInfo = {
  basic: {
    name: "Basic",
    price: 29,
    color: "blue",
    features: ["5,000 API calls", "Basic analytics", "Email support"],
  },
  pro: {
    name: "Professional",
    price: 99,
    color: "purple",
    features: ["25,000 API calls", "Advanced analytics", "Priority support", "Custom integrations"],
  },
  enterprise: {
    name: "Enterprise",
    price: 299,
    color: "green",
    features: ["Unlimited API calls", "Full analytics suite", "24/7 support", "Custom features", "SLA"],
  },
};

const reasonDescriptions = {
  usage_growth: "Your usage patterns indicate rapid growth that would benefit from a higher tier",
  feature_needs: "You're using features that suggest you need more advanced capabilities",
  cost_optimization: "You could save money by optimizing to a more suitable tier",
  value_alignment: "Your current usage aligns better with a different tier's value proposition",
};

export default function TierRecommendationWidget() {
  const [selectedCustomer, setSelectedCustomer] = useState<string>("");
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Fetch tier recommendation
  const fetchRecommendation = async () => {
    if (!selectedCustomer) {
      errorSignal.value = "Please enter a customer ID";
      return;
    }

    try {
      isLoadingSignal.value = true;
      errorSignal.value = null;

      const response = await revenueAnalyticsApi.getTierRecommendation(selectedCustomer);
      tierRecommendationSignal.value = response.recommendation;
    } catch (error) {
      console.error('Failed to fetch tier recommendation:', error);
      errorSignal.value = error instanceof Error ? error.message : 'Unknown error occurred';
    } finally {
      isLoadingSignal.value = false;
    }
  };

  // Auto-refresh every 10 minutes
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      if (selectedCustomer && tierRecommendationSignal.value) {
        fetchRecommendation();
      }
    }, 10 * 60 * 1000);

    return () => clearInterval(interval);
  }, [selectedCustomer, autoRefresh]);

  const recommendation = tierRecommendationSignal.value;
  const isLoading = isLoadingSignal.value;
  const error = errorSignal.value;

  const getCurrentTierInfo = () => tierInfo[recommendation?.currentTier as keyof typeof tierInfo];
  const getRecommendedTierInfo = () => tierInfo[recommendation?.recommendedTier as keyof typeof tierInfo];

  return (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
          Tier Recommendation
        </h2>
        <div class="flex items-center gap-2">
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            class={`text-xs px-2 py-1 rounded ${
              autoRefresh 
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
            }`}
          >
            Auto-refresh: {autoRefresh ? 'ON' : 'OFF'}
          </button>
        </div>
      </div>

      {/* Customer Input */}
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Customer ID
        </label>
        <div class="flex gap-2">
          <input
            type="text"
            value={selectedCustomer}
            onChange={(e) => setSelectedCustomer(e.currentTarget.value)}
            placeholder="Enter customer ID"
            class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
          />
          <button
            onClick={fetchRecommendation}
            disabled={isLoading || !selectedCustomer}
            class="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-md transition-colors"
          >
            {isLoading ? (
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              'Analyze'
            )}
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div class="mb-6 p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-red-800 dark:text-red-200 text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* Recommendation Display */}
      {recommendation && (
        <div class="space-y-6">
          {/* Current vs Recommended Tiers */}
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Current Tier */}
            <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
              <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Current Tier</h3>
                <span class={`px-2 py-1 text-xs font-medium rounded-full bg-${getCurrentTierInfo()?.color}-100 text-${getCurrentTierInfo()?.color}-800 dark:bg-${getCurrentTierInfo()?.color}-900 dark:text-${getCurrentTierInfo()?.color}-200`}>
                  {getCurrentTierInfo()?.name}
                </span>
              </div>
              
              <div class="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                {formatCurrency(getCurrentTierInfo()?.price || 0)}<span class="text-sm font-normal text-gray-500">/month</span>
              </div>
              
              <ul class="space-y-1">
                {getCurrentTierInfo()?.features.map((feature, index) => (
                  <li key={index} class="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                    <span class="w-1 h-1 bg-gray-400 rounded-full mr-2"></span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            {/* Recommended Tier */}
            <div class={`border-2 rounded-lg p-4 ${
              recommendation.currentTier !== recommendation.recommendedTier
                ? 'border-green-500 bg-green-50 dark:bg-green-900'
                : 'border-blue-500 bg-blue-50 dark:bg-blue-900'
            }`}>
              <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Recommended Tier</h3>
                <div class="flex items-center gap-2">
                  <span class={`px-2 py-1 text-xs font-medium rounded-full bg-${getRecommendedTierInfo()?.color}-100 text-${getRecommendedTierInfo()?.color}-800 dark:bg-${getRecommendedTierInfo()?.color}-900 dark:text-${getRecommendedTierInfo()?.color}-200`}>
                    {getRecommendedTierInfo()?.name}
                  </span>
                  {recommendation.currentTier !== recommendation.recommendedTier && (
                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      Recommended
                    </span>
                  )}
                </div>
              </div>
              
              <div class="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                {formatCurrency(getRecommendedTierInfo()?.price || 0)}<span class="text-sm font-normal text-gray-500">/month</span>
              </div>
              
              <ul class="space-y-1">
                {getRecommendedTierInfo()?.features.map((feature, index) => (
                  <li key={index} class="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                    <span class="w-1 h-1 bg-green-500 rounded-full mr-2"></span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Recommendation Details */}
          {recommendation.currentTier !== recommendation.recommendedTier && (
            <div class="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900 dark:to-green-800 rounded-lg p-6">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-green-900 dark:text-green-100">
                  Upgrade Recommendation
                </h3>
                <div class="flex items-center gap-2">
                  <span class="text-sm text-green-700 dark:text-green-300">Confidence:</span>
                  <span class="text-lg font-bold text-green-900 dark:text-green-100">
                    {Math.round(recommendation.confidence * 100)}%
                  </span>
                </div>
              </div>

              <p class="text-green-800 dark:text-green-200 mb-4">
                {reasonDescriptions[recommendation.reason]}
              </p>

              {/* Expected Benefits */}
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 class="text-sm font-medium text-green-900 dark:text-green-100 mb-2">Expected Benefits</h4>
                  <ul class="space-y-1">
                    {recommendation.expectedBenefits.costSavings && (
                      <li class="text-sm text-green-700 dark:text-green-300 flex items-center">
                        <span class="w-1 h-1 bg-green-600 rounded-full mr-2"></span>
                        Save {formatCurrency(recommendation.expectedBenefits.costSavings)} annually
                      </li>
                    )}
                    {recommendation.expectedBenefits.additionalFeatures.map((feature, index) => (
                      <li key={index} class="text-sm text-green-700 dark:text-green-300 flex items-center">
                        <span class="w-1 h-1 bg-green-600 rounded-full mr-2"></span>
                        {feature}
                      </li>
                    ))}
                    {recommendation.expectedBenefits.performanceImprovements.map((improvement, index) => (
                      <li key={index} class="text-sm text-green-700 dark:text-green-300 flex items-center">
                        <span class="w-1 h-1 bg-green-600 rounded-full mr-2"></span>
                        {improvement}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 class="text-sm font-medium text-green-900 dark:text-green-100 mb-2">Migration Plan</h4>
                  <div class="space-y-2">
                    <div class="text-sm text-green-700 dark:text-green-300">
                      <span class="font-medium">Timeline:</span> {recommendation.migrationPath.timeline}
                    </div>
                    <div class="text-sm text-green-700 dark:text-green-300">
                      <span class="font-medium">Steps:</span>
                      <ul class="mt-1 ml-4 space-y-1">
                        {recommendation.migrationPath.steps.map((step, index) => (
                          <li key={index} class="flex items-center">
                            <span class="w-1 h-1 bg-green-600 rounded-full mr-2"></span>
                            {step}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Risk Factors */}
              {recommendation.migrationPath.riskFactors.length > 0 && (
                <div class="mt-4 pt-4 border-t border-green-200 dark:border-green-700">
                  <h4 class="text-sm font-medium text-green-900 dark:text-green-100 mb-2">Risk Factors</h4>
                  <ul class="space-y-1">
                    {recommendation.migrationPath.riskFactors.map((risk, index) => (
                      <li key={index} class="text-sm text-green-700 dark:text-green-300 flex items-center">
                        <span class="w-1 h-1 bg-yellow-500 rounded-full mr-2"></span>
                        {risk}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* No Change Recommendation */}
          {recommendation.currentTier === recommendation.recommendedTier && (
            <div class="bg-blue-50 dark:bg-blue-900 rounded-lg p-6">
              <div class="flex items-center justify-center">
                <svg class="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                  <h3 class="text-lg font-medium text-blue-900 dark:text-blue-100">Perfect Fit!</h3>
                  <p class="text-blue-700 dark:text-blue-300">
                    Your current tier aligns perfectly with your usage patterns and needs.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div class="flex gap-4">
            {recommendation.currentTier !== recommendation.recommendedTier ? (
              <>
                <button class="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                  Upgrade to {getRecommendedTierInfo()?.name}
                </button>
                <button class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                  Schedule Migration
                </button>
              </>
            ) : (
              <button class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                Review Usage Trends
              </button>
            )}
            <button 
              onClick={fetchRecommendation}
              class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Refresh
            </button>
          </div>
        </div>
      )}

      {/* Empty State */}
      {!recommendation && !isLoading && !error && (
        <div class="text-center py-12">
          <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Tier Analysis Ready
          </h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            Enter a customer ID to get intelligent tier recommendations based on usage patterns and customer profile.
          </p>
        </div>
      )}
    </div>
  );
}
