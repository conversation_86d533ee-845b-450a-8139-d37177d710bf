// Performance Comparison Tools Island
// Comprehensive competitive analysis tools showcasing 97-98% performance advantage

import { useEffect, useState, useRef } from "preact/hooks";
import { signal } from "@preact/signals";
import * as d3 from "https://cdn.skypack.dev/d3@7";

// Performance comparison state signals
const performanceBenchmarks = signal([]);
const comparisonResults = signal(null);
const selectedCompetitors = signal(["google_analytics", "mixpanel", "adobe_analytics"]);

interface PerformanceComparisonToolsProps {
  mode?: 'interactive' | 'presentation' | 'report';
}

export default function PerformanceComparisonTools({ mode = 'interactive' }: PerformanceComparisonToolsProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedMetric, setSelectedMetric] = useState('queryResponseTime');
  const [showDetailedView, setShowDetailedView] = useState(false);

  // D3 chart refs
  const performanceChartRef = useRef<SVGSVGElement>(null);
  const advantageChartRef = useRef<SVGSVGElement>(null);
  const costComparisonRef = useRef<SVGSVGElement>(null);
  const capabilityMatrixRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    loadPerformanceBenchmarks();
  }, []);

  useEffect(() => {
    if (performanceBenchmarks.value.length > 0) {
      generateComparison();
    }
  }, [performanceBenchmarks.value, selectedCompetitors.value]);

  useEffect(() => {
    if (comparisonResults.value) {
      renderCharts();
    }
  }, [comparisonResults.value, selectedMetric]);

  const loadPerformanceBenchmarks = async () => {
    try {
      const response = await fetch('/api/demo/performance-benchmarks');
      if (response.ok) {
        const result = await response.json();
        performanceBenchmarks.value = result.data.benchmarks;
      }
    } catch (error) {
      console.error("Failed to load performance benchmarks:", error);
    }
  };

  const generateComparison = async () => {
    setIsGenerating(true);
    try {
      const response = await fetch('/api/demo/performance-comparison', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ competitors: selectedCompetitors.value }),
      });

      if (response.ok) {
        const result = await response.json();
        comparisonResults.value = result.data;
      }
    } catch (error) {
      console.error("Failed to generate performance comparison:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  const renderCharts = () => {
    renderPerformanceChart();
    renderAdvantageChart();
    renderCostComparison();
    renderCapabilityMatrix();
  };

  const renderPerformanceChart = () => {
    if (!performanceChartRef.current || !performanceBenchmarks.value) return;

    const svg = d3.select(performanceChartRef.current);
    svg.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 60, left: 100 };
    const width = 600 - margin.left - margin.right;
    const height = 400 - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const data = performanceBenchmarks.value.map(benchmark => ({
      competitor: benchmark.competitor,
      value: benchmark.metrics[selectedMetric],
      isOurs: benchmark.competitor === "Our Platform"
    }));

    const xScale = d3.scaleBand()
      .domain(data.map(d => d.competitor))
      .range([0, width])
      .padding(0.1);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.value) as number])
      .range([height, 0]);

    // Add bars
    g.selectAll(".bar")
      .data(data)
      .enter().append("rect")
      .attr("class", "bar")
      .attr("x", d => xScale(d.competitor)!)
      .attr("width", xScale.bandwidth())
      .attr("y", d => yScale(d.value))
      .attr("height", d => height - yScale(d.value))
      .attr("fill", d => d.isOurs ? "#10b981" : "#ef4444")
      .attr("rx", 4);

    // Add value labels
    g.selectAll(".label")
      .data(data)
      .enter().append("text")
      .attr("class", "label")
      .attr("x", d => xScale(d.competitor)! + xScale.bandwidth() / 2)
      .attr("y", d => yScale(d.value) - 5)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .text(d => {
        if (selectedMetric === 'queryResponseTime') return `${d.value}ms`;
        if (selectedMetric === 'costPerEvent') return `$${d.value.toFixed(4)}`;
        if (selectedMetric === 'dataAccuracy') return `${d.value}%`;
        return d.value.toString();
      });

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale))
      .selectAll("text")
      .style("text-anchor", "end")
      .attr("dx", "-.8em")
      .attr("dy", ".15em")
      .attr("transform", "rotate(-45)");

    g.append("g")
      .call(d3.axisLeft(yScale));

    // Add title
    svg.append("text")
      .attr("x", width / 2 + margin.left)
      .attr("y", margin.top / 2)
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .text(getMetricTitle(selectedMetric));
  };

  const renderAdvantageChart = () => {
    if (!advantageChartRef.current || !comparisonResults.value) return;

    const svg = d3.select(advantageChartRef.current);
    svg.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 120 };
    const width = 500 - margin.left - margin.right;
    const height = 300 - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const data = comparisonResults.value.competitorComparison.map(comp => ({
      competitor: comp.competitor,
      advantage: parseFloat(comp.performanceAdvantages.querySpeedImprovement)
    }));

    const xScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.advantage) as number])
      .range([0, width]);

    const yScale = d3.scaleBand()
      .domain(data.map(d => d.competitor))
      .range([0, height])
      .padding(0.1);

    // Add bars
    g.selectAll(".bar")
      .data(data)
      .enter().append("rect")
      .attr("class", "bar")
      .attr("x", 0)
      .attr("y", d => yScale(d.competitor)!)
      .attr("width", d => xScale(d.advantage))
      .attr("height", yScale.bandwidth())
      .attr("fill", "#3b82f6")
      .attr("rx", 4);

    // Add percentage labels
    g.selectAll(".label")
      .data(data)
      .enter().append("text")
      .attr("class", "label")
      .attr("x", d => xScale(d.advantage) + 5)
      .attr("y", d => yScale(d.competitor)! + yScale.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .text(d => `${d.advantage}% faster`);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale).tickFormat(d => `${d}%`));

    g.append("g")
      .call(d3.axisLeft(yScale));

    // Add title
    svg.append("text")
      .attr("x", width / 2 + margin.left)
      .attr("y", margin.top / 2)
      .attr("text-anchor", "middle")
      .style("font-size", "14px")
      .style("font-weight", "bold")
      .text("Query Speed Advantage");
  };

  const renderCostComparison = () => {
    if (!costComparisonRef.current || !performanceBenchmarks.value) return;

    const svg = d3.select(costComparisonRef.current);
    svg.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 60, left: 70 };
    const width = 400 - margin.left - margin.right;
    const height = 300 - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const data = performanceBenchmarks.value.map(benchmark => ({
      competitor: benchmark.competitor,
      cost: benchmark.metrics.costPerEvent * 1000000, // Cost per million events
      isOurs: benchmark.competitor === "Our Platform"
    }));

    const xScale = d3.scaleBand()
      .domain(data.map(d => d.competitor))
      .range([0, width])
      .padding(0.1);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.cost) as number])
      .range([height, 0]);

    // Add bars
    g.selectAll(".bar")
      .data(data)
      .enter().append("rect")
      .attr("class", "bar")
      .attr("x", d => xScale(d.competitor)!)
      .attr("width", xScale.bandwidth())
      .attr("y", d => yScale(d.cost))
      .attr("height", d => height - yScale(d.cost))
      .attr("fill", d => d.isOurs ? "#10b981" : "#ef4444")
      .attr("rx", 4);

    // Add cost labels
    g.selectAll(".label")
      .data(data)
      .enter().append("text")
      .attr("class", "label")
      .attr("x", d => xScale(d.competitor)! + xScale.bandwidth() / 2)
      .attr("y", d => yScale(d.cost) - 5)
      .attr("text-anchor", "middle")
      .style("font-size", "11px")
      .style("font-weight", "bold")
      .text(d => `$${d.cost.toFixed(0)}`);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale))
      .selectAll("text")
      .style("text-anchor", "end")
      .attr("dx", "-.8em")
      .attr("dy", ".15em")
      .attr("transform", "rotate(-45)");

    g.append("g")
      .call(d3.axisLeft(yScale).tickFormat(d => `$${d}`));

    // Add title
    svg.append("text")
      .attr("x", width / 2 + margin.left)
      .attr("y", margin.top / 2)
      .attr("text-anchor", "middle")
      .style("font-size", "14px")
      .style("font-weight", "bold")
      .text("Cost per Million Events");
  };

  const renderCapabilityMatrix = () => {
    if (!capabilityMatrixRef.current || !performanceBenchmarks.value) return;

    const svg = d3.select(capabilityMatrixRef.current);
    svg.selectAll("*").remove();

    const margin = { top: 40, right: 30, bottom: 60, left: 120 };
    const width = 500 - margin.left - margin.right;
    const height = 300 - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const capabilities = ["Real-time", "High Accuracy", "Low Cost", "Fast Setup", "High Uptime"];
    const competitors = performanceBenchmarks.value.map(b => b.competitor);

    const cellWidth = width / capabilities.length;
    const cellHeight = height / competitors.length;

    // Create capability matrix data
    const matrixData = [];
    competitors.forEach((competitor, i) => {
      const benchmark = performanceBenchmarks.value.find(b => b.competitor === competitor);
      capabilities.forEach((capability, j) => {
        let score = 0;
        if (capability === "Real-time") score = benchmark.metrics.realTimeCapability ? 1 : 0;
        if (capability === "High Accuracy") score = benchmark.metrics.dataAccuracy > 95 ? 1 : 0;
        if (capability === "Low Cost") score = benchmark.metrics.costPerEvent < 0.0005 ? 1 : 0;
        if (capability === "Fast Setup") score = benchmark.metrics.setupTime < 24 ? 1 : 0;
        if (capability === "High Uptime") score = benchmark.metrics.uptimeGuarantee > 99.5 ? 1 : 0;

        matrixData.push({
          competitor,
          capability,
          score,
          x: j * cellWidth,
          y: i * cellHeight
        });
      });
    });

    // Add cells
    g.selectAll(".cell")
      .data(matrixData)
      .enter().append("rect")
      .attr("class", "cell")
      .attr("x", d => d.x)
      .attr("y", d => d.y)
      .attr("width", cellWidth - 1)
      .attr("height", cellHeight - 1)
      .attr("fill", d => d.score ? "#10b981" : "#ef4444")
      .attr("stroke", "#fff")
      .attr("stroke-width", 1);

    // Add checkmarks/crosses
    g.selectAll(".symbol")
      .data(matrixData)
      .enter().append("text")
      .attr("class", "symbol")
      .attr("x", d => d.x + cellWidth / 2)
      .attr("y", d => d.y + cellHeight / 2)
      .attr("text-anchor", "middle")
      .attr("dy", "0.35em")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .style("fill", "white")
      .text(d => d.score ? "✓" : "✗");

    // Add capability labels
    g.selectAll(".cap-label")
      .data(capabilities)
      .enter().append("text")
      .attr("class", "cap-label")
      .attr("x", (d, i) => i * cellWidth + cellWidth / 2)
      .attr("y", -10)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .text(d => d);

    // Add competitor labels
    g.selectAll(".comp-label")
      .data(competitors)
      .enter().append("text")
      .attr("class", "comp-label")
      .attr("x", -10)
      .attr("y", (d, i) => i * cellHeight + cellHeight / 2)
      .attr("text-anchor", "end")
      .attr("dy", "0.35em")
      .style("font-size", "11px")
      .style("font-weight", "bold")
      .text(d => d);
  };

  const getMetricTitle = (metric: string): string => {
    const titles = {
      queryResponseTime: "Query Response Time (ms)",
      eventProcessingDelay: "Event Processing Delay (ms)",
      dataAccuracy: "Data Accuracy (%)",
      costPerEvent: "Cost per Event ($)",
      setupTime: "Setup Time (hours)",
      uptimeGuarantee: "Uptime Guarantee (%)"
    };
    return titles[metric] || metric;
  };

  const toggleCompetitor = (competitorId: string) => {
    const current = selectedCompetitors.value;
    if (current.includes(competitorId)) {
      selectedCompetitors.value = current.filter(id => id !== competitorId);
    } else {
      selectedCompetitors.value = [...current, competitorId];
    }
  };

  return (
    <div class="bg-white rounded-lg shadow-lg p-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900">Performance Comparison Tools</h2>
        <div class="flex space-x-2">
          <button
            onClick={() => setShowDetailedView(!showDetailedView)}
            class="px-4 py-2 bg-gray-600 text-white rounded-md font-medium hover:bg-gray-700 transition-colors"
          >
            {showDetailedView ? 'Simple View' : 'Detailed View'}
          </button>
          <button
            onClick={generateComparison}
            disabled={isGenerating}
            class="px-4 py-2 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            {isGenerating ? 'Generating...' : 'Refresh Comparison'}
          </button>
        </div>
      </div>

      {/* Metric Selector */}
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">Select Metric</label>
        <select
          value={selectedMetric}
          onChange={(e) => setSelectedMetric((e.target as HTMLSelectElement).value)}
          class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="queryResponseTime">Query Response Time</option>
          <option value="eventProcessingDelay">Event Processing Delay</option>
          <option value="dataAccuracy">Data Accuracy</option>
          <option value="costPerEvent">Cost per Event</option>
          <option value="setupTime">Setup Time</option>
          <option value="uptimeGuarantee">Uptime Guarantee</option>
        </select>
      </div>

      {/* Performance Summary */}
      {comparisonResults.value && (
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div class="bg-green-50 p-4 rounded-lg text-center">
            <div class="text-2xl font-bold text-green-600">
              {comparisonResults.value.summary.averageQuerySpeedImprovement.toFixed(1)}%
            </div>
            <div class="text-sm text-green-800">Average Speed Advantage</div>
          </div>
          <div class="bg-blue-50 p-4 rounded-lg text-center">
            <div class="text-2xl font-bold text-blue-600">
              {comparisonResults.value.summary.averageCostAdvantage.toFixed(1)}%
            </div>
            <div class="text-sm text-blue-800">Average Cost Advantage</div>
          </div>
          <div class="bg-purple-50 p-4 rounded-lg text-center">
            <div class="text-2xl font-bold text-purple-600">
              {comparisonResults.value.summary.competitorsAnalyzed}
            </div>
            <div class="text-sm text-purple-800">Competitors Analyzed</div>
          </div>
        </div>
      )}

      {/* Charts Grid */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-gray-50 rounded-lg p-4">
          <h4 class="text-lg font-semibold mb-4">Performance Metrics</h4>
          <svg ref={performanceChartRef} width="600" height="400"></svg>
        </div>
        
        <div class="bg-gray-50 rounded-lg p-4">
          <h4 class="text-lg font-semibold mb-4">Performance Advantage</h4>
          <svg ref={advantageChartRef} width="500" height="300"></svg>
        </div>
        
        <div class="bg-gray-50 rounded-lg p-4">
          <h4 class="text-lg font-semibold mb-4">Cost Comparison</h4>
          <svg ref={costComparisonRef} width="400" height="300"></svg>
        </div>
        
        <div class="bg-gray-50 rounded-lg p-4">
          <h4 class="text-lg font-semibold mb-4">Capability Matrix</h4>
          <svg ref={capabilityMatrixRef} width="500" height="300"></svg>
        </div>
      </div>
    </div>
  );
}
