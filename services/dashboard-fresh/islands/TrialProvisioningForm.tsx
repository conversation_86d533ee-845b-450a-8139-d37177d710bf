// Trial Provisioning Form Island
// Self-service trial environment provisioning with real-time validation

import { useState } from "preact/hooks";
import { signal } from "@preact/signals";

// Form state signals
const isProvisioning = signal(false);
const provisioningProgress = signal(0);
const provisionedTrial = signal(null);

interface TrialProvisioningFormProps {
  onSuccess?: (trialId: string) => void;
}

export default function TrialProvisioningForm({ onSuccess }: TrialProvisioningFormProps) {
  const [formData, setFormData] = useState({
    companyName: "",
    contactEmail: "",
    contactName: "",
    industry: "ecommerce",
    monthlyEvents: 1000000,
    currentAnalytics: "google_analytics",
    useCase: "",
    scenario: "" // Will be auto-determined
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [step, setStep] = useState(1);

  const validateStep = (stepNumber: number): boolean => {
    const newErrors: Record<string, string> = {};

    if (stepNumber === 1) {
      if (!formData.companyName.trim()) {
        newErrors.companyName = "Company name is required";
      }
      if (!formData.contactEmail.trim()) {
        newErrors.contactEmail = "Email is required";
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contactEmail)) {
        newErrors.contactEmail = "Please enter a valid email address";
      }
      if (!formData.contactName.trim()) {
        newErrors.contactName = "Contact name is required";
      }
    }

    if (stepNumber === 2) {
      if (!formData.industry) {
        newErrors.industry = "Please select your industry";
      }
      if (formData.monthlyEvents < 1000) {
        newErrors.monthlyEvents = "Monthly events must be at least 1,000";
      }
      if (!formData.currentAnalytics) {
        newErrors.currentAnalytics = "Please select your current analytics solution";
      }
    }

    if (stepNumber === 3) {
      if (!formData.useCase.trim()) {
        newErrors.useCase = "Please describe your primary use case";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(step)) {
      setStep(step + 1);
    }
  };

  const prevStep = () => {
    setStep(step - 1);
    setErrors({});
  };

  const provisionTrial = async () => {
    if (!validateStep(3)) return;

    isProvisioning.value = true;
    provisioningProgress.value = 0;

    try {
      // Simulate provisioning progress
      const progressInterval = setInterval(() => {
        provisioningProgress.value = Math.min(provisioningProgress.value + 10, 90);
      }, 200);

      const response = await fetch('/api/enhanced-subscriptions/trial/provision', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      clearInterval(progressInterval);
      provisioningProgress.value = 100;

      if (response.ok) {
        const result = await response.json();
        provisionedTrial.value = result.data.trialEnvironment;
        
        // Redirect to trial environment after short delay
        setTimeout(() => {
          if (onSuccess) {
            onSuccess(result.data.trialEnvironment.id);
          } else {
            window.location.href = `/trial/${result.data.trialEnvironment.id}`;
          }
        }, 2000);
      } else {
        throw new Error('Failed to provision trial');
      }
    } catch (error) {
      console.error("Failed to provision trial:", error);
      setErrors({ general: "Failed to provision trial. Please try again." });
      provisioningProgress.value = 0;
    } finally {
      isProvisioning.value = false;
    }
  };

  const updateFormData = (field: string, value: string | number) => {
    setFormData({ ...formData, [field]: value });
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors({ ...errors, [field]: "" });
    }
  };

  const getIndustryScenario = (industry: string): string => {
    if (industry === "ecommerce" || industry === "retail") {
      return formData.monthlyEvents > 10000000 ? "enterprise_retail" : "ecommerce_smb";
    }
    return "ecommerce_smb"; // Default scenario
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(0)}K`;
    }
    return num.toString();
  };

  if (provisionedTrial.value) {
    return (
      <div class="text-center py-8">
        <div class="mb-6">
          <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl">🎉</span>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-2">Trial Environment Ready!</h3>
          <p class="text-gray-600">
            Your trial environment has been provisioned successfully. Redirecting you now...
          </p>
        </div>
        
        <div class="bg-gray-50 rounded-lg p-6 max-w-md mx-auto">
          <div class="text-sm text-gray-600 mb-2">Trial Details</div>
          <div class="space-y-2 text-left">
            <div class="flex justify-between">
              <span class="text-gray-600">Company:</span>
              <span class="font-medium">{provisionedTrial.value.companyName}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Trial ID:</span>
              <span class="font-mono text-sm">{provisionedTrial.value.id}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Expires:</span>
              <span class="font-medium">
                {new Date(provisionedTrial.value.expiresAt).toLocaleDateString()}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Scenario:</span>
              <span class="font-medium">{provisionedTrial.value.scenario.replace('_', ' ')}</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isProvisioning.value) {
    return (
      <div class="text-center py-8">
        <div class="mb-6">
          <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
            <span class="text-2xl">⚙️</span>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-2">Provisioning Your Trial...</h3>
          <p class="text-gray-600 mb-6">
            Setting up your personalized analytics environment with sample data
          </p>
        </div>
        
        <div class="max-w-md mx-auto">
          <div class="flex justify-between text-sm text-gray-600 mb-2">
            <span>Progress</span>
            <span>{provisioningProgress.value}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-3 mb-6">
            <div 
              class="bg-blue-600 h-3 rounded-full transition-all duration-300"
              style={{ width: `${provisioningProgress.value}%` }}
            ></div>
          </div>
          
          <div class="text-sm text-gray-500 space-y-2">
            <div class={provisioningProgress.value >= 20 ? "text-green-600" : ""}>
              ✓ Creating isolated environment
            </div>
            <div class={provisioningProgress.value >= 40 ? "text-green-600" : ""}>
              ✓ Loading sample data ({formatNumber(formData.monthlyEvents)} events)
            </div>
            <div class={provisioningProgress.value >= 60 ? "text-green-600" : ""}>
              ✓ Configuring analytics features
            </div>
            <div class={provisioningProgress.value >= 80 ? "text-green-600" : ""}>
              ✓ Setting up onboarding workflow
            </div>
            <div class={provisioningProgress.value >= 100 ? "text-green-600" : ""}>
              ✓ Trial environment ready
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div class="max-w-2xl mx-auto">
      {/* Progress Indicator */}
      <div class="flex items-center justify-center mb-8">
        {[1, 2, 3].map((stepNumber) => (
          <div key={stepNumber} class="flex items-center">
            <div class={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${
              step >= stepNumber 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-600'
            }`}>
              {stepNumber}
            </div>
            {stepNumber < 3 && (
              <div class={`w-16 h-1 mx-2 ${
                step > stepNumber ? 'bg-blue-600' : 'bg-gray-200'
              }`}></div>
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <div class="bg-white rounded-lg p-8 shadow-lg">
        {/* Step 1: Company Information */}
        {step === 1 && (
          <div>
            <h3 class="text-xl font-bold text-gray-900 mb-6">Company Information</h3>
            
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Company Name *
                </label>
                <input
                  type="text"
                  value={formData.companyName}
                  onChange={(e) => updateFormData('companyName', (e.target as HTMLInputElement).value)}
                  class={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.companyName ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter your company name"
                />
                {errors.companyName && (
                  <p class="text-red-500 text-sm mt-1">{errors.companyName}</p>
                )}
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Your Name *
                </label>
                <input
                  type="text"
                  value={formData.contactName}
                  onChange={(e) => updateFormData('contactName', (e.target as HTMLInputElement).value)}
                  class={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.contactName ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter your full name"
                />
                {errors.contactName && (
                  <p class="text-red-500 text-sm mt-1">{errors.contactName}</p>
                )}
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Business Email *
                </label>
                <input
                  type="email"
                  value={formData.contactEmail}
                  onChange={(e) => updateFormData('contactEmail', (e.target as HTMLInputElement).value)}
                  class={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.contactEmail ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter your business email"
                />
                {errors.contactEmail && (
                  <p class="text-red-500 text-sm mt-1">{errors.contactEmail}</p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Step 2: Business Details */}
        {step === 2 && (
          <div>
            <h3 class="text-xl font-bold text-gray-900 mb-6">Business Details</h3>
            
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Industry *
                </label>
                <select
                  value={formData.industry}
                  onChange={(e) => updateFormData('industry', (e.target as HTMLSelectElement).value)}
                  class={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.industry ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="ecommerce">E-commerce & Online Retail</option>
                  <option value="retail">Physical Retail</option>
                  <option value="saas">SaaS & Technology</option>
                  <option value="marketplace">Marketplace</option>
                  <option value="media">Media & Publishing</option>
                  <option value="other">Other</option>
                </select>
                {errors.industry && (
                  <p class="text-red-500 text-sm mt-1">{errors.industry}</p>
                )}
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Monthly Events Volume *
                </label>
                <select
                  value={formData.monthlyEvents}
                  onChange={(e) => updateFormData('monthlyEvents', parseInt((e.target as HTMLSelectElement).value))}
                  class={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.monthlyEvents ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value={100000}>100K - Small business</option>
                  <option value={500000}>500K - Growing business</option>
                  <option value={1000000}>1M - Medium business</option>
                  <option value={5000000}>5M - Large business</option>
                  <option value={10000000}>10M+ - Enterprise</option>
                </select>
                {errors.monthlyEvents && (
                  <p class="text-red-500 text-sm mt-1">{errors.monthlyEvents}</p>
                )}
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Current Analytics Solution *
                </label>
                <select
                  value={formData.currentAnalytics}
                  onChange={(e) => updateFormData('currentAnalytics', (e.target as HTMLSelectElement).value)}
                  class={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.currentAnalytics ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="google_analytics">Google Analytics</option>
                  <option value="mixpanel">Mixpanel</option>
                  <option value="adobe_analytics">Adobe Analytics</option>
                  <option value="amplitude">Amplitude</option>
                  <option value="segment">Segment</option>
                  <option value="none">No current solution</option>
                  <option value="other">Other</option>
                </select>
                {errors.currentAnalytics && (
                  <p class="text-red-500 text-sm mt-1">{errors.currentAnalytics}</p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Use Case */}
        {step === 3 && (
          <div>
            <h3 class="text-xl font-bold text-gray-900 mb-6">Primary Use Case</h3>
            
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  What's your primary analytics goal? *
                </label>
                <textarea
                  value={formData.useCase}
                  onChange={(e) => updateFormData('useCase', (e.target as HTMLTextAreaElement).value)}
                  rows={4}
                  class={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.useCase ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Describe what you want to achieve with analytics (e.g., improve conversion rates, reduce churn, optimize marketing spend, etc.)"
                />
                {errors.useCase && (
                  <p class="text-red-500 text-sm mt-1">{errors.useCase}</p>
                )}
              </div>

              {/* Trial Preview */}
              <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 class="font-semibold text-blue-900 mb-2">Your Trial Will Include:</h4>
                <ul class="text-blue-700 space-y-1 text-sm">
                  <li>✓ {getIndustryScenario(formData.industry).replace('_', ' ')} scenario with sample data</li>
                  <li>✓ {formatNumber(formData.monthlyEvents)} events pre-loaded for testing</li>
                  <li>✓ Guided onboarding workflow</li>
                  <li>✓ Performance comparison with {formData.currentAnalytics.replace('_', ' ')}</li>
                  <li>✓ ROI calculator with your business metrics</li>
                  <li>✓ 14-day full access to all features</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {errors.general && (
          <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <p class="text-red-700">{errors.general}</p>
          </div>
        )}

        {/* Navigation Buttons */}
        <div class="flex justify-between mt-8">
          <button
            onClick={prevStep}
            disabled={step === 1}
            class={`px-6 py-2 rounded-md font-medium transition-colors ${
              step === 1 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Previous
          </button>

          {step < 3 ? (
            <button
              onClick={nextStep}
              class="px-6 py-2 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700 transition-colors"
            >
              Next
            </button>
          ) : (
            <button
              onClick={provisionTrial}
              disabled={isProvisioning.value}
              class="px-8 py-2 bg-green-600 text-white rounded-md font-medium hover:bg-green-700 disabled:opacity-50 transition-colors"
            >
              Start My Free Trial
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
