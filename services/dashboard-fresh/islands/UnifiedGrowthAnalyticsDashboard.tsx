import { useEffect, useState, useRef } from "preact/hooks";
import { signal } from "@preact/signals";
import * as d3 from "d3";
import { revenueAnalyticsApi, formatCurrency, formatPercentage, formatNumber } from "../utils/revenueAnalyticsApi.ts";

// Unified Growth Analytics Dashboard Island Component
// Comprehensive growth analytics with D3.js visualizations, real-time streaming, and interactive analytics

interface UnifiedGrowthMetrics {
  revenueMetrics: {
    totalRevenue: number;
    monthlyRecurringRevenue: number;
    annualRecurringRevenue: number;
    revenueGrowthRate: number;
    netRevenueRetention: number;
    averageRevenuePerUser: number;
    customerLifetimeValue: number;
  };
  cohortMetrics: {
    retentionRates: Record<string, number>;
    cohortRevenue: Record<string, number>;
    bestPerformingCohort: string;
    cohortTrends: 'improving' | 'declining' | 'stable';
    predictedChurn: number;
  };
  customerSuccessMetrics: {
    averageHealthScore: number;
    churnRisk: number;
    expansionOpportunities: number;
    interventionSuccess: number;
    segmentPerformance: Record<string, number>;
  };
  funnelMetrics: {
    overallConversionRate: number;
    revenueConversionRate: number;
    funnelEfficiency: number;
    bottleneckSteps: string[];
    optimizationPotential: number;
  };
  predictiveMetrics: {
    revenueForecasting: {
      nextMonth: number;
      nextQuarter: number;
      confidence: number;
    };
    churnPrediction: {
      expectedChurn: number;
      revenueAtRisk: number;
      preventionOpportunity: number;
    };
    expansionPrediction: {
      potentialRevenue: number;
      highProbabilityCustomers: number;
      timeToExpansion: number;
    };
  };
}

interface GrowthOptimizationRecommendation {
  priority: 'high' | 'medium' | 'low';
  category: 'revenue' | 'retention' | 'expansion' | 'acquisition' | 'optimization';
  title: string;
  description: string;
  expectedImpact: {
    revenueIncrease: number;
    churnReduction: number;
    conversionImprovement: number;
  };
  implementationComplexity: 'low' | 'medium' | 'high';
  timeToImpact: string;
  requiredActions: string[];
  successMetrics: string[];
}

// Global signals for real-time updates
const unifiedAnalyticsSignal = signal<any>(null);
const isLoadingSignal = signal<boolean>(true);
const errorSignal = signal<string | null>(null);
const lastUpdatedSignal = signal<string>("");

export default function UnifiedGrowthAnalyticsDashboard() {
  const [timeRange, setTimeRange] = useState("30d");
  const [optimizationFocus, setOptimizationFocus] = useState("all");
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 seconds

  // D3.js chart refs
  const revenueChartRef = useRef<SVGSVGElement>(null);
  const cohortChartRef = useRef<SVGSVGElement>(null);
  const funnelChartRef = useRef<SVGSVGElement>(null);
  const predictiveChartRef = useRef<SVGSVGElement>(null);

  // Fetch unified growth analytics
  const fetchUnifiedAnalytics = async () => {
    try {
      isLoadingSignal.value = true;
      errorSignal.value = null;

      const response = await revenueAnalyticsApi.getUnifiedGrowthAnalysis({
        timeRange,
        includeForecasting: true,
        includeRecommendations: true,
        includeSegmentation: true,
        optimizationFocus,
      });

      unifiedAnalyticsSignal.value = response;
      lastUpdatedSignal.value = new Date().toLocaleTimeString();
    } catch (error) {
      console.error('Failed to fetch unified analytics:', error);
      errorSignal.value = error instanceof Error ? error.message : 'Unknown error occurred';
    } finally {
      isLoadingSignal.value = false;
    }
  };

  // Auto-refresh functionality
  useEffect(() => {
    fetchUnifiedAnalytics();

    let intervalId: number | undefined;
    
    if (autoRefresh) {
      intervalId = setInterval(fetchUnifiedAnalytics, refreshInterval);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [timeRange, optimizationFocus, autoRefresh, refreshInterval]);

  // Create revenue trend chart
  useEffect(() => {
    if (!revenueChartRef.current || !unifiedAnalyticsSignal.value) return;

    const svg = d3.select(revenueChartRef.current);
    svg.selectAll("*").remove();

    const width = 400;
    const height = 200;
    const margin = { top: 20, right: 30, bottom: 40, left: 50 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left}, ${margin.top})`);

    // Sample revenue trend data
    const revenueData = [
      { month: "Jan", revenue: 85000, mrr: 28000 },
      { month: "Feb", revenue: 92000, mrr: 30000 },
      { month: "Mar", revenue: 98000, mrr: 32000 },
      { month: "Apr", revenue: 105000, mrr: 34000 },
      { month: "May", revenue: 112000, mrr: 36000 },
      { month: "Jun", revenue: 125000, mrr: 38000 },
    ];

    const xScale = d3.scaleBand()
      .domain(revenueData.map(d => d.month))
      .range([0, chartWidth])
      .padding(0.1);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(revenueData, d => d.revenue) || 0])
      .range([chartHeight, 0]);

    // Revenue bars
    g.selectAll(".revenue-bar")
      .data(revenueData)
      .enter()
      .append("rect")
      .attr("class", "revenue-bar")
      .attr("x", d => xScale(d.month) || 0)
      .attr("y", chartHeight)
      .attr("width", xScale.bandwidth())
      .attr("height", 0)
      .attr("fill", "#3B82F6")
      .attr("rx", 4)
      .transition()
      .duration(800)
      .attr("y", d => yScale(d.revenue))
      .attr("height", d => chartHeight - yScale(d.revenue));

    // MRR line
    const line = d3.line<any>()
      .x(d => (xScale(d.month) || 0) + xScale.bandwidth() / 2)
      .y(d => yScale(d.mrr))
      .curve(d3.curveMonotoneX);

    g.append("path")
      .datum(revenueData)
      .attr("fill", "none")
      .attr("stroke", "#10B981")
      .attr("stroke-width", 3)
      .attr("d", line);

    // Add dots for MRR
    g.selectAll(".mrr-dot")
      .data(revenueData)
      .enter()
      .append("circle")
      .attr("class", "mrr-dot")
      .attr("cx", d => (xScale(d.month) || 0) + xScale.bandwidth() / 2)
      .attr("cy", d => yScale(d.mrr))
      .attr("r", 4)
      .attr("fill", "#10B981");

    // X-axis
    g.append("g")
      .attr("transform", `translate(0, ${chartHeight})`)
      .call(d3.axisBottom(xScale))
      .selectAll("text")
      .attr("font-size", "12px")
      .attr("fill", "#6B7280");

    // Y-axis
    g.append("g")
      .call(d3.axisLeft(yScale).tickFormat(d => `$${d3.format(".0s")(d as number)}`))
      .selectAll("text")
      .attr("font-size", "12px")
      .attr("fill", "#6B7280");

    // Remove axis lines
    g.selectAll(".domain").remove();
    g.selectAll(".tick line").attr("stroke", "#E5E7EB");

  }, [unifiedAnalyticsSignal.value]);

  // Create cohort retention heatmap
  useEffect(() => {
    if (!cohortChartRef.current || !unifiedAnalyticsSignal.value) return;

    const svg = d3.select(cohortChartRef.current);
    svg.selectAll("*").remove();

    const width = 400;
    const height = 200;
    const margin = { top: 20, right: 20, bottom: 40, left: 60 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left}, ${margin.top})`);

    // Sample cohort data
    const cohortData = [
      { cohort: "Jan", month0: 100, month1: 85, month2: 72, month3: 65, month4: 58, month5: 52 },
      { cohort: "Feb", month0: 100, month1: 88, month2: 75, month3: 68, month4: 61, month5: 55 },
      { cohort: "Mar", month0: 100, month1: 90, month2: 78, month3: 71, month4: 64, month5: 58 },
      { cohort: "Apr", month0: 100, month1: 87, month2: 74, month3: 67, month4: 60, month5: 54 },
    ];

    const months = ["0", "1", "2", "3", "4", "5"];
    const cohorts = cohortData.map(d => d.cohort);

    const xScale = d3.scaleBand()
      .domain(months)
      .range([0, chartWidth])
      .padding(0.05);

    const yScale = d3.scaleBand()
      .domain(cohorts)
      .range([0, chartHeight])
      .padding(0.05);

    const colorScale = d3.scaleSequential(d3.interpolateBlues)
      .domain([0, 100]);

    // Create heatmap cells
    cohortData.forEach((cohort, i) => {
      months.forEach((month, j) => {
        const value = (cohort as any)[`month${month}`];
        
        g.append("rect")
          .attr("x", xScale(month) || 0)
          .attr("y", yScale(cohort.cohort) || 0)
          .attr("width", xScale.bandwidth())
          .attr("height", yScale.bandwidth())
          .attr("fill", colorScale(value))
          .attr("rx", 2)
          .on("mouseover", function(event) {
            d3.select(this).attr("stroke", "#374151").attr("stroke-width", 2);
          })
          .on("mouseout", function(event) {
            d3.select(this).attr("stroke", "none");
          });

        // Add text labels
        g.append("text")
          .attr("x", (xScale(month) || 0) + xScale.bandwidth() / 2)
          .attr("y", (yScale(cohort.cohort) || 0) + yScale.bandwidth() / 2)
          .attr("text-anchor", "middle")
          .attr("dominant-baseline", "middle")
          .attr("font-size", "10px")
          .attr("font-weight", "600")
          .attr("fill", value > 50 ? "#fff" : "#000")
          .text(`${value}%`);
      });
    });

    // X-axis
    g.append("g")
      .attr("transform", `translate(0, ${chartHeight})`)
      .call(d3.axisBottom(xScale))
      .selectAll("text")
      .attr("font-size", "12px")
      .attr("fill", "#6B7280");

    // Y-axis
    g.append("g")
      .call(d3.axisLeft(yScale))
      .selectAll("text")
      .attr("font-size", "12px")
      .attr("fill", "#6B7280");

    // Remove axis lines
    g.selectAll(".domain").remove();
    g.selectAll(".tick line").remove();

  }, [unifiedAnalyticsSignal.value]);

  const analytics = unifiedAnalyticsSignal.value;
  const isLoading = isLoadingSignal.value;
  const error = errorSignal.value;

  if (isLoading && !analytics) {
    return (
      <div class="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p class="text-gray-600 dark:text-gray-400">Loading Unified Growth Analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div class="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div class="text-center max-w-md">
          <div class="bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-200 px-4 py-3 rounded mb-4">
            <strong class="font-bold">Error: </strong>
            <span class="block sm:inline">{error}</span>
          </div>
          <button
            onClick={fetchUnifiedAnalytics}
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!analytics) return null;

  return (
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      {/* Header */}
      <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
              Unified Growth Analytics
            </h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">
              Comprehensive growth optimization with predictive insights
            </p>
          </div>
          
          {/* Controls */}
          <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-4">
            {/* Time Range Selector */}
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.currentTarget.value)}
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>

            {/* Optimization Focus */}
            <select
              value={optimizationFocus}
              onChange={(e) => setOptimizationFocus(e.currentTarget.value)}
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Areas</option>
              <option value="revenue">Revenue Focus</option>
              <option value="retention">Retention Focus</option>
              <option value="expansion">Expansion Focus</option>
            </select>

            {/* Auto-refresh Controls */}
            <div class="flex items-center gap-2">
              <button
                onClick={() => setAutoRefresh(!autoRefresh)}
                class={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  autoRefresh
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
                }`}
              >
                Auto-refresh: {autoRefresh ? 'ON' : 'OFF'}
              </button>
              
              <button
                onClick={fetchUnifiedAnalytics}
                disabled={isLoading}
                class="px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-md text-sm font-medium transition-colors"
              >
                {isLoading ? 'Refreshing...' : 'Refresh'}
              </button>
            </div>
          </div>
        </div>

        {/* Last Updated */}
        {lastUpdatedSignal.value && (
          <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Last updated: {lastUpdatedSignal.value}
          </div>
        )}
      </div>

      {/* Key Metrics Overview */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Revenue</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(analytics.metrics?.revenueMetrics?.totalRevenue || 125000)}
              </p>
            </div>
            <div class="text-green-600 dark:text-green-400">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
          </div>
          <div class="mt-2 text-sm text-green-600 dark:text-green-400">
            ↗ {formatPercentage((analytics.metrics?.revenueMetrics?.revenueGrowthRate || 0.15))} growth
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Customer Health</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {Math.round(analytics.metrics?.customerSuccessMetrics?.averageHealthScore || 75)}/100
              </p>
            </div>
            <div class="text-blue-600 dark:text-blue-400">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
              </svg>
            </div>
          </div>
          <div class="mt-2 text-sm text-blue-600 dark:text-blue-400">
            {analytics.metrics?.customerSuccessMetrics?.churnRisk || 12} at risk
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Expansion Revenue</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(analytics.metrics?.predictiveMetrics?.expansionPrediction?.potentialRevenue || 45000)}
              </p>
            </div>
            <div class="text-purple-600 dark:text-purple-400">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
              </svg>
            </div>
          </div>
          <div class="mt-2 text-sm text-purple-600 dark:text-purple-400">
            {analytics.metrics?.customerSuccessMetrics?.expansionOpportunities || 23} opportunities
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Funnel Efficiency</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">
                {formatPercentage(analytics.metrics?.funnelMetrics?.funnelEfficiency || 0.68)}
              </p>
            </div>
            <div class="text-orange-600 dark:text-orange-400">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
          </div>
          <div class="mt-2 text-sm text-orange-600 dark:text-orange-400">
            {formatPercentage(analytics.metrics?.funnelMetrics?.optimizationPotential || 0.15)} potential
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Revenue Trend Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Revenue Trends</h3>
          <svg ref={revenueChartRef} class="w-full"></svg>
        </div>

        {/* Cohort Retention Heatmap */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Cohort Retention</h3>
          <svg ref={cohortChartRef} class="w-full"></svg>
        </div>
      </div>

      {/* Recommendations */}
      {analytics.recommendations && analytics.recommendations.length > 0 && (
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Growth Optimization Recommendations</h3>
          <div class="space-y-4">
            {analytics.recommendations.slice(0, 3).map((rec: GrowthOptimizationRecommendation, index: number) => (
              <div key={index} class={`border-l-4 pl-4 ${
                rec.priority === 'high' ? 'border-red-500' : 
                rec.priority === 'medium' ? 'border-yellow-500' : 'border-green-500'
              }`}>
                <div class="flex items-center justify-between mb-2">
                  <h4 class="text-md font-medium text-gray-900 dark:text-white">{rec.title}</h4>
                  <div class="flex items-center gap-2">
                    <span class={`px-2 py-1 text-xs font-medium rounded-full ${
                      rec.priority === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                      rec.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    }`}>
                      {rec.priority} priority
                    </span>
                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                      {rec.category}
                    </span>
                  </div>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">{rec.description}</p>
                <div class="flex items-center gap-4 text-sm">
                  <span class="text-green-600 dark:text-green-400">
                    Revenue Impact: {formatCurrency(rec.expectedImpact.revenueIncrease)}
                  </span>
                  <span class="text-blue-600 dark:text-blue-400">
                    Timeline: {rec.timeToImpact}
                  </span>
                  <span class="text-purple-600 dark:text-purple-400">
                    Effort: {rec.implementationComplexity}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Real-time Status Indicator */}
      <div class="fixed bottom-4 right-4">
        <div class={`flex items-center gap-2 px-3 py-2 rounded-full text-sm font-medium ${
          autoRefresh && !isLoading
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            : isLoading
            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
            : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
        }`}>
          <div class={`w-2 h-2 rounded-full ${
            autoRefresh && !isLoading
              ? 'bg-green-500 animate-pulse'
              : isLoading
              ? 'bg-blue-500 animate-spin'
              : 'bg-gray-500'
          }`}></div>
          {isLoading ? 'Updating...' : autoRefresh ? 'Live' : 'Paused'}
        </div>
      </div>
    </div>
  );
}
