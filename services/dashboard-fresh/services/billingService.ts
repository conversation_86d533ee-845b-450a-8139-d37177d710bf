// Enhanced Billing Service for Fresh Frontend
// Handles subscription management, revenue intelligence, and billing analytics

export interface SubscriptionData {
  id: string;
  customerId: string;
  planId: string;
  status: 'active' | 'inactive' | 'cancelled' | 'past_due';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  mrr: number;
  tier: string;
  features: string[];
}

export interface RevenueIntelligence {
  opportunities: Array<{
    type: string;
    description: string;
    potentialRevenue: number;
    confidence: number;
    priority: 'high' | 'medium' | 'low';
  }>;
  risks: Array<{
    type: string;
    description: string;
    impact: number;
    probability: number;
    severity: 'critical' | 'high' | 'medium' | 'low';
  }>;
  insights: Array<{
    category: string;
    title: string;
    description: string;
    actionable: boolean;
  }>;
  predictions: {
    nextMonthRevenue: {
      amount: number;
      confidence: number;
    };
    churnRisk: {
      probability: number;
      factors: string[];
    };
  };
}

export interface UsageAnalytics {
  metrics: Array<{
    name: string;
    value: number;
    unit: string;
    trend: 'up' | 'down' | 'stable';
    change: number;
  }>;
  recommendations: Array<{
    type: 'tier_upgrade' | 'feature_optimization' | 'usage_optimization';
    title: string;
    description: string;
    impact: string;
  }>;
  tierRecommendation?: {
    currentTier: string;
    recommendedTier: string;
    reason: string;
    confidence: number;
  };
}

export interface HealthScore {
  overallScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  factors: Array<{
    name: string;
    score: number;
    weight: number;
    impact: string;
  }>;
  interventionTriggers: string[];
}

export class BillingService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = '/api/billing';
  }

  async getRevenueOperationsDashboard(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/revenue-operations-dashboard`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Failed to fetch revenue operations dashboard:', error);
      return this.getFallbackDashboardData();
    }
  }

  async getRevenueIntelligence(): Promise<RevenueIntelligence> {
    try {
      const response = await fetch(`${this.baseUrl}/revenue-intelligence`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      return result.data.intelligence;
    } catch (error) {
      console.error('Failed to fetch revenue intelligence:', error);
      return this.getFallbackRevenueIntelligence();
    }
  }

  async getSubscriptionData(subscriptionId: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/unified-data/${subscriptionId}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      return result.data.unifiedData;
    } catch (error) {
      console.error('Failed to fetch subscription data:', error);
      return this.getFallbackSubscriptionData();
    }
  }

  async getUsageAnalytics(subscriptionId: string, startDate?: string, endDate?: string): Promise<UsageAnalytics> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);
      
      const response = await fetch(`${this.baseUrl}/usage-analytics/${subscriptionId}?${params}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      return result.data.analytics;
    } catch (error) {
      console.error('Failed to fetch usage analytics:', error);
      return this.getFallbackUsageAnalytics();
    }
  }

  async getChurnPrediction(subscriptionId: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/churn-prediction/${subscriptionId}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Failed to fetch churn prediction:', error);
      return this.getFallbackChurnPrediction();
    }
  }

  async getHealthMonitoring(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/health-monitoring`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Failed to fetch health monitoring:', error);
      return this.getFallbackHealthMonitoring();
    }
  }

  // Fallback data methods for offline/error scenarios
  private getFallbackDashboardData() {
    return {
      summary: {
        totalSubscriptions: 1247,
        totalMRR: 89750,
        averageHealthScore: 87.3,
        churnRate: 2.1,
        expansionRevenue: 15420,
      },
      subscriptions: [
        {
          id: 'sub_demo_001',
          customerId: 'cust_demo_001',
          planId: 'plan_professional',
          status: 'active',
          mrr: 299,
          healthScore: 92,
          tier: 'Professional',
        }
      ],
      alerts: [
        {
          id: 'alert_001',
          type: 'churn_risk',
          severity: 'medium',
          message: 'Customer usage decreased by 40% this month',
          subscriptionId: 'sub_demo_001',
        }
      ]
    };
  }

  private getFallbackRevenueIntelligence(): RevenueIntelligence {
    return {
      opportunities: [
        {
          type: 'upsell',
          description: 'Customer ready for Professional tier upgrade',
          potentialRevenue: 1200,
          confidence: 0.85,
          priority: 'high'
        }
      ],
      risks: [
        {
          type: 'churn',
          description: 'Decreased usage pattern detected',
          impact: 2400,
          probability: 0.25,
          severity: 'medium'
        }
      ],
      insights: [
        {
          category: 'usage',
          title: 'Peak usage hours identified',
          description: 'Most activity occurs between 9-11 AM EST',
          actionable: true
        }
      ],
      predictions: {
        nextMonthRevenue: {
          amount: 92500,
          confidence: 0.89
        },
        churnRisk: {
          probability: 0.12,
          factors: ['decreased_usage', 'support_tickets']
        }
      }
    };
  }

  private getFallbackSubscriptionData() {
    return {
      subscription: {
        id: 'sub_demo_001',
        customerId: 'cust_demo_001',
        planId: 'plan_professional',
        status: 'active',
        mrr: 299,
        tier: 'Professional'
      },
      healthScore: 87,
      riskLevel: 'low',
      expansionOpportunities: [
        {
          type: 'feature_upgrade',
          description: 'Advanced analytics package',
          potentialRevenue: 100,
          confidence: 0.75
        }
      ],
      churnPrediction: {
        churnProbability: 0.08,
        riskLevel: 'low',
        factors: []
      }
    };
  }

  private getFallbackUsageAnalytics(): UsageAnalytics {
    return {
      metrics: [
        {
          name: 'API Calls',
          value: 15420,
          unit: 'calls',
          trend: 'up',
          change: 12.5
        },
        {
          name: 'Data Volume',
          value: 2.3,
          unit: 'GB',
          trend: 'stable',
          change: 0.2
        }
      ],
      recommendations: [
        {
          type: 'tier_upgrade',
          title: 'Consider Professional Tier',
          description: 'Your usage suggests you would benefit from higher limits',
          impact: 'Improved performance and additional features'
        }
      ],
      tierRecommendation: {
        currentTier: 'Starter',
        recommendedTier: 'Professional',
        reason: 'usage_growth',
        confidence: 0.82
      }
    };
  }

  private getFallbackChurnPrediction() {
    return {
      churnPrediction: {
        churnProbability: 0.08,
        riskLevel: 'low',
        factors: [],
        recommendations: []
      },
      renewalForecast: {
        renewalProbability: 0.92,
        expectedRevenue: 299,
        confidence: 0.88
      }
    };
  }

  private getFallbackHealthMonitoring() {
    return {
      alerts: [],
      summary: {
        critical: 0,
        high: 0,
        medium: 1,
        low: 2,
        total: 3
      }
    };
  }
}

export const billingService = new BillingService();
