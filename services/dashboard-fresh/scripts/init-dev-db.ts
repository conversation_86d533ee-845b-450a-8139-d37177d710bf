#!/usr/bin/env -S deno run -A
// Development Database Initialization Script
// Sets up PostgreSQL with TimescaleDB for local development

import { Client } from "postgres";

const DB_CONFIG = {
  hostname: Deno.env.get("DB_HOST") || "localhost",
  port: parseInt(Deno.env.get("DB_PORT") || "5432"),
  user: Deno.env.get("DB_USER") || "postgres",
  password: Deno.env.get("DB_PASSWORD") || "password",
  database: "postgres", // Connect to default database first
  tls: {
    enabled: false,
    enforce: false,
  },
};

const TARGET_DB = Deno.env.get("DB_NAME") || "ecommerce_analytics";

async function initializeDatabase() {
  console.log("🚀 Initializing development database...");
  
  let client: Client | null = null;
  
  try {
    // Connect to PostgreSQL
    console.log(`📡 Connecting to PostgreSQL at ${DB_CONFIG.hostname}:${DB_CONFIG.port}...`);
    client = new Client(DB_CONFIG);
    await client.connect();
    console.log("✅ Connected to PostgreSQL");
    
    // Check if database exists
    console.log(`🔍 Checking if database '${TARGET_DB}' exists...`);
    const dbExists = await client.queryArray(
      "SELECT 1 FROM pg_database WHERE datname = $1",
      [TARGET_DB]
    );
    
    if (dbExists.rows.length === 0) {
      console.log(`📦 Creating database '${TARGET_DB}'...`);
      await client.queryArray(`CREATE DATABASE "${TARGET_DB}"`);
      console.log(`✅ Database '${TARGET_DB}' created`);
    } else {
      console.log(`✅ Database '${TARGET_DB}' already exists`);
    }
    
    await client.end();
    
    // Connect to target database
    const targetConfig = { ...DB_CONFIG, database: TARGET_DB };
    client = new Client(targetConfig);
    await client.connect();
    console.log(`✅ Connected to database '${TARGET_DB}'`);
    
    // Check TimescaleDB extension
    console.log("🔍 Checking TimescaleDB extension...");
    const timescaleExists = await client.queryArray(
      "SELECT 1 FROM pg_extension WHERE extname = 'timescaledb'"
    );
    
    if (timescaleExists.rows.length === 0) {
      console.log("📦 Installing TimescaleDB extension...");
      try {
        await client.queryArray("CREATE EXTENSION IF NOT EXISTS timescaledb");
        console.log("✅ TimescaleDB extension installed");
      } catch (error) {
        console.warn("⚠️ TimescaleDB extension not available:", (error as Error).message);
        console.log("💡 You can continue without TimescaleDB for basic functionality");
      }
    } else {
      console.log("✅ TimescaleDB extension already installed");
    }
    
    // Create basic tables for development
    console.log("📦 Creating basic development tables...");
    
    // Users table
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        tenant_id VARCHAR(255) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
    
    // Customer events table (for analytics)
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS customer_events (
        id BIGSERIAL PRIMARY KEY,
        tenant_id VARCHAR(255) NOT NULL,
        user_id VARCHAR(255),
        event_type VARCHAR(100) NOT NULL,
        event_data JSONB,
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        session_id VARCHAR(255),
        ip_address INET,
        user_agent TEXT
      )
    `);
    
    // Links table (for link tracking)
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS links (
        id SERIAL PRIMARY KEY,
        tenant_id VARCHAR(255) NOT NULL,
        short_code VARCHAR(50) UNIQUE NOT NULL,
        original_url TEXT NOT NULL,
        title VARCHAR(255),
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        expires_at TIMESTAMP WITH TIME ZONE,
        is_active BOOLEAN DEFAULT true
      )
    `);
    
    // Link clicks table
    await client.queryArray(`
      CREATE TABLE IF NOT EXISTS link_clicks (
        id BIGSERIAL PRIMARY KEY,
        tenant_id VARCHAR(255) NOT NULL,
        link_id INTEGER REFERENCES links(id),
        clicked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        ip_address INET,
        user_agent TEXT,
        referrer TEXT,
        country VARCHAR(2),
        city VARCHAR(100)
      )
    `);
    
    // Create TimescaleDB hypertables if extension is available
    const timescaleAvailable = await client.queryArray(
      "SELECT 1 FROM pg_extension WHERE extname = 'timescaledb'"
    );
    
    if (timescaleAvailable.rows.length > 0) {
      console.log("📊 Creating TimescaleDB hypertables...");
      
      try {
        // Convert customer_events to hypertable
        await client.queryArray(`
          SELECT create_hypertable('customer_events', 'timestamp', 
            chunk_time_interval => INTERVAL '1 day',
            if_not_exists => TRUE
          )
        `);
        
        // Convert link_clicks to hypertable
        await client.queryArray(`
          SELECT create_hypertable('link_clicks', 'clicked_at', 
            chunk_time_interval => INTERVAL '1 day',
            if_not_exists => TRUE
          )
        `);
        
        console.log("✅ TimescaleDB hypertables created");
      } catch (error) {
        console.warn("⚠️ Failed to create hypertables:", (error as Error).message);
      }
    }
    
    // Create indexes for better performance
    console.log("📊 Creating database indexes...");
    
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_customer_events_tenant_timestamp 
      ON customer_events (tenant_id, timestamp DESC)
    `);
    
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_customer_events_event_type 
      ON customer_events (event_type)
    `);
    
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_link_clicks_tenant_clicked_at 
      ON link_clicks (tenant_id, clicked_at DESC)
    `);
    
    await client.queryArray(`
      CREATE INDEX IF NOT EXISTS idx_links_tenant_active 
      ON links (tenant_id, is_active)
    `);
    
    // Insert sample data for development
    console.log("📦 Inserting sample development data...");
    
    // Sample user
    await client.queryArray(`
      INSERT INTO users (email, name, tenant_id) 
      VALUES ('<EMAIL>', 'Demo User', 'tenant_demo_456')
      ON CONFLICT (email) DO NOTHING
    `);
    
    // Sample link
    await client.queryArray(`
      INSERT INTO links (tenant_id, short_code, original_url, title) 
      VALUES ('tenant_demo_456', 'demo123', 'https://example.com', 'Demo Link')
      ON CONFLICT (short_code) DO NOTHING
    `);
    
    // Sample events (last 7 days)
    const now = new Date();
    for (let i = 0; i < 7; i++) {
      const eventDate = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000));
      const eventCount = Math.floor(Math.random() * 100) + 50;
      
      for (let j = 0; j < eventCount; j++) {
        const eventTime = new Date(eventDate.getTime() + (Math.random() * 24 * 60 * 60 * 1000));
        await client.queryArray(`
          INSERT INTO customer_events (tenant_id, event_type, event_data, timestamp)
          VALUES ($1, $2, $3, $4)
        `, [
          'tenant_demo_456',
          ['page_view', 'click', 'conversion', 'signup'][Math.floor(Math.random() * 4)],
          JSON.stringify({ page: '/demo', source: 'organic' }),
          eventTime.toISOString()
        ]);
      }
    }
    
    console.log("✅ Sample data inserted");
    
    // Test the connection
    console.log("🧪 Testing database functionality...");
    
    const eventCount = await client.queryArray(`
      SELECT COUNT(*) FROM customer_events WHERE tenant_id = 'tenant_demo_456'
    `);
    
    const linkCount = await client.queryArray(`
      SELECT COUNT(*) FROM links WHERE tenant_id = 'tenant_demo_456'
    `);
    
    console.log(`✅ Database test successful:`);
    console.log(`   - Events: ${eventCount.rows[0][0]}`);
    console.log(`   - Links: ${linkCount.rows[0][0]}`);
    
    await client.end();
    
    console.log("🎉 Development database initialization complete!");
    console.log("");
    console.log("📋 Connection details:");
    console.log(`   Host: ${DB_CONFIG.hostname}:${DB_CONFIG.port}`);
    console.log(`   Database: ${TARGET_DB}`);
    console.log(`   User: ${DB_CONFIG.user}`);
    console.log("");
    console.log("🚀 You can now start the development server with: deno task dev");
    
  } catch (error) {
    console.error("❌ Database initialization failed:", error);
    console.log("");
    console.log("💡 Troubleshooting tips:");
    console.log("   1. Make sure PostgreSQL is running");
    console.log("   2. Check your database credentials in .env");
    console.log("   3. Ensure the database user has CREATE privileges");
    console.log("   4. For TimescaleDB, make sure the extension is available");
    
    if (client) {
      try {
        await client.end();
      } catch {
        // Ignore cleanup errors
      }
    }
    
    Deno.exit(1);
  }
}

// Run the initialization
if (import.meta.main) {
  await initializeDatabase();
}
