# E-commerce Analytics SaaS - Dashboard Fresh Environment Configuration

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=ecommerce_analytics
DB_SSL=false

# Alternative: Full DATABASE_URL (overrides individual DB_* settings)
# DATABASE_URL=postgresql://postgres:password@localhost:5432/ecommerce_analytics

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Application Configuration
NODE_ENV=development
PORT=8000
HOST=localhost

# Authentication
JWT_SECRET=your-jwt-secret-key-here
SESSION_SECRET=your-session-secret-key-here

# Mock Authentication (for development)
MOCK_AUTH=true
MOCK_USER_ID=user_123
MOCK_TENANT_ID=tenant_demo

# API Configuration
API_BASE_URL=http://localhost:3003
BILLING_SERVICE_URL=http://localhost:3003
ANALYTICS_SERVICE_URL=http://localhost:3001
INTEGRATION_SERVICE_URL=http://localhost:3002

# Feature Flags
ENABLE_REAL_TIME_UPDATES=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_DEBUG_LOGGING=false

# TimescaleDB Configuration
TIMESCALEDB_ENABLED=true
TIMESCALEDB_CHUNK_TIME_INTERVAL=1d
TIMESCALEDB_COMPRESSION_ENABLED=true

# Development Settings
DEV_MODE=true
HOT_RELOAD=true
CACHE_STATIC_ASSETS=false

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Security
CORS_ORIGIN=http://localhost:8000
CSRF_PROTECTION=true

# Performance
QUERY_TIMEOUT=30000
CONNECTION_POOL_SIZE=10
MAX_QUERY_COMPLEXITY=1000

# Monitoring
HEALTH_CHECK_INTERVAL=30000
METRICS_COLLECTION=true
ERROR_REPORTING=true
