# Development Environment Configuration
# E-commerce Analytics SaaS - Dashboard Fresh

# Database Configuration (Development)
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=ecommerce_analytics
DB_SSL=false

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Application Configuration
NODE_ENV=development
PORT=8000
HOST=localhost

# Authentication (Development)
JWT_SECRET=dev-jwt-secret-key-not-for-production
SESSION_SECRET=dev-session-secret-key-not-for-production

# Mock Authentication (Enabled for Development)
MOCK_AUTH=true
MOCK_USER_ID=user_demo_123
MOCK_TENANT_ID=tenant_demo_456

# API Configuration (Development Services)
API_BASE_URL=http://localhost:3003
BILLING_SERVICE_URL=http://localhost:3003
ANALYTICS_SERVICE_URL=http://localhost:3001
INTEGRATION_SERVICE_URL=http://localhost:3002

# Feature Flags (Development)
ENABLE_REAL_TIME_UPDATES=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_DEBUG_LOGGING=true

# TimescaleDB Configuration
TIMESCALEDB_ENABLED=true
TIMESCALEDB_CHUNK_TIME_INTERVAL=1d
TIMESCALEDB_COMPRESSION_ENABLED=false

# Development Settings
DEV_MODE=true
HOT_RELOAD=true
CACHE_STATIC_ASSETS=false

# Logging (Development)
LOG_LEVEL=debug
LOG_FORMAT=pretty

# Security (Development)
CORS_ORIGIN=http://localhost:8000
CSRF_PROTECTION=false

# Performance (Development)
QUERY_TIMEOUT=30000
CONNECTION_POOL_SIZE=5
MAX_QUERY_COMPLEXITY=1000

# Monitoring (Development)
HEALTH_CHECK_INTERVAL=30000
METRICS_COLLECTION=true
ERROR_REPORTING=true

# Development Database Fallback
USE_MOCK_DATA_ON_DB_ERROR=true
MOCK_DATA_DELAY=100
