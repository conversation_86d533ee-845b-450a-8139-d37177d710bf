import { Client } from "postgres";

// Database configuration with proper SSL handling
const DATABASE_CONFIG = {
  hostname: Deno.env.get("DB_HOST") || "localhost",
  port: parseInt(Deno.env.get("DB_PORT") || "5432"),
  user: Deno.env.get("DB_USER") || "postgres",
  password: Deno.env.get("DB_PASSWORD") || "password",
  database: Deno.env.get("DB_NAME") || "ecommerce_analytics",
  tls: {
    enabled: Deno.env.get("DB_SSL") === "true" || Deno.env.get("NODE_ENV") === "production",
    enforce: false, // Don't enforce SSL in development
    caCertificates: [], // Add CA certificates if needed
  },
  connection: {
    attempts: 3,
    interval: 1000,
  },
  pool: {
    size: 10,
    lazy: true,
  },
};

// Fallback to DATABASE_URL if provided
const DATABASE_URL = Deno.env.get("DATABASE_URL");
if (DATABASE_URL) {
  try {
    const url = new URL(DATABASE_URL);
    DATABASE_CONFIG.hostname = url.hostname;
    DATABASE_CONFIG.port = parseInt(url.port) || 5432;
    DATABASE_CONFIG.user = url.username;
    DATABASE_CONFIG.password = url.password;
    DATABASE_CONFIG.database = url.pathname.slice(1);

    // Check for SSL in URL parameters
    const sslParam = url.searchParams.get("sslmode");
    if (sslParam === "require" || sslParam === "prefer") {
      DATABASE_CONFIG.tls.enabled = true;
    }
  } catch (error) {
    console.warn("Failed to parse DATABASE_URL, using individual config:", error.message);
  }
}

let client: Client | null = null;
let dbAvailable = true;
let lastConnectionAttempt = 0;
const CONNECTION_RETRY_DELAY = 30000; // 30 seconds

export async function getDatabase(): Promise<Client> {
  // If database is known to be unavailable and we haven't waited long enough, throw immediately
  if (!dbAvailable && Date.now() - lastConnectionAttempt < CONNECTION_RETRY_DELAY) {
    throw new Error("Database unavailable - using fallback data");
  }

  if (!client) {
    try {
      lastConnectionAttempt = Date.now();

      // Create client with proper configuration
      client = new Client(DATABASE_CONFIG);

      // Connect with timeout and retry logic
      await connectWithRetry(client);
      dbAvailable = true;

      console.log(`✅ Database connected to ${DATABASE_CONFIG.hostname}:${DATABASE_CONFIG.port}/${DATABASE_CONFIG.database}`);
    } catch (error) {
      dbAvailable = false;
      client = null;
      console.error("❌ Database connection failed:", error.message);
      throw new Error("Database unavailable - using fallback data");
    }
  }
  return client;
}

async function connectWithRetry(client: Client, maxRetries = 3): Promise<void> {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await client.connect();
      return; // Success
    } catch (error) {
      lastError = error as Error;
      console.warn(`Database connection attempt ${attempt}/${maxRetries} failed:`, error.message);

      if (attempt < maxRetries) {
        // Wait before retry with exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError || new Error("Failed to connect after retries");
}

export async function initializeDatabase(): Promise<void> {
  try {
    const db = await getDatabase();
    
    // Test connection
    await db.queryArray("SELECT 1");
    console.log("✅ Database connection established");
    
    // Ensure required tables exist (basic check)
    const tables = await db.queryArray(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    
    const tableNames = tables.rows.map(row => row[0]);
    const requiredTables = ['users', 'links', 'analytics_events', 'integrations'];
    
    for (const table of requiredTables) {
      if (!tableNames.includes(table)) {
        console.warn(`⚠️  Table '${table}' not found in database`);
      }
    }
    
  } catch (error) {
    console.error("❌ Database connection failed:", error);
    throw error;
  }
}

export async function closeDatabase(): Promise<void> {
  if (client) {
    await client.end();
    client = null;
    console.log("Database connection closed");
  }
}

// Query helpers with tenant isolation
export async function queryWithTenant<T = Record<string, unknown>>(
  sql: string,
  params: unknown[] = [],
  tenantId?: string
): Promise<T[]> {
  const db = await getDatabase();
  
  // Add tenant_id filter if provided and not already in query
  if (tenantId && !sql.toLowerCase().includes('tenant_id')) {
    if (sql.toLowerCase().includes('where')) {
      sql += ` AND tenant_id = $${params.length + 1}`;
    } else {
      sql += ` WHERE tenant_id = $${params.length + 1}`;
    }
    params.push(tenantId);
  }
  
  const result = await db.queryObject<T>(sql, params);
  return result.rows;
}

export async function queryOneWithTenant<T = Record<string, unknown>>(
  sql: string,
  params: unknown[] = [],
  tenantId?: string
): Promise<T | null> {
  const results = await queryWithTenant<T>(sql, params, tenantId);
  return results.length > 0 ? results[0] : null;
}

// Time-series query helpers for TimescaleDB
export async function queryTimeSeries<T = Record<string, unknown>>(
  table: string,
  timeColumn: string,
  startTime: Date,
  endTime: Date,
  tenantId?: string,
  additionalWhere?: string,
  params: unknown[] = []
): Promise<T[]> {
  let sql = `
    SELECT * FROM ${table}
    WHERE ${timeColumn} >= $1 AND ${timeColumn} <= $2
  `;
  
  const queryParams = [startTime.toISOString(), endTime.toISOString(), ...params];
  
  if (tenantId) {
    sql += ` AND tenant_id = $${queryParams.length + 1}`;
    queryParams.push(tenantId);
  }
  
  if (additionalWhere) {
    sql += ` AND ${additionalWhere}`;
  }
  
  sql += ` ORDER BY ${timeColumn} ASC`;
  
  const db = await getDatabase();
  const result = await db.queryObject<T>(sql, queryParams);
  return result.rows;
}

// Aggregation helpers
export async function queryAggregated<T = Record<string, unknown>>(
  table: string,
  aggregations: string[],
  timeColumn: string,
  interval: string,
  startTime: Date,
  endTime: Date,
  tenantId?: string,
  groupBy?: string[]
): Promise<T[]> {
  const aggFields = aggregations.join(', ');
  const groupFields = groupBy ? `, ${groupBy.join(', ')}` : '';
  const groupByClause = groupBy ? `GROUP BY time_bucket, ${groupBy.join(', ')}` : 'GROUP BY time_bucket';
  
  let sql = `
    SELECT 
      time_bucket('${interval}', ${timeColumn}) as time_bucket,
      ${aggFields}${groupFields}
    FROM ${table}
    WHERE ${timeColumn} >= $1 AND ${timeColumn} <= $2
  `;
  
  const queryParams = [startTime.toISOString(), endTime.toISOString()];
  
  if (tenantId) {
    sql += ` AND tenant_id = $${queryParams.length + 1}`;
    queryParams.push(tenantId);
  }
  
  sql += ` ${groupByClause} ORDER BY time_bucket ASC`;
  
  const db = await getDatabase();
  const result = await db.queryObject<T>(sql, queryParams);
  return result.rows;
}

// Health check with detailed diagnostics
export async function checkDatabaseHealth(): Promise<{
  healthy: boolean;
  details: {
    connected: boolean;
    timescaledb: boolean;
    latency: number;
    error?: string;
  };
}> {
  const startTime = Date.now();
  const details = {
    connected: false,
    timescaledb: false,
    latency: 0,
    error: undefined as string | undefined,
  };

  try {
    const db = await getDatabase();
    details.connected = true;

    // Test basic connectivity
    const healthResult = await db.queryArray("SELECT 1");
    if (healthResult.rows.length === 0) {
      throw new Error("Health check query returned no results");
    }

    // Test TimescaleDB extension
    try {
      const timescaleResult = await db.queryArray(
        "SELECT extname FROM pg_extension WHERE extname = 'timescaledb'"
      );
      details.timescaledb = timescaleResult.rows.length > 0;
    } catch (error) {
      console.warn("TimescaleDB extension check failed:", (error as Error).message);
      details.timescaledb = false;
    }

    details.latency = Date.now() - startTime;
    return { healthy: true, details };
  } catch (error) {
    details.latency = Date.now() - startTime;
    details.error = (error as Error).message;
    console.error("Database health check failed:", error);
    return { healthy: false, details };
  }
}

// Connection status
export function getDatabaseStatus(): {
  available: boolean;
  lastAttempt: number;
  retryDelay: number;
} {
  return {
    available: dbAvailable,
    lastAttempt: lastConnectionAttempt,
    retryDelay: CONNECTION_RETRY_DELAY,
  };
}

// Transaction helper
export async function withTransaction<T>(
  callback: (client: Client) => Promise<T>
): Promise<T> {
  const db = await getDatabase();
  
  try {
    await db.queryArray("BEGIN");
    const result = await callback(db);
    await db.queryArray("COMMIT");
    return result;
  } catch (error) {
    await db.queryArray("ROLLBACK");
    throw error;
  }
}
