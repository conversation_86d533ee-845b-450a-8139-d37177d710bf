// Revenue Analytics API Integration Layer
// Comprehensive API client with error handling, caching, and real-time updates

import { signal } from "@preact/signals";

// Global state signals for real-time updates
export const revenueMetricsSignal = signal<any>(null);
export const customerHealthSignal = signal<any>(null);
export const churnPredictionsSignal = signal<any>(null);
export const expansionOpportunitiesSignal = signal<any>(null);
export const unifiedAnalyticsSignal = signal<any>(null);
export const subscriptionInsightsSignal = signal<any>(null);

// API configuration
const API_BASE_URL = "/api";
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
const RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 1000; // 1 second

interface ApiResponse<T> {
  success: boolean;
  data: T;
  metadata?: {
    tenantId: string;
    queryTime: string;
    generatedAt: string;
  };
  error?: string;
  timestamp: string;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class RevenueAnalyticsApiClient {
  private cache = new Map<string, CacheEntry<any>>();
  private authToken: string | null = null;
  private tenantId: string | null = null;

  constructor() {
    this.initializeAuth();
  }

  private initializeAuth() {
    // Initialize authentication from localStorage or session
    this.authToken = localStorage.getItem('auth_token');
    this.tenantId = localStorage.getItem('tenant_id');
  }

  /**
   * Generic API request method with retry logic and error handling
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    useCache = true
  ): Promise<T> {
    const cacheKey = `${endpoint}_${JSON.stringify(options)}`;
    
    // Check cache first
    if (useCache && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)!;
      if (Date.now() - cached.timestamp < cached.ttl) {
        return cached.data;
      }
      this.cache.delete(cacheKey);
    }

    const url = `${API_BASE_URL}${endpoint}`;
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...options.headers as Record<string, string>,
    };

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    if (this.tenantId) {
      headers['X-Tenant-ID'] = this.tenantId;
    }

    const requestOptions: RequestInit = {
      ...options,
      headers,
    };

    let lastError: Error | null = null;

    // Retry logic
    for (let attempt = 1; attempt <= RETRY_ATTEMPTS; attempt++) {
      try {
        const response = await fetch(url, requestOptions);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result: ApiResponse<T> = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'API request failed');
        }

        // Cache successful responses
        if (useCache) {
          this.cache.set(cacheKey, {
            data: result.data,
            timestamp: Date.now(),
            ttl: CACHE_TTL,
          });
        }

        return result.data;
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < RETRY_ATTEMPTS) {
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * attempt));
        }
      }
    }

    throw lastError || new Error('Request failed after retries');
  }

  /**
   * Revenue Analytics API Methods
   */
  async getRevenueMetrics(timeRange = '30d', useCache = true) {
    try {
      const data = await this.makeRequest<any>(
        `/revenue-analytics/metrics?timeRange=${timeRange}`,
        { method: 'GET' },
        useCache
      );
      
      revenueMetricsSignal.value = data;
      return data;
    } catch (error) {
      console.error('Failed to fetch revenue metrics:', error);
      throw error;
    }
  }

  async getCustomerHealthScores(customerId?: string, useCache = true) {
    try {
      const endpoint = customerId 
        ? `/revenue-analytics/customer-health?customerId=${customerId}`
        : '/revenue-analytics/customer-health';
        
      const data = await this.makeRequest<any>(endpoint, { method: 'GET' }, useCache);
      
      customerHealthSignal.value = data;
      return data;
    } catch (error) {
      console.error('Failed to fetch customer health scores:', error);
      throw error;
    }
  }

  async getMLChurnPredictions(riskThreshold = 0.5, useCache = true) {
    try {
      const data = await this.makeRequest<any>(
        `/revenue-analytics/ml-churn-predictions?riskThreshold=${riskThreshold}`,
        { method: 'GET' },
        useCache
      );
      
      churnPredictionsSignal.value = data;
      return data;
    } catch (error) {
      console.error('Failed to fetch churn predictions:', error);
      throw error;
    }
  }

  async getAdvancedExpansionOpportunities(useCache = true) {
    try {
      const data = await this.makeRequest<any>(
        '/revenue-analytics/advanced-expansion-opportunities',
        { method: 'GET' },
        useCache
      );
      
      expansionOpportunitiesSignal.value = data;
      return data;
    } catch (error) {
      console.error('Failed to fetch expansion opportunities:', error);
      throw error;
    }
  }

  async getUnifiedGrowthAnalysis(options: {
    timeRange?: string;
    includeForecasting?: boolean;
    includeRecommendations?: boolean;
    includeSegmentation?: boolean;
    optimizationFocus?: string;
  } = {}, useCache = true) {
    try {
      const params = new URLSearchParams();
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, value.toString());
        }
      });

      const data = await this.makeRequest<any>(
        `/revenue-analytics/unified-growth-analysis?${params.toString()}`,
        { method: 'GET' },
        useCache
      );
      
      unifiedAnalyticsSignal.value = data;
      return data;
    } catch (error) {
      console.error('Failed to fetch unified growth analysis:', error);
      throw error;
    }
  }

  async getAdvancedCustomerSegments(useCache = true) {
    try {
      const data = await this.makeRequest<any>(
        '/revenue-analytics/advanced-segments',
        { method: 'GET' },
        useCache
      );
      
      return data;
    } catch (error) {
      console.error('Failed to fetch customer segments:', error);
      throw error;
    }
  }

  async getCohortRevenueAnalysis(timeRange = '12m', useCache = true) {
    try {
      const data = await this.makeRequest<any>(
        `/revenue-analytics/cohort-revenue-analysis?timeRange=${timeRange}`,
        { method: 'GET' },
        useCache
      );
      
      return data;
    } catch (error) {
      console.error('Failed to fetch cohort revenue analysis:', error);
      throw error;
    }
  }

  async getRevenueOptimizationInsights(timeRange = '30d', useCache = true) {
    try {
      const data = await this.makeRequest<any>(
        `/revenue-analytics/revenue-optimization-insights?timeRange=${timeRange}`,
        { method: 'GET' },
        useCache
      );
      
      return data;
    } catch (error) {
      console.error('Failed to fetch revenue optimization insights:', error);
      throw error;
    }
  }

  /**
   * Enhanced Subscription Management API Methods
   */
  async generateDynamicPricing(request: {
    customerId: string;
    planId: string;
    usageMetrics: any;
    marketConditions?: any;
    customerProfile?: any;
  }) {
    try {
      const data = await this.makeRequest<any>(
        '/enhanced-subscriptions/dynamic-pricing',
        {
          method: 'POST',
          body: JSON.stringify(request),
        },
        false // Don't cache POST requests
      );
      
      return data;
    } catch (error) {
      console.error('Failed to generate dynamic pricing:', error);
      throw error;
    }
  }

  async getTierRecommendation(customerId: string, useCache = true) {
    try {
      const data = await this.makeRequest<any>(
        `/enhanced-subscriptions/tier-recommendation/${customerId}`,
        { method: 'GET' },
        useCache
      );
      
      return data;
    } catch (error) {
      console.error('Failed to get tier recommendation:', error);
      throw error;
    }
  }

  async getRevenueIntelligenceInsights(subscriptionId: string, useCache = true) {
    try {
      const data = await this.makeRequest<any>(
        `/enhanced-subscriptions/revenue-insights/${subscriptionId}`,
        { method: 'GET' },
        useCache
      );
      
      subscriptionInsightsSignal.value = data;
      return data;
    } catch (error) {
      console.error('Failed to get revenue intelligence insights:', error);
      throw error;
    }
  }

  async getSubscriptionOptimizationDashboard(useCache = true) {
    try {
      const data = await this.makeRequest<any>(
        '/enhanced-subscriptions/optimization-dashboard',
        { method: 'GET' },
        useCache
      );
      
      return data;
    } catch (error) {
      console.error('Failed to get subscription optimization dashboard:', error);
      throw error;
    }
  }

  async optimizeUsageBasedBilling(request: {
    planId: string;
    historicalUsage: Array<{
      period: string;
      usage: number;
      revenue: number;
    }>;
  }) {
    try {
      const data = await this.makeRequest<any>(
        '/enhanced-subscriptions/optimize-usage-billing',
        {
          method: 'POST',
          body: JSON.stringify(request),
        },
        false
      );
      
      return data;
    } catch (error) {
      console.error('Failed to optimize usage-based billing:', error);
      throw error;
    }
  }

  /**
   * Real-time data refresh methods
   */
  async refreshAllData() {
    try {
      await Promise.all([
        this.getRevenueMetrics('30d', false),
        this.getCustomerHealthScores(undefined, false),
        this.getMLChurnPredictions(0.5, false),
        this.getAdvancedExpansionOpportunities(false),
      ]);
    } catch (error) {
      console.error('Failed to refresh all data:', error);
    }
  }

  /**
   * Cache management
   */
  clearCache() {
    this.cache.clear();
  }

  getCacheStats() {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys()),
    };
  }

  /**
   * Authentication management
   */
  setAuth(token: string, tenantId: string) {
    this.authToken = token;
    this.tenantId = tenantId;
    localStorage.setItem('auth_token', token);
    localStorage.setItem('tenant_id', tenantId);
  }

  clearAuth() {
    this.authToken = null;
    this.tenantId = null;
    localStorage.removeItem('auth_token');
    localStorage.removeItem('tenant_id');
    this.clearCache();
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      await this.makeRequest<any>('/health', { method: 'GET' }, false);
      return true;
    } catch (error) {
      return false;
    }
  }
}

// Export singleton instance
export const revenueAnalyticsApi = new RevenueAnalyticsApiClient();

// Export utility functions
export const formatCurrency = (value: number, currency = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

export const formatPercentage = (value: number, decimals = 1): string => {
  return `${(value * 100).toFixed(decimals)}%`;
};

export const formatNumber = (value: number): string => {
  return new Intl.NumberFormat('en-US').format(value);
};

export const getHealthScoreColor = (score: number): string => {
  if (score >= 80) return 'text-green-600 dark:text-green-400';
  if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
  if (score >= 40) return 'text-orange-600 dark:text-orange-400';
  return 'text-red-600 dark:text-red-400';
};

export const getChurnRiskColor = (risk: string): string => {
  switch (risk) {
    case 'low': return 'text-green-600 dark:text-green-400';
    case 'medium': return 'text-yellow-600 dark:text-yellow-400';
    case 'high': return 'text-orange-600 dark:text-orange-400';
    case 'critical': return 'text-red-600 dark:text-red-400';
    default: return 'text-gray-600 dark:text-gray-400';
  }
};
