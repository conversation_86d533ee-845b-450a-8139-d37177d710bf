// Sales Materials Route
// Comprehensive sales enablement tools including ROI calculator, performance comparisons, and battle cards

import { defineRoute } from "$fresh/server.ts";
import { Head } from "$fresh/runtime.ts";
import EnhancedROICalculator from "../islands/EnhancedROICalculator.tsx";
import PerformanceComparisonTools from "../islands/PerformanceComparisonTools.tsx";
import SalesMaterialsDashboard from "../islands/SalesMaterialsDashboard.tsx";

interface SalesMaterialsPageProps {
  tool?: 'roi' | 'performance' | 'battle-cards' | 'case-studies' | 'reports';
}

export default defineRoute<SalesMaterialsPageProps>(async (req, ctx) => {
  const url = new URL(req.url);
  const tool = url.searchParams.get("tool") as SalesMaterialsPageProps['tool'] || "roi";

  return (
    <>
      <Head>
        <title>Sales Materials - E-commerce Analytics SaaS Platform</title>
        <meta name="description" content="Comprehensive sales enablement tools including ROI calculator, performance comparisons, battle cards, and case studies for demonstrating our 97-98% performance advantage." />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        
        {/* Sales-specific meta tags */}
        <meta property="og:title" content="Sales Materials - E-commerce Analytics SaaS Platform" />
        <meta property="og:description" content="Sales enablement tools showcasing 24,390 events/sec processing and 97-98% performance advantage." />
        <meta property="og:type" content="website" />
        
        {/* Preload critical assets */}
        <link rel="preload" href="/static/styles.css" as="style" />
        <link rel="preconnect" href="https://cdn.skypack.dev" />
        
        {/* Sales materials specific styling */}
        <style>{`
          .sales-header {
            background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
          }
          
          .tool-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            margin: 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
          }
          
          .tool-badge:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
          }
          
          .sales-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
          }
          
          .performance-highlight {
            background: linear-gradient(45deg, #059669, #047857);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            display: inline-block;
            margin: 0.25rem;
            animation: glow 2s ease-in-out infinite alternate;
          }
          
          @keyframes glow {
            from { box-shadow: 0 0 5px rgba(5, 150, 105, 0.5); }
            to { box-shadow: 0 0 20px rgba(5, 150, 105, 0.8); }
          }
          
          .tool-navigation {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 10;
          }
          
          .tool-nav-item {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            margin: 0 0.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
          }
          
          .tool-nav-item.active {
            background: #3b82f6;
            color: white;
          }
          
          .tool-nav-item:not(.active) {
            background: #f3f4f6;
            color: #374151;
          }
          
          .tool-nav-item:hover:not(.active) {
            background: #e5e7eb;
          }
          
          .sales-metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
          }
          
          .metric-highlight {
            background: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #3b82f6;
            text-align: center;
          }
          
          .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
          }
          
          .metric-label {
            color: #6b7280;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
          }
          
          .metric-description {
            color: #059669;
            font-weight: 600;
            font-size: 0.875rem;
            margin-top: 0.5rem;
          }
        `}</style>
      </Head>

      <div class="min-h-screen bg-gray-50">
        {/* Sales Header */}
        <div class="sales-header">
          <div class="sales-container">
            <h1 class="text-4xl font-bold mb-4">
              Sales Enablement Materials
            </h1>
            <p class="text-xl mb-6">
              Comprehensive tools for demonstrating our competitive advantages and calculating customer ROI
            </p>
            
            <div class="flex flex-wrap justify-center items-center">
              <div class="performance-highlight">
                📊 24,390+ Events/Sec Processing
              </div>
              <div class="performance-highlight">
                ⚡ 97-98% Performance Advantage
              </div>
              <div class="performance-highlight">
                💰 60-80% Cost Reduction
              </div>
              <div class="performance-highlight">
                🚀 15-Minute Setup
              </div>
            </div>
          </div>
        </div>

        {/* Tool Navigation */}
        <div class="tool-navigation">
          <div class="sales-container">
            <div class="flex flex-wrap justify-center">
              <a 
                href="/sales-materials?tool=roi"
                class={`tool-nav-item ${tool === 'roi' ? 'active' : ''}`}
              >
                💰 ROI Calculator
              </a>
              <a 
                href="/sales-materials?tool=performance"
                class={`tool-nav-item ${tool === 'performance' ? 'active' : ''}`}
              >
                📈 Performance Comparison
              </a>
              <a 
                href="/sales-materials?tool=battle-cards"
                class={`tool-nav-item ${tool === 'battle-cards' ? 'active' : ''}`}
              >
                ⚔️ Battle Cards
              </a>
              <a 
                href="/sales-materials?tool=case-studies"
                class={`tool-nav-item ${tool === 'case-studies' ? 'active' : ''}`}
              >
                📋 Case Studies
              </a>
              <a 
                href="/sales-materials?tool=reports"
                class={`tool-nav-item ${tool === 'reports' ? 'active' : ''}`}
              >
                📄 Reports
              </a>
            </div>
          </div>
        </div>

        {/* Key Performance Metrics */}
        <div class="sales-container py-8">
          <div class="sales-metrics-grid">
            <div class="metric-highlight">
              <div class="metric-value">24,390+</div>
              <div class="metric-label">Events per Second</div>
              <div class="metric-description">2,400% faster than Google Analytics</div>
            </div>
            
            <div class="metric-highlight">
              <div class="metric-value">6-11ms</div>
              <div class="metric-label">Query Response Time</div>
              <div class="metric-description">97-99% faster than competitors</div>
            </div>
            
            <div class="metric-highlight">
              <div class="metric-value">99.95%</div>
              <div class="metric-label">Data Accuracy</div>
              <div class="metric-description">Industry-leading precision</div>
            </div>
            
            <div class="metric-highlight">
              <div class="metric-value">15 min</div>
              <div class="metric-label">Setup Time</div>
              <div class="metric-description">99% faster implementation</div>
            </div>
            
            <div class="metric-highlight">
              <div class="metric-value">60-80%</div>
              <div class="metric-label">Cost Reduction</div>
              <div class="metric-description">vs enterprise competitors</div>
            </div>
            
            <div class="metric-highlight">
              <div class="metric-value">99.97%</div>
              <div class="metric-label">System Uptime</div>
              <div class="metric-description">Enterprise-grade reliability</div>
            </div>
          </div>
        </div>

        {/* Tool Content */}
        <div class="sales-container pb-8">
          {tool === 'roi' && (
            <div>
              <h2 class="text-3xl font-bold text-gray-900 mb-6 text-center">
                Interactive ROI Calculator
              </h2>
              <p class="text-lg text-gray-600 mb-8 text-center max-w-3xl mx-auto">
                Calculate customer-specific ROI with industry templates, competitive comparisons, and downloadable reports.
              </p>
              <EnhancedROICalculator mode="interactive" />
            </div>
          )}

          {tool === 'performance' && (
            <div>
              <h2 class="text-3xl font-bold text-gray-900 mb-6 text-center">
                Performance Comparison Tools
              </h2>
              <p class="text-lg text-gray-600 mb-8 text-center max-w-3xl mx-auto">
                Comprehensive competitive analysis showcasing our 97-98% performance advantage over major competitors.
              </p>
              <PerformanceComparisonTools mode="interactive" />
            </div>
          )}

          {(tool === 'battle-cards' || tool === 'case-studies' || tool === 'reports') && (
            <div>
              <h2 class="text-3xl font-bold text-gray-900 mb-6 text-center">
                {tool === 'battle-cards' && 'Competitive Battle Cards'}
                {tool === 'case-studies' && 'Customer Case Studies'}
                {tool === 'reports' && 'Sales Reports & Materials'}
              </h2>
              <p class="text-lg text-gray-600 mb-8 text-center max-w-3xl mx-auto">
                {tool === 'battle-cards' && 'Detailed competitive analysis, objection handling, and winning messages for sales conversations.'}
                {tool === 'case-studies' && 'Real customer success stories showcasing 20-35% revenue improvements and implementation results.'}
                {tool === 'reports' && 'Generate professional reports, presentations, and sales materials for customer meetings.'}
              </p>
              <SalesMaterialsDashboard tool={tool} />
            </div>
          )}
        </div>

        {/* Sales Footer */}
        <div class="bg-gray-800 text-white py-8">
          <div class="sales-container text-center">
            <h3 class="text-2xl font-bold mb-4">Ready to Close More Deals?</h3>
            <p class="text-gray-300 mb-6">
              Use these sales materials to demonstrate our competitive advantages and accelerate your sales cycle.
            </p>
            <div class="space-x-4">
              <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                Download Sales Kit
              </button>
              <button class="bg-transparent border-2 border-white text-white font-bold py-3 px-6 rounded-lg hover:bg-white hover:text-gray-800 transition-colors">
                Schedule Training
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
});
