import { FreshContext } from "$fresh/server.ts";
import { verifyJWT } from "../utils/auth.ts";
import { getCookies } from "$std/http/cookie.ts";
import { AppState } from "../types/fresh.ts";

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Security headers configuration
const SECURITY_HEADERS = {
  "X-Content-Type-Options": "nosniff",
  "X-Frame-Options": "DENY",
  "X-XSS-Protection": "1; mode=block",
  "Referrer-Policy": "strict-origin-when-cross-origin",
  "Permissions-Policy": "camera=(), microphone=(), geolocation=()",
};

// Add security headers to response
function addSecurityHeaders(response: Response): Response {
  const enableSecurityHeaders = Deno.env.get("ENABLE_SECURITY_HEADERS") === "true";

  if (enableSecurityHeaders) {
    Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    // Add CSP header if configured
    const csp = Deno.env.get("CONTENT_SECURITY_POLICY");
    if (csp) {
      response.headers.set("Content-Security-Policy", csp);
    }

    // Add HSTS header in production
    if (Deno.env.get("DENO_ENV") === "production") {
      response.headers.set(
        "Strict-Transport-Security",
        "max-age=31536000; includeSubDomains"
      );
    }
  }

  return response;
}

// Check rate limit for client IP
function checkRateLimit(clientIP: string): boolean {
  // Skip rate limiting in development
  if (Deno.env.get("DENO_ENV") !== "production") {
    return true;
  }

  const maxRequests = parseInt(Deno.env.get("RATE_LIMIT_MAX_REQUESTS") || "100");
  const windowMs = parseInt(Deno.env.get("RATE_LIMIT_WINDOW_MS") || "900000"); // 15 minutes

  const now = Date.now();
  const clientData = rateLimitStore.get(clientIP);

  if (!clientData || now > clientData.resetTime) {
    rateLimitStore.set(clientIP, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (clientData.count >= maxRequests) {
    return false;
  }

  clientData.count++;
  return true;
}

export async function handler(
  req: Request,
  ctx: FreshContext<AppState>,
) {
  // Apply rate limiting
  const clientIP = ctx.remoteAddr?.hostname || "unknown";
  if (!checkRateLimit(clientIP)) {
    const response = new Response(JSON.stringify({ error: "Too Many Requests" }), {
      status: 429,
      headers: { "Content-Type": "application/json" }
    });
    return addSecurityHeaders(response);
  }

  // Handle source map requests silently to prevent 404 errors in dev tools
  if (ctx.url.pathname.endsWith('.map')) {
    return new Response('', { status: 404 });
  }

  // Skip auth for public routes and static assets
  const publicRoutes = ['/auth/login', '/auth/register', '/auth/forgot-password', '/auth/logout', '/marketing-landing', '/api/auth/login', '/api/auth/register', '/api/auth/logout', '/api/health'];
  const staticAssetPaths = ['/_frsh/', '/favicon.ico', '/static/', '/styles.css'];

  const isPublicRoute = publicRoutes.some(route => ctx.url.pathname.startsWith(route));
  const isStaticAsset = staticAssetPaths.some(path => ctx.url.pathname.startsWith(path));

  if (isPublicRoute || isStaticAsset) {
    const response = await ctx.next();
    return addSecurityHeaders(response);
  }

  // Production-ready authentication with development fallback
  const isDevelopment = Deno.env.get("DENO_ENV") !== "production";

  // In development mode, provide mock authentication for easier testing
  if (isDevelopment && !ctx.state.isAuthenticated) {
    console.log("🔧 Development mode: Using mock authentication");
    ctx.state.user = {
      id: "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11",
      email: "<EMAIL>",
      firstName: "Demo",
      lastName: "User",
      companyName: "Demo Company",
      role: "admin",
      roles: ["admin", "marketplace_participant", "partner_seeker"],
      tenant_id: "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11",
      tenantId: "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11",
      isActive: true,
      emailVerified: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      // Marketplace-specific properties for development testing
      marketplace_tier: "advanced",
      data_sharing_consent: true,
      partner_access_level: "collaborate",
      network_permissions: {
        can_view_benchmarks: true,
        can_initiate_partnerships: true,
        can_access_shared_analytics: true,
        can_create_data_products: true,
        can_manage_revenue_sharing: true
      },
      privacy_settings: {
        allow_partner_discovery: true,
        share_anonymized_metrics: true,
        participate_in_benchmarks: true
      }
    };
    ctx.state.isAuthenticated = true;
    ctx.state.token = "dev-mock-token";
    return ctx.next();
  }

  // Try to get token from Authorization header or cookie
  let token: string | null = null;

  const authHeader = req.headers.get("Authorization");
  if (authHeader?.startsWith("Bearer ")) {
    token = authHeader.substring(7);
  } else {
    // Fallback to cookie for browser requests
    const cookies = getCookies(req.headers);
    token = cookies.auth_token || null;
  }

  if (token) {
    try {
      // Enhanced JWT verification with additional security checks
      const user = await verifyJWT(token);

      // Validate tenant_id is present for multi-tenant security
      if (!user.tenant_id) {
        console.warn("User missing tenant_id, using user.id as fallback");
        user.tenant_id = user.id;
      }

      // Set tenantId for compatibility with existing code
      if (!user.tenantId) {
        user.tenantId = user.tenant_id;
      }

      // Additional security validation in production
      if (Deno.env.get("DENO_ENV") === "production") {
        // Validate user is active and email is verified
        if (!user.isActive) {
          throw new Error("User account is inactive");
        }

        if (!user.emailVerified) {
          throw new Error("Email verification required");
        }
      }

      ctx.state.user = user;
      ctx.state.isAuthenticated = true;
      ctx.state.token = token; // Store token for API proxy calls
    } catch (error) {
      console.error("Auth verification failed:", error);
      ctx.state.user = null;
      ctx.state.isAuthenticated = false;
      ctx.state.token = undefined;
    }
  } else {
    ctx.state.user = null;
    ctx.state.isAuthenticated = false;
    ctx.state.token = undefined;
  }

  // Redirect unauthenticated users to login for protected routes
  if (!ctx.state.isAuthenticated && !isPublicRoute && !isStaticAsset) {
    // For API routes, return 401 with security headers
    if (ctx.url.pathname.startsWith('/api/')) {
      const response = new Response(JSON.stringify({
        error: "Unauthorized",
        message: "Authentication required to access this resource"
      }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
      return addSecurityHeaders(response);
    }

    // For page routes, redirect to login with security headers
    const loginUrl = `/auth/login?redirect=${encodeURIComponent(ctx.url.pathname)}`;
    const response = new Response("", {
      status: 302,
      headers: { Location: loginUrl },
    });
    return addSecurityHeaders(response);
  }

  // Continue to next middleware/handler and add security headers to response
  const response = await ctx.next();
  return addSecurityHeaders(response);
}
