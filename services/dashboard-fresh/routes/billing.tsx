import { defineRoute } from "$fresh/server.ts";
import { billingService } from "../services/billingService.ts";
import { AppState } from "../types/fresh.ts";
import BillingDashboard from "../islands/billing/BillingDashboard.tsx";

export default defineRoute<AppState>(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch billing data server-side with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Billing data timeout")), 3000)
    );

    const [revenueOps, revenueIntelligence, healthMonitoring] = await Promise.race([
      Promise.all([
        billingService.getRevenueOperationsDashboard(),
        billingService.getRevenueIntelligence(),
        billingService.getHealthMonitoring(),
      ]),
      timeoutPromise
    ]) as any[];

    return (
      <div class="billing-page">
        {/* Page Header */}
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Billing & Revenue Intelligence
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Comprehensive subscription management and revenue optimization insights
          </p>
        </div>

        {/* Billing Dashboard Component */}
        <BillingDashboard 
          initialRevenueOps={revenueOps}
          initialRevenueIntelligence={revenueIntelligence}
          initialHealthMonitoring={healthMonitoring}
          user={user}
        />
      </div>
    );
  } catch (error) {
    console.error("Billing dashboard error:", error);

    // Use fallback data when service is unavailable
    const fallbackRevenueOps = {
      summary: {
        totalSubscriptions: 1247,
        totalMRR: 89750,
        averageHealthScore: 87.3,
        churnRate: 2.1,
        expansionRevenue: 15420,
      },
      subscriptions: [
        {
          id: 'sub_demo_001',
          customerId: 'cust_demo_001',
          planId: 'plan_professional',
          status: 'active',
          mrr: 299,
          healthScore: 92,
          tier: 'Professional',
        }
      ],
      alerts: []
    };

    const fallbackRevenueIntelligence = {
      opportunities: [
        {
          type: 'upsell',
          description: 'Customer ready for Professional tier upgrade',
          potentialRevenue: 1200,
          confidence: 0.85,
          priority: 'high'
        }
      ],
      risks: [
        {
          type: 'churn',
          description: 'Decreased usage pattern detected',
          impact: 2400,
          probability: 0.25,
          severity: 'medium'
        }
      ],
      insights: [
        {
          category: 'usage',
          title: 'Peak usage hours identified',
          description: 'Most activity occurs between 9-11 AM EST',
          actionable: true
        }
      ],
      predictions: {
        nextMonthRevenue: {
          amount: 92500,
          confidence: 0.89
        },
        churnRisk: {
          probability: 0.12,
          factors: ['decreased_usage', 'support_tickets']
        }
      }
    };

    const fallbackHealthMonitoring = {
      alerts: [],
      summary: {
        critical: 0,
        high: 0,
        medium: 1,
        low: 2,
        total: 3
      }
    };

    return (
      <div class="billing-page">
        {/* Offline Notice */}
        <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-yellow-800 dark:text-yellow-200 text-sm">
              Showing demo billing data - billing service is currently unavailable
            </span>
          </div>
        </div>

        {/* Page Header */}
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Billing & Revenue Intelligence
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Comprehensive subscription management and revenue optimization insights
          </p>
        </div>

        {/* Billing Dashboard Component with Fallback Data */}
        <BillingDashboard 
          initialRevenueOps={fallbackRevenueOps}
          initialRevenueIntelligence={fallbackRevenueIntelligence}
          initialHealthMonitoring={fallbackHealthMonitoring}
          user={user}
          isOffline={true}
        />
      </div>
    );
  }
});
