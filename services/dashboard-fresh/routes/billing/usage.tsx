import { defineRoute } from "$fresh/server.ts";
import { AppState } from "../../types/fresh.ts";
import UsageBillingDashboard from "../../islands/billing/UsageBillingDashboard.tsx";

export default defineRoute<AppState>(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch usage billing data server-side with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Usage billing data timeout")), 3000)
    );

    const [usageAnalytics, billingOptimization, tierRecommendations] = await Promise.race([
      Promise.all([
        fetch('http://localhost:8000/api/billing/usage-analytics/all').then(r => r.json()),
        fetch('http://localhost:8000/api/billing/optimize-usage-billing').then(r => r.json()),
        fetch('http://localhost:8000/api/billing/tier-recommendations').then(r => r.json()),
      ]),
      timeoutPromise
    ]) as any[];

    return (
      <div class="usage-billing-page">
        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/billing" class="hover:text-gray-900 dark:hover:text-gray-200">Billing</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Usage Analytics</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Usage-Based Billing Dashboard
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Real-time usage analytics, tier recommendations, and billing optimization insights
          </p>
        </div>

        {/* Usage Billing Dashboard Component */}
        <UsageBillingDashboard 
          initialUsageAnalytics={usageAnalytics?.data || null}
          initialBillingOptimization={billingOptimization?.data || null}
          initialTierRecommendations={tierRecommendations?.data || null}
          user={user}
        />
      </div>
    );
  } catch (error) {
    console.error("Usage billing error:", error);

    // Use fallback data when service is unavailable
    const fallbackUsageAnalytics = {
      summary: {
        totalCustomers: 1247,
        totalUsage: 2847392,
        averageUsagePerCustomer: 2284,
        usageGrowthRate: 18.7,
        overageCustomers: 23,
        underutilizedCustomers: 156,
        optimalUsageCustomers: 1068,
        totalRevenue: 89750,
        usageBasedRevenue: 34200
      },
      customers: [
        {
          customerId: 'cust_usage_001',
          subscriptionId: 'sub_usage_001',
          companyName: 'DataFlow Corp',
          currentTier: 'Professional',
          monthlyUsage: 8750,
          tierLimit: 10000,
          usagePercentage: 87.5,
          overage: 0,
          cost: 299,
          usageCost: 0,
          totalCost: 299,
          trend: 'increasing',
          efficiency: 0.92,
          recommendation: {
            type: 'tier_upgrade',
            suggestedTier: 'Enterprise',
            reasoning: 'Approaching tier limit with increasing usage trend',
            potentialSavings: 0,
            potentialCost: 150
          },
          usageHistory: [
            { date: '2024-01-01', usage: 7200 },
            { date: '2024-01-08', usage: 7800 },
            { date: '2024-01-15', usage: 8200 },
            { date: '2024-01-22', usage: 8750 }
          ]
        },
        {
          customerId: 'cust_usage_002',
          subscriptionId: 'sub_usage_002',
          companyName: 'Analytics Plus',
          currentTier: 'Enterprise',
          monthlyUsage: 15420,
          tierLimit: 50000,
          usagePercentage: 30.8,
          overage: 0,
          cost: 999,
          usageCost: 0,
          totalCost: 999,
          trend: 'stable',
          efficiency: 0.31,
          recommendation: {
            type: 'tier_downgrade',
            suggestedTier: 'Professional',
            reasoning: 'Significantly underutilizing current tier capacity',
            potentialSavings: 700,
            potentialCost: 0
          },
          usageHistory: [
            { date: '2024-01-01', usage: 14800 },
            { date: '2024-01-08', usage: 15200 },
            { date: '2024-01-15', usage: 15100 },
            { date: '2024-01-22', usage: 15420 }
          ]
        },
        {
          customerId: 'cust_usage_003',
          subscriptionId: 'sub_usage_003',
          companyName: 'MetricsMaster',
          currentTier: 'Professional',
          monthlyUsage: 12340,
          tierLimit: 10000,
          usagePercentage: 123.4,
          overage: 2340,
          cost: 299,
          usageCost: 117,
          totalCost: 416,
          trend: 'increasing',
          efficiency: 1.23,
          recommendation: {
            type: 'usage_based_pricing',
            suggestedTier: 'Usage-Based Pro',
            reasoning: 'Consistent overage indicates need for flexible pricing',
            potentialSavings: 89,
            potentialCost: 0
          },
          usageHistory: [
            { date: '2024-01-01', usage: 10800 },
            { date: '2024-01-08', usage: 11200 },
            { date: '2024-01-15', usage: 11800 },
            { date: '2024-01-22', usage: 12340 }
          ]
        }
      ],
      insights: {
        usagePatterns: [
          {
            pattern: 'Peak usage during business hours',
            description: 'Most API calls occur between 9 AM - 5 PM EST',
            impact: 'Optimize server capacity during peak hours',
            actionable: true
          },
          {
            pattern: 'Weekly usage cycles',
            description: 'Usage typically peaks mid-week (Tuesday-Thursday)',
            impact: 'Predictable load patterns for infrastructure planning',
            actionable: false
          },
          {
            pattern: 'Seasonal variations',
            description: 'Q4 shows 35% higher usage due to holiday analytics',
            impact: 'Plan capacity and pricing for seasonal demands',
            actionable: true
          }
        ],
        optimizationOpportunities: [
          {
            type: 'tier_optimization',
            title: 'Tier Optimization Opportunities',
            description: '156 customers could benefit from tier adjustments',
            potentialSavings: 45200,
            effort: 'low',
            customers: 156
          },
          {
            type: 'usage_based_migration',
            title: 'Usage-Based Pricing Migration',
            description: '23 customers with consistent overage should migrate to usage-based pricing',
            potentialSavings: 12800,
            effort: 'medium',
            customers: 23
          },
          {
            type: 'efficiency_improvement',
            title: 'Usage Efficiency Programs',
            description: 'Help customers optimize their usage patterns',
            potentialSavings: 8900,
            effort: 'high',
            customers: 89
          }
        ],
        revenueImpact: {
          currentMonthlyRevenue: 89750,
          potentialAdditionalRevenue: 15600,
          optimizationSavings: 66900,
          netRevenueImpact: 82500,
          confidenceLevel: 0.87
        }
      },
      trends: {
        usageGrowth: [
          { period: 'Week 1', usage: 2650000, customers: 1198 },
          { period: 'Week 2', usage: 2720000, customers: 1215 },
          { period: 'Week 3', usage: 2790000, customers: 1232 },
          { period: 'Week 4', usage: 2847392, customers: 1247 }
        ],
        revenueGrowth: [
          { period: 'Week 1', revenue: 86200, usageBased: 31800 },
          { period: 'Week 2', revenue: 87500, usageBased: 32400 },
          { period: 'Week 3', revenue: 88900, usageBased: 33600 },
          { period: 'Week 4', revenue: 89750, usageBased: 34200 }
        ],
        tierDistribution: [
          { tier: 'Starter', customers: 423, revenue: 12690, avgUsage: 1250 },
          { tier: 'Professional', customers: 567, revenue: 42030, avgUsage: 3200 },
          { tier: 'Enterprise', customers: 234, revenue: 23400, avgUsage: 8900 },
          { tier: 'Usage-Based', customers: 23, revenue: 11630, avgUsage: 15600 }
        ]
      }
    };

    const fallbackBillingOptimization = {
      recommendations: [
        {
          id: 'opt_001',
          type: 'tier_adjustment',
          priority: 'high',
          title: 'Optimize Tier Assignments',
          description: 'Automatically adjust customer tiers based on usage patterns',
          impact: {
            revenueIncrease: 15600,
            costReduction: 8900,
            customerSatisfaction: 0.15,
            churnReduction: 0.08
          },
          implementation: {
            effort: 'low',
            timeframe: '2 weeks',
            resources: ['billing_team', 'customer_success']
          },
          affectedCustomers: 156
        },
        {
          id: 'opt_002',
          type: 'usage_based_migration',
          priority: 'medium',
          title: 'Migrate High-Usage Customers',
          description: 'Move customers with consistent overage to usage-based pricing',
          impact: {
            revenueIncrease: 12800,
            costReduction: 0,
            customerSatisfaction: 0.22,
            churnReduction: 0.12
          },
          implementation: {
            effort: 'medium',
            timeframe: '4 weeks',
            resources: ['billing_team', 'sales_team', 'customer_success']
          },
          affectedCustomers: 23
        },
        {
          id: 'opt_003',
          type: 'efficiency_program',
          priority: 'low',
          title: 'Usage Efficiency Initiative',
          description: 'Help customers optimize their usage patterns and reduce waste',
          impact: {
            revenueIncrease: 0,
            costReduction: 8900,
            customerSatisfaction: 0.18,
            churnReduction: 0.05
          },
          implementation: {
            effort: 'high',
            timeframe: '8 weeks',
            resources: ['customer_success', 'technical_support', 'product_team']
          },
          affectedCustomers: 89
        }
      ],
      automationOpportunities: [
        {
          process: 'Tier Upgrade Notifications',
          description: 'Automatically notify customers approaching tier limits',
          complexity: 'low',
          expectedSavings: 2400,
          implementationTime: '1 week'
        },
        {
          process: 'Usage-Based Pricing Recommendations',
          description: 'Automated recommendations for usage-based pricing migration',
          complexity: 'medium',
          expectedSavings: 5600,
          implementationTime: '3 weeks'
        },
        {
          process: 'Overage Alerts and Optimization',
          description: 'Real-time overage alerts with optimization suggestions',
          complexity: 'high',
          expectedSavings: 8900,
          implementationTime: '6 weeks'
        }
      ]
    };

    const fallbackTierRecommendations = [
      {
        customerId: 'cust_usage_001',
        currentTier: 'Professional',
        recommendedTier: 'Enterprise',
        confidence: 0.89,
        reasoning: [
          'Usage at 87.5% of tier limit',
          'Increasing usage trend (+15% month-over-month)',
          'High engagement with premium features'
        ],
        impact: {
          costChange: 150,
          usageHeadroom: 40000,
          featureBenefits: ['Advanced analytics', 'Priority support', 'Custom integrations']
        },
        timeline: 'Recommend upgrade within 2 weeks'
      },
      {
        customerId: 'cust_usage_002',
        currentTier: 'Enterprise',
        recommendedTier: 'Professional',
        confidence: 0.92,
        reasoning: [
          'Usage at only 30.8% of tier limit',
          'Stable usage pattern with no growth',
          'Not utilizing premium features'
        ],
        impact: {
          costChange: -700,
          usageHeadroom: 10000,
          featureBenefits: ['Cost savings', 'Right-sized plan', 'Maintained core functionality']
        },
        timeline: 'Safe to downgrade immediately'
      },
      {
        customerId: 'cust_usage_003',
        currentTier: 'Professional',
        recommendedTier: 'Usage-Based Pro',
        confidence: 0.85,
        reasoning: [
          'Consistent overage charges (23.4% over limit)',
          'Variable usage patterns',
          'Cost-conscious customer segment'
        ],
        impact: {
          costChange: -89,
          usageHeadroom: 'unlimited',
          featureBenefits: ['Pay-per-use flexibility', 'No overage charges', 'Scalable pricing']
        },
        timeline: 'Migrate at next billing cycle'
      }
    ];

    return (
      <div class="usage-billing-page">
        {/* Offline Notice */}
        <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-yellow-800 dark:text-yellow-200 text-sm">
              Showing demo usage billing data - service is currently unavailable
            </span>
          </div>
        </div>

        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/billing" class="hover:text-gray-900 dark:hover:text-gray-200">Billing</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Usage Analytics</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Usage-Based Billing Dashboard
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Real-time usage analytics, tier recommendations, and billing optimization insights
          </p>
        </div>

        {/* Usage Billing Dashboard Component with Fallback Data */}
        <UsageBillingDashboard 
          initialUsageAnalytics={fallbackUsageAnalytics}
          initialBillingOptimization={fallbackBillingOptimization}
          initialTierRecommendations={fallbackTierRecommendations}
          user={user}
          isOffline={true}
        />
      </div>
    );
  }
});
