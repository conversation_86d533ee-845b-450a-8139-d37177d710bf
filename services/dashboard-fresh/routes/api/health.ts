import { checkDatabaseHealth, getDatabaseStatus } from "../../utils/database.ts";
import { checkRedisHealth } from "../../utils/redis.ts";

export const handler = {
  async GET(_req: Request) {
    const startTime = Date.now();

    try {
      // Check database connectivity with detailed diagnostics
      const dbHealth = await checkDatabaseHealth();
      const dbStatus = getDatabaseStatus();

      // Check Redis connectivity
      const redisHealthy = await checkRedisHealth();

      // Calculate response time
      const responseTime = Date.now() - startTime;

      // Determine overall health status
      const isHealthy = dbHealth.healthy && redisHealthy;
      const status = isHealthy ? "healthy" : "unhealthy";

      const healthData = {
        status,
        timestamp: new Date().toISOString(),
        responseTime: `${responseTime}ms`,
        services: {
          database: {
            status: dbHealth.healthy ? "healthy" : "unhealthy",
            type: "PostgreSQL with TimescaleDB",
            connected: dbHealth.details.connected,
            timescaledb: dbHealth.details.timescaledb,
            latency: `${dbHealth.details.latency}ms`,
            error: dbHealth.details.error,
            lastAttempt: new Date(dbStatus.lastAttempt).toISOString(),
            available: dbStatus.available,
          },
          redis: {
            status: redisHealthy ? "healthy" : "unhealthy",
            type: "Redis Cache",
          },
        },
        version: "1.0.0",
        environment: Deno.env.get("NODE_ENV") || "development",
        devMode: Deno.env.get("DEV_MODE") === "true",
        mockAuth: Deno.env.get("MOCK_AUTH") === "true",
      };
      
      return Response.json(healthData, {
        status: isHealthy ? 200 : 503,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache",
        },
      });
    } catch (error) {
      console.error("Health check error:", error);
      
      const responseTime = Date.now() - startTime;
      
      return Response.json({
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        responseTime: `${responseTime}ms`,
        error: "Health check failed",
        services: {
          database: { status: "unknown" },
          redis: { status: "unknown" },
        },
        version: "1.0.0",
        environment: Deno.env.get("DENO_ENV") || "development",
      }, {
        status: 503,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache",
        },
      });
    }
  },
};
