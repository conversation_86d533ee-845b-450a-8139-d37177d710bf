// Enhanced Billing API Proxy Route
// Proxies billing requests to the billing service for subscription management and revenue intelligence

import { defineRoute } from "$fresh/server.ts";

const BILLING_SERVICE_URL = Deno.env.get("BILLING_SERVICE_URL") || "http://localhost:3003";

export default defineRoute(async (req, ctx) => {
  const { path } = ctx.params;
  const url = new URL(req.url);
  const user = ctx.state.user;
  
  // Require authentication for billing endpoints
  if (!user) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Authentication required",
        message: "Please log in to access billing features",
      }),
      {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
  
  // Construct the target URL
  const targetUrl = `${BILLING_SERVICE_URL}/api/enhanced-subscriptions/${path}${url.search}`;
  
  try {
    // Forward the request to the billing service
    const response = await fetch(targetUrl, {
      method: req.method,
      headers: {
        ...Object.fromEntries(req.headers.entries()),
        'X-Tenant-ID': user.tenant_id || user.id, // Use user's tenant ID
        'Content-Type': 'application/json',
      },
      body: req.method !== 'GET' && req.method !== 'HEAD' ? await req.text() : undefined,
    });

    // Forward the response
    const responseBody = await response.text();
    
    return new Response(responseBody, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...Object.fromEntries(response.headers.entries()),
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Tenant-ID',
      },
    });
  } catch (error) {
    console.error("Billing API proxy error:", error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: "Billing service unavailable",
        message: "Unable to connect to billing service. Please try again later.",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 503,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});
