import { defineRoute } from "$fresh/server.ts";
import { AppState } from "../../types/fresh.ts";
import TrialManagementDashboard from "../../islands/trials/TrialManagementDashboard.tsx";

export default defineRoute<AppState>(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch trial management data server-side with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Trial management data timeout")), 3000)
    );

    const [activeTrials, trialMetrics, trialWorkflows] = await Promise.race([
      Promise.all([
        fetch('http://localhost:8000/api/billing/trial/active-overview').then(r => r.json()),
        fetch('http://localhost:8000/api/billing/trial/metrics').then(r => r.json()),
        fetch('http://localhost:8000/api/billing/trial/workflows').then(r => r.json()),
      ]),
      timeoutPromise
    ]) as any[];

    return (
      <div class="trial-management-page">
        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/customer-success" class="hover:text-gray-900 dark:hover:text-gray-200">Customer Success</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Trial Management</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Enhanced Trial Management
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Comprehensive trial analytics, onboarding progress tracking, and automated workflow management
          </p>
        </div>

        {/* Trial Management Dashboard Component */}
        <TrialManagementDashboard 
          initialActiveTrials={activeTrials?.data || null}
          initialTrialMetrics={trialMetrics?.data || null}
          initialTrialWorkflows={trialWorkflows?.data || null}
          user={user}
        />
      </div>
    );
  } catch (error) {
    console.error("Trial management error:", error);

    // Use fallback data when service is unavailable
    const fallbackActiveTrials = {
      summary: {
        totalActiveTrials: 47,
        averageHealthScore: 78.5,
        conversionRate: 23.8,
        averageTrialLength: 14,
        onboardingCompletionRate: 67.2,
        atRiskTrials: 8,
        highValueTrials: 12,
        expiringThisWeek: 15
      },
      trials: [
        {
          id: 'trial_001',
          customerId: 'cust_trial_001',
          companyName: 'TechStart Inc.',
          email: '<EMAIL>',
          planTier: 'Professional',
          startDate: '2024-01-15T10:30:00Z',
          endDate: '2024-01-29T10:30:00Z',
          daysRemaining: 8,
          healthScore: 92,
          riskLevel: 'low' as const,
          onboardingProgress: {
            totalSteps: 8,
            completedSteps: 7,
            currentStep: 'integration_setup',
            completionRate: 87.5
          },
          usageMetrics: {
            loginCount: 24,
            featuresUsed: 12,
            dataPointsProcessed: 15420,
            apiCalls: 1250,
            lastActivity: '2024-01-22T14:30:00Z'
          },
          conversionPrediction: {
            probability: 0.89,
            factors: ['high_engagement', 'feature_adoption', 'support_interaction'],
            confidence: 0.92
          },
          interventions: [
            {
              type: 'success_call',
              scheduledDate: '2024-01-24T15:00:00Z',
              status: 'scheduled'
            }
          ]
        },
        {
          id: 'trial_002',
          customerId: 'cust_trial_002',
          companyName: 'DataCorp Solutions',
          email: '<EMAIL>',
          planTier: 'Enterprise',
          startDate: '2024-01-10T09:15:00Z',
          endDate: '2024-01-24T09:15:00Z',
          daysRemaining: 3,
          healthScore: 45,
          riskLevel: 'high' as const,
          onboardingProgress: {
            totalSteps: 10,
            completedSteps: 3,
            currentStep: 'data_integration',
            completionRate: 30.0
          },
          usageMetrics: {
            loginCount: 5,
            featuresUsed: 3,
            dataPointsProcessed: 850,
            apiCalls: 120,
            lastActivity: '2024-01-19T11:20:00Z'
          },
          conversionPrediction: {
            probability: 0.15,
            factors: ['low_engagement', 'incomplete_onboarding', 'limited_usage'],
            confidence: 0.87
          },
          interventions: [
            {
              type: 'urgent_outreach',
              scheduledDate: '2024-01-23T10:00:00Z',
              status: 'pending'
            },
            {
              type: 'onboarding_assistance',
              scheduledDate: '2024-01-23T14:00:00Z',
              status: 'scheduled'
            }
          ]
        },
        {
          id: 'trial_003',
          customerId: 'cust_trial_003',
          companyName: 'GrowthMetrics Ltd.',
          email: '<EMAIL>',
          planTier: 'Professional',
          startDate: '2024-01-18T16:45:00Z',
          endDate: '2024-02-01T16:45:00Z',
          daysRemaining: 11,
          healthScore: 76,
          riskLevel: 'medium' as const,
          onboardingProgress: {
            totalSteps: 8,
            completedSteps: 5,
            currentStep: 'dashboard_customization',
            completionRate: 62.5
          },
          usageMetrics: {
            loginCount: 18,
            featuresUsed: 8,
            dataPointsProcessed: 8920,
            apiCalls: 680,
            lastActivity: '2024-01-22T16:15:00Z'
          },
          conversionPrediction: {
            probability: 0.72,
            factors: ['moderate_engagement', 'steady_progress', 'feature_exploration'],
            confidence: 0.84
          },
          interventions: [
            {
              type: 'feature_demo',
              scheduledDate: '2024-01-25T11:00:00Z',
              status: 'scheduled'
            }
          ]
        }
      ],
      insights: {
        topConversionFactors: [
          { factor: 'Complete onboarding', impact: 0.45, description: 'Trials completing >80% of onboarding convert at 89% rate' },
          { factor: 'Regular usage', impact: 0.38, description: 'Daily active usage increases conversion by 38%' },
          { factor: 'Feature adoption', impact: 0.32, description: 'Using 5+ features correlates with 72% conversion rate' },
          { factor: 'Support engagement', impact: 0.28, description: 'Proactive support contact improves conversion by 28%' }
        ],
        riskIndicators: [
          { indicator: 'Low login frequency', threshold: '<3 logins/week', impact: 'High churn risk' },
          { indicator: 'Incomplete onboarding', threshold: '<50% completion', impact: 'Conversion unlikely' },
          { indicator: 'No API usage', threshold: '0 API calls', impact: 'Limited engagement' },
          { indicator: 'Extended inactivity', threshold: '>3 days', impact: 'Requires intervention' }
        ],
        recommendations: [
          {
            type: 'automated_workflow',
            title: 'Implement onboarding nudges',
            description: 'Automated email sequence for trials stuck at specific onboarding steps',
            expectedImpact: '+15% completion rate',
            effort: 'low'
          },
          {
            type: 'success_intervention',
            title: 'Proactive success calls',
            description: 'Schedule success calls for trials with >70% health score',
            expectedImpact: '+22% conversion rate',
            effort: 'medium'
          },
          {
            type: 'risk_mitigation',
            title: 'Early warning system',
            description: 'Automated alerts for trials showing risk indicators',
            expectedImpact: '+18% retention',
            effort: 'low'
          }
        ]
      }
    };

    const fallbackTrialMetrics = {
      conversionTrends: [
        { period: 'Week 1', conversions: 12, trials: 45, rate: 26.7 },
        { period: 'Week 2', conversions: 8, trials: 38, rate: 21.1 },
        { period: 'Week 3', conversions: 15, trials: 52, rate: 28.8 },
        { period: 'Week 4', conversions: 11, trials: 47, rate: 23.4 }
      ],
      onboardingMetrics: {
        averageCompletionTime: '3.2 days',
        dropoffPoints: [
          { step: 'Account Setup', dropoffRate: 5.2 },
          { step: 'Data Integration', dropoffRate: 18.7 },
          { step: 'Dashboard Configuration', dropoffRate: 12.3 },
          { step: 'Team Invitation', dropoffRate: 8.9 }
        ],
        completionRateByTier: {
          'Starter': 58.3,
          'Professional': 67.2,
          'Enterprise': 78.9
        }
      },
      usagePatterns: {
        averageSessionDuration: '24 minutes',
        peakUsageHours: ['9-11 AM', '2-4 PM'],
        mostUsedFeatures: [
          { feature: 'Analytics Dashboard', usage: 89.2 },
          { feature: 'Data Export', usage: 67.8 },
          { feature: 'Custom Reports', usage: 54.3 },
          { feature: 'API Integration', usage: 43.7 }
        ]
      },
      performanceMetrics: {
        averageTimeToValue: '2.8 days',
        customerSatisfactionScore: 8.4,
        supportTicketsPerTrial: 1.3,
        featureAdoptionRate: 72.5
      }
    };

    const fallbackTrialWorkflows = {
      activeWorkflows: [
        {
          id: 'workflow_onboarding',
          name: 'Automated Onboarding Sequence',
          description: 'Progressive onboarding with step-by-step guidance',
          triggers: ['trial_start', 'step_completion'],
          actions: ['send_email', 'schedule_call', 'assign_task'],
          status: 'active',
          performance: {
            triggered: 47,
            completed: 32,
            successRate: 68.1
          }
        },
        {
          id: 'workflow_risk_intervention',
          name: 'Risk Intervention Protocol',
          description: 'Automated interventions for at-risk trials',
          triggers: ['low_health_score', 'inactivity', 'onboarding_stall'],
          actions: ['urgent_outreach', 'success_call', 'onboarding_assistance'],
          status: 'active',
          performance: {
            triggered: 23,
            completed: 18,
            successRate: 78.3
          }
        },
        {
          id: 'workflow_conversion',
          name: 'Conversion Optimization',
          description: 'Targeted actions for high-potential trials',
          triggers: ['high_health_score', 'feature_adoption', 'engagement_spike'],
          actions: ['pricing_discussion', 'feature_demo', 'success_story'],
          status: 'active',
          performance: {
            triggered: 35,
            completed: 29,
            successRate: 82.9
          }
        }
      ],
      workflowTemplates: [
        {
          id: 'template_saas_onboarding',
          name: 'SaaS Onboarding Template',
          description: 'Standard onboarding workflow for SaaS trials',
          steps: 8,
          estimatedDuration: '5-7 days',
          conversionRate: 72.3
        },
        {
          id: 'template_enterprise_onboarding',
          name: 'Enterprise Onboarding Template',
          description: 'Extended onboarding for enterprise trials',
          steps: 12,
          estimatedDuration: '10-14 days',
          conversionRate: 84.7
        }
      ]
    };

    return (
      <div class="trial-management-page">
        {/* Offline Notice */}
        <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-yellow-800 dark:text-yellow-200 text-sm">
              Showing demo trial management data - service is currently unavailable
            </span>
          </div>
        </div>

        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/customer-success" class="hover:text-gray-900 dark:hover:text-gray-200">Customer Success</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Trial Management</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Enhanced Trial Management
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Comprehensive trial analytics, onboarding progress tracking, and automated workflow management
          </p>
        </div>

        {/* Trial Management Dashboard Component with Fallback Data */}
        <TrialManagementDashboard 
          initialActiveTrials={fallbackActiveTrials}
          initialTrialMetrics={fallbackTrialMetrics}
          initialTrialWorkflows={fallbackTrialWorkflows}
          user={user}
          isOffline={true}
        />
      </div>
    );
  }
});
