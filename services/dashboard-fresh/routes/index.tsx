import { defineRoute } from "$fresh/server.ts";
import KPIScorecard from "../islands/dashboard/KPIScorecard.tsx";
import RealtimeMetrics from "../islands/dashboard/RealtimeMetrics.tsx";
import { dashboardService } from "../services/dashboardService.ts";
import { AppState } from "../types/fresh.ts";

export default defineRoute<AppState>(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch dashboard data server-side with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Dashboard data timeout")), 2000)
    );

    const overview = await Promise.race([
      dashboardService.getOverview(user, { period: "30d" }),
      timeoutPromise
    ]) as any;

    const kpiData: Array<{
      title: string;
      value: number;
      change: number;
      trend: "up" | "down" | "neutral";
      format: "currency" | "number" | "percentage";
    }> = [
      {
        title: "Total Revenue",
        value: overview.totalRevenue || 0,
        change: overview.revenueChange || 0,
        trend: (overview.revenueChange || 0) >= 0 ? "up" : "down",
        format: "currency"
      },
      {
        title: "Active Users",
        value: overview.activeUsers || 0,
        change: overview.usersChange || 0,
        trend: (overview.usersChange || 0) >= 0 ? "up" : "down",
        format: "number"
      },
      {
        title: "Conversion Rate",
        value: overview.conversionRate || 0,
        change: overview.conversionChange || 0,
        trend: (overview.conversionChange || 0) >= 0 ? "up" : "down",
        format: "percentage"
      },
      {
        title: "Total Links",
        value: overview.totalLinks || 0,
        change: overview.linksChange || 0,
        trend: (overview.linksChange || 0) >= 0 ? "up" : "down",
        format: "number"
      }
    ];

    return (
      <div class="dashboard-page">
        {/* Page Header */}
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900">
            Welcome back, {user.firstName}!
          </h1>
          <p class="text-gray-600 mt-2">
            Here's what's happening with your {user.companyName || 'business'} today.
          </p>
        </div>

        {/* KPI Cards Grid */}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {kpiData.map((kpi, index) => (
            <KPIScorecard key={index} {...kpi} />
          ))}
        </div>

        {/* Real-time Metrics */}
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <RealtimeMetrics initialData={{
            totalRevenue: overview.totalRevenue || 0,
            activeUsers: overview.activeUsers || 0,
            conversionRate: overview.conversionRate || 0,
            totalLinks: overview.totalLinks || 0,
            lastUpdated: new Date().toISOString()
          }} />
          
          <div class="bg-white rounded-lg shadow-soft p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
              Recent Activity
            </h3>
            <div class="space-y-3">
              {overview.recentActivity?.map((activity, index: number) => (
                <div key={index} class="flex items-center space-x-3">
                  <div class="w-2 h-2 bg-primary-500 rounded-full"></div>
                  <span class="text-sm text-gray-600">{activity.description}</span>
                  <span class="text-xs text-gray-400 ml-auto">{activity.time}</span>
                </div>
              )) || (
                <p class="text-gray-500 text-sm">No recent activity</p>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div class="bg-white rounded-lg shadow-soft p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">
            Quick Actions
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a
              href="/links"
              class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
            >
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">Create Link</h4>
                <p class="text-sm text-gray-500">Generate a new branded link</p>
              </div>
            </a>

            <a 
              href="/analytics" 
              class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
            >
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">View Analytics</h4>
                <p class="text-sm text-gray-500">Detailed performance insights</p>
              </div>
            </a>

            <a 
              href="/integrations" 
              class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
            >
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">Manage Integrations</h4>
                <p class="text-sm text-gray-500">Connect your platforms</p>
              </div>
            </a>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Dashboard error:", error);

    // Use fallback data when database is unavailable
    const fallbackOverview = {
      totalRevenue: 125000,
      revenueChange: 12.5,
      activeUsers: 1250,
      usersChange: 8.3,
      conversionRate: 3.2,
      conversionChange: -0.5,
      totalLinks: 85,
      linksChange: 5,
      recentActivity: [
        { description: "New conversion: $125.50", time: "2 minutes ago" },
        { description: "Link clicked: summer-sale", time: "5 minutes ago" },
        { description: "New user registered", time: "8 minutes ago" }
      ]
    };

    const kpiData = [
      {
        title: "Total Revenue",
        value: fallbackOverview.totalRevenue,
        change: fallbackOverview.revenueChange,
        trend: "up" as const,
        format: "currency" as const
      },
      {
        title: "Active Users",
        value: fallbackOverview.activeUsers,
        change: fallbackOverview.usersChange,
        trend: "up" as const,
        format: "number" as const
      },
      {
        title: "Conversion Rate",
        value: fallbackOverview.conversionRate,
        change: fallbackOverview.conversionChange,
        trend: "down" as const,
        format: "percentage" as const
      },
      {
        title: "Total Links",
        value: fallbackOverview.totalLinks,
        change: fallbackOverview.linksChange,
        trend: "up" as const,
        format: "number" as const
      }
    ];

    return (
      <div class="dashboard-page">
        {/* Offline Notice */}
        <div class="mb-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-yellow-800 text-sm">
              Showing demo data - backend services are currently unavailable
            </span>
          </div>
        </div>

        {/* Page Header */}
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900">
            Welcome back, {user.firstName}!
          </h1>
          <p class="text-gray-600 mt-2">
            Here's what's happening with your {user.companyName || 'business'} today.
          </p>
        </div>

        {/* KPI Cards Grid */}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {kpiData.map((kpi, index) => (
            <KPIScorecard key={index} {...kpi} />
          ))}
        </div>

        {/* Real-time Metrics */}
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <RealtimeMetrics initialData={{
            totalRevenue: fallbackOverview.totalRevenue,
            activeUsers: fallbackOverview.activeUsers,
            conversionRate: fallbackOverview.conversionRate,
            totalLinks: fallbackOverview.totalLinks,
            recentActivity: fallbackOverview.recentActivity
          }} />

          {/* Recent Activity */}
          <div class="bg-white rounded-lg shadow-soft p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
              Recent Activity
            </h3>
            <div class="space-y-3">
              {fallbackOverview.recentActivity?.map((activity: any, index: number) => (
                <div key={index} class="flex items-center space-x-3">
                  <div class="w-2 h-2 bg-primary-500 rounded-full"></div>
                  <span class="text-sm text-gray-600">{activity.description}</span>
                  <span class="text-xs text-gray-400 ml-auto">{activity.time}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div class="bg-white rounded-lg shadow-soft p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">
            Quick Actions
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a
              href="/links"
              class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
            >
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">Create Link</h4>
                <p class="text-sm text-gray-500">Generate a new branded link</p>
              </div>
            </a>

            <a
              href="/analytics"
              class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
            >
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">View Analytics</h4>
                <p class="text-sm text-gray-500">Detailed performance insights</p>
              </div>
            </a>

            <a
              href="/billing"
              class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
            >
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">Billing & Revenue</h4>
                <p class="text-sm text-gray-500">Subscription management</p>
              </div>
            </a>

            <a
              href="/customer-success"
              class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
            >
              <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">Customer Success</h4>
                <p class="text-sm text-gray-500">Trial & health monitoring</p>
              </div>
            </a>

            <a
              href="/sales"
              class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
            >
              <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">Sales Tools</h4>
                <p class="text-sm text-gray-500">Demo & battle cards</p>
              </div>
            </a>

            <a
              href="/settings"
              class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
            >
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">Settings</h4>
                <p class="text-sm text-gray-500">Manage your account</p>
              </div>
            </a>
          </div>
        </div>
      </div>
    );
  }
});
