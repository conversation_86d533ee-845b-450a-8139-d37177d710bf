import { defineRoute } from "$fresh/server.ts";
import { AppState } from "../../types/fresh.ts";
import BattleCards from "../../islands/sales/BattleCards.tsx";

export default defineRoute<AppState>(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch battle cards data server-side with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Battle cards data timeout")), 3000)
    );

    // Fetch battle cards and competitive intelligence
    const [battleCards, competitiveIntel] = await Promise.race([
      Promise.all([
        fetch('/api/demo/battle-cards').then(r => r.json()),
        fetch('/api/demo/competitive-intelligence').then(r => r.json()),
      ]),
      timeoutPromise
    ]) as any[];

    return (
      <div class="battle-cards-page">
        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/sales" class="hover:text-gray-900 dark:hover:text-gray-200">Sales Tools</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Battle Cards</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Competitive Battle Cards
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Comprehensive competitive intelligence and positioning strategies for sales conversations
          </p>
        </div>

        {/* Battle Cards Component */}
        <BattleCards 
          initialBattleCards={battleCards?.data || null}
          initialCompetitiveIntel={competitiveIntel?.data || null}
          user={user}
        />
      </div>
    );
  } catch (error) {
    console.error("Battle cards error:", error);

    // Use fallback data when service is unavailable
    const fallbackBattleCards = [
      {
        id: 'battle_google_analytics',
        competitor: 'Google Analytics',
        category: 'Web Analytics',
        marketShare: 85.2,
        strengths: [
          'Market leader with widespread adoption',
          'Free tier available for small businesses',
          'Integration with Google Ads ecosystem',
          'Comprehensive reporting capabilities'
        ],
        weaknesses: [
          'Complex setup and configuration',
          'Limited real-time capabilities',
          'Data sampling in free version',
          'Steep learning curve for advanced features',
          'Privacy concerns and data ownership'
        ],
        ourAdvantages: [
          '97% faster query performance',
          'Real-time analytics out of the box',
          'E-commerce focused features',
          'Better customer journey tracking',
          'No data sampling limitations',
          'GDPR compliant by design'
        ],
        talkingPoints: [
          'Performance advantage in query speed',
          'Specialized for e-commerce use cases',
          'Better ROI tracking and attribution',
          'Simpler implementation and onboarding',
          'Real-time insights for immediate action'
        ],
        objectionHandling: [
          {
            objection: 'We already use Google Analytics',
            response: 'Our platform complements GA by providing real-time e-commerce insights that GA cannot deliver at the speed your business needs.'
          },
          {
            objection: 'Google Analytics is free',
            response: 'While GA is free, the time and resources needed to get actionable insights often cost more than our solution, which provides immediate value.'
          }
        ],
        winRate: 68.5,
        lastUpdated: '2024-03-01'
      },
      {
        id: 'battle_adobe_analytics',
        competitor: 'Adobe Analytics',
        category: 'Enterprise Analytics',
        marketShare: 12.8,
        strengths: [
          'Enterprise-grade capabilities',
          'Advanced segmentation features',
          'Strong data visualization',
          'Integration with Adobe ecosystem'
        ],
        weaknesses: [
          'Extremely high cost',
          'Complex implementation requiring consultants',
          'Slow query performance',
          'Limited real-time capabilities',
          'Steep learning curve'
        ],
        ourAdvantages: [
          '90% lower total cost of ownership',
          'Faster implementation (weeks vs months)',
          'Superior real-time performance',
          'E-commerce specialized features',
          'No need for expensive consultants'
        ],
        talkingPoints: [
          'Significant cost savings without feature compromise',
          'Faster time to value and ROI',
          'Built specifically for e-commerce needs',
          'Real-time insights for agile decision making'
        ],
        objectionHandling: [
          {
            objection: 'Adobe has more enterprise features',
            response: 'We provide the essential analytics features that drive revenue, without the complexity and cost of unused enterprise bloat.'
          },
          {
            objection: 'Adobe integrates with our existing stack',
            response: 'Our platform offers superior integrations with e-commerce platforms and provides faster, more actionable insights.'
          }
        ],
        winRate: 72.3,
        lastUpdated: '2024-02-28'
      },
      {
        id: 'battle_mixpanel',
        competitor: 'Mixpanel',
        category: 'Product Analytics',
        marketShare: 3.1,
        strengths: [
          'Strong event tracking capabilities',
          'Good user behavior analysis',
          'Cohort analysis features',
          'Mobile analytics focus'
        ],
        weaknesses: [
          'Limited e-commerce specific features',
          'Complex pricing model',
          'Requires significant technical setup',
          'Not optimized for conversion tracking'
        ],
        ourAdvantages: [
          'E-commerce focused analytics',
          'Better conversion tracking',
          'Simpler pricing model',
          'Faster implementation',
          'Superior customer journey mapping'
        ],
        talkingPoints: [
          'Purpose-built for e-commerce success',
          'Better ROI tracking and attribution',
          'Simpler setup and maintenance',
          'More predictable pricing'
        ],
        objectionHandling: [
          {
            objection: 'Mixpanel has better event tracking',
            response: 'While Mixpanel tracks events well, our platform provides e-commerce specific insights that directly impact revenue.'
          }
        ],
        winRate: 78.9,
        lastUpdated: '2024-02-25'
      }
    ];

    const fallbackCompetitiveIntel = {
      marketTrends: [
        {
          trend: 'Real-time analytics demand increasing',
          impact: 'High',
          description: 'Businesses need faster insights for agile decision making',
          ourPosition: 'Strong - real-time capabilities are core to our platform'
        },
        {
          trend: 'Privacy regulations tightening',
          impact: 'Medium',
          description: 'GDPR and CCPA compliance becoming critical',
          ourPosition: 'Advantage - privacy-first design and compliance'
        },
        {
          trend: 'E-commerce specialization valued',
          impact: 'High',
          description: 'Generic analytics tools not meeting e-commerce needs',
          ourPosition: 'Strong - purpose-built for e-commerce'
        }
      ],
      competitorUpdates: [
        {
          competitor: 'Google Analytics',
          update: 'GA4 migration causing user confusion',
          date: '2024-02-15',
          impact: 'Opportunity for us to capture frustrated users'
        },
        {
          competitor: 'Adobe Analytics',
          update: 'Price increase announced for 2024',
          date: '2024-01-30',
          impact: 'Cost-conscious customers may consider alternatives'
        }
      ]
    };

    return (
      <div class="battle-cards-page">
        {/* Offline Notice */}
        <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-yellow-800 dark:text-yellow-200 text-sm">
              Showing demo battle cards data - service is currently unavailable
            </span>
          </div>
        </div>

        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/sales" class="hover:text-gray-900 dark:hover:text-gray-200">Sales Tools</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Battle Cards</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Competitive Battle Cards
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Comprehensive competitive intelligence and positioning strategies for sales conversations
          </p>
        </div>

        {/* Battle Cards Component with Fallback Data */}
        <BattleCards 
          initialBattleCards={fallbackBattleCards}
          initialCompetitiveIntel={fallbackCompetitiveIntel}
          user={user}
          isOffline={true}
        />
      </div>
    );
  }
});
