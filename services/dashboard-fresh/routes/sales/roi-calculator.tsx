import { defineRoute } from "$fresh/server.ts";
import { AppState } from "../../types/fresh.ts";
import ROICalculator from "../../islands/sales/ROICalculator.tsx";

export default defineRoute<AppState>(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch ROI calculator data server-side with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("ROI calculator data timeout")), 3000)
    );

    // Fetch ROI templates and benchmarks
    const [roiTemplates, industryBenchmarks] = await Promise.race([
      Promise.all([
        fetch('/api/demo/roi-templates').then(r => r.json()),
        fetch('/api/demo/industry-benchmarks').then(r => r.json()),
      ]),
      timeoutPromise
    ]) as any[];

    return (
      <div class="roi-calculator-page">
        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/sales" class="hover:text-gray-900 dark:hover:text-gray-200">Sales Tools</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>ROI Calculator</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            ROI Calculator
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Interactive ROI calculation tool with industry benchmarks and automated report generation
          </p>
        </div>

        {/* ROI Calculator Component */}
        <ROICalculator 
          initialROITemplates={roiTemplates?.data || null}
          initialIndustryBenchmarks={industryBenchmarks?.data || null}
          user={user}
        />
      </div>
    );
  } catch (error) {
    console.error("ROI calculator error:", error);

    // Use fallback data when service is unavailable
    const fallbackROITemplates = [
      {
        id: 'template_ecommerce',
        name: 'E-commerce ROI Template',
        industry: 'E-commerce',
        description: 'ROI calculation template for e-commerce businesses',
        variables: [
          { name: 'monthlyRevenue', label: 'Monthly Revenue', type: 'currency', defaultValue: 100000 },
          { name: 'conversionRate', label: 'Current Conversion Rate', type: 'percentage', defaultValue: 2.5 },
          { name: 'averageOrderValue', label: 'Average Order Value', type: 'currency', defaultValue: 75 },
          { name: 'monthlyTraffic', label: 'Monthly Website Traffic', type: 'number', defaultValue: 50000 },
          { name: 'currentAnalyticsCost', label: 'Current Analytics Cost', type: 'currency', defaultValue: 500 }
        ],
        calculations: {
          improvementFactor: 1.25,
          implementationCost: 2000,
          monthlyCost: 299
        }
      },
      {
        id: 'template_saas',
        name: 'SaaS ROI Template',
        industry: 'Technology',
        description: 'ROI calculation template for SaaS businesses',
        variables: [
          { name: 'monthlyRecurringRevenue', label: 'Monthly Recurring Revenue', type: 'currency', defaultValue: 250000 },
          { name: 'customerAcquisitionCost', label: 'Customer Acquisition Cost', type: 'currency', defaultValue: 150 },
          { name: 'churnRate', label: 'Monthly Churn Rate', type: 'percentage', defaultValue: 5 },
          { name: 'averageCustomerLifetime', label: 'Average Customer Lifetime (months)', type: 'number', defaultValue: 24 },
          { name: 'currentAnalyticsCost', label: 'Current Analytics Cost', type: 'currency', defaultValue: 1000 }
        ],
        calculations: {
          improvementFactor: 1.35,
          implementationCost: 5000,
          monthlyCost: 999
        }
      }
    ];

    const fallbackIndustryBenchmarks = {
      'E-commerce': {
        averageConversionRate: 2.86,
        averageOrderValue: 82,
        averageROIImprovement: 28.5,
        timeToValue: '6 weeks',
        commonChallenges: ['Attribution tracking', 'Cross-channel visibility', 'Customer journey mapping']
      },
      'Technology': {
        averageConversionRate: 3.2,
        averageCustomerLifetime: 28,
        averageROIImprovement: 35.2,
        timeToValue: '4 weeks',
        commonChallenges: ['User behavior tracking', 'Feature adoption', 'Churn prediction']
      },
      'Retail': {
        averageConversionRate: 2.1,
        averageOrderValue: 95,
        averageROIImprovement: 22.8,
        timeToValue: '8 weeks',
        commonChallenges: ['Omnichannel tracking', 'Inventory optimization', 'Seasonal analysis']
      }
    };

    return (
      <div class="roi-calculator-page">
        {/* Offline Notice */}
        <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-yellow-800 dark:text-yellow-200 text-sm">
              Showing demo ROI calculator data - service is currently unavailable
            </span>
          </div>
        </div>

        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/sales" class="hover:text-gray-900 dark:hover:text-gray-200">Sales Tools</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>ROI Calculator</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            ROI Calculator
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Interactive ROI calculation tool with industry benchmarks and automated report generation
          </p>
        </div>

        {/* ROI Calculator Component with Fallback Data */}
        <ROICalculator 
          initialROITemplates={fallbackROITemplates}
          initialIndustryBenchmarks={fallbackIndustryBenchmarks}
          user={user}
          isOffline={true}
        />
      </div>
    );
  }
});
