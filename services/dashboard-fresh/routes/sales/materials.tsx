import { defineRoute } from "$fresh/server.ts";
import { AppState } from "../../types/fresh.ts";
import SalesMaterials from "../../islands/sales/SalesMaterials.tsx";

export default defineRoute<AppState>(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch sales materials data server-side with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Sales materials data timeout")), 3000)
    );

    // Fetch case studies, sales collateral, and templates
    const [caseStudies, salesCollateral, templates] = await Promise.race([
      Promise.all([
        fetch('/api/demo/case-studies').then(r => r.json()),
        fetch('/api/demo/sales-collateral').then(r => r.json()),
        fetch('/api/demo/templates').then(r => r.json()),
      ]),
      timeoutPromise
    ]) as any[];

    return (
      <div class="sales-materials-page">
        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/sales" class="hover:text-gray-900 dark:hover:text-gray-200">Sales Tools</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Sales Materials</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Sales Materials Portal
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Comprehensive sales enablement materials, case studies, and automated content generation
          </p>
        </div>

        {/* Sales Materials Component */}
        <SalesMaterials 
          initialCaseStudies={caseStudies?.data || null}
          initialSalesCollateral={salesCollateral?.data || null}
          initialTemplates={templates?.data || null}
          user={user}
        />
      </div>
    );
  } catch (error) {
    console.error("Sales materials error:", error);

    // Use fallback data when service is unavailable
    const fallbackCaseStudies = [
      {
        id: 'case_001',
        companyName: 'TechCorp Inc.',
        industry: 'Technology',
        challenge: 'Poor conversion tracking across multiple channels',
        solution: 'Implemented comprehensive analytics with branded link tracking',
        results: {
          conversionIncrease: 45,
          revenueGrowth: 32,
          timeToInsight: '90% faster'
        },
        testimonial: 'The analytics platform transformed our understanding of customer behavior.',
        downloadUrl: '/materials/case-study-techcorp.pdf',
        createdAt: '2024-01-15',
        tags: ['B2B', 'Technology', 'Analytics']
      },
      {
        id: 'case_002',
        companyName: 'RetailMax Solutions',
        industry: 'Retail',
        challenge: 'Inability to track customer journey across online and offline channels',
        solution: 'Deployed omnichannel tracking with real-time analytics dashboard',
        results: {
          conversionIncrease: 67,
          revenueGrowth: 28,
          timeToInsight: '85% faster'
        },
        testimonial: 'We finally have a complete view of our customer journey.',
        downloadUrl: '/materials/case-study-retailmax.pdf',
        createdAt: '2024-02-10',
        tags: ['Retail', 'Omnichannel', 'E-commerce']
      }
    ];

    const fallbackSalesCollateral = [
      {
        id: 'collateral_001',
        title: 'Product Overview Deck',
        description: 'Comprehensive product overview and value proposition',
        type: 'presentation',
        format: 'PDF',
        pages: 24,
        downloadUrl: '/materials/product-overview.pdf',
        lastUpdated: '2024-03-01',
        category: 'product'
      },
      {
        id: 'collateral_002',
        title: 'ROI Calculator Worksheet',
        description: 'Interactive ROI calculation tool for prospects',
        type: 'worksheet',
        format: 'Excel',
        pages: 1,
        downloadUrl: '/materials/roi-calculator.xlsx',
        lastUpdated: '2024-02-28',
        category: 'financial'
      },
      {
        id: 'collateral_003',
        title: 'Implementation Guide',
        description: 'Step-by-step implementation and onboarding guide',
        type: 'guide',
        format: 'PDF',
        pages: 16,
        downloadUrl: '/materials/implementation-guide.pdf',
        lastUpdated: '2024-02-25',
        category: 'technical'
      }
    ];

    const fallbackTemplates = [
      {
        id: 'template_001',
        name: 'Discovery Call Template',
        description: 'Structured template for discovery calls with prospects',
        type: 'call_script',
        category: 'sales_process',
        content: 'Comprehensive discovery questions and qualification framework',
        lastUpdated: '2024-02-20'
      },
      {
        id: 'template_002',
        name: 'Proposal Template',
        description: 'Professional proposal template with pricing and terms',
        type: 'document',
        category: 'proposals',
        content: 'Customizable proposal template with dynamic pricing',
        lastUpdated: '2024-02-18'
      },
      {
        id: 'template_003',
        name: 'Follow-up Email Sequence',
        description: 'Automated email sequence for prospect nurturing',
        type: 'email_sequence',
        category: 'nurturing',
        content: '7-email sequence with personalization tokens',
        lastUpdated: '2024-02-15'
      }
    ];

    return (
      <div class="sales-materials-page">
        {/* Offline Notice */}
        <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-yellow-800 dark:text-yellow-200 text-sm">
              Showing demo sales materials data - service is currently unavailable
            </span>
          </div>
        </div>

        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/sales" class="hover:text-gray-900 dark:hover:text-gray-200">Sales Tools</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Sales Materials</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Sales Materials Portal
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Comprehensive sales enablement materials, case studies, and automated content generation
          </p>
        </div>

        {/* Sales Materials Component with Fallback Data */}
        <SalesMaterials 
          initialCaseStudies={fallbackCaseStudies}
          initialSalesCollateral={fallbackSalesCollateral}
          initialTemplates={fallbackTemplates}
          user={user}
          isOffline={true}
        />
      </div>
    );
  }
});
