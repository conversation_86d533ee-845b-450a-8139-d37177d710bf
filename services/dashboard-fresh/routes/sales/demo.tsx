import { defineRoute } from "$fresh/server.ts";
import { AppState } from "../../types/fresh.ts";
import DemoEnvironment from "../../islands/sales/DemoEnvironment.tsx";

export default defineRoute<AppState>(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch demo environment data server-side with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Demo environment data timeout")), 3000)
    );

    // Fetch demo scenarios and performance metrics
    const [demoScenarios, demoMetrics] = await Promise.race([
      Promise.all([
        fetch('/api/demo/scenarios').then(r => r.json()),
        fetch('/api/demo/performance-metrics').then(r => r.json()),
      ]),
      timeoutPromise
    ]) as any[];

    return (
      <div class="demo-environment-page">
        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/sales" class="hover:text-gray-900 dark:hover:text-gray-200">Sales Tools</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Demo Environment</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Demo Environment
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Interactive sales demonstrations with real-time performance benchmarks and ROI calculations
          </p>
        </div>

        {/* Demo Environment Component */}
        <DemoEnvironment 
          initialDemoScenarios={demoScenarios?.data || null}
          initialDemoMetrics={demoMetrics?.data || null}
          user={user}
        />
      </div>
    );
  } catch (error) {
    console.error("Demo environment error:", error);

    // Use fallback data when service is unavailable
    const fallbackDemoScenarios = [
      {
        id: 'ecommerce_smb',
        name: 'E-commerce SMB',
        description: 'Small to medium e-commerce business demonstration',
        industry: 'E-commerce',
        monthlyEvents: 50000,
        expectedROI: 285,
        features: ['Link tracking', 'Conversion analytics', 'Customer journey mapping'],
        demoUrl: '/demo/ecommerce-smb',
        setupTime: '5 minutes',
        dataPoints: 15000,
        conversionRate: 3.2,
        status: 'active'
      },
      {
        id: 'enterprise_retail',
        name: 'Enterprise Retail',
        description: 'Large enterprise retail demonstration',
        industry: 'Retail',
        monthlyEvents: 500000,
        expectedROI: 450,
        features: ['Advanced analytics', 'Multi-channel tracking', 'Predictive insights'],
        demoUrl: '/demo/enterprise-retail',
        setupTime: '10 minutes',
        dataPoints: 150000,
        conversionRate: 4.8,
        status: 'active'
      },
      {
        id: 'saas_b2b',
        name: 'SaaS B2B',
        description: 'B2B SaaS platform demonstration',
        industry: 'Technology',
        monthlyEvents: 100000,
        expectedROI: 320,
        features: ['User journey tracking', 'Cohort analysis', 'Revenue attribution'],
        demoUrl: '/demo/saas-b2b',
        setupTime: '7 minutes',
        dataPoints: 45000,
        conversionRate: 2.9,
        status: 'active'
      }
    ];

    const fallbackDemoMetrics = {
      totalDemos: 156,
      averageEngagement: 87.3,
      conversionRate: 23.5,
      averageSessionTime: '12:34',
      topPerformingScenario: 'enterprise_retail',
      recentActivity: [
        {
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          action: 'Demo Started',
          scenario: 'E-commerce SMB',
          prospect: 'TechCorp Inc.',
          duration: '15:23'
        },
        {
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          action: 'ROI Report Generated',
          scenario: 'Enterprise Retail',
          prospect: 'RetailMax Solutions',
          value: '$2.3M potential'
        }
      ]
    };

    return (
      <div class="demo-environment-page">
        {/* Offline Notice */}
        <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-yellow-800 dark:text-yellow-200 text-sm">
              Showing demo environment data - service is currently unavailable
            </span>
          </div>
        </div>

        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/sales" class="hover:text-gray-900 dark:hover:text-gray-200">Sales Tools</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Demo Environment</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Demo Environment
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Interactive sales demonstrations with real-time performance benchmarks and ROI calculations
          </p>
        </div>

        {/* Demo Environment Component with Fallback Data */}
        <DemoEnvironment 
          initialDemoScenarios={fallbackDemoScenarios}
          initialDemoMetrics={fallbackDemoMetrics}
          user={user}
          isOffline={true}
        />
      </div>
    );
  }
});
