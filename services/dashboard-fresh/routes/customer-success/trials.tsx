import { defineRoute } from "$fresh/server.ts";
import { AppState } from "../../types/fresh.ts";
import TrialManagement from "../../islands/customer-success/TrialManagement.tsx";

export default defineRoute<AppState>(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch trial management data server-side with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Trial management data timeout")), 3000)
    );

    // Fetch active trials and onboarding steps
    const [activeTrials, onboardingSteps] = await Promise.race([
      Promise.all([
        fetch('/api/billing/trial/active-overview').then(r => r.json()),
        fetch('/api/billing/trial/onboarding-steps/ecommerce_smb').then(r => r.json()),
      ]),
      timeoutPromise
    ]) as any[];

    return (
      <div class="trial-management-page">
        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/customer-success" class="hover:text-gray-900 dark:hover:text-gray-200">Customer Success</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Trial Management</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Trial Management
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Provision, monitor, and manage customer trial environments
          </p>
        </div>

        {/* Trial Management Component */}
        <TrialManagement 
          initialActiveTrials={activeTrials?.data || null}
          initialOnboardingSteps={onboardingSteps?.data || null}
          user={user}
        />
      </div>
    );
  } catch (error) {
    console.error("Trial management error:", error);

    // Use fallback data when service is unavailable
    const fallbackActiveTrials = {
      activeTrials: [
        {
          trialId: 'trial_demo_001',
          companyName: 'Demo Company Inc.',
          industry: 'E-commerce',
          scenario: 'ecommerce_smb',
          status: 'active',
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          onboardingProgress: {
            completedSteps: 3,
            totalSteps: 5,
            currentStep: 'integration_setup'
          },
          healthScore: {
            overallScore: 85,
            riskLevel: 'low',
            interventionTriggers: []
          }
        }
      ],
      summary: {
        totalActiveTrials: 1,
        averageHealthScore: 85,
        highRiskTrials: 0
      }
    };

    const fallbackOnboardingSteps = [
      {
        id: 'account_setup',
        title: 'Account Setup',
        description: 'Complete initial account configuration',
        estimatedTime: '5 minutes',
        required: true
      },
      {
        id: 'integration_setup',
        title: 'Integration Setup',
        description: 'Connect your e-commerce platform',
        estimatedTime: '15 minutes',
        required: true
      },
      {
        id: 'link_creation',
        title: 'Create First Link',
        description: 'Generate your first branded tracking link',
        estimatedTime: '5 minutes',
        required: true
      },
      {
        id: 'analytics_review',
        title: 'Analytics Review',
        description: 'Review your first analytics data',
        estimatedTime: '10 minutes',
        required: false
      },
      {
        id: 'advanced_features',
        title: 'Advanced Features',
        description: 'Explore advanced analytics features',
        estimatedTime: '20 minutes',
        required: false
      }
    ];

    return (
      <div class="trial-management-page">
        {/* Offline Notice */}
        <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-yellow-800 dark:text-yellow-200 text-sm">
              Showing demo trial management data - service is currently unavailable
            </span>
          </div>
        </div>

        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/customer-success" class="hover:text-gray-900 dark:hover:text-gray-200">Customer Success</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Trial Management</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Trial Management
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Provision, monitor, and manage customer trial environments
          </p>
        </div>

        {/* Trial Management Component with Fallback Data */}
        <TrialManagement 
          initialActiveTrials={fallbackActiveTrials}
          initialOnboardingSteps={fallbackOnboardingSteps}
          user={user}
          isOffline={true}
        />
      </div>
    );
  }
});
