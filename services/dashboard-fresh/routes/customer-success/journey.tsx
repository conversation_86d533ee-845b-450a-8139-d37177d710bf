import { defineRoute } from "$fresh/server.ts";
import { AppState } from "../../types/fresh.ts";
import JourneyTracking from "../../islands/customer-success/JourneyTracking.tsx";

export default defineRoute<AppState>(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch journey tracking data server-side with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Journey tracking data timeout")), 3000)
    );

    // Fetch customer journey events and milestones
    const [journeyEvents, milestones] = await Promise.race([
      Promise.all([
        fetch('/api/billing/customer-journey/events').then(r => r.json()),
        fetch('/api/billing/customer-journey/milestones').then(r => r.json()),
      ]),
      timeoutPromise
    ]) as any[];

    return (
      <div class="journey-tracking-page">
        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/customer-success" class="hover:text-gray-900 dark:hover:text-gray-200">Customer Success</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Journey Tracking</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Customer Journey Tracking
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Track customer journey events, milestones, and success progression across the entire lifecycle
          </p>
        </div>

        {/* Journey Tracking Component */}
        <JourneyTracking 
          initialJourneyEvents={journeyEvents?.data || null}
          initialMilestones={milestones?.data || null}
          user={user}
        />
      </div>
    );
  } catch (error) {
    console.error("Journey tracking error:", error);

    // Use fallback data when service is unavailable
    const fallbackJourneyEvents = [
      {
        id: 'event_001',
        customerId: 'cust_demo_001',
        companyName: 'Demo Company Inc.',
        eventType: 'trial_started',
        eventName: 'Trial Started',
        timestamp: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
        metadata: {
          trialDuration: 14,
          scenario: 'ecommerce_smb',
          source: 'website_signup'
        }
      },
      {
        id: 'event_002',
        customerId: 'cust_demo_001',
        companyName: 'Demo Company Inc.',
        eventType: 'integration_completed',
        eventName: 'Integration Completed',
        timestamp: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000).toISOString(),
        metadata: {
          platform: 'Shopify',
          setupTime: '15 minutes'
        }
      },
      {
        id: 'event_003',
        customerId: 'cust_demo_001',
        companyName: 'Demo Company Inc.',
        eventType: 'first_link_created',
        eventName: 'First Link Created',
        timestamp: new Date(Date.now() - 11 * 24 * 60 * 60 * 1000).toISOString(),
        metadata: {
          linkType: 'branded',
          campaign: 'summer_sale'
        }
      },
      {
        id: 'event_004',
        customerId: 'cust_demo_001',
        companyName: 'Demo Company Inc.',
        eventType: 'milestone_reached',
        eventName: 'First Analytics Review',
        timestamp: new Date(Date.now() - 9 * 24 * 60 * 60 * 1000).toISOString(),
        metadata: {
          milestone: 'analytics_review',
          dataPoints: 1250,
          insights: 8
        }
      },
      {
        id: 'event_005',
        customerId: 'cust_demo_001',
        companyName: 'Demo Company Inc.',
        eventType: 'subscription_converted',
        eventName: 'Converted to Paid',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        metadata: {
          plan: 'Professional',
          mrr: 299,
          conversionTime: '12 days'
        }
      }
    ];

    const fallbackMilestones = [
      {
        id: 'milestone_001',
        name: 'Trial Activation',
        description: 'Customer successfully starts their trial',
        category: 'onboarding',
        targetTimeframe: '1 day',
        completionRate: 95,
        averageTime: '2 hours',
        impactOnSuccess: 'high'
      },
      {
        id: 'milestone_002',
        name: 'Integration Setup',
        description: 'Customer connects their e-commerce platform',
        category: 'onboarding',
        targetTimeframe: '3 days',
        completionRate: 78,
        averageTime: '1 day',
        impactOnSuccess: 'critical'
      },
      {
        id: 'milestone_003',
        name: 'First Link Creation',
        description: 'Customer creates their first tracking link',
        category: 'activation',
        targetTimeframe: '5 days',
        completionRate: 85,
        averageTime: '2 days',
        impactOnSuccess: 'high'
      },
      {
        id: 'milestone_004',
        name: 'Analytics Review',
        description: 'Customer reviews their first analytics data',
        category: 'value_realization',
        targetTimeframe: '7 days',
        completionRate: 72,
        averageTime: '4 days',
        impactOnSuccess: 'critical'
      },
      {
        id: 'milestone_005',
        name: 'Advanced Features',
        description: 'Customer explores advanced analytics features',
        category: 'expansion',
        targetTimeframe: '14 days',
        completionRate: 45,
        averageTime: '8 days',
        impactOnSuccess: 'medium'
      }
    ];

    return (
      <div class="journey-tracking-page">
        {/* Offline Notice */}
        <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-yellow-800 dark:text-yellow-200 text-sm">
              Showing demo journey tracking data - service is currently unavailable
            </span>
          </div>
        </div>

        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/customer-success" class="hover:text-gray-900 dark:hover:text-gray-200">Customer Success</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Journey Tracking</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Customer Journey Tracking
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Track customer journey events, milestones, and success progression across the entire lifecycle
          </p>
        </div>

        {/* Journey Tracking Component with Fallback Data */}
        <JourneyTracking 
          initialJourneyEvents={fallbackJourneyEvents}
          initialMilestones={fallbackMilestones}
          user={user}
          isOffline={true}
        />
      </div>
    );
  }
});
