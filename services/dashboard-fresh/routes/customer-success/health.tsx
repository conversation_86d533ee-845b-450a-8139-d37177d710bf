import { defineRoute } from "$fresh/server.ts";
import { AppState } from "../../types/fresh.ts";
import AdvancedCustomerSuccessDashboard from "../../islands/customer-success/AdvancedCustomerSuccessDashboard.tsx";

export default defineRoute<AppState>(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch health monitoring data server-side with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Health monitoring data timeout")), 3000)
    );

    // Fetch customer success data server-side with timeout
    const [healthMonitoring, customerJourney, successMetrics] = await Promise.race([
      Promise.all([
        fetch('http://localhost:8000/api/billing/health-monitoring').then(r => r.json()),
        fetch('http://localhost:8000/api/billing/customer-journey/all').then(r => r.json()),
        fetch('http://localhost:8000/api/billing/customer-success-metrics').then(r => r.json()),
      ]),
      timeoutPromise
    ]) as any[];

    return (
      <div class="health-monitoring-page">
        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/customer-success" class="hover:text-gray-900 dark:hover:text-gray-200">Customer Success</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Health Monitoring</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Advanced Customer Success Dashboard
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Customer journey visualization, health monitoring, and predictive success analytics
          </p>
        </div>

        {/* Advanced Customer Success Dashboard Component */}
        <AdvancedCustomerSuccessDashboard
          initialHealthMonitoring={healthMonitoring?.data || null}
          initialCustomerJourney={customerJourney?.data || null}
          initialSuccessMetrics={successMetrics?.data || null}
          user={user}
        />
      </div>
    );
  } catch (error) {
    console.error("Health monitoring error:", error);

    // Use fallback data when service is unavailable
    const fallbackHealthMonitoring = {
      alerts: [
        {
          id: 'alert_001',
          type: 'usage_decline',
          severity: 'medium',
          message: 'Customer usage decreased by 40% this month',
          subscriptionId: 'sub_demo_001',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          customerId: 'cust_demo_001',
          companyName: 'Demo Company Inc.'
        },
        {
          id: 'alert_002',
          type: 'support_tickets',
          severity: 'low',
          message: 'Multiple support tickets opened in past week',
          subscriptionId: 'sub_demo_002',
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          customerId: 'cust_demo_002',
          companyName: 'TechCorp Solutions'
        }
      ],
      summary: {
        critical: 0,
        high: 0,
        medium: 1,
        low: 1,
        total: 2
      }
    };

    const fallbackCustomerJourney = {
      customers: [
        {
          customerId: 'cust_demo_001',
          companyName: 'TechStart Inc.',
          currentStage: 'onboarding',
          healthScore: 85,
          riskLevel: 'low',
          milestones: [
            {
              id: 'milestone_001',
              name: 'Account Setup',
              status: 'completed',
              completedAt: '2024-01-15T10:30:00Z',
              progress: 100
            },
            {
              id: 'milestone_002',
              name: 'Data Integration',
              status: 'in_progress',
              dueDate: '2024-01-25T17:00:00Z',
              progress: 75
            },
            {
              id: 'milestone_003',
              name: 'Team Training',
              status: 'pending',
              dueDate: '2024-01-30T17:00:00Z',
              progress: 0
            }
          ],
          interventions: [
            {
              type: 'success_call',
              scheduledDate: '2024-01-24T15:00:00Z',
              status: 'scheduled'
            }
          ],
          journey: [
            {
              stage: 'trial',
              enteredAt: '2024-01-01T09:00:00Z',
              exitedAt: '2024-01-14T17:00:00Z',
              duration: 13
            },
            {
              stage: 'onboarding',
              enteredAt: '2024-01-15T10:30:00Z'
            }
          ],
          predictedOutcome: {
            successProbability: 0.89,
            churnRisk: 0.11,
            expansionPotential: 0.65,
            timeToValue: 18
          }
        }
      ],
      stages: [
        {
          name: 'trial',
          description: 'Customer trial period',
          averageDuration: 14,
          conversionRate: 23.8,
          dropoffRate: 76.2
        },
        {
          name: 'onboarding',
          description: 'Customer onboarding process',
          averageDuration: 21,
          conversionRate: 87.5,
          dropoffRate: 12.5
        },
        {
          name: 'active',
          description: 'Active customer usage',
          averageDuration: 365,
          conversionRate: 95.2,
          dropoffRate: 4.8
        }
      ],
      insights: {
        averageJourneyDuration: 45,
        successRate: 78.3,
        commonDropoffPoints: ['data_integration', 'team_training'],
        topSuccessFactors: ['early_engagement', 'complete_onboarding', 'regular_usage']
      }
    };

    const fallbackSuccessMetrics = {
      overview: {
        totalCustomers: 1247,
        healthyCustomers: 1068,
        atRiskCustomers: 179,
        averageHealthScore: 78.5,
        successRate: 85.7,
        interventionSuccessRate: 72.3,
        timeToValue: 18.5,
        customerSatisfaction: 8.4
      },
      trends: {
        healthScoreTrend: [
          { period: 'Week 1', score: 76.2 },
          { period: 'Week 2', score: 77.8 },
          { period: 'Week 3', score: 78.1 },
          { period: 'Week 4', score: 78.5 }
        ],
        successRateTrend: [
          { period: 'Week 1', rate: 83.5 },
          { period: 'Week 2', rate: 84.2 },
          { period: 'Week 3', rate: 85.1 },
          { period: 'Week 4', rate: 85.7 }
        ],
        interventionTrend: [
          { period: 'Week 1', interventions: 45, successRate: 68.9 },
          { period: 'Week 2', interventions: 52, successRate: 71.2 },
          { period: 'Week 3', interventions: 48, successRate: 72.9 },
          { period: 'Week 4', interventions: 41, successRate: 72.3 }
        ]
      },
      predictions: {
        nextMonthRisk: {
          atRiskCustomers: 195,
          churnProbability: 0.15,
          revenueAtRisk: 45600
        },
        successOpportunities: [
          {
            customerId: 'cust_demo_001',
            opportunity: 'expansion_ready',
            probability: 0.78,
            impact: 2400
          },
          {
            customerId: 'cust_demo_002',
            opportunity: 'upsell_potential',
            probability: 0.65,
            impact: 1800
          }
        ]
      }
    };

    const fallbackSubscriptionHealth = {
      healthScores: [
        {
          subscriptionId: 'sub_demo_001',
          companyName: 'Demo Company Inc.',
          overallScore: 75,
          riskLevel: 'medium',
          factors: [
            { name: 'Usage Trend', score: 65, weight: 0.3, impact: 'Declining usage pattern' },
            { name: 'Engagement', score: 80, weight: 0.25, impact: 'Good feature adoption' },
            { name: 'Support Activity', score: 85, weight: 0.2, impact: 'Low support burden' },
            { name: 'Payment History', score: 95, weight: 0.25, impact: 'Excellent payment record' }
          ],
          interventionTriggers: ['usage_decline'],
          lastUpdated: new Date().toISOString()
        },
        {
          subscriptionId: 'sub_demo_002',
          companyName: 'TechCorp Solutions',
          overallScore: 92,
          riskLevel: 'low',
          factors: [
            { name: 'Usage Trend', score: 95, weight: 0.3, impact: 'Growing usage pattern' },
            { name: 'Engagement', score: 90, weight: 0.25, impact: 'Excellent feature adoption' },
            { name: 'Support Activity', score: 88, weight: 0.2, impact: 'Minimal support needs' },
            { name: 'Payment History', score: 100, weight: 0.25, impact: 'Perfect payment record' }
          ],
          interventionTriggers: [],
          lastUpdated: new Date().toISOString()
        }
      ],
      summary: {
        averageScore: 83.5,
        totalCustomers: 2,
        highRisk: 0,
        mediumRisk: 1,
        lowRisk: 1
      }
    };

    return (
      <div class="health-monitoring-page">
        {/* Offline Notice */}
        <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-yellow-800 dark:text-yellow-200 text-sm">
              Showing demo health monitoring data - service is currently unavailable
            </span>
          </div>
        </div>

        {/* Page Header */}
        <div class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
            <a href="/customer-success" class="hover:text-gray-900 dark:hover:text-gray-200">Customer Success</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Health Monitoring</span>
          </div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Health Monitoring
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Real-time customer health monitoring, risk assessment, and intervention tracking
          </p>
        </div>

        {/* Advanced Customer Success Dashboard Component with Fallback Data */}
        <AdvancedCustomerSuccessDashboard
          initialHealthMonitoring={fallbackHealthMonitoring}
          initialCustomerJourney={fallbackCustomerJourney}
          initialSuccessMetrics={fallbackSuccessMetrics}
          user={user}
          isOffline
        />
      </div>
    );
  }
});
