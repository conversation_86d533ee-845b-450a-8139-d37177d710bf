import { defineRoute } from "$fresh/server.ts";
import { billingService } from "../services/billingService.ts";
import { AppState } from "../types/fresh.ts";
import CustomerSuccessOverview from "../islands/customer-success/CustomerSuccessOverview.tsx";

export default defineRoute<AppState>(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch customer success data server-side with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Customer success data timeout")), 3000)
    );

    // Fetch active trials overview and health monitoring
    const [activeTrials, healthMonitoring] = await Promise.race([
      Promise.all([
        fetch('/api/billing/trial/active-overview').then(r => r.json()),
        billingService.getHealthMonitoring(),
      ]),
      timeoutPromise
    ]) as any[];

    return (
      <div class="customer-success-page">
        {/* Page Header */}
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Customer Success Dashboard
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Monitor customer health, trial progress, and success milestones
          </p>
        </div>

        {/* Customer Success Overview Component */}
        <CustomerSuccessOverview 
          initialActiveTrials={activeTrials?.data || null}
          initialHealthMonitoring={healthMonitoring}
          user={user}
        />
      </div>
    );
  } catch (error) {
    console.error("Customer success dashboard error:", error);

    // Use fallback data when service is unavailable
    const fallbackActiveTrials = {
      activeTrials: [
        {
          trialId: 'trial_demo_001',
          companyName: 'Demo Company Inc.',
          industry: 'E-commerce',
          scenario: 'ecommerce_smb',
          status: 'active',
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          onboardingProgress: {
            completedSteps: 3,
            totalSteps: 5,
            currentStep: 'integration_setup'
          },
          healthScore: {
            overallScore: 85,
            riskLevel: 'low',
            interventionTriggers: []
          }
        }
      ],
      summary: {
        totalActiveTrials: 1,
        averageHealthScore: 85,
        highRiskTrials: 0
      }
    };

    const fallbackHealthMonitoring = {
      alerts: [],
      summary: {
        critical: 0,
        high: 0,
        medium: 1,
        low: 2,
        total: 3
      }
    };

    return (
      <div class="customer-success-page">
        {/* Offline Notice */}
        <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-yellow-800 dark:text-yellow-200 text-sm">
              Showing demo customer success data - service is currently unavailable
            </span>
          </div>
        </div>

        {/* Page Header */}
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Customer Success Dashboard
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Monitor customer health, trial progress, and success milestones
          </p>
        </div>

        {/* Customer Success Overview Component with Fallback Data */}
        <CustomerSuccessOverview 
          initialActiveTrials={fallbackActiveTrials}
          initialHealthMonitoring={fallbackHealthMonitoring}
          user={user}
          isOffline={true}
        />
      </div>
    );
  }
});
