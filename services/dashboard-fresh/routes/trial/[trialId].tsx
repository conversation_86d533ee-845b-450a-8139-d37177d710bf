// Trial Environment Route
// Individual trial environment dashboard with onboarding workflow and success tracking

import { defineRoute } from "$fresh/server.ts";
import { Head } from "$fresh/runtime.ts";
import TrialOnboardingWorkflow from "../../islands/TrialOnboardingWorkflow.tsx";
import CustomerSuccessDashboard from "../../islands/CustomerSuccessDashboard.tsx";

interface TrialPageProps {
  trialId: string;
}

export default defineRoute<TrialPageProps>(async (req, ctx) => {
  const { trialId } = ctx.params;
  
  // Fetch trial data
  let trialData = null;
  try {
    const response = await fetch(`${Deno.env.get("BILLING_SERVICE_URL") || "http://localhost:3003"}/api/enhanced-subscriptions/trial/${trialId}`);
    if (response.ok) {
      const result = await response.json();
      trialData = result.data.trialEnvironment;
    }
  } catch (error) {
    console.error("Failed to fetch trial data:", error);
  }

  if (!trialData) {
    return (
      <>
        <Head>
          <title>Trial Not Found - E-commerce Analytics Platform</title>
        </Head>
        <div class="min-h-screen bg-gray-50 flex items-center justify-center">
          <div class="text-center">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Trial Not Found</h1>
            <p class="text-gray-600 mb-8">The trial environment you're looking for doesn't exist or has expired.</p>
            <a href="/trial/provision" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
              Start New Trial
            </a>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>{trialData.companyName} Trial - E-commerce Analytics Platform</title>
        <meta name="description" content={`Trial environment for ${trialData.companyName} showcasing our 24,390 events/sec processing capability and 6-11ms query performance.`} />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        
        {/* Trial-specific meta tags */}
        <meta property="og:title" content={`${trialData.companyName} Trial - E-commerce Analytics Platform`} />
        <meta property="og:description" content="Experience industry-leading analytics performance with real-time processing and sub-10ms query response times." />
        <meta property="og:type" content="website" />
        
        {/* Preload critical assets */}
        <link rel="preload" href="/static/styles.css" as="style" />
        <link rel="preconnect" href="https://cdn.skypack.dev" />
        
        {/* Trial-specific styling */}
        <style>{`
          .trial-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 2rem 0;
          }
          
          .trial-status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
          }
          
          .status-active {
            background: rgba(16, 185, 129, 0.2);
            color: #059669;
            border: 1px solid rgba(16, 185, 129, 0.3);
          }
          
          .status-provisioning {
            background: rgba(245, 158, 11, 0.2);
            color: #d97706;
            border: 1px solid rgba(245, 158, 11, 0.3);
          }
          
          .status-expired {
            background: rgba(239, 68, 68, 0.2);
            color: #dc2626;
            border: 1px solid rgba(239, 68, 68, 0.3);
          }
          
          .trial-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
          }
          
          .performance-indicator {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            margin: 0.25rem;
            font-weight: 600;
            display: inline-block;
          }
          
          .trial-navigation {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 10;
          }
          
          .nav-item {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            margin: 0 0.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
          }
          
          .nav-item.active {
            background: #3b82f6;
            color: white;
          }
          
          .nav-item:not(.active) {
            background: #f3f4f6;
            color: #374151;
          }
          
          .nav-item:hover:not(.active) {
            background: #e5e7eb;
          }
          
          .trial-metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
          }
          
          .metric-card {
            background: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            text-align: center;
          }
          
          .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
          }
          
          .metric-label {
            color: #6b7280;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
          }
          
          .expiry-warning {
            background: linear-gradient(45deg, #f59e0b, #d97706);
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
            text-align: center;
          }
          
          .expiry-critical {
            background: linear-gradient(45deg, #ef4444, #dc2626);
          }
        `}</style>
      </Head>

      <div class="min-h-screen bg-gray-50">
        {/* Trial Header */}
        <div class="trial-header">
          <div class="trial-container">
            <div class="flex justify-between items-center">
              <div>
                <h1 class="text-3xl font-bold mb-2">{trialData.companyName}</h1>
                <p class="text-blue-100 mb-4">
                  {trialData.industry} • {trialData.scenario.replace('_', ' ').toUpperCase()} Scenario
                </p>
                <div class="flex items-center space-x-4">
                  <span class={`trial-status-badge status-${trialData.status}`}>
                    {trialData.status}
                  </span>
                  <span class="text-blue-100 text-sm">
                    Trial ID: {trialData.id}
                  </span>
                </div>
              </div>
              
              <div class="text-right">
                <div class="text-blue-100 text-sm mb-1">Expires</div>
                <div class="text-xl font-bold">
                  {new Date(trialData.expiresAt).toLocaleDateString()}
                </div>
                <div class="text-blue-100 text-sm">
                  {Math.ceil((new Date(trialData.expiresAt).getTime() - Date.now()) / (1000 * 60 * 60 * 24))} days remaining
                </div>
              </div>
            </div>
            
            <div class="flex flex-wrap justify-center mt-6">
              <div class="performance-indicator">
                🚀 24,390+ Events/Sec Processing
              </div>
              <div class="performance-indicator">
                ⚡ 6-11ms Query Response
              </div>
              <div class="performance-indicator">
                📊 99.95% Data Accuracy
              </div>
              <div class="performance-indicator">
                🔒 Enterprise Security
              </div>
            </div>
          </div>
        </div>

        {/* Expiry Warning */}
        {(() => {
          const daysRemaining = Math.ceil((new Date(trialData.expiresAt).getTime() - Date.now()) / (1000 * 60 * 60 * 24));
          if (daysRemaining <= 1) {
            return (
              <div class="trial-container">
                <div class="expiry-warning expiry-critical">
                  <h3 class="font-bold text-lg mb-2">⚠️ Trial Expiring Soon!</h3>
                  <p>Your trial expires in {daysRemaining} day{daysRemaining !== 1 ? 's' : ''}. Contact our sales team to continue your experience.</p>
                  <button class="mt-3 bg-white text-red-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                    Extend Trial
                  </button>
                </div>
              </div>
            );
          } else if (daysRemaining <= 3) {
            return (
              <div class="trial-container">
                <div class="expiry-warning">
                  <h3 class="font-bold text-lg mb-2">📅 Trial Reminder</h3>
                  <p>Your trial expires in {daysRemaining} days. Make sure to complete your evaluation and reach out if you need more time.</p>
                </div>
              </div>
            );
          }
          return null;
        })()}

        {/* Trial Navigation */}
        <div class="trial-navigation">
          <div class="trial-container">
            <div class="flex flex-wrap justify-center">
              <a href={`/trial/${trialId}`} class="nav-item active">
                🎯 Onboarding
              </a>
              <a href={`/trial/${trialId}/analytics`} class="nav-item">
                📊 Analytics Dashboard
              </a>
              <a href={`/trial/${trialId}/performance`} class="nav-item">
                ⚡ Performance Demo
              </a>
              <a href={`/trial/${trialId}/integrations`} class="nav-item">
                🔗 Integrations
              </a>
              <a href={`/trial/${trialId}/success`} class="nav-item">
                📈 Success Metrics
              </a>
            </div>
          </div>
        </div>

        {/* Trial Metrics */}
        <div class="trial-container py-6">
          <div class="trial-metrics-grid">
            <div class="metric-card">
              <div class="metric-value">{trialData.metrics.totalSessions}</div>
              <div class="metric-label">Total Sessions</div>
            </div>
            
            <div class="metric-card">
              <div class="metric-value">{trialData.metrics.totalQueries}</div>
              <div class="metric-label">Queries Executed</div>
            </div>
            
            <div class="metric-card">
              <div class="metric-value">
                {trialData.metrics.averageQueryTime > 0 ? `${trialData.metrics.averageQueryTime.toFixed(1)}ms` : 'N/A'}
              </div>
              <div class="metric-label">Avg Query Time</div>
            </div>
            
            <div class="metric-card">
              <div class="metric-value">{trialData.metrics.featuresExplored.length}</div>
              <div class="metric-label">Features Explored</div>
            </div>
            
            <div class="metric-card">
              <div class="metric-value">{trialData.metrics.timeSpent}</div>
              <div class="metric-label">Minutes Spent</div>
            </div>
            
            <div class="metric-card">
              <div class="metric-value">{trialData.onboardingProgress.progressPercentage}%</div>
              <div class="metric-label">Onboarding Progress</div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div class="trial-container pb-8">
          <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* Onboarding Workflow */}
            <div class="xl:col-span-2">
              <TrialOnboardingWorkflow 
                trialId={trialId}
                scenario={trialData.scenario}
              />
            </div>
            
            {/* Success Dashboard */}
            <div class="xl:col-span-1">
              <CustomerSuccessDashboard 
                trialId={trialId}
                mode="single"
              />
            </div>
          </div>
        </div>

        {/* Trial Footer */}
        <div class="bg-gray-800 text-white py-8">
          <div class="trial-container text-center">
            <h3 class="text-2xl font-bold mb-4">Ready to Get Started?</h3>
            <p class="text-gray-300 mb-6">
              Experience the full power of our platform with your own data and team.
            </p>
            <div class="space-x-4">
              <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                Schedule Demo Call
              </button>
              <button class="bg-transparent border-2 border-white text-white font-bold py-3 px-6 rounded-lg hover:bg-white hover:text-gray-800 transition-colors">
                Contact Sales
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
});
