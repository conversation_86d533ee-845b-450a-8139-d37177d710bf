import { defineRoute } from "$fresh/server.ts";
import { AppState } from "../types/fresh.ts";
import RevenueIntelligenceDashboard from "../islands/billing/RevenueIntelligenceDashboard.tsx";

export default defineRoute<AppState>(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch revenue intelligence data server-side with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Revenue intelligence data timeout")), 3000)
    );

    const [revenueIntelligence, churnPredictions, revenueForecasts] = await Promise.race([
      Promise.all([
        fetch('http://localhost:8000/api/billing/revenue-intelligence').then(r => r.json()),
        fetch('http://localhost:8000/api/billing/churn-prediction/all').then(r => r.json()),
        fetch('http://localhost:8000/api/billing/revenue-forecasts').then(r => r.json()),
      ]),
      timeoutPromise
    ]) as any[];

    return (
      <div class="revenue-intelligence-page">
        {/* Page Header */}
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Revenue Intelligence & Predictions
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Advanced revenue forecasting, churn prediction, and strategic opportunity analysis
          </p>
        </div>

        {/* Revenue Intelligence Dashboard Component */}
        <RevenueIntelligenceDashboard 
          initialRevenueIntelligence={revenueIntelligence?.data || null}
          initialChurnPredictions={churnPredictions?.data || null}
          initialRevenueForecasts={revenueForecasts?.data || null}
          user={user}
        />
      </div>
    );
  } catch (error) {
    console.error("Revenue intelligence error:", error);

    // Use fallback data when service is unavailable
    const fallbackRevenueIntelligence = {
      opportunities: [
        {
          type: 'upsell',
          description: 'Customer ready for Professional tier upgrade',
          potentialRevenue: 1200,
          confidence: 0.85,
          priority: 'high',
          customerId: 'cust_demo_001',
          timeframe: '30 days',
          factors: ['high_usage', 'feature_requests', 'support_engagement']
        },
        {
          type: 'cross_sell',
          description: 'Advanced analytics add-on opportunity',
          potentialRevenue: 600,
          confidence: 0.72,
          priority: 'medium',
          customerId: 'cust_demo_003',
          timeframe: '45 days',
          factors: ['usage_patterns', 'industry_trends']
        },
        {
          type: 'expansion',
          description: 'Multi-tenant deployment expansion',
          potentialRevenue: 2400,
          confidence: 0.68,
          priority: 'high',
          customerId: 'cust_demo_004',
          timeframe: '60 days',
          factors: ['team_growth', 'usage_scaling']
        }
      ],
      risks: [
        {
          type: 'churn',
          description: 'Decreased usage pattern detected',
          impact: 2400,
          probability: 0.25,
          severity: 'medium',
          customerId: 'cust_demo_002',
          mitigationActions: ['customer_outreach', 'usage_optimization', 'feature_training'],
          factors: ['decreased_logins', 'support_tickets', 'feature_underutilization']
        },
        {
          type: 'downgrade',
          description: 'Customer may downgrade due to cost concerns',
          impact: 800,
          probability: 0.15,
          severity: 'low',
          customerId: 'cust_demo_005',
          mitigationActions: ['pricing_discussion', 'value_demonstration'],
          factors: ['payment_delays', 'usage_below_tier']
        }
      ],
      insights: [
        {
          category: 'usage',
          title: 'Peak usage hours identified',
          description: 'Most activity occurs between 9-11 AM EST, suggesting optimal support hours',
          actionable: true,
          impact: 'medium',
          confidence: 0.92
        },
        {
          category: 'revenue',
          title: 'Expansion revenue trending up',
          description: '18.5% expansion rate, up from 15.2% last quarter',
          actionable: false,
          impact: 'high',
          confidence: 0.95
        },
        {
          category: 'churn',
          title: 'Early warning indicators improved',
          description: 'Churn prediction accuracy increased to 89% with new ML model',
          actionable: true,
          impact: 'high',
          confidence: 0.89
        }
      ],
      recommendations: [
        {
          type: 'pricing_strategy',
          title: 'Implement usage-based pricing',
          description: 'High-volume customers would benefit from usage-based pricing model',
          expectedImpact: 15000,
          effort: 'medium',
          priority: 'high',
          timeframe: '90 days',
          confidence: 0.78
        },
        {
          type: 'retention_campaign',
          title: 'Proactive customer success outreach',
          description: 'Target medium-risk customers with personalized success plans',
          expectedImpact: 8500,
          effort: 'low',
          priority: 'medium',
          timeframe: '30 days',
          confidence: 0.85
        },
        {
          type: 'feature_development',
          title: 'Advanced reporting features',
          description: 'Customers requesting enhanced reporting capabilities',
          expectedImpact: 12000,
          effort: 'high',
          priority: 'medium',
          timeframe: '120 days',
          confidence: 0.72
        }
      ],
      predictions: {
        nextMonthRevenue: {
          amount: 92500,
          confidence: 0.89,
          factors: ['seasonal_trends', 'expansion_pipeline', 'churn_predictions'],
          breakdown: {
            existing: 87200,
            expansion: 3800,
            new: 1500
          }
        },
        quarterlyForecast: {
          q1: 278500,
          q2: 295200,
          q3: 312800,
          q4: 331500,
          confidence: 0.82,
          assumptions: ['current_growth_rate', 'market_conditions', 'competitive_landscape']
        },
        churnRisk: {
          probability: 0.12,
          factors: ['decreased_usage', 'support_tickets', 'payment_delays'],
          atRiskRevenue: 12800,
          timeline: '90 days'
        },
        expansionForecast: {
          probability: 0.68,
          potentialRevenue: 45200,
          timeline: '60 days',
          topOpportunities: 3
        }
      },
      marketAnalysis: {
        competitivePosition: 'strong',
        marketTrends: [
          {
            trend: 'Increased demand for real-time analytics',
            impact: 'positive',
            confidence: 0.87,
            timeframe: '6 months'
          },
          {
            trend: 'Privacy regulations tightening',
            impact: 'neutral',
            confidence: 0.92,
            timeframe: '12 months'
          }
        ],
        opportunityScore: 8.2,
        riskScore: 3.1
      }
    };

    const fallbackChurnPredictions = [
      {
        customerId: 'cust_demo_002',
        subscriptionId: 'sub_demo_002',
        churnProbability: 0.35,
        riskLevel: 'medium',
        factors: [
          { factor: 'decreased_usage', weight: 0.4, impact: 'negative' },
          { factor: 'support_tickets', weight: 0.3, impact: 'negative' },
          { factor: 'payment_delays', weight: 0.2, impact: 'negative' },
          { factor: 'feature_adoption', weight: 0.1, impact: 'positive' }
        ],
        recommendations: [
          'Schedule customer success call',
          'Provide usage optimization training',
          'Review pricing options'
        ],
        timeline: '60 days',
        confidence: 0.82
      },
      {
        customerId: 'cust_demo_005',
        subscriptionId: 'sub_demo_005',
        churnProbability: 0.15,
        riskLevel: 'low',
        factors: [
          { factor: 'stable_usage', weight: 0.5, impact: 'positive' },
          { factor: 'regular_payments', weight: 0.3, impact: 'positive' },
          { factor: 'feature_requests', weight: 0.2, impact: 'positive' }
        ],
        recommendations: [
          'Explore expansion opportunities',
          'Gather product feedback'
        ],
        timeline: '90 days',
        confidence: 0.91
      }
    ];

    const fallbackRevenueForecasts = {
      monthly: [
        { month: 'Jan 2024', revenue: 89750, confidence: 0.95 },
        { month: 'Feb 2024', revenue: 92500, confidence: 0.89 },
        { month: 'Mar 2024', revenue: 95200, confidence: 0.84 },
        { month: 'Apr 2024', revenue: 98100, confidence: 0.79 },
        { month: 'May 2024', revenue: 101300, confidence: 0.74 },
        { month: 'Jun 2024', revenue: 104800, confidence: 0.69 }
      ],
      scenarios: {
        conservative: {
          q1: 265400,
          q2: 278900,
          q3: 292800,
          q4: 307200,
          confidence: 0.92
        },
        optimistic: {
          q1: 295200,
          q2: 318500,
          q3: 343200,
          q4: 369800,
          confidence: 0.68
        },
        pessimistic: {
          q1: 245800,
          q2: 251200,
          q3: 256900,
          q4: 262800,
          confidence: 0.85
        }
      },
      drivers: [
        { driver: 'New customer acquisition', impact: 0.35, trend: 'positive' },
        { driver: 'Expansion revenue', impact: 0.28, trend: 'positive' },
        { driver: 'Churn prevention', impact: 0.22, trend: 'stable' },
        { driver: 'Pricing optimization', impact: 0.15, trend: 'positive' }
      ]
    };

    return (
      <div class="revenue-intelligence-page">
        {/* Offline Notice */}
        <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-yellow-800 dark:text-yellow-200 text-sm">
              Showing demo revenue intelligence data - service is currently unavailable
            </span>
          </div>
        </div>

        {/* Page Header */}
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Revenue Intelligence & Predictions
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Advanced revenue forecasting, churn prediction, and strategic opportunity analysis
          </p>
        </div>

        {/* Revenue Intelligence Dashboard Component with Fallback Data */}
        <RevenueIntelligenceDashboard 
          initialRevenueIntelligence={fallbackRevenueIntelligence}
          initialChurnPredictions={fallbackChurnPredictions}
          initialRevenueForecasts={fallbackRevenueForecasts}
          user={user}
          isOffline={true}
        />
      </div>
    );
  }
});
