import { defineRoute } from "$fresh/server.ts";
import { AppState } from "../types/fresh.ts";
import RevenueOperationsDashboard from "../islands/billing/RevenueOperationsDashboard.tsx";

export default defineRoute<AppState>(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch revenue operations data server-side with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Revenue operations data timeout")), 3000)
    );

    const [revenueOps, healthMonitoring, revenueIntelligence] = await Promise.race([
      Promise.all([
        fetch('http://localhost:8000/api/billing/revenue-operations-dashboard').then(r => r.json()),
        fetch('http://localhost:8000/api/billing/health-monitoring').then(r => r.json()),
        fetch('http://localhost:8000/api/billing/revenue-intelligence').then(r => r.json()),
      ]),
      timeoutPromise
    ]) as any[];

    return (
      <div class="revenue-operations-page">
        {/* Page Header */}
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Revenue Operations & Intelligence
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Comprehensive subscription management, health monitoring, and revenue optimization insights
          </p>
        </div>

        {/* Revenue Operations Dashboard Component */}
        <RevenueOperationsDashboard 
          initialRevenueOps={revenueOps?.data || null}
          initialHealthMonitoring={healthMonitoring?.data || null}
          initialRevenueIntelligence={revenueIntelligence?.data || null}
          user={user}
        />
      </div>
    );
  } catch (error) {
    console.error("Revenue operations error:", error);

    // Use fallback data when service is unavailable
    const fallbackRevenueOps = {
      summary: {
        totalSubscriptions: 1247,
        totalMRR: 89750,
        totalARR: 1077000,
        averageHealthScore: 87.3,
        churnRate: 2.1,
        expansionRate: 18.5,
        totalExpansionOpportunities: 23,
        totalRisks: 8,
        potentialExpansionRevenue: 45200,
        atRiskRevenue: 12800
      },
      subscriptions: [
        {
          subscription: {
            id: 'sub_demo_001',
            customerId: 'cust_demo_001',
            planId: 'plan_professional',
            status: 'active',
            mrr: 299,
            tier: 'Professional',
            createdAt: '2024-01-15T10:30:00Z'
          },
          healthScore: 92,
          riskLevel: 'low' as const,
          expansionOpportunities: [
            {
              type: 'feature_upgrade',
              description: 'Advanced analytics package',
              potentialRevenue: 150,
              confidence: 0.85,
              priority: 'high'
            }
          ],
          usageAnalytics: {
            currentUsage: 75,
            trend: 'increasing',
            efficiency: 0.89
          },
          churnPrediction: {
            churnProbability: 0.08,
            factors: ['high_engagement', 'feature_adoption'],
            riskLevel: 'low'
          }
        },
        {
          subscription: {
            id: 'sub_demo_002',
            customerId: 'cust_demo_002',
            planId: 'plan_enterprise',
            status: 'active',
            mrr: 999,
            tier: 'Enterprise',
            createdAt: '2023-11-20T14:15:00Z'
          },
          healthScore: 65,
          riskLevel: 'medium' as const,
          expansionOpportunities: [],
          usageAnalytics: {
            currentUsage: 45,
            trend: 'decreasing',
            efficiency: 0.62
          },
          churnPrediction: {
            churnProbability: 0.35,
            factors: ['decreased_usage', 'support_tickets'],
            riskLevel: 'medium'
          }
        }
      ],
      insights: {
        topExpansionOpportunities: [
          {
            customerId: 'cust_demo_001',
            opportunity: 'Advanced analytics upgrade',
            potentialRevenue: 1800,
            confidence: 0.85,
            timeframe: '30 days'
          },
          {
            customerId: 'cust_demo_003',
            opportunity: 'Multi-tenant expansion',
            potentialRevenue: 2400,
            confidence: 0.72,
            timeframe: '60 days'
          }
        ],
        criticalRisks: [
          {
            customerId: 'cust_demo_002',
            risk: 'Churn risk - decreased usage',
            impact: 11988,
            probability: 0.35,
            urgency: 'high'
          }
        ],
        revenueRecommendations: [
          {
            type: 'pricing_optimization',
            description: 'Implement usage-based pricing for high-volume customers',
            expectedImpact: 15000,
            effort: 'medium'
          },
          {
            type: 'retention_campaign',
            description: 'Proactive outreach to medium-risk customers',
            expectedImpact: 8500,
            effort: 'low'
          }
        ],
        performanceAlerts: [
          {
            type: 'expansion_opportunity',
            message: '3 customers ready for tier upgrade',
            severity: 'medium',
            actionRequired: true
          }
        ]
      },
      trends: {
        mrrGrowth: [85200, 86100, 87300, 88900, 89750],
        churnTrend: [2.8, 2.5, 2.3, 2.2, 2.1],
        expansionTrend: [15.2, 16.1, 17.3, 18.0, 18.5],
        healthScoreTrend: [84.2, 85.1, 86.3, 87.0, 87.3]
      }
    };

    const fallbackHealthMonitoring = {
      alerts: [
        {
          id: 'alert_001',
          type: 'churn_risk',
          severity: 'high',
          subscriptionId: 'sub_demo_002',
          customerId: 'cust_demo_002',
          message: 'Customer usage decreased by 40% in last 30 days',
          data: {
            usageChange: -0.4,
            healthScore: 65,
            riskFactors: ['decreased_usage', 'support_tickets']
          },
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          acknowledged: false
        },
        {
          id: 'alert_002',
          type: 'expansion_opportunity',
          severity: 'medium',
          subscriptionId: 'sub_demo_001',
          customerId: 'cust_demo_001',
          message: 'Customer exceeding usage limits - upgrade opportunity',
          data: {
            usageOverage: 0.25,
            recommendedTier: 'Enterprise',
            potentialRevenue: 700
          },
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          acknowledged: false
        }
      ],
      summary: {
        critical: 0,
        high: 1,
        medium: 1,
        low: 0,
        total: 2
      }
    };

    const fallbackRevenueIntelligence = {
      opportunities: [
        {
          type: 'upsell',
          description: 'Customer ready for Professional tier upgrade',
          potentialRevenue: 1200,
          confidence: 0.85,
          priority: 'high',
          customerId: 'cust_demo_001',
          timeframe: '30 days'
        },
        {
          type: 'cross_sell',
          description: 'Advanced analytics add-on opportunity',
          potentialRevenue: 600,
          confidence: 0.72,
          priority: 'medium',
          customerId: 'cust_demo_003',
          timeframe: '45 days'
        }
      ],
      risks: [
        {
          type: 'churn',
          description: 'Decreased usage pattern detected',
          impact: 2400,
          probability: 0.25,
          severity: 'medium',
          customerId: 'cust_demo_002',
          mitigationActions: ['customer_outreach', 'usage_optimization']
        }
      ],
      insights: [
        {
          category: 'usage',
          title: 'Peak usage hours identified',
          description: 'Most activity occurs between 9-11 AM EST',
          actionable: true,
          impact: 'medium'
        },
        {
          category: 'revenue',
          title: 'Expansion revenue trending up',
          description: '18.5% expansion rate, up from 15.2% last quarter',
          actionable: false,
          impact: 'high'
        }
      ],
      recommendations: [
        {
          type: 'pricing_strategy',
          title: 'Implement usage-based pricing',
          description: 'High-volume customers would benefit from usage-based pricing model',
          expectedImpact: 15000,
          effort: 'medium',
          priority: 'high'
        }
      ],
      predictions: {
        nextMonthRevenue: {
          amount: 92500,
          confidence: 0.89,
          factors: ['seasonal_trends', 'expansion_pipeline', 'churn_predictions']
        },
        quarterlyForecast: {
          q1: 278500,
          q2: 295200,
          q3: 312800,
          q4: 331500,
          confidence: 0.82
        },
        churnRisk: {
          probability: 0.12,
          factors: ['decreased_usage', 'support_tickets', 'payment_delays'],
          atRiskRevenue: 12800
        }
      }
    };

    return (
      <div class="revenue-operations-page">
        {/* Offline Notice */}
        <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-yellow-800 dark:text-yellow-200 text-sm">
              Showing demo revenue operations data - service is currently unavailable
            </span>
          </div>
        </div>

        {/* Page Header */}
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Revenue Operations & Intelligence
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Comprehensive subscription management, health monitoring, and revenue optimization insights
          </p>
        </div>

        {/* Revenue Operations Dashboard Component with Fallback Data */}
        <RevenueOperationsDashboard 
          initialRevenueOps={fallbackRevenueOps}
          initialHealthMonitoring={fallbackHealthMonitoring}
          initialRevenueIntelligence={fallbackRevenueIntelligence}
          user={user}
          isOffline={true}
        />
      </div>
    );
  }
});
