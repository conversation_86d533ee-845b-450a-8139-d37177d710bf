import { defineRoute } from "$fresh/server.ts";
import { AppState } from "../types/fresh.ts";
import SalesToolsOverview from "../islands/sales/SalesToolsOverview.tsx";

export default defineRoute<AppState>(async (_req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch sales tools data server-side with timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Sales tools data timeout")), 3000)
    );

    // Fetch demo scenarios, battle cards, and case studies
    const [demoScenarios, battleCards, caseStudies] = await Promise.race([
      Promise.all([
        fetch('/api/demo/scenarios').then(r => r.json()),
        fetch('/api/demo/battle-cards').then(r => r.json()),
        fetch('/api/demo/case-studies').then(r => r.json()),
      ]),
      timeoutPromise
    ]) as any[];

    return (
      <div class="sales-tools-page">
        {/* Page Header */}
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Sales Tools & Demo Environment
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Comprehensive sales enablement tools, demo environments, and competitive intelligence
          </p>
        </div>

        {/* Sales Tools Overview Component */}
        <SalesToolsOverview 
          initialDemoScenarios={demoScenarios?.data || null}
          initialBattleCards={battleCards?.data || null}
          initialCaseStudies={caseStudies?.data || null}
          user={user}
        />
      </div>
    );
  } catch (error) {
    console.error("Sales tools dashboard error:", error);

    // Use fallback data when service is unavailable
    const fallbackDemoScenarios = [
      {
        id: 'ecommerce_smb',
        name: 'E-commerce SMB',
        description: 'Small to medium e-commerce business scenario',
        industry: 'E-commerce',
        monthlyEvents: 50000,
        expectedROI: 285,
        features: ['Link tracking', 'Conversion analytics', 'Customer journey mapping']
      },
      {
        id: 'enterprise_retail',
        name: 'Enterprise Retail',
        description: 'Large enterprise retail scenario',
        industry: 'Retail',
        monthlyEvents: 500000,
        expectedROI: 450,
        features: ['Advanced analytics', 'Multi-channel tracking', 'Predictive insights']
      }
    ];

    const fallbackBattleCards = [
      {
        competitor: 'Google Analytics',
        strengths: ['Market leader', 'Free tier available'],
        weaknesses: ['Complex setup', 'Limited real-time features'],
        ourAdvantages: ['97% faster queries', 'Real-time insights', 'E-commerce focused'],
        talkingPoints: ['Performance advantage', 'Specialized for e-commerce', 'Better ROI tracking']
      }
    ];

    const fallbackCaseStudies = [
      {
        id: 'case_001',
        companyName: 'TechCorp Inc.',
        industry: 'Technology',
        challenge: 'Poor conversion tracking across multiple channels',
        solution: 'Implemented comprehensive analytics with branded link tracking',
        results: {
          conversionIncrease: 45,
          revenueGrowth: 32,
          timeToInsight: '90% faster'
        }
      }
    ];

    return (
      <div class="sales-tools-page">
        {/* Offline Notice */}
        <div class="mb-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-yellow-800 dark:text-yellow-200 text-sm">
              Showing demo sales tools data - service is currently unavailable
            </span>
          </div>
        </div>

        {/* Page Header */}
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Sales Tools & Demo Environment
          </h1>
          <p class="text-gray-600 dark:text-gray-300 mt-2">
            Comprehensive sales enablement tools, demo environments, and competitive intelligence
          </p>
        </div>

        {/* Sales Tools Overview Component with Fallback Data */}
        <SalesToolsOverview 
          initialDemoScenarios={fallbackDemoScenarios}
          initialBattleCards={fallbackBattleCards}
          initialCaseStudies={fallbackCaseStudies}
          user={user}
          isOffline={true}
        />
      </div>
    );
  }
});
