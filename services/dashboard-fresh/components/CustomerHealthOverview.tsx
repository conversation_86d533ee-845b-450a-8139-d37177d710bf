import { useEffect, useRef } from "preact/hooks";
import * as d3 from "d3";

interface CustomerHealthSummary {
  averageScore: number;
  totalCustomers: number;
  riskDistribution: Record<string, number>;
}

interface CustomerHealthOverviewProps {
  healthData: CustomerHealthSummary;
  isLoading: boolean;
}

export function CustomerHealthOverview({ healthData, isLoading }: CustomerHealthOverviewProps) {
  const chartRef = useRef<SVGSVGElement>(null);

  // Create health distribution chart
  useEffect(() => {
    if (!chartRef.current || isLoading || !healthData.riskDistribution) return;

    const svg = d3.select(chartRef.current);
    svg.selectAll("*").remove();

    const width = 280;
    const height = 180;
    const margin = { top: 20, right: 20, bottom: 40, left: 40 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left}, ${margin.top})`);

    // Prepare data
    const riskLevels = ['low', 'medium', 'high', 'critical'];
    const riskColors = {
      low: '#10B981',
      medium: '#F59E0B', 
      high: '#EF4444',
      critical: '#DC2626'
    };

    const data = riskLevels.map(level => ({
      level,
      count: healthData.riskDistribution[level] || 0,
      color: riskColors[level as keyof typeof riskColors]
    }));

    // Scales
    const xScale = d3.scaleBand()
      .domain(riskLevels)
      .range([0, chartWidth])
      .padding(0.2);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.count) || 0])
      .range([chartHeight, 0]);

    // Bars
    g.selectAll(".bar")
      .data(data)
      .enter()
      .append("rect")
      .attr("class", "bar")
      .attr("x", d => xScale(d.level) || 0)
      .attr("y", chartHeight)
      .attr("width", xScale.bandwidth())
      .attr("height", 0)
      .attr("fill", d => d.color)
      .attr("rx", 4)
      .transition()
      .duration(800)
      .attr("y", d => yScale(d.count))
      .attr("height", d => chartHeight - yScale(d.count));

    // Value labels on bars
    g.selectAll(".bar-label")
      .data(data)
      .enter()
      .append("text")
      .attr("class", "bar-label")
      .attr("x", d => (xScale(d.level) || 0) + xScale.bandwidth() / 2)
      .attr("y", d => yScale(d.count) - 5)
      .attr("text-anchor", "middle")
      .attr("font-size", "12px")
      .attr("font-weight", "600")
      .attr("fill", "#374151")
      .text(d => d.count)
      .style("opacity", 0)
      .transition()
      .delay(800)
      .duration(400)
      .style("opacity", 1);

    // X-axis
    g.append("g")
      .attr("transform", `translate(0, ${chartHeight})`)
      .call(d3.axisBottom(xScale))
      .selectAll("text")
      .attr("font-size", "11px")
      .attr("fill", "#6B7280")
      .style("text-transform", "capitalize");

    // Y-axis
    g.append("g")
      .call(d3.axisLeft(yScale).ticks(5))
      .selectAll("text")
      .attr("font-size", "11px")
      .attr("fill", "#6B7280");

    // Remove axis lines
    g.selectAll(".domain").remove();
    g.selectAll(".tick line").attr("stroke", "#E5E7EB");

  }, [healthData, isLoading]);

  // Calculate health score color
  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400';
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
    if (score >= 40) return 'text-orange-600 dark:text-orange-400';
    return 'text-red-600 dark:text-red-400';
  };

  // Calculate health score background
  const getHealthScoreBg = (score: number) => {
    if (score >= 80) return 'bg-green-50 dark:bg-green-900';
    if (score >= 60) return 'bg-yellow-50 dark:bg-yellow-900';
    if (score >= 40) return 'bg-orange-50 dark:bg-orange-900';
    return 'bg-red-50 dark:bg-red-900';
  };

  if (isLoading) {
    return (
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div class="animate-pulse">
          <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>
          <div class="space-y-4">
            <div class="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="grid grid-cols-2 gap-4">
              <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const averageScore = Math.round(healthData.averageScore || 0);
  const totalCustomers = healthData.totalCustomers || 0;
  const riskDistribution = healthData.riskDistribution || {};

  // Calculate risk percentages
  const highRiskCustomers = (riskDistribution.high || 0) + (riskDistribution.critical || 0);
  const healthyCustomers = riskDistribution.low || 0;
  const riskPercentage = totalCustomers > 0 ? (highRiskCustomers / totalCustomers) * 100 : 0;

  return (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
          Customer Health
        </h2>
        <div class="text-sm text-gray-500 dark:text-gray-400">
          {totalCustomers} customers
        </div>
      </div>

      {/* Average Health Score */}
      <div class={`${getHealthScoreBg(averageScore)} rounded-lg p-4 mb-6`}>
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Average Health Score</p>
            <p class={`text-3xl font-bold ${getHealthScoreColor(averageScore)}`}>
              {averageScore}
              <span class="text-lg">/100</span>
            </p>
          </div>
          <div class="text-right">
            <div class={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
              averageScore >= 80 
                ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                : averageScore >= 60
                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                : averageScore >= 40
                ? 'bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100'
                : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
            }`}>
              {averageScore >= 80 ? 'Excellent' : averageScore >= 60 ? 'Good' : averageScore >= 40 ? 'Fair' : 'Poor'}
            </div>
          </div>
        </div>
      </div>

      {/* Risk Distribution Chart */}
      <div class="mb-6">
        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Risk Distribution</h3>
        <svg ref={chartRef} class="w-full"></svg>
      </div>

      {/* Key Metrics */}
      <div class="grid grid-cols-2 gap-4">
        <div class="bg-green-50 dark:bg-green-900 rounded-lg p-3">
          <div class="text-center">
            <p class="text-sm text-green-600 dark:text-green-300 font-medium">Healthy Customers</p>
            <p class="text-2xl font-bold text-green-700 dark:text-green-200">
              {healthyCustomers}
            </p>
            <p class="text-xs text-green-600 dark:text-green-400">
              {totalCustomers > 0 ? Math.round((healthyCustomers / totalCustomers) * 100) : 0}% of total
            </p>
          </div>
        </div>

        <div class="bg-red-50 dark:bg-red-900 rounded-lg p-3">
          <div class="text-center">
            <p class="text-sm text-red-600 dark:text-red-300 font-medium">At Risk</p>
            <p class="text-2xl font-bold text-red-700 dark:text-red-200">
              {highRiskCustomers}
            </p>
            <p class="text-xs text-red-600 dark:text-red-400">
              {Math.round(riskPercentage)}% of total
            </p>
          </div>
        </div>
      </div>

      {/* Health Trend Indicator */}
      <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-600 dark:text-gray-400">Health Trend</span>
          <div class="flex items-center gap-2">
            {/* Simplified trend calculation based on risk percentage */}
            {riskPercentage < 10 ? (
              <>
                <span class="text-green-600 dark:text-green-400">↗</span>
                <span class="text-sm font-medium text-green-600 dark:text-green-400">Improving</span>
              </>
            ) : riskPercentage < 25 ? (
              <>
                <span class="text-yellow-600 dark:text-yellow-400">→</span>
                <span class="text-sm font-medium text-yellow-600 dark:text-yellow-400">Stable</span>
              </>
            ) : (
              <>
                <span class="text-red-600 dark:text-red-400">↘</span>
                <span class="text-sm font-medium text-red-600 dark:text-red-400">Declining</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Action Items */}
      {riskPercentage > 15 && (
        <div class="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg">
          <div class="flex items-start gap-2">
            <div class="text-yellow-600 dark:text-yellow-400 mt-0.5">⚠️</div>
            <div>
              <p class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Action Required</p>
              <p class="text-xs text-yellow-700 dark:text-yellow-300 mt-1">
                {Math.round(riskPercentage)}% of customers are at risk. Consider implementing retention campaigns.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
