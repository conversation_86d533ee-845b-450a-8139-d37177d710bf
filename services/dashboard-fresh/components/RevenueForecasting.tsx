// Revenue Forecasting Component
// Advanced revenue forecasting with ML-powered predictions and scenario analysis

import { useEffect, useRef } from "preact/hooks";
import * as d3 from "d3";

interface ForecastDataPoint {
  date: string;
  actualRevenue?: number;
  predictedRevenue: number;
  confidenceInterval: {
    lower: number;
    upper: number;
  };
  scenario: 'conservative' | 'realistic' | 'optimistic';
}

interface RevenueScenario {
  name: string;
  description: string;
  assumptions: string[];
  forecastData: ForecastDataPoint[];
  totalRevenue: number;
  growthRate: number;
  confidence: number;
}

interface ForecastingData {
  historicalData: Array<{
    date: string;
    revenue: number;
    newCustomers: number;
    churnRate: number;
    expansionRevenue: number;
  }>;
  scenarios: RevenueScenario[];
  keyMetrics: {
    currentMRR: number;
    projectedMRR: number;
    growthRate: number;
    churnRate: number;
    expansionRate: number;
    customerAcquisitionCost: number;
    customerLifetimeValue: number;
  };
  forecastAccuracy: {
    lastMonthAccuracy: number;
    last3MonthsAccuracy: number;
    averageAccuracy: number;
  };
  riskFactors: Array<{
    factor: string;
    impact: 'high' | 'medium' | 'low';
    probability: number;
    description: string;
  }>;
}

interface RevenueForecastingProps {
  forecastingData: ForecastingData;
  isLoading: boolean;
  selectedScenario: string;
  onScenarioChange?: (scenario: string) => void;
  onExportForecast?: () => void;
}

export function RevenueForecasting({ 
  forecastingData, 
  isLoading, 
  selectedScenario,
  onScenarioChange,
  onExportForecast 
}: RevenueForecastingProps) {
  const forecastChartRef = useRef<SVGSVGElement>(null);
  const accuracyChartRef = useRef<SVGSVGElement>(null);

  // Create main forecast chart
  useEffect(() => {
    if (!forecastChartRef.current || isLoading || !forecastingData.scenarios?.length) return;

    const svg = d3.select(forecastChartRef.current);
    svg.selectAll("*").remove();

    const width = 600;
    const height = 300;
    const margin = { top: 20, right: 30, bottom: 40, left: 70 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Get selected scenario data
    const scenario = forecastingData.scenarios.find(s => s.name === selectedScenario) || forecastingData.scenarios[0];
    const allData = [...forecastingData.historicalData, ...scenario.forecastData];

    // Scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(allData, d => new Date(d.date)) as [Date, Date])
      .range([0, chartWidth]);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(allData, d => 'revenue' in d ? d.revenue : d.predictedRevenue) || 100000])
      .range([chartHeight, 0]);

    // Historical data line
    const historicalLine = d3.line<typeof forecastingData.historicalData[0]>()
      .x(d => xScale(new Date(d.date)))
      .y(d => yScale(d.revenue))
      .curve(d3.curveMonotoneX);

    // Forecast line
    const forecastLine = d3.line<ForecastDataPoint>()
      .x(d => xScale(new Date(d.date)))
      .y(d => yScale(d.predictedRevenue))
      .curve(d3.curveMonotoneX);

    // Confidence interval area
    const confidenceArea = d3.area<ForecastDataPoint>()
      .x(d => xScale(new Date(d.date)))
      .y0(d => yScale(d.confidenceInterval.lower))
      .y1(d => yScale(d.confidenceInterval.upper))
      .curve(d3.curveMonotoneX);

    // Add confidence interval
    g.append("path")
      .datum(scenario.forecastData)
      .attr("fill", "#3b82f6")
      .attr("fill-opacity", 0.2)
      .attr("d", confidenceArea);

    // Add historical line
    g.append("path")
      .datum(forecastingData.historicalData)
      .attr("fill", "none")
      .attr("stroke", "#374151")
      .attr("stroke-width", 2)
      .attr("d", historicalLine);

    // Add forecast line
    g.append("path")
      .datum(scenario.forecastData)
      .attr("fill", "none")
      .attr("stroke", "#3b82f6")
      .attr("stroke-width", 2)
      .attr("stroke-dasharray", "5,5")
      .attr("d", forecastLine);

    // Add historical dots
    g.selectAll(".historical-dot")
      .data(forecastingData.historicalData)
      .enter().append("circle")
      .attr("class", "historical-dot")
      .attr("cx", d => xScale(new Date(d.date)))
      .attr("cy", d => yScale(d.revenue))
      .attr("r", 3)
      .attr("fill", "#374151");

    // Add forecast dots
    g.selectAll(".forecast-dot")
      .data(scenario.forecastData)
      .enter().append("circle")
      .attr("class", "forecast-dot")
      .attr("cx", d => xScale(new Date(d.date)))
      .attr("cy", d => yScale(d.predictedRevenue))
      .attr("r", 3)
      .attr("fill", "#3b82f6");

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat("%b %Y")));

    g.append("g")
      .call(d3.axisLeft(yScale).tickFormat(d => `$${(d / 1000).toFixed(0)}K`));

    // Add legend
    const legend = g.append("g")
      .attr("transform", `translate(${chartWidth - 150}, 10)`);

    legend.append("line")
      .attr("x1", 0)
      .attr("x2", 20)
      .attr("y1", 0)
      .attr("y2", 0)
      .attr("stroke", "#374151")
      .attr("stroke-width", 2);

    legend.append("text")
      .attr("x", 25)
      .attr("y", 4)
      .style("font-size", "12px")
      .text("Historical");

    legend.append("line")
      .attr("x1", 0)
      .attr("x2", 20)
      .attr("y1", 20)
      .attr("y2", 20)
      .attr("stroke", "#3b82f6")
      .attr("stroke-width", 2)
      .attr("stroke-dasharray", "5,5");

    legend.append("text")
      .attr("x", 25)
      .attr("y", 24)
      .style("font-size", "12px")
      .text("Forecast");

  }, [forecastingData, selectedScenario, isLoading]);

  // Create accuracy chart
  useEffect(() => {
    if (!accuracyChartRef.current || isLoading || !forecastingData.forecastAccuracy) return;

    const svg = d3.select(accuracyChartRef.current);
    svg.selectAll("*").remove();

    const width = 300;
    const height = 200;
    const radius = Math.min(width, height) / 2 - 20;

    const g = svg.append("g")
      .attr("transform", `translate(${width / 2},${height / 2})`);

    const accuracy = forecastingData.forecastAccuracy.averageAccuracy;
    const angle = (accuracy / 100) * 2 * Math.PI;

    // Background circle
    g.append("circle")
      .attr("r", radius)
      .attr("fill", "none")
      .attr("stroke", "#e5e7eb")
      .attr("stroke-width", 10);

    // Accuracy arc
    const arc = d3.arc()
      .innerRadius(radius - 5)
      .outerRadius(radius + 5)
      .startAngle(0)
      .endAngle(angle);

    g.append("path")
      .attr("d", arc)
      .attr("fill", accuracy >= 90 ? "#10b981" : accuracy >= 80 ? "#f59e0b" : "#ef4444");

    // Center text
    g.append("text")
      .attr("text-anchor", "middle")
      .attr("dy", "-0.5em")
      .style("font-size", "24px")
      .style("font-weight", "bold")
      .style("fill", accuracy >= 90 ? "#10b981" : accuracy >= 80 ? "#f59e0b" : "#ef4444")
      .text(`${accuracy.toFixed(1)}%`);

    g.append("text")
      .attr("text-anchor", "middle")
      .attr("dy", "1em")
      .style("font-size", "12px")
      .style("fill", "#6b7280")
      .text("Accuracy");

  }, [forecastingData.forecastAccuracy, isLoading]);

  const getScenarioColor = (scenario: string): string => {
    switch (scenario) {
      case 'conservative': return '#ef4444';
      case 'realistic': return '#3b82f6';
      case 'optimistic': return '#10b981';
      default: return '#6b7280';
    }
  };

  const getRiskImpactColor = (impact: string): string => {
    switch (impact) {
      case 'high': return 'text-red-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`;
  };

  if (isLoading) {
    return (
      <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="animate-pulse">
          <div class="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div class="space-y-3">
            <div class="h-4 bg-gray-200 rounded"></div>
            <div class="h-4 bg-gray-200 rounded w-5/6"></div>
            <div class="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div class="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
          <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
          <h3 class="text-lg font-semibold text-gray-900">Revenue Forecasting</h3>
        </div>
        <div class="flex items-center space-x-3">
          <select
            value={selectedScenario}
            onChange={(e) => onScenarioChange?.((e.target as HTMLSelectElement).value)}
            class="px-3 py-1 border border-gray-300 rounded-md text-sm"
          >
            {forecastingData.scenarios.map((scenario) => (
              <option key={scenario.name} value={scenario.name}>
                {scenario.name.charAt(0).toUpperCase() + scenario.name.slice(1)}
              </option>
            ))}
          </select>
          <button
            onClick={onExportForecast}
            class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
          >
            Export
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-blue-600">{formatCurrency(forecastingData.keyMetrics.currentMRR)}</div>
          <div class="text-sm text-blue-800">Current MRR</div>
        </div>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-green-600">{formatCurrency(forecastingData.keyMetrics.projectedMRR)}</div>
          <div class="text-sm text-green-800">Projected MRR</div>
        </div>
        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-purple-600">{formatPercentage(forecastingData.keyMetrics.growthRate)}</div>
          <div class="text-sm text-purple-800">Growth Rate</div>
        </div>
        <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-indigo-600">{formatCurrency(forecastingData.keyMetrics.customerLifetimeValue)}</div>
          <div class="text-sm text-indigo-800">Customer LTV</div>
        </div>
      </div>

      {/* Charts */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div>
          <h4 class="text-md font-medium text-gray-900 mb-3">Revenue Forecast</h4>
          <svg ref={forecastChartRef} width="600" height="300" class="w-full"></svg>
        </div>
        <div>
          <h4 class="text-md font-medium text-gray-900 mb-3">Forecast Accuracy</h4>
          <div class="flex items-center justify-center">
            <svg ref={accuracyChartRef} width="300" height="200"></svg>
          </div>
          <div class="mt-4 space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">Last Month:</span>
              <span class="font-medium">{forecastingData.forecastAccuracy.lastMonthAccuracy.toFixed(1)}%</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Last 3 Months:</span>
              <span class="font-medium">{forecastingData.forecastAccuracy.last3MonthsAccuracy.toFixed(1)}%</span>
            </div>
          </div>
        </div>
      </div>

      {/* Scenario Comparison */}
      <div class="mb-6">
        <h4 class="text-md font-medium text-gray-900 mb-3">Scenario Comparison</h4>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          {forecastingData.scenarios.map((scenario) => (
            <div 
              key={scenario.name}
              class={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                selectedScenario === scenario.name 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => onScenarioChange?.(scenario.name)}
            >
              <div class="flex items-center mb-2">
                <div 
                  class="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: getScenarioColor(scenario.name) }}
                ></div>
                <h5 class="font-medium text-gray-900 capitalize">{scenario.name}</h5>
              </div>
              <div class="text-sm text-gray-600 mb-2">{scenario.description}</div>
              <div class="space-y-1 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">Total Revenue:</span>
                  <span class="font-medium">{formatCurrency(scenario.totalRevenue)}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Growth Rate:</span>
                  <span class="font-medium">{formatPercentage(scenario.growthRate)}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Confidence:</span>
                  <span class="font-medium">{formatPercentage(scenario.confidence)}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Risk Factors */}
      <div>
        <h4 class="text-md font-medium text-gray-900 mb-3">Risk Factors</h4>
        <div class="space-y-3">
          {forecastingData.riskFactors.map((risk, index) => (
            <div key={index} class="border border-gray-200 rounded-lg p-3">
              <div class="flex items-center justify-between mb-2">
                <h5 class="font-medium text-gray-900">{risk.factor}</h5>
                <div class="flex items-center space-x-2">
                  <span class={`text-sm font-medium ${getRiskImpactColor(risk.impact)}`}>
                    {risk.impact.toUpperCase()} IMPACT
                  </span>
                  <span class="text-sm text-gray-600">
                    {formatPercentage(risk.probability)} probability
                  </span>
                </div>
              </div>
              <p class="text-sm text-gray-600">{risk.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
