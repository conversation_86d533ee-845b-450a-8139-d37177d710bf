// Churn Risk Alert Component
// Real-time churn risk monitoring and intervention alerts for customer success

import { useEffect, useRef } from "preact/hooks";
import * as d3 from "d3";

interface ChurnRiskCustomer {
  id: string;
  name: string;
  email: string;
  healthScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  riskFactors: string[];
  lastActivity: string;
  subscriptionValue: number;
  daysUntilRenewal: number;
  interventionStatus: 'none' | 'scheduled' | 'in_progress' | 'completed';
  predictedChurnProbability: number;
  revenueAtRisk: number;
}

interface ChurnRiskData {
  totalCustomersAtRisk: number;
  totalRevenueAtRisk: number;
  riskDistribution: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  topRiskCustomers: ChurnRiskCustomer[];
  interventionMetrics: {
    totalInterventions: number;
    successfulInterventions: number;
    pendingInterventions: number;
    successRate: number;
  };
  trendData: Array<{
    date: string;
    riskScore: number;
    customersAtRisk: number;
    revenueAtRisk: number;
  }>;
}

interface ChurnRiskAlertProps {
  riskData: ChurnRiskData;
  isLoading: boolean;
  onInterventionTrigger?: (customerId: string, interventionType: string) => void;
  onCustomerDetails?: (customerId: string) => void;
}

export function ChurnRiskAlert({ 
  riskData, 
  isLoading, 
  onInterventionTrigger,
  onCustomerDetails 
}: ChurnRiskAlertProps) {
  const trendChartRef = useRef<SVGSVGElement>(null);
  const riskDistributionRef = useRef<SVGSVGElement>(null);

  // Create risk trend chart
  useEffect(() => {
    if (!trendChartRef.current || isLoading || !riskData.trendData?.length) return;

    const svg = d3.select(trendChartRef.current);
    svg.selectAll("*").remove();

    const width = 400;
    const height = 200;
    const margin = { top: 20, right: 30, bottom: 40, left: 50 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(riskData.trendData, d => new Date(d.date)) as [Date, Date])
      .range([0, chartWidth]);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(riskData.trendData, d => d.customersAtRisk) || 100])
      .range([chartHeight, 0]);

    // Line generator
    const line = d3.line<typeof riskData.trendData[0]>()
      .x(d => xScale(new Date(d.date)))
      .y(d => yScale(d.customersAtRisk))
      .curve(d3.curveMonotoneX);

    // Add line
    g.append("path")
      .datum(riskData.trendData)
      .attr("fill", "none")
      .attr("stroke", "#ef4444")
      .attr("stroke-width", 2)
      .attr("d", line);

    // Add dots
    g.selectAll(".dot")
      .data(riskData.trendData)
      .enter().append("circle")
      .attr("class", "dot")
      .attr("cx", d => xScale(new Date(d.date)))
      .attr("cy", d => yScale(d.customersAtRisk))
      .attr("r", 4)
      .attr("fill", "#ef4444");

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat("%m/%d")));

    g.append("g")
      .call(d3.axisLeft(yScale));

  }, [riskData.trendData, isLoading]);

  // Create risk distribution chart
  useEffect(() => {
    if (!riskDistributionRef.current || isLoading || !riskData.riskDistribution) return;

    const svg = d3.select(riskDistributionRef.current);
    svg.selectAll("*").remove();

    const width = 200;
    const height = 200;
    const radius = Math.min(width, height) / 2 - 10;

    const g = svg.append("g")
      .attr("transform", `translate(${width / 2},${height / 2})`);

    const data = Object.entries(riskData.riskDistribution).map(([level, count]) => ({
      level,
      count,
      color: getRiskLevelColor(level)
    }));

    const pie = d3.pie<typeof data[0]>()
      .value(d => d.count)
      .sort(null);

    const arc = d3.arc<d3.PieArcDatum<typeof data[0]>>()
      .innerRadius(0)
      .outerRadius(radius);

    const arcs = g.selectAll(".arc")
      .data(pie(data))
      .enter().append("g")
      .attr("class", "arc");

    arcs.append("path")
      .attr("d", arc)
      .attr("fill", d => d.data.color);

    arcs.append("text")
      .attr("transform", d => `translate(${arc.centroid(d)})`)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .style("fill", "white")
      .text(d => d.data.count);

  }, [riskData.riskDistribution, isLoading]);

  const getRiskLevelColor = (level: string): string => {
    switch (level) {
      case 'critical': return '#dc2626';
      case 'high': return '#ea580c';
      case 'medium': return '#d97706';
      case 'low': return '#65a30d';
      default: return '#6b7280';
    }
  };

  const getRiskLevelBadgeClass = (level: string): string => {
    switch (level) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`;
  };

  if (isLoading) {
    return (
      <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="animate-pulse">
          <div class="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div class="space-y-3">
            <div class="h-4 bg-gray-200 rounded"></div>
            <div class="h-4 bg-gray-200 rounded w-5/6"></div>
            <div class="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div class="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
          <div class="w-3 h-3 bg-red-500 rounded-full mr-3 animate-pulse"></div>
          <h3 class="text-lg font-semibold text-gray-900">Churn Risk Alerts</h3>
        </div>
        <div class="text-sm text-gray-500">
          Last updated: {new Date().toLocaleTimeString()}
        </div>
      </div>

      {/* Summary Metrics */}
      <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-red-600">{riskData.totalCustomersAtRisk}</div>
          <div class="text-sm text-red-800">Customers at Risk</div>
        </div>
        <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-orange-600">{formatCurrency(riskData.totalRevenueAtRisk)}</div>
          <div class="text-sm text-orange-800">Revenue at Risk</div>
        </div>
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-blue-600">{riskData.interventionMetrics.pendingInterventions}</div>
          <div class="text-sm text-blue-800">Pending Interventions</div>
        </div>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-green-600">{formatPercentage(riskData.interventionMetrics.successRate)}</div>
          <div class="text-sm text-green-800">Success Rate</div>
        </div>
      </div>

      {/* Charts */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div>
          <h4 class="text-md font-medium text-gray-900 mb-3">Risk Trend (7 Days)</h4>
          <svg ref={trendChartRef} width="400" height="200" class="w-full"></svg>
        </div>
        <div>
          <h4 class="text-md font-medium text-gray-900 mb-3">Risk Distribution</h4>
          <div class="flex items-center">
            <svg ref={riskDistributionRef} width="200" height="200"></svg>
            <div class="ml-4 space-y-2">
              {Object.entries(riskData.riskDistribution).map(([level, count]) => (
                <div key={level} class="flex items-center">
                  <div 
                    class="w-3 h-3 rounded-full mr-2"
                    style={{ backgroundColor: getRiskLevelColor(level) }}
                  ></div>
                  <span class="text-sm text-gray-600 capitalize">{level}: {count}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* High-Risk Customers */}
      <div>
        <h4 class="text-md font-medium text-gray-900 mb-3">High-Risk Customers</h4>
        <div class="space-y-3">
          {riskData.topRiskCustomers.slice(0, 5).map((customer) => (
            <div key={customer.id} class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="flex items-center mb-2">
                    <h5 class="font-medium text-gray-900 mr-3">{customer.name}</h5>
                    <span class={`px-2 py-1 text-xs font-medium rounded-full border ${getRiskLevelBadgeClass(customer.riskLevel)}`}>
                      {customer.riskLevel.toUpperCase()}
                    </span>
                  </div>
                  <div class="text-sm text-gray-600 mb-2">
                    Health Score: {customer.healthScore}/100 | 
                    Churn Probability: {formatPercentage(customer.predictedChurnProbability)} | 
                    Revenue: {formatCurrency(customer.subscriptionValue)}
                  </div>
                  <div class="flex flex-wrap gap-1">
                    {customer.riskFactors.slice(0, 3).map((factor, index) => (
                      <span key={index} class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                        {factor}
                      </span>
                    ))}
                    {customer.riskFactors.length > 3 && (
                      <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                        +{customer.riskFactors.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
                <div class="flex flex-col space-y-2 ml-4">
                  <button
                    onClick={() => onCustomerDetails?.(customer.id)}
                    class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                  >
                    View Details
                  </button>
                  {customer.interventionStatus === 'none' && (
                    <button
                      onClick={() => onInterventionTrigger?.(customer.id, 'outreach')}
                      class="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
                    >
                      Trigger Intervention
                    </button>
                  )}
                  {customer.interventionStatus === 'scheduled' && (
                    <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded text-center">
                      Scheduled
                    </span>
                  )}
                  {customer.interventionStatus === 'in_progress' && (
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded text-center">
                      In Progress
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Action Items */}
      {riskData.totalCustomersAtRisk > 0 && (
        <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h4 class="font-medium text-yellow-800 mb-2">⚠️ Immediate Actions Required</h4>
          <ul class="text-sm text-yellow-700 space-y-1">
            <li>• {riskData.riskDistribution.critical} customers require immediate intervention</li>
            <li>• {formatCurrency(riskData.totalRevenueAtRisk)} in revenue needs protection</li>
            <li>• {riskData.interventionMetrics.pendingInterventions} interventions pending execution</li>
          </ul>
        </div>
      )}
    </div>
  );
}
