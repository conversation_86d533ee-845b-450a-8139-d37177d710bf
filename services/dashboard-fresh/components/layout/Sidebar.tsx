import { User } from "../../utils/auth.ts";
import LogoutButton from "../../islands/auth/LogoutButton.tsx";

interface SidebarProps {
  user?: User;
  currentPath: string;
}

interface NavItem {
  name: string;
  href: string;
  icon: string;
  current?: boolean;
  children?: NavItem[];
}

export default function Sidebar({ user, currentPath }: SidebarProps) {
  const navigation: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/',
      icon: 'home',
      current: currentPath === '/'
    },
    {
      name: 'Analytics',
      href: '/analytics',
      icon: 'chart-bar',
      current: currentPath.startsWith('/analytics'),
      children: [
        { name: 'Overview', href: '/analytics', icon: 'chart-line' },
        { name: 'D3 Dashboard', href: '/analytics/d3-dashboard', icon: 'chart-pie' },
        { name: 'Cohort Analysis', href: '/analytics/cohorts', icon: 'users' },
        { name: 'Attribution', href: '/analytics/attribution', icon: 'link' },
        { name: 'Real-time', href: '/analytics/realtime', icon: 'lightning-bolt' }
      ]
    },
    {
      name: 'Links',
      href: '/links',
      icon: 'link',
      current: currentPath.startsWith('/links')
    },
    {
      name: 'Campaigns',
      href: '/campaigns',
      icon: 'megaphone',
      current: currentPath.startsWith('/campaigns')
    },
    {
      name: 'Marketplace',
      href: '/marketplace',
      icon: 'globe',
      current: currentPath.startsWith('/marketplace'),
      children: [
        { name: 'Overview', href: '/marketplace', icon: 'chart-bar' },
        { name: 'Discover Partners', href: '/marketplace/discover', icon: 'search' },
        { name: 'Partnerships', href: '/marketplace/partnerships', icon: 'users' },
        { name: 'Analytics', href: '/marketplace/analytics', icon: 'chart-line' }
      ]
    },
    {
      name: 'Revenue Operations',
      href: '/revenue-operations',
      icon: 'currency-dollar',
      current: currentPath.startsWith('/revenue-operations')
    },
    {
      name: 'Revenue Intelligence',
      href: '/revenue-intelligence',
      icon: 'light-bulb',
      current: currentPath.startsWith('/revenue-intelligence')
    },
    {
      name: 'Billing',
      href: '/billing',
      icon: 'credit-card',
      current: currentPath.startsWith('/billing')
    },
    {
      name: 'Customer Success',
      href: '/customer-success',
      icon: 'heart',
      current: currentPath.startsWith('/customer-success'),
      children: [
        { name: 'Overview', href: '/customer-success', icon: 'chart-bar' },
        { name: 'Trial Management', href: '/customer-success/trials', icon: 'clock' },
        { name: 'Health Monitoring', href: '/customer-success/health', icon: 'shield-check' },
        { name: 'Journey Tracking', href: '/customer-success/journey', icon: 'map' }
      ]
    },
    {
      name: 'Enhanced Trial Management',
      href: '/trials/management',
      icon: 'academic-cap',
      current: currentPath.startsWith('/trials/management')
    },
    {
      name: 'Usage Analytics',
      href: '/billing/usage',
      icon: 'chart-pie',
      current: currentPath.startsWith('/billing/usage')
    },
    {
      name: 'Sales Tools',
      href: '/sales',
      icon: 'briefcase',
      current: currentPath.startsWith('/sales'),
      children: [
        { name: 'Demo Environment', href: '/sales/demo', icon: 'presentation-chart-line' },
        { name: 'Sales Materials', href: '/sales/materials', icon: 'document-duplicate' },
        { name: 'ROI Calculator', href: '/sales/roi-calculator', icon: 'calculator' },
        { name: 'Battle Cards', href: '/sales/battle-cards', icon: 'shield' }
      ]
    },
    {
      name: 'Reports',
      href: '/reports',
      icon: 'document-text',
      current: currentPath.startsWith('/reports')
    },
    {
      name: 'Integrations',
      href: '/integrations',
      icon: 'puzzle-piece',
      current: currentPath.startsWith('/integrations')
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: 'cog',
      current: currentPath.startsWith('/settings')
    }
  ];

  const getIcon = (iconName: string) => {
    const icons: Record<string, string> = {
      'home': 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6',
      'chart-bar': 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
      'chart-line': 'M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z',
      'users': 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z',
      'link': 'M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1',
      'lightning-bolt': 'M13 10V3L4 14h7v7l9-11h-7z',
      'megaphone': 'M7 4V2a1 1 0 011-1h4a1 1 0 011 1v2h3a1 1 0 011 1v10a1 1 0 01-1 1h-3v2a1 1 0 01-1 1H8a1 1 0 01-1-1v-2H4a1 1 0 01-1-1V5a1 1 0 011-1h3z',
      'document-text': 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
      'globe': 'M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9',
      'search': 'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z',
      'currency-dollar': 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1',
      'light-bulb': 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z',
      'academic-cap': 'M12 14l9-5-9-5-9 5 9 5z M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z',
      'chart-pie': 'M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z',
      'credit-card': 'M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z',
      'heart': 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z',
      'briefcase': 'M12 14l9-5-9-5-9 5 9 5z M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z',
      'clock': 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z',
      'shield-check': 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
      'map': 'M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7',
      'presentation-chart-line': 'M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z',
      'document-duplicate': 'M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2',
      'calculator': 'M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z',
      'shield': 'M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
      'puzzle-piece': 'M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V5a1 1 0 011-1h3a1 1 0 001-1V4z',
      'cog': 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z'
    };
    return icons[iconName] || icons['home'];
  };

  return (
    <div class="hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-40 lg:w-64 lg:bg-white dark:bg-gray-900 lg:shadow-lg lg:border-r lg:border-gray-200 dark:border-gray-700 lg:pt-16 lg:block transition-colors">
      <div class="flex flex-col h-full">
        {/* User info */}
        {user && (
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
              <div class="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                <span class="text-sm font-medium text-primary-600 dark:text-primary-400">
                  {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                </span>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {user.firstName} {user.lastName}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  {user.companyName || user.role}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Navigation */}
        <nav class="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
          {navigation.map((item) => (
            <div key={item.name}>
              <a
                href={item.href}
                class={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                  item.current
                    ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-900 dark:text-primary-100 border-r-2 border-primary-500'
                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100'
                }`}
              >
                <svg
                  class={`mr-3 h-5 w-5 ${
                    item.current ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d={getIcon(item.icon)}
                  />
                </svg>
                {item.name}
              </a>

              {/* Sub-navigation */}
              {item.children && item.current && (
                <div class="ml-8 mt-1 space-y-1">
                  {item.children.map((child) => (
                    <a
                      key={child.name}
                      href={child.href}
                      class={`group flex items-center px-2 py-1 text-xs font-medium rounded-md transition-colors ${
                        currentPath === child.href
                          ? 'bg-primary-50 dark:bg-primary-900/10 text-primary-700 dark:text-primary-300'
                          : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-700 dark:hover:text-gray-300'
                      }`}
                    >
                      <svg
                        class={`mr-2 h-4 w-4 ${
                          currentPath === child.href ? 'text-primary-500' : 'text-gray-400'
                        }`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d={getIcon(child.icon)}
                        />
                      </svg>
                      {child.name}
                    </a>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>

        {/* Footer */}
        <div class="px-4 py-4 border-t border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <div class="text-xs text-gray-500 dark:text-gray-400">
              Fresh Dashboard v1.0
            </div>
            <LogoutButton
              className="text-xs"
              showIcon={false}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
