// Pricing Recommendations Component
// AI-powered pricing optimization recommendations based on customer behavior and market analysis

import { useEffect, useRef } from "preact/hooks";
import * as d3 from "d3";

interface PricingRecommendation {
  id: string;
  type: 'plan_adjustment' | 'feature_pricing' | 'usage_tier' | 'discount_optimization' | 'bundle_creation';
  title: string;
  description: string;
  currentPricing: {
    plan: string;
    price: number;
    features: string[];
  };
  recommendedPricing: {
    plan: string;
    price: number;
    features: string[];
  };
  impact: {
    revenueChange: number;
    customerRetention: number;
    acquisitionRate: number;
    competitivePosition: number;
  };
  confidence: number;
  implementation: {
    effort: 'low' | 'medium' | 'high';
    timeline: string;
    requirements: string[];
  };
  riskFactors: string[];
  supportingData: {
    customerFeedback: number;
    competitorAnalysis: string;
    usagePatterns: string;
    priceElasticity: number;
  };
}

interface PricingAnalysis {
  recommendations: PricingRecommendation[];
  marketPosition: {
    competitorComparison: Array<{
      competitor: string;
      plan: string;
      price: number;
      features: number;
      marketShare: number;
    }>;
    priceElasticity: number;
    optimalPriceRange: {
      min: number;
      max: number;
      recommended: number;
    };
  };
  customerSegments: Array<{
    segment: string;
    size: number;
    averageRevenue: number;
    pricesensitivity: number;
    preferredFeatures: string[];
    churnRisk: number;
  }>;
  revenueImpact: {
    totalPotentialIncrease: number;
    timeToRealization: string;
    riskAdjustedReturn: number;
  };
}

interface PricingRecommendationsProps {
  pricingAnalysis: PricingAnalysis;
  isLoading: boolean;
  onImplementRecommendation?: (recommendationId: string) => void;
  onViewDetails?: (recommendationId: string) => void;
}

export function PricingRecommendations({ 
  pricingAnalysis, 
  isLoading, 
  onImplementRecommendation,
  onViewDetails 
}: PricingRecommendationsProps) {
  const competitorChartRef = useRef<SVGSVGElement>(null);
  const segmentChartRef = useRef<SVGSVGElement>(null);

  // Create competitor comparison chart
  useEffect(() => {
    if (!competitorChartRef.current || isLoading || !pricingAnalysis.marketPosition?.competitorComparison?.length) return;

    const svg = d3.select(competitorChartRef.current);
    svg.selectAll("*").remove();

    const width = 400;
    const height = 250;
    const margin = { top: 20, right: 30, bottom: 60, left: 50 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const data = pricingAnalysis.marketPosition.competitorComparison;

    // Scales
    const xScale = d3.scaleBand()
      .domain(data.map(d => d.competitor))
      .range([0, chartWidth])
      .padding(0.1);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.price) || 1000])
      .range([chartHeight, 0]);

    // Bars
    g.selectAll(".competitor-bar")
      .data(data)
      .enter().append("rect")
      .attr("class", "competitor-bar")
      .attr("x", d => xScale(d.competitor) || 0)
      .attr("y", d => yScale(d.price))
      .attr("width", xScale.bandwidth())
      .attr("height", d => chartHeight - yScale(d.price))
      .attr("fill", d => d.competitor === "Our Platform" ? "#3b82f6" : "#94a3b8");

    // Value labels
    g.selectAll(".price-label")
      .data(data)
      .enter().append("text")
      .attr("class", "price-label")
      .attr("x", d => (xScale(d.competitor) || 0) + xScale.bandwidth() / 2)
      .attr("y", d => yScale(d.price) - 5)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .text(d => `$${d.price}`);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale))
      .selectAll("text")
      .style("text-anchor", "end")
      .attr("dx", "-.8em")
      .attr("dy", ".15em")
      .attr("transform", "rotate(-45)");

    g.append("g")
      .call(d3.axisLeft(yScale).tickFormat(d => `$${d}`));

  }, [pricingAnalysis.marketPosition, isLoading]);

  // Create customer segment chart
  useEffect(() => {
    if (!segmentChartRef.current || isLoading || !pricingAnalysis.customerSegments?.length) return;

    const svg = d3.select(segmentChartRef.current);
    svg.selectAll("*").remove();

    const width = 300;
    const height = 200;
    const radius = Math.min(width, height) / 2 - 20;

    const g = svg.append("g")
      .attr("transform", `translate(${width / 2},${height / 2})`);

    const data = pricingAnalysis.customerSegments.map(segment => ({
      ...segment,
      color: getSegmentColor(segment.segment)
    }));

    const pie = d3.pie<typeof data[0]>()
      .value(d => d.size)
      .sort(null);

    const arc = d3.arc<d3.PieArcDatum<typeof data[0]>>()
      .innerRadius(0)
      .outerRadius(radius);

    const arcs = g.selectAll(".arc")
      .data(pie(data))
      .enter().append("g")
      .attr("class", "arc");

    arcs.append("path")
      .attr("d", arc)
      .attr("fill", d => d.data.color);

    arcs.append("text")
      .attr("transform", d => `translate(${arc.centroid(d)})`)
      .attr("text-anchor", "middle")
      .style("font-size", "10px")
      .style("font-weight", "bold")
      .style("fill", "white")
      .text(d => d.data.size);

  }, [pricingAnalysis.customerSegments, isLoading]);

  const getRecommendationTypeIcon = (type: string): string => {
    switch (type) {
      case 'plan_adjustment': return '📊';
      case 'feature_pricing': return '⚙️';
      case 'usage_tier': return '📈';
      case 'discount_optimization': return '💰';
      case 'bundle_creation': return '📦';
      default: return '💡';
    }
  };

  const getImplementationEffortColor = (effort: string): string => {
    switch (effort) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getSegmentColor = (segment: string): string => {
    const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
    const index = segment.length % colors.length;
    return colors[index];
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`;
  };

  if (isLoading) {
    return (
      <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="animate-pulse">
          <div class="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div class="space-y-3">
            <div class="h-4 bg-gray-200 rounded"></div>
            <div class="h-4 bg-gray-200 rounded w-5/6"></div>
            <div class="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div class="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
          <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
          <h3 class="text-lg font-semibold text-gray-900">Pricing Recommendations</h3>
        </div>
        <div class="text-sm text-gray-500">
          {pricingAnalysis.recommendations?.length || 0} recommendations
        </div>
      </div>

      {/* Revenue Impact Summary */}
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-green-600">{formatCurrency(pricingAnalysis.revenueImpact?.totalPotentialIncrease || 0)}</div>
          <div class="text-sm text-green-800">Potential Revenue Increase</div>
        </div>
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-blue-600">{pricingAnalysis.revenueImpact?.timeToRealization || 'N/A'}</div>
          <div class="text-sm text-blue-800">Time to Realization</div>
        </div>
        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-purple-600">{formatPercentage(pricingAnalysis.revenueImpact?.riskAdjustedReturn || 0)}</div>
          <div class="text-sm text-purple-800">Risk-Adjusted Return</div>
        </div>
      </div>

      {/* Charts */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div>
          <h4 class="text-md font-medium text-gray-900 mb-3">Competitor Price Comparison</h4>
          <svg ref={competitorChartRef} width="400" height="250" class="w-full"></svg>
        </div>
        <div>
          <h4 class="text-md font-medium text-gray-900 mb-3">Customer Segments</h4>
          <div class="flex items-center">
            <svg ref={segmentChartRef} width="300" height="200"></svg>
            <div class="ml-4 space-y-2">
              {pricingAnalysis.customerSegments?.map((segment, index) => (
                <div key={index} class="flex items-center">
                  <div 
                    class="w-3 h-3 rounded-full mr-2"
                    style={{ backgroundColor: getSegmentColor(segment.segment) }}
                  ></div>
                  <div class="text-sm">
                    <div class="font-medium">{segment.segment}</div>
                    <div class="text-gray-600">{segment.size} customers</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Recommendations */}
      <div>
        <h4 class="text-md font-medium text-gray-900 mb-3">Top Recommendations</h4>
        <div class="space-y-4">
          {pricingAnalysis.recommendations?.slice(0, 3).map((recommendation) => (
            <div key={recommendation.id} class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center mb-2">
                    <span class="text-lg mr-2">{getRecommendationTypeIcon(recommendation.type)}</span>
                    <h5 class="font-medium text-gray-900 mr-3">{recommendation.title}</h5>
                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                      {formatPercentage(recommendation.confidence)} confidence
                    </span>
                  </div>
                  <p class="text-sm text-gray-600 mb-3">{recommendation.description}</p>
                  
                  {/* Current vs Recommended */}
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                    <div class="bg-gray-50 rounded p-3">
                      <div class="text-sm font-medium text-gray-700 mb-1">Current</div>
                      <div class="text-lg font-bold text-gray-900">{formatCurrency(recommendation.currentPricing.price)}</div>
                      <div class="text-sm text-gray-600">{recommendation.currentPricing.plan}</div>
                    </div>
                    <div class="bg-green-50 rounded p-3">
                      <div class="text-sm font-medium text-green-700 mb-1">Recommended</div>
                      <div class="text-lg font-bold text-green-900">{formatCurrency(recommendation.recommendedPricing.price)}</div>
                      <div class="text-sm text-green-600">{recommendation.recommendedPricing.plan}</div>
                    </div>
                  </div>

                  {/* Impact Metrics */}
                  <div class="grid grid-cols-2 lg:grid-cols-4 gap-3 mb-3">
                    <div class="text-center">
                      <div class="text-sm font-medium text-green-600">
                        {recommendation.impact.revenueChange > 0 ? '+' : ''}{formatPercentage(recommendation.impact.revenueChange)}
                      </div>
                      <div class="text-xs text-gray-600">Revenue</div>
                    </div>
                    <div class="text-center">
                      <div class="text-sm font-medium text-blue-600">
                        {formatPercentage(recommendation.impact.customerRetention)}
                      </div>
                      <div class="text-xs text-gray-600">Retention</div>
                    </div>
                    <div class="text-center">
                      <div class="text-sm font-medium text-purple-600">
                        {formatPercentage(recommendation.impact.acquisitionRate)}
                      </div>
                      <div class="text-xs text-gray-600">Acquisition</div>
                    </div>
                    <div class="text-center">
                      <div class="text-sm font-medium text-indigo-600">
                        {formatPercentage(recommendation.impact.competitivePosition)}
                      </div>
                      <div class="text-xs text-gray-600">Competitive</div>
                    </div>
                  </div>

                  {/* Implementation Details */}
                  <div class="flex items-center space-x-4 text-sm">
                    <span class="text-gray-600">
                      Effort: <span class={`font-medium ${getImplementationEffortColor(recommendation.implementation.effort)}`}>
                        {recommendation.implementation.effort.toUpperCase()}
                      </span>
                    </span>
                    <span class="text-gray-600">
                      Timeline: <span class="font-medium">{recommendation.implementation.timeline}</span>
                    </span>
                  </div>

                  {/* Risk Factors */}
                  {recommendation.riskFactors.length > 0 && (
                    <div class="mt-3">
                      <div class="text-sm font-medium text-gray-700 mb-1">Risk Factors:</div>
                      <div class="flex flex-wrap gap-1">
                        {recommendation.riskFactors.slice(0, 3).map((risk, index) => (
                          <span key={index} class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded">
                            {risk}
                          </span>
                        ))}
                        {recommendation.riskFactors.length > 3 && (
                          <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                            +{recommendation.riskFactors.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                <div class="flex flex-col space-y-2 ml-4">
                  <button
                    onClick={() => onViewDetails?.(recommendation.id)}
                    class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                  >
                    View Details
                  </button>
                  <button
                    onClick={() => onImplementRecommendation?.(recommendation.id)}
                    class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors"
                  >
                    Implement
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Market Position Summary */}
      {pricingAnalysis.marketPosition && (
        <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 class="font-medium text-blue-800 mb-2">💡 Market Position Insights</h4>
          <div class="text-sm text-blue-700 space-y-1">
            <div>• Optimal price range: {formatCurrency(pricingAnalysis.marketPosition.optimalPriceRange.min)} - {formatCurrency(pricingAnalysis.marketPosition.optimalPriceRange.max)}</div>
            <div>• Recommended price: {formatCurrency(pricingAnalysis.marketPosition.optimalPriceRange.recommended)}</div>
            <div>• Price elasticity: {pricingAnalysis.marketPosition.priceElasticity.toFixed(2)} (demand sensitivity)</div>
          </div>
        </div>
      )}
    </div>
  );
}
