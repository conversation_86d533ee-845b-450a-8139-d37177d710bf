import { useEffect, useRef } from "preact/hooks";
import * as d3 from "d3";

interface RevenueMetrics {
  totalRevenue: number;
  recurringRevenue: number;
  oneTimeRevenue: number;
  averageRevenuePerUser: number;
  monthlyRecurringRevenue: number;
  annualRecurringRevenue: number;
  revenueGrowthRate: number;
  customerLifetimeValue: number;
  revenuePerCustomer: number;
  conversionRate: number;
  churnRate: number;
  expansionRevenue: number;
  contractionRevenue: number;
  netRevenueRetention: number;
}

interface RevenueMetricsCardProps {
  metrics: RevenueMetrics;
  timeRange: string;
  isLoading: boolean;
}

export function RevenueMetricsCard({ metrics, timeRange, isLoading }: RevenueMetricsCardProps) {
  const chartRef = useRef<SVGSVGElement>(null);

  // Format currency values
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Format percentage values
  const formatPercentage = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`;
  };

  // Create revenue breakdown chart
  useEffect(() => {
    if (!chartRef.current || isLoading) return;

    const svg = d3.select(chartRef.current);
    svg.selectAll("*").remove();

    const width = 300;
    const height = 200;
    const radius = Math.min(width, height) / 2 - 10;

    const g = svg
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${width / 2}, ${height / 2})`);

    // Revenue breakdown data
    const revenueData = [
      { label: "Recurring", value: metrics.recurringRevenue, color: "#3B82F6" },
      { label: "One-time", value: metrics.oneTimeRevenue, color: "#10B981" },
      { label: "Expansion", value: metrics.expansionRevenue, color: "#F59E0B" },
    ].filter(d => d.value > 0);

    const pie = d3.pie<any>()
      .value(d => d.value)
      .sort(null);

    const arc = d3.arc<any>()
      .innerRadius(radius * 0.6)
      .outerRadius(radius);

    const arcs = g.selectAll(".arc")
      .data(pie(revenueData))
      .enter()
      .append("g")
      .attr("class", "arc");

    arcs.append("path")
      .attr("d", arc)
      .attr("fill", d => d.data.color)
      .attr("stroke", "#fff")
      .attr("stroke-width", 2)
      .style("opacity", 0.8)
      .on("mouseover", function(event, d) {
        d3.select(this).style("opacity", 1);
      })
      .on("mouseout", function(event, d) {
        d3.select(this).style("opacity", 0.8);
      });

    // Add labels
    arcs.append("text")
      .attr("transform", d => `translate(${arc.centroid(d)})`)
      .attr("text-anchor", "middle")
      .attr("font-size", "12px")
      .attr("font-weight", "500")
      .attr("fill", "#fff")
      .text(d => d.data.label);

    // Center text with total revenue
    g.append("text")
      .attr("text-anchor", "middle")
      .attr("font-size", "14px")
      .attr("font-weight", "600")
      .attr("fill", "#374151")
      .attr("dy", "-0.5em")
      .text("Total Revenue");

    g.append("text")
      .attr("text-anchor", "middle")
      .attr("font-size", "18px")
      .attr("font-weight", "700")
      .attr("fill", "#1F2937")
      .attr("dy", "1em")
      .text(formatCurrency(metrics.totalRevenue));

  }, [metrics, isLoading]);

  if (isLoading) {
    return (
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div class="animate-pulse">
          <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} class="space-y-2">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
          Revenue Metrics
        </h2>
        <span class="text-sm text-gray-500 dark:text-gray-400 capitalize">
          {timeRange.replace('d', ' days').replace('y', ' year')}
        </span>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Key Metrics Grid */}
        <div class="space-y-4">
          {/* Total Revenue */}
          <div class="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-blue-600 dark:text-blue-300">Total Revenue</p>
                <p class="text-2xl font-bold text-blue-900 dark:text-blue-100">
                  {formatCurrency(metrics.totalRevenue)}
                </p>
              </div>
              <div class={`text-sm font-medium ${
                metrics.revenueGrowthRate >= 0 
                  ? 'text-green-600 dark:text-green-400' 
                  : 'text-red-600 dark:text-red-400'
              }`}>
                {metrics.revenueGrowthRate >= 0 ? '↗' : '↘'} {formatPercentage(Math.abs(metrics.revenueGrowthRate))}
              </div>
            </div>
          </div>

          {/* MRR */}
          <div class="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900 dark:to-green-800 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-green-600 dark:text-green-300">Monthly Recurring Revenue</p>
                <p class="text-2xl font-bold text-green-900 dark:text-green-100">
                  {formatCurrency(metrics.monthlyRecurringRevenue)}
                </p>
              </div>
              <div class="text-xs text-green-600 dark:text-green-400">
                ARR: {formatCurrency(metrics.annualRecurringRevenue)}
              </div>
            </div>
          </div>

          {/* ARPU */}
          <div class="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900 dark:to-purple-800 rounded-lg p-4">
            <div>
              <p class="text-sm font-medium text-purple-600 dark:text-purple-300">Average Revenue Per User</p>
              <p class="text-2xl font-bold text-purple-900 dark:text-purple-100">
                {formatCurrency(metrics.averageRevenuePerUser)}
              </p>
            </div>
          </div>

          {/* Customer Lifetime Value */}
          <div class="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900 dark:to-orange-800 rounded-lg p-4">
            <div>
              <p class="text-sm font-medium text-orange-600 dark:text-orange-300">Customer Lifetime Value</p>
              <p class="text-2xl font-bold text-orange-900 dark:text-orange-100">
                {formatCurrency(metrics.customerLifetimeValue)}
              </p>
            </div>
          </div>
        </div>

        {/* Revenue Breakdown Chart */}
        <div class="flex flex-col items-center">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Revenue Breakdown</h3>
          <svg ref={chartRef} class="w-full max-w-sm"></svg>
        </div>
      </div>

      {/* Secondary Metrics */}
      <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="text-center">
            <p class="text-sm text-gray-500 dark:text-gray-400">Conversion Rate</p>
            <p class="text-lg font-semibold text-gray-900 dark:text-white">
              {formatPercentage(metrics.conversionRate)}
            </p>
          </div>
          
          <div class="text-center">
            <p class="text-sm text-gray-500 dark:text-gray-400">Churn Rate</p>
            <p class={`text-lg font-semibold ${
              metrics.churnRate > 0.05 
                ? 'text-red-600 dark:text-red-400' 
                : 'text-green-600 dark:text-green-400'
            }`}>
              {formatPercentage(metrics.churnRate)}
            </p>
          </div>
          
          <div class="text-center">
            <p class="text-sm text-gray-500 dark:text-gray-400">Net Revenue Retention</p>
            <p class={`text-lg font-semibold ${
              metrics.netRevenueRetention > 1 
                ? 'text-green-600 dark:text-green-400' 
                : 'text-orange-600 dark:text-orange-400'
            }`}>
              {formatPercentage(metrics.netRevenueRetention)}
            </p>
          </div>
          
          <div class="text-center">
            <p class="text-sm text-gray-500 dark:text-gray-400">Expansion Revenue</p>
            <p class="text-lg font-semibold text-blue-600 dark:text-blue-400">
              {formatCurrency(metrics.expansionRevenue)}
            </p>
          </div>
        </div>
      </div>

      {/* Revenue Health Indicator */}
      <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Revenue Health Score</span>
          <div class="flex items-center gap-2">
            {/* Calculate health score based on multiple factors */}
            {(() => {
              const healthScore = Math.min(100, Math.max(0, 
                (metrics.revenueGrowthRate > 0 ? 25 : 0) +
                (metrics.netRevenueRetention > 1 ? 25 : 0) +
                (metrics.churnRate < 0.05 ? 25 : 0) +
                (metrics.conversionRate > 0.02 ? 25 : 0)
              ));
              
              const healthColor = healthScore >= 75 ? 'green' : healthScore >= 50 ? 'yellow' : 'red';
              
              return (
                <>
                  <div class={`w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden`}>
                    <div 
                      class={`h-full bg-${healthColor}-500 transition-all duration-500`}
                      style={{ width: `${healthScore}%` }}
                    ></div>
                  </div>
                  <span class={`text-sm font-semibold text-${healthColor}-600 dark:text-${healthColor}-400`}>
                    {healthScore}%
                  </span>
                </>
              );
            })()}
          </div>
        </div>
      </div>
    </div>
  );
}
