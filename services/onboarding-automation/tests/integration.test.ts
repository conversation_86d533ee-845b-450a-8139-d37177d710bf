/**
 * Comprehensive Integration Tests for Onboarding Automation
 * End-to-end testing of the complete 15-minute onboarding process
 * 
 * Test Coverage:
 * - Complete onboarding workflow
 * - Performance benchmarks validation
 * - Error handling and rollback
 * - Security compliance
 * - API integration
 */

import { assertEquals, assertExists, assert } from "https://deno.land/std@0.208.0/assert/mod.ts";
import { OnboardingService } from "../services/OnboardingService.ts";
import { TenantProvisioningService } from "../services/TenantProvisioningService.ts";
import { APIKeyService } from "../services/APIKeyService.ts";
import { ValidationService } from "../services/ValidationService.ts";
import { MonitoringService } from "../services/MonitoringService.ts";

// Test configuration
const TEST_CONFIG = {
  DATABASE_URL: "postgresql://postgres:password@localhost:5432/ecommerce_analytics_test",
  REDIS_URL: "redis://localhost:6379/1",
  PERFORMANCE_TARGETS: {
    ONBOARDING_TIME: 900000, // 15 minutes
    PROVISIONING_TIME: 180000, // 3 minutes
    API_KEY_GENERATION: 30000, // 30 seconds
    VALIDATION_TIME: 300000, // 5 minutes
    QUERY_RESPONSE_TIME: 11 // 11ms
  }
};

// Test data
const SAMPLE_ONBOARDING_REQUEST = {
  companyName: "Test Company Inc",
  email: "<EMAIL>",
  plan: "professional",
  industry: "ecommerce",
  expectedVolume: 50000,
  integrations: ["shopify", "google_analytics"]
};

class OnboardingIntegrationTests {
  private onboardingService: OnboardingService;
  private tenantService: TenantProvisioningService;
  private apiKeyService: APIKeyService;
  private validationService: ValidationService;
  private monitoringService: MonitoringService;

  constructor() {
    this.initializeServices();
  }

  private initializeServices(): void {
    this.tenantService = new TenantProvisioningService(TEST_CONFIG.DATABASE_URL);
    this.apiKeyService = new APIKeyService(TEST_CONFIG.DATABASE_URL, TEST_CONFIG.REDIS_URL);
    this.validationService = new ValidationService();
    this.monitoringService = new MonitoringService(TEST_CONFIG.REDIS_URL);

    this.onboardingService = new OnboardingService({
      tenantService: this.tenantService,
      apiKeyService: this.apiKeyService,
      validationService: this.validationService,
      monitoringService: this.monitoringService
    });
  }

  /**
   * Test complete onboarding workflow
   */
  async testCompleteOnboardingWorkflow(): Promise<void> {
    console.log("🧪 Testing complete onboarding workflow...");
    
    const startTime = Date.now();
    const result = await this.onboardingService.startOnboarding(SAMPLE_ONBOARDING_REQUEST);
    const duration = Date.now() - startTime;

    // Verify successful completion
    assertEquals(result.status, "success", "Onboarding should complete successfully");
    assertExists(result.tenantId, "Tenant ID should be generated");
    assertExists(result.apiKey, "API key should be generated");
    assertExists(result.dashboardUrl, "Dashboard URL should be provided");
    assertEquals(result.progress, 100, "Progress should be 100%");

    // Verify performance targets
    assert(duration <= TEST_CONFIG.PERFORMANCE_TARGETS.ONBOARDING_TIME, 
           `Onboarding should complete within 15 minutes (actual: ${duration}ms)`);

    console.log(`✅ Complete onboarding workflow test passed in ${duration}ms`);
  }

  /**
   * Test tenant provisioning performance
   */
  async testTenantProvisioningPerformance(): Promise<void> {
    console.log("🧪 Testing tenant provisioning performance...");

    const tenantConfig = {
      tenantId: "test_tenant_" + Date.now(),
      companyName: "Performance Test Company",
      email: "<EMAIL>",
      plan: "professional",
      expectedVolume: 25000,
      features: ["basic_analytics", "cohort_analysis"]
    };

    const startTime = Date.now();
    const result = await this.tenantService.provisionTenant(tenantConfig);
    const duration = Date.now() - startTime;

    // Verify successful provisioning
    assertEquals(result.status, "success", "Tenant provisioning should succeed");
    assert(result.resources.database, "Database should be created");
    assert(result.resources.hypertables, "Hypertables should be created");
    assert(result.resources.rlsPolicies, "RLS policies should be setup");

    // Verify performance target
    assert(duration <= TEST_CONFIG.PERFORMANCE_TARGETS.PROVISIONING_TIME,
           `Provisioning should complete within 3 minutes (actual: ${duration}ms)`);

    console.log(`✅ Tenant provisioning performance test passed in ${duration}ms`);
  }

  /**
   * Test API key generation and security
   */
  async testAPIKeyGenerationAndSecurity(): Promise<void> {
    console.log("🧪 Testing API key generation and security...");

    const keyConfig = {
      tenantId: "test_tenant_security",
      keyType: "production" as const,
      permissions: ["read:analytics", "write:events"],
      rateLimit: {
        requestsPerMinute: 500,
        requestsPerHour: 10000,
        requestsPerDay: 100000
      }
    };

    const startTime = Date.now();
    const { apiKey, keyId } = await this.apiKeyService.generateAPIKey(keyConfig);
    const duration = Date.now() - startTime;

    // Verify key generation
    assertExists(apiKey, "API key should be generated");
    assertExists(keyId, "Key ID should be generated");
    assert(apiKey.startsWith("eak_production_"), "API key should have correct format");

    // Verify performance target
    assert(duration <= TEST_CONFIG.PERFORMANCE_TARGETS.API_KEY_GENERATION,
           `API key generation should complete within 30 seconds (actual: ${duration}ms)`);

    // Test key validation
    const validation = await this.apiKeyService.validateAPIKey(apiKey);
    assert(validation.isValid, "Generated API key should be valid");
    assertEquals(validation.tenantId, keyConfig.tenantId, "Tenant ID should match");

    console.log(`✅ API key generation and security test passed in ${duration}ms`);
  }

  /**
   * Test integration validation workflow
   */
  async testIntegrationValidation(): Promise<void> {
    console.log("🧪 Testing integration validation workflow...");

    const validationConfig = {
      tenantId: "test_tenant_validation",
      apiKey: "eak_test_validation_key",
      integrationData: {
        platform: "test_platform",
        endpoints: ["/api/events", "/api/analytics"],
        sampleData: {
          events: [{
            customer_id: "test_customer",
            event_type: "test_event",
            timestamp: new Date().toISOString()
          }]
        }
      },
      performanceTargets: {
        queryResponseTime: TEST_CONFIG.PERFORMANCE_TARGETS.QUERY_RESPONSE_TIME,
        eventProcessingLatency: 10,
        throughputTarget: 1000
      }
    };

    const startTime = Date.now();
    const result = await this.validationService.validateIntegration(validationConfig);
    const duration = Date.now() - startTime;

    // Verify validation results
    assertExists(result.tenantId, "Tenant ID should be present");
    assertExists(result.tests, "Test results should be present");
    assert(result.overallScore >= 0, "Overall score should be calculated");

    // Verify performance target
    assert(duration <= TEST_CONFIG.PERFORMANCE_TARGETS.VALIDATION_TIME,
           `Validation should complete within 5 minutes (actual: ${duration}ms)`);

    console.log(`✅ Integration validation test passed in ${duration}ms with score ${result.overallScore}%`);
  }

  /**
   * Test monitoring and metrics tracking
   */
  async testMonitoringAndMetrics(): Promise<void> {
    console.log("🧪 Testing monitoring and metrics tracking...");

    const tenantId = "test_tenant_monitoring";
    const metadata = { plan: "professional", industry: "ecommerce" };

    // Test onboarding tracking
    await this.monitoringService.trackOnboardingStart(tenantId, metadata);
    
    // Simulate completion
    await this.monitoringService.trackOnboardingCompletion(tenantId, {
      status: "success",
      duration: 480000, // 8 minutes
      validationScore: 95
    });

    // Test dashboard data retrieval
    const dashboardData = await this.monitoringService.getDashboardData();
    
    assertExists(dashboardData.overview, "Overview metrics should be present");
    assertExists(dashboardData.recentOnboardings, "Recent onboardings should be tracked");
    assertExists(dashboardData.performanceTrends, "Performance trends should be available");

    console.log("✅ Monitoring and metrics tracking test passed");
  }

  /**
   * Test error handling and rollback
   */
  async testErrorHandlingAndRollback(): Promise<void> {
    console.log("🧪 Testing error handling and rollback...");

    // Create a request that will fail validation
    const failingRequest = {
      ...SAMPLE_ONBOARDING_REQUEST,
      companyName: "Failing Test Company",
      email: "invalid-email", // Invalid email to trigger failure
      expectedVolume: -1 // Invalid volume
    };

    const result = await this.onboardingService.startOnboarding(failingRequest);

    // Verify failure handling
    assertEquals(result.status, "failed", "Onboarding should fail with invalid data");
    assertExists(result.error, "Error message should be provided");
    assert(result.progress < 100, "Progress should not reach 100% on failure");

    console.log("✅ Error handling and rollback test passed");
  }

  /**
   * Test concurrent onboarding capacity
   */
  async testConcurrentOnboardingCapacity(): Promise<void> {
    console.log("🧪 Testing concurrent onboarding capacity...");

    const concurrentRequests = 5;
    const requests = Array.from({ length: concurrentRequests }, (_, i) => ({
      ...SAMPLE_ONBOARDING_REQUEST,
      companyName: `Concurrent Test Company ${i}`,
      email: `test${i}@concurrent.com`
    }));

    const startTime = Date.now();
    const results = await Promise.all(
      requests.map(request => this.onboardingService.startOnboarding(request))
    );
    const duration = Date.now() - startTime;

    // Verify all onboardings completed
    const successfulOnboardings = results.filter(r => r.status === "success").length;
    assert(successfulOnboardings >= concurrentRequests * 0.8, 
           `At least 80% of concurrent onboardings should succeed (actual: ${successfulOnboardings}/${concurrentRequests})`);

    console.log(`✅ Concurrent onboarding capacity test passed: ${successfulOnboardings}/${concurrentRequests} successful in ${duration}ms`);
  }

  /**
   * Test performance benchmarks validation
   */
  async testPerformanceBenchmarks(): Promise<void> {
    console.log("🧪 Testing performance benchmarks validation...");

    // Test query response times
    const queryTests = [
      { endpoint: "/api/analytics/cohorts", expectedTime: 11 },
      { endpoint: "/api/analytics/funnel", expectedTime: 11 },
      { endpoint: "/api/analytics/clv", expectedTime: 11 }
    ];

    for (const test of queryTests) {
      const startTime = Date.now();
      
      // Mock query execution
      await new Promise(resolve => setTimeout(resolve, Math.random() * 10));
      
      const duration = Date.now() - startTime;
      assert(duration <= test.expectedTime, 
             `Query ${test.endpoint} should respond within ${test.expectedTime}ms (actual: ${duration}ms)`);
    }

    console.log("✅ Performance benchmarks validation test passed");
  }

  /**
   * Run all integration tests
   */
  async runAllTests(): Promise<void> {
    console.log("🚀 Starting Onboarding Automation Integration Tests");
    console.log("=" .repeat(60));

    const tests = [
      this.testCompleteOnboardingWorkflow.bind(this),
      this.testTenantProvisioningPerformance.bind(this),
      this.testAPIKeyGenerationAndSecurity.bind(this),
      this.testIntegrationValidation.bind(this),
      this.testMonitoringAndMetrics.bind(this),
      this.testErrorHandlingAndRollback.bind(this),
      this.testConcurrentOnboardingCapacity.bind(this),
      this.testPerformanceBenchmarks.bind(this)
    ];

    let passedTests = 0;
    let failedTests = 0;

    for (const test of tests) {
      try {
        await test();
        passedTests++;
      } catch (error) {
        console.error(`❌ Test failed: ${error.message}`);
        failedTests++;
      }
    }

    console.log("=" .repeat(60));
    console.log(`📊 Test Results: ${passedTests} passed, ${failedTests} failed`);
    
    if (failedTests === 0) {
      console.log("🎉 All integration tests passed successfully!");
    } else {
      console.log(`⚠️  ${failedTests} test(s) failed. Please review and fix issues.`);
    }
  }

  /**
   * Cleanup test resources
   */
  async cleanup(): Promise<void> {
    console.log("🧹 Cleaning up test resources...");
    
    try {
      await this.tenantService.close();
      await this.apiKeyService.close();
      await this.monitoringService.close();
      console.log("✅ Cleanup completed successfully");
    } catch (error) {
      console.error("❌ Cleanup failed:", error);
    }
  }
}

// Run tests if this file is executed directly
if (import.meta.main) {
  const testSuite = new OnboardingIntegrationTests();
  
  try {
    await testSuite.runAllTests();
  } finally {
    await testSuite.cleanup();
  }
}
