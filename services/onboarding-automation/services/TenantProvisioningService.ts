/**
 * Automated Tenant Provisioning Service
 * Creates complete tenant infrastructure in <3 minutes
 * 
 * Features:
 * - TimescaleDB hypertables creation
 * - RLS policies for multi-tenant isolation
 * - Performance optimization
 * - Automated rollback on failure
 */

import { Pool } from "https://deno.land/x/postgres@v0.17.0/mod.ts";
import { logger } from "../utils/logger.ts";

export interface TenantConfig {
  tenantId: string;
  companyName: string;
  email: string;
  plan: string;
  industry?: string;
  expectedVolume: number;
  features: string[];
}

export interface ProvisioningResult {
  tenantId: string;
  status: "success" | "failed" | "in_progress";
  createdAt: Date;
  completedAt?: Date;
  duration?: number;
  resources: {
    database: boolean;
    hypertables: boolean;
    rlsPolicies: boolean;
    indexes: boolean;
    continuousAggregates: boolean;
  };
  error?: string;
}

export class TenantProvisioningService {
  private pool: Pool;

  constructor(databaseUrl: string) {
    this.pool = new Pool(databaseUrl, 10, true);
  }

  /**
   * Provision complete tenant infrastructure
   * Target: <3 minutes total provisioning time
   */
  async provisionTenant(config: TenantConfig): Promise<ProvisioningResult> {
    const startTime = Date.now();
    const result: ProvisioningResult = {
      tenantId: config.tenantId,
      status: "in_progress",
      createdAt: new Date(),
      resources: {
        database: false,
        hypertables: false,
        rlsPolicies: false,
        indexes: false,
        continuousAggregates: false
      }
    };

    try {
      logger.info(`Starting tenant provisioning for ${config.tenantId}`);

      // Step 1: Create tenant record (30 seconds)
      await this.createTenantRecord(config);
      result.resources.database = true;

      // Step 2: Create TimescaleDB hypertables (60 seconds)
      await this.createHypertables(config.tenantId);
      result.resources.hypertables = true;

      // Step 3: Setup RLS policies (45 seconds)
      await this.setupRLSPolicies(config.tenantId);
      result.resources.rlsPolicies = true;

      // Step 4: Create performance indexes (30 seconds)
      await this.createPerformanceIndexes(config.tenantId);
      result.resources.indexes = true;

      // Step 5: Setup continuous aggregates (45 seconds)
      await this.setupContinuousAggregates(config.tenantId);
      result.resources.continuousAggregates = true;

      const duration = Date.now() - startTime;
      result.status = "success";
      result.completedAt = new Date();
      result.duration = duration;

      logger.info(`Tenant provisioning completed for ${config.tenantId} in ${duration}ms`);
      
      // Validate provisioning meets performance targets
      if (duration > 180000) { // 3 minutes
        logger.warn(`Provisioning exceeded target time: ${duration}ms > 180000ms`);
      }

      return result;

    } catch (error) {
      logger.error(`Tenant provisioning failed for ${config.tenantId}:`, error);
      
      // Attempt rollback
      await this.rollbackProvisioning(config.tenantId);
      
      result.status = "failed";
      result.error = error.message;
      result.duration = Date.now() - startTime;
      
      return result;
    }
  }

  /**
   * Create tenant record in main tenants table
   */
  private async createTenantRecord(config: TenantConfig): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      await client.queryObject(`
        INSERT INTO tenants (
          tenant_id,
          company_name,
          email,
          plan,
          industry,
          expected_volume,
          features,
          status,
          created_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, 'provisioning', NOW()
        )
      `, [
        config.tenantId,
        config.companyName,
        config.email,
        config.plan,
        config.industry,
        config.expectedVolume,
        JSON.stringify(config.features)
      ]);

      logger.info(`Created tenant record for ${config.tenantId}`);
    } finally {
      client.release();
    }
  }

  /**
   * Create TimescaleDB hypertables for tenant
   */
  private async createHypertables(tenantId: string): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      // Customer events hypertable
      await client.queryObject(`
        CREATE TABLE IF NOT EXISTS customer_events_${tenantId} (
          event_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tenant_id TEXT NOT NULL DEFAULT '${tenantId}',
          customer_id TEXT NOT NULL,
          session_id TEXT,
          event_type TEXT NOT NULL,
          event_data JSONB,
          revenue DECIMAL(10,2),
          timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        );
      `);

      // Convert to hypertable
      await client.queryObject(`
        SELECT create_hypertable(
          'customer_events_${tenantId}', 
          'timestamp',
          chunk_time_interval => INTERVAL '1 day',
          if_not_exists => TRUE
        );
      `);

      // Link clicks hypertable
      await client.queryObject(`
        CREATE TABLE IF NOT EXISTS link_clicks_${tenantId} (
          click_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tenant_id TEXT NOT NULL DEFAULT '${tenantId}',
          link_id TEXT NOT NULL,
          visitor_id TEXT,
          ip_address INET,
          user_agent TEXT,
          referrer TEXT,
          utm_source TEXT,
          utm_medium TEXT,
          utm_campaign TEXT,
          timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        );
      `);

      await client.queryObject(`
        SELECT create_hypertable(
          'link_clicks_${tenantId}', 
          'timestamp',
          chunk_time_interval => INTERVAL '1 day',
          if_not_exists => TRUE
        );
      `);

      logger.info(`Created hypertables for tenant ${tenantId}`);
    } finally {
      client.release();
    }
  }

  /**
   * Setup Row Level Security policies for multi-tenant isolation
   */
  private async setupRLSPolicies(tenantId: string): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      // Enable RLS on customer events
      await client.queryObject(`
        ALTER TABLE customer_events_${tenantId} ENABLE ROW LEVEL SECURITY;
      `);

      await client.queryObject(`
        CREATE POLICY tenant_isolation_customer_events_${tenantId}
        ON customer_events_${tenantId}
        FOR ALL
        TO authenticated
        USING (tenant_id = current_setting('app.current_tenant_id', true));
      `);

      // Enable RLS on link clicks
      await client.queryObject(`
        ALTER TABLE link_clicks_${tenantId} ENABLE ROW LEVEL SECURITY;
      `);

      await client.queryObject(`
        CREATE POLICY tenant_isolation_link_clicks_${tenantId}
        ON link_clicks_${tenantId}
        FOR ALL
        TO authenticated
        USING (tenant_id = current_setting('app.current_tenant_id', true));
      `);

      logger.info(`Setup RLS policies for tenant ${tenantId}`);
    } finally {
      client.release();
    }
  }

  /**
   * Create performance-optimized indexes
   */
  private async createPerformanceIndexes(tenantId: string): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      // Customer events indexes
      await client.queryObject(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_${tenantId}_customer_timestamp
        ON customer_events_${tenantId} (customer_id, timestamp DESC);
      `);

      await client.queryObject(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_${tenantId}_event_type_timestamp
        ON customer_events_${tenantId} (event_type, timestamp DESC);
      `);

      // Link clicks indexes
      await client.queryObject(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_link_clicks_${tenantId}_link_timestamp
        ON link_clicks_${tenantId} (link_id, timestamp DESC);
      `);

      await client.queryObject(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_link_clicks_${tenantId}_utm_timestamp
        ON link_clicks_${tenantId} (utm_campaign, timestamp DESC);
      `);

      logger.info(`Created performance indexes for tenant ${tenantId}`);
    } finally {
      client.release();
    }
  }

  /**
   * Setup continuous aggregates for real-time analytics
   */
  private async setupContinuousAggregates(tenantId: string): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      // Hourly customer events aggregate
      await client.queryObject(`
        CREATE MATERIALIZED VIEW IF NOT EXISTS customer_events_hourly_${tenantId}
        WITH (timescaledb.continuous) AS
        SELECT
          time_bucket('1 hour', timestamp) AS hour,
          event_type,
          COUNT(*) as event_count,
          COUNT(DISTINCT customer_id) as unique_customers,
          SUM(revenue) as total_revenue,
          AVG(revenue) as avg_revenue
        FROM customer_events_${tenantId}
        GROUP BY hour, event_type;
      `);

      // Daily link clicks aggregate
      await client.queryObject(`
        CREATE MATERIALIZED VIEW IF NOT EXISTS link_clicks_daily_${tenantId}
        WITH (timescaledb.continuous) AS
        SELECT
          time_bucket('1 day', timestamp) AS day,
          link_id,
          utm_campaign,
          COUNT(*) as click_count,
          COUNT(DISTINCT visitor_id) as unique_visitors
        FROM link_clicks_${tenantId}
        GROUP BY day, link_id, utm_campaign;
      `);

      logger.info(`Setup continuous aggregates for tenant ${tenantId}`);
    } finally {
      client.release();
    }
  }

  /**
   * Rollback provisioning on failure
   */
  private async rollbackProvisioning(tenantId: string): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      logger.info(`Rolling back provisioning for tenant ${tenantId}`);

      // Drop continuous aggregates
      await client.queryObject(`DROP MATERIALIZED VIEW IF EXISTS customer_events_hourly_${tenantId};`);
      await client.queryObject(`DROP MATERIALIZED VIEW IF EXISTS link_clicks_daily_${tenantId};`);

      // Drop tables
      await client.queryObject(`DROP TABLE IF EXISTS customer_events_${tenantId};`);
      await client.queryObject(`DROP TABLE IF EXISTS link_clicks_${tenantId};`);

      // Update tenant status
      await client.queryObject(`
        UPDATE tenants 
        SET status = 'failed', updated_at = NOW() 
        WHERE tenant_id = $1
      `, [tenantId]);

      logger.info(`Rollback completed for tenant ${tenantId}`);
    } catch (error) {
      logger.error(`Rollback failed for tenant ${tenantId}:`, error);
    } finally {
      client.release();
    }
  }

  /**
   * Get tenant provisioning status
   */
  async getTenantStatus(tenantId: string): Promise<any> {
    const client = await this.pool.connect();
    
    try {
      const result = await client.queryObject(`
        SELECT * FROM tenants WHERE tenant_id = $1
      `, [tenantId]);

      return result.rows[0] || null;
    } finally {
      client.release();
    }
  }

  /**
   * Cleanup resources
   */
  async close(): Promise<void> {
    await this.pool.end();
  }
}
