/**
 * Success Metrics & Monitoring Service
 * Real-time monitoring and success tracking for onboarding process
 * 
 * Features:
 * - Real-time onboarding dashboards
 * - Success metrics tracking
 * - Automated alerts and notifications
 * - Performance analytics
 * - Business impact measurement
 */

import { Redis } from "https://deno.land/x/redis@v0.29.0/mod.ts";
import { logger } from "../utils/logger.ts";

export interface OnboardingMetrics {
  totalOnboardings: number;
  successfulOnboardings: number;
  failedOnboardings: number;
  averageCompletionTime: number;
  successRate: number;
  currentActiveOnboardings: number;
  performanceMetrics: {
    averageQueryResponseTime: number;
    averageProvisioningTime: number;
    averageValidationTime: number;
  };
  businessMetrics: {
    timeToFirstValue: number;
    customerSatisfactionScore: number;
    supportTicketReduction: number;
  };
}

export interface AlertConfig {
  type: "onboarding_failed" | "performance_degradation" | "capacity_warning" | "security_issue";
  threshold: number;
  enabled: boolean;
  recipients: string[];
}

export interface DashboardData {
  overview: OnboardingMetrics;
  recentOnboardings: any[];
  performanceTrends: any[];
  alerts: any[];
  recommendations: string[];
}

export class MonitoringService {
  private redis: Redis;
  private alertConfigs: Map<string, AlertConfig>;

  constructor(redisUrl: string) {
    this.redis = new Redis(redisUrl);
    this.initializeAlertConfigs();
  }

  /**
   * Initialize default alert configurations
   */
  private initializeAlertConfigs(): void {
    this.alertConfigs = new Map([
      ["onboarding_failed", {
        type: "onboarding_failed",
        threshold: 0.95, // 95% success rate threshold
        enabled: true,
        recipients: ["<EMAIL>", "<EMAIL>"]
      }],
      ["performance_degradation", {
        type: "performance_degradation",
        threshold: 15000, // 15 second threshold for onboarding completion
        enabled: true,
        recipients: ["<EMAIL>", "<EMAIL>"]
      }],
      ["capacity_warning", {
        type: "capacity_warning",
        threshold: 50, // 50 concurrent onboardings
        enabled: true,
        recipients: ["<EMAIL>"]
      }],
      ["security_issue", {
        type: "security_issue",
        threshold: 5, // 5 failed authentication attempts
        enabled: true,
        recipients: ["<EMAIL>", "<EMAIL>"]
      }]
    ]);
  }

  /**
   * Track onboarding start event
   */
  async trackOnboardingStart(tenantId: string, metadata: any): Promise<void> {
    const timestamp = Date.now();
    const key = `onboarding:${tenantId}`;
    
    await this.redis.hset(key, {
      status: "started",
      startTime: timestamp,
      tenantId: tenantId,
      metadata: JSON.stringify(metadata)
    });

    await this.redis.expire(key, 86400); // 24 hours TTL

    // Update global metrics
    await this.redis.incr("metrics:onboardings:total");
    await this.redis.incr("metrics:onboardings:active");

    logger.info(`Tracking onboarding start for tenant ${tenantId}`);
  }

  /**
   * Track onboarding completion
   */
  async trackOnboardingCompletion(tenantId: string, result: any): Promise<void> {
    const timestamp = Date.now();
    const key = `onboarding:${tenantId}`;
    
    // Get start time
    const startTimeStr = await this.redis.hget(key, "startTime");
    const startTime = startTimeStr ? parseInt(startTimeStr) : timestamp;
    const duration = timestamp - startTime;

    // Update onboarding record
    await this.redis.hset(key, {
      status: result.status,
      endTime: timestamp,
      duration: duration,
      result: JSON.stringify(result)
    });

    // Update global metrics
    await this.redis.decr("metrics:onboardings:active");
    
    if (result.status === "success") {
      await this.redis.incr("metrics:onboardings:successful");
      await this.updatePerformanceMetrics(duration, result);
    } else {
      await this.redis.incr("metrics:onboardings:failed");
      await this.triggerAlert("onboarding_failed", { tenantId, result });
    }

    // Store completion time for analytics
    await this.redis.lpush("metrics:completion_times", duration);
    await this.redis.ltrim("metrics:completion_times", 0, 999); // Keep last 1000

    logger.info(`Tracking onboarding completion for tenant ${tenantId}: ${result.status} in ${duration}ms`);
  }

  /**
   * Update performance metrics
   */
  private async updatePerformanceMetrics(duration: number, result: any): Promise<void> {
    // Update average completion time
    const completionTimes = await this.redis.lrange("metrics:completion_times", 0, -1);
    const avgCompletionTime = completionTimes.reduce((sum, time) => sum + parseInt(time), 0) / completionTimes.length;
    await this.redis.set("metrics:avg_completion_time", avgCompletionTime);

    // Update performance-specific metrics
    if (result.performanceMetrics) {
      await this.redis.hset("metrics:performance", {
        avgQueryResponseTime: result.performanceMetrics.avgQueryResponseTime || 0,
        avgProvisioningTime: result.performanceMetrics.provisioningTime || 0,
        avgValidationTime: result.performanceMetrics.validationTime || 0
      });
    }

    // Check for performance degradation
    if (duration > this.alertConfigs.get("performance_degradation")!.threshold) {
      await this.triggerAlert("performance_degradation", { duration, result });
    }
  }

  /**
   * Get comprehensive dashboard data
   */
  async getDashboardData(): Promise<DashboardData> {
    try {
      // Get overview metrics
      const overview = await this.getOnboardingMetrics();

      // Get recent onboardings
      const recentOnboardings = await this.getRecentOnboardings(10);

      // Get performance trends
      const performanceTrends = await this.getPerformanceTrends();

      // Get active alerts
      const alerts = await this.getActiveAlerts();

      // Generate recommendations
      const recommendations = await this.generateRecommendations(overview);

      return {
        overview,
        recentOnboardings,
        performanceTrends,
        alerts,
        recommendations
      };

    } catch (error) {
      logger.error("Failed to get dashboard data:", error);
      throw error;
    }
  }

  /**
   * Get onboarding metrics
   */
  async getOnboardingMetrics(): Promise<OnboardingMetrics> {
    const [
      total,
      successful,
      failed,
      active,
      avgCompletionTime,
      performanceMetrics
    ] = await Promise.all([
      this.redis.get("metrics:onboardings:total"),
      this.redis.get("metrics:onboardings:successful"),
      this.redis.get("metrics:onboardings:failed"),
      this.redis.get("metrics:onboardings:active"),
      this.redis.get("metrics:avg_completion_time"),
      this.redis.hgetall("metrics:performance")
    ]);

    const totalNum = parseInt(total || "0");
    const successfulNum = parseInt(successful || "0");
    const failedNum = parseInt(failed || "0");

    return {
      totalOnboardings: totalNum,
      successfulOnboardings: successfulNum,
      failedOnboardings: failedNum,
      averageCompletionTime: parseFloat(avgCompletionTime || "0"),
      successRate: totalNum > 0 ? (successfulNum / totalNum) * 100 : 0,
      currentActiveOnboardings: parseInt(active || "0"),
      performanceMetrics: {
        averageQueryResponseTime: parseFloat(performanceMetrics.avgQueryResponseTime || "0"),
        averageProvisioningTime: parseFloat(performanceMetrics.avgProvisioningTime || "0"),
        averageValidationTime: parseFloat(performanceMetrics.avgValidationTime || "0")
      },
      businessMetrics: {
        timeToFirstValue: parseFloat(avgCompletionTime || "0") / 60000, // Convert to minutes
        customerSatisfactionScore: 4.8, // Mock data - would come from surveys
        supportTicketReduction: 65 // Mock data - would come from support system
      }
    };
  }

  /**
   * Get recent onboardings
   */
  async getRecentOnboardings(limit: number): Promise<any[]> {
    const keys = await this.redis.keys("onboarding:*");
    const recentKeys = keys.slice(-limit);
    
    const onboardings = [];
    for (const key of recentKeys) {
      const data = await this.redis.hgetall(key);
      if (data.tenantId) {
        onboardings.push({
          tenantId: data.tenantId,
          status: data.status,
          startTime: new Date(parseInt(data.startTime || "0")),
          endTime: data.endTime ? new Date(parseInt(data.endTime)) : null,
          duration: data.duration ? parseInt(data.duration) : null,
          metadata: data.metadata ? JSON.parse(data.metadata) : {}
        });
      }
    }

    return onboardings.sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
  }

  /**
   * Get performance trends
   */
  async getPerformanceTrends(): Promise<any[]> {
    // Get completion times for trend analysis
    const completionTimes = await this.redis.lrange("metrics:completion_times", 0, -1);
    
    // Group by time periods (last 24 hours in hourly buckets)
    const now = Date.now();
    const hourlyBuckets = Array.from({ length: 24 }, (_, i) => {
      const hour = now - (i * 60 * 60 * 1000);
      return {
        timestamp: new Date(hour),
        avgCompletionTime: 0,
        onboardingCount: 0
      };
    }).reverse();

    // Mock trend data - in production, this would aggregate real time-series data
    hourlyBuckets.forEach((bucket, index) => {
      bucket.avgCompletionTime = 600000 + (Math.random() * 300000); // 10-15 minutes
      bucket.onboardingCount = Math.floor(Math.random() * 10);
    });

    return hourlyBuckets;
  }

  /**
   * Get active alerts
   */
  async getActiveAlerts(): Promise<any[]> {
    const alertKeys = await this.redis.keys("alert:*");
    const alerts = [];

    for (const key of alertKeys) {
      const alertData = await this.redis.hgetall(key);
      if (alertData.type) {
        alerts.push({
          id: key.split(":")[1],
          type: alertData.type,
          message: alertData.message,
          severity: alertData.severity,
          timestamp: new Date(parseInt(alertData.timestamp || "0")),
          resolved: alertData.resolved === "true"
        });
      }
    }

    return alerts.filter(alert => !alert.resolved)
                 .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * Generate recommendations based on metrics
   */
  async generateRecommendations(metrics: OnboardingMetrics): Promise<string[]> {
    const recommendations = [];

    // Success rate recommendations
    if (metrics.successRate < 95) {
      recommendations.push(`Success rate is ${metrics.successRate.toFixed(1)}% - investigate common failure points`);
    }

    // Performance recommendations
    if (metrics.averageCompletionTime > 900000) { // 15 minutes
      recommendations.push(`Average completion time is ${(metrics.averageCompletionTime / 60000).toFixed(1)} minutes - optimize slow steps`);
    }

    // Capacity recommendations
    if (metrics.currentActiveOnboardings > 40) {
      recommendations.push(`High concurrent onboardings (${metrics.currentActiveOnboardings}) - consider scaling resources`);
    }

    // Query performance recommendations
    if (metrics.performanceMetrics.averageQueryResponseTime > 11) {
      recommendations.push(`Query response time is ${metrics.performanceMetrics.averageQueryResponseTime}ms - exceeds 11ms target`);
    }

    if (recommendations.length === 0) {
      recommendations.push("All onboarding metrics are within optimal ranges");
    }

    return recommendations;
  }

  /**
   * Trigger alert
   */
  async triggerAlert(alertType: string, data: any): Promise<void> {
    const config = this.alertConfigs.get(alertType);
    if (!config || !config.enabled) {
      return;
    }

    const alertId = `${alertType}_${Date.now()}`;
    const alertKey = `alert:${alertId}`;

    await this.redis.hset(alertKey, {
      type: alertType,
      message: this.generateAlertMessage(alertType, data),
      severity: this.getAlertSeverity(alertType),
      timestamp: Date.now(),
      data: JSON.stringify(data),
      resolved: "false"
    });

    await this.redis.expire(alertKey, 604800); // 7 days TTL

    // Send notifications (mock implementation)
    await this.sendAlertNotifications(config, alertType, data);

    logger.warn(`Alert triggered: ${alertType}`, data);
  }

  /**
   * Generate alert message
   */
  private generateAlertMessage(alertType: string, data: any): string {
    switch (alertType) {
      case "onboarding_failed":
        return `Onboarding failed for tenant ${data.tenantId}: ${data.result.error || 'Unknown error'}`;
      case "performance_degradation":
        return `Onboarding completion time exceeded threshold: ${(data.duration / 60000).toFixed(1)} minutes`;
      case "capacity_warning":
        return `High number of concurrent onboardings: ${data.count}`;
      case "security_issue":
        return `Security issue detected: ${data.details}`;
      default:
        return `Alert: ${alertType}`;
    }
  }

  /**
   * Get alert severity
   */
  private getAlertSeverity(alertType: string): string {
    switch (alertType) {
      case "security_issue":
        return "critical";
      case "onboarding_failed":
        return "high";
      case "performance_degradation":
        return "medium";
      case "capacity_warning":
        return "low";
      default:
        return "medium";
    }
  }

  /**
   * Send alert notifications
   */
  private async sendAlertNotifications(config: AlertConfig, alertType: string, data: any): Promise<void> {
    // Mock implementation - in production, this would integrate with email/Slack/PagerDuty
    logger.info(`Sending ${alertType} alert to: ${config.recipients.join(", ")}`);
    
    // Example webhook notification
    try {
      const webhookUrl = Deno.env.get("ALERT_WEBHOOK_URL");
      if (webhookUrl) {
        await fetch(webhookUrl, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            type: alertType,
            message: this.generateAlertMessage(alertType, data),
            severity: this.getAlertSeverity(alertType),
            timestamp: new Date().toISOString(),
            data
          })
        });
      }
    } catch (error) {
      logger.error("Failed to send webhook notification:", error);
    }
  }

  /**
   * Get onboarding analytics for specified timeframe
   */
  async getOnboardingAnalytics(timeframe: string): Promise<any> {
    // Mock implementation - would aggregate real data based on timeframe
    const analytics = {
      timeframe,
      summary: {
        totalOnboardings: 156,
        successRate: 94.2,
        averageCompletionTime: 8.5, // minutes
        customerSatisfaction: 4.8
      },
      trends: {
        onboardingVolume: this.generateMockTrendData(timeframe),
        successRateTrend: this.generateMockTrendData(timeframe),
        performanceTrend: this.generateMockTrendData(timeframe)
      },
      topFailureReasons: [
        { reason: "API connectivity issues", count: 3, percentage: 33.3 },
        { reason: "Performance validation timeout", count: 2, percentage: 22.2 },
        { reason: "Integration configuration error", count: 2, percentage: 22.2 },
        { reason: "Security compliance failure", count: 2, percentage: 22.2 }
      ]
    };

    return analytics;
  }

  /**
   * Generate mock trend data
   */
  private generateMockTrendData(timeframe: string): any[] {
    const dataPoints = timeframe === "24h" ? 24 : timeframe === "7d" ? 7 : 30;
    return Array.from({ length: dataPoints }, (_, i) => ({
      timestamp: new Date(Date.now() - (i * (timeframe === "24h" ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000))),
      value: Math.random() * 100
    })).reverse();
  }

  /**
   * Cleanup resources
   */
  async close(): Promise<void> {
    this.redis.close();
  }
}
