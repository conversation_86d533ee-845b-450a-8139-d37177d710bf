/**
 * API Key Management & Security Service
 * Secure API key generation, rotation, and management
 * 
 * Features:
 * - Cryptographically secure key generation
 * - Automatic key rotation
 * - Rate limiting and usage tracking
 * - JWT token management
 * - Audit logging
 */

import { Pool } from "https://deno.land/x/postgres@v0.17.0/mod.ts";
import { Redis } from "https://deno.land/x/redis@v0.29.0/mod.ts";
import { create, verify, getNumericDate } from "https://deno.land/x/djwt@v2.8/mod.ts";
import { crypto } from "https://deno.land/std@0.208.0/crypto/mod.ts";
import { encode } from "https://deno.land/std@0.208.0/encoding/base64.ts";
import { logger } from "../utils/logger.ts";

export interface APIKeyConfig {
  tenantId: string;
  keyType: "production" | "development" | "test";
  permissions: string[];
  rateLimit: {
    requestsPerMinute: number;
    requestsPerHour: number;
    requestsPerDay: number;
  };
  expiresAt?: Date;
}

export interface APIKey {
  keyId: string;
  tenantId: string;
  keyHash: string;
  keyType: string;
  permissions: string[];
  rateLimit: any;
  isActive: boolean;
  createdAt: Date;
  expiresAt?: Date;
  lastUsedAt?: Date;
  usageCount: number;
}

export interface JWTPayload {
  tenantId: string;
  keyId: string;
  permissions: string[];
  iat: number;
  exp: number;
}

export class APIKeyService {
  private pool: Pool;
  private redis: Redis;
  private jwtSecret: CryptoKey;

  constructor(databaseUrl: string, redisUrl: string) {
    this.pool = new Pool(databaseUrl, 10, true);
    this.redis = new Redis(redisUrl);
    this.initializeJWTSecret();
  }

  /**
   * Initialize JWT secret key
   */
  private async initializeJWTSecret(): Promise<void> {
    const secret = Deno.env.get("JWT_SECRET") || "your-super-secret-jwt-key-change-in-production";
    this.jwtSecret = await crypto.subtle.importKey(
      "raw",
      new TextEncoder().encode(secret),
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign", "verify"]
    );
  }

  /**
   * Generate secure API key for tenant
   * Target: <30 seconds generation time
   */
  async generateAPIKey(config: APIKeyConfig): Promise<{ apiKey: string; keyId: string }> {
    const startTime = Date.now();
    
    try {
      // Generate cryptographically secure API key
      const keyBytes = new Uint8Array(32);
      crypto.getRandomValues(keyBytes);
      const apiKey = `eak_${config.keyType}_${encode(keyBytes).replace(/[+/=]/g, '')}`;
      
      // Generate key ID
      const keyIdBytes = new Uint8Array(16);
      crypto.getRandomValues(keyIdBytes);
      const keyId = encode(keyIdBytes).replace(/[+/=]/g, '');

      // Hash the API key for storage
      const keyHash = await this.hashAPIKey(apiKey);

      // Store in database
      await this.storeAPIKey({
        keyId,
        tenantId: config.tenantId,
        keyHash,
        keyType: config.keyType,
        permissions: config.permissions,
        rateLimit: config.rateLimit,
        isActive: true,
        createdAt: new Date(),
        expiresAt: config.expiresAt,
        usageCount: 0
      });

      // Cache key metadata in Redis for fast lookups
      await this.cacheKeyMetadata(keyId, {
        tenantId: config.tenantId,
        permissions: config.permissions,
        rateLimit: config.rateLimit,
        isActive: true
      });

      const duration = Date.now() - startTime;
      logger.info(`Generated API key for tenant ${config.tenantId} in ${duration}ms`);

      // Validate generation time meets target
      if (duration > 30000) { // 30 seconds
        logger.warn(`API key generation exceeded target time: ${duration}ms > 30000ms`);
      }

      return { apiKey, keyId };

    } catch (error) {
      logger.error(`Failed to generate API key for tenant ${config.tenantId}:`, error);
      throw error;
    }
  }

  /**
   * Validate API key and return tenant information
   */
  async validateAPIKey(apiKey: string): Promise<{ isValid: boolean; tenantId?: string; permissions?: string[] }> {
    try {
      // Extract key ID from API key format
      const keyParts = apiKey.split('_');
      if (keyParts.length < 3) {
        return { isValid: false };
      }

      // Hash the provided key
      const keyHash = await this.hashAPIKey(apiKey);

      // Check cache first for performance
      const cachedKey = await this.redis.get(`api_key:${keyHash}`);
      if (cachedKey) {
        const keyData = JSON.parse(cachedKey);
        if (keyData.isActive) {
          await this.updateKeyUsage(keyData.keyId);
          return {
            isValid: true,
            tenantId: keyData.tenantId,
            permissions: keyData.permissions
          };
        }
      }

      // Fallback to database lookup
      const client = await this.pool.connect();
      try {
        const result = await client.queryObject(`
          SELECT tenant_id, permissions, is_active, expires_at
          FROM api_keys
          WHERE key_hash = $1 AND is_active = true
        `, [keyHash]);

        if (result.rows.length === 0) {
          return { isValid: false };
        }

        const keyData = result.rows[0] as any;
        
        // Check expiration
        if (keyData.expires_at && new Date() > keyData.expires_at) {
          return { isValid: false };
        }

        return {
          isValid: true,
          tenantId: keyData.tenant_id,
          permissions: keyData.permissions
        };

      } finally {
        client.release();
      }

    } catch (error) {
      logger.error("Failed to validate API key:", error);
      return { isValid: false };
    }
  }

  /**
   * Generate JWT token for authenticated requests
   */
  async generateJWTToken(tenantId: string, keyId: string, permissions: string[]): Promise<string> {
    const payload: JWTPayload = {
      tenantId,
      keyId,
      permissions,
      iat: getNumericDate(new Date()),
      exp: getNumericDate(new Date(Date.now() + 24 * 60 * 60 * 1000)) // 24 hours
    };

    return await create({ alg: "HS256", typ: "JWT" }, payload, this.jwtSecret);
  }

  /**
   * Verify JWT token
   */
  async verifyJWTToken(token: string): Promise<JWTPayload | null> {
    try {
      const payload = await verify(token, this.jwtSecret);
      return payload as JWTPayload;
    } catch (error) {
      logger.error("Failed to verify JWT token:", error);
      return null;
    }
  }

  /**
   * Rotate API key for security
   */
  async rotateAPIKey(keyId: string): Promise<{ apiKey: string; keyId: string }> {
    const client = await this.pool.connect();
    
    try {
      // Get current key configuration
      const result = await client.queryObject(`
        SELECT tenant_id, key_type, permissions, rate_limit, expires_at
        FROM api_keys
        WHERE key_id = $1 AND is_active = true
      `, [keyId]);

      if (result.rows.length === 0) {
        throw new Error("API key not found");
      }

      const currentKey = result.rows[0] as any;

      // Deactivate current key
      await client.queryObject(`
        UPDATE api_keys 
        SET is_active = false, updated_at = NOW()
        WHERE key_id = $1
      `, [keyId]);

      // Generate new key with same configuration
      const newKey = await this.generateAPIKey({
        tenantId: currentKey.tenant_id,
        keyType: currentKey.key_type,
        permissions: currentKey.permissions,
        rateLimit: currentKey.rate_limit,
        expiresAt: currentKey.expires_at
      });

      logger.info(`Rotated API key for tenant ${currentKey.tenant_id}`);
      return newKey;

    } finally {
      client.release();
    }
  }

  /**
   * Check rate limits for API key
   */
  async checkRateLimit(keyId: string): Promise<{ allowed: boolean; remaining: number; resetTime: Date }> {
    try {
      const now = new Date();
      const minuteKey = `rate_limit:${keyId}:${now.getMinutes()}`;
      const hourKey = `rate_limit:${keyId}:${now.getHours()}`;
      const dayKey = `rate_limit:${keyId}:${now.getDate()}`;

      // Get current usage
      const [minuteCount, hourCount, dayCount] = await Promise.all([
        this.redis.get(minuteKey),
        this.redis.get(hourKey),
        this.redis.get(dayKey)
      ]);

      // Get rate limits from cache or database
      const keyMetadata = await this.getKeyMetadata(keyId);
      if (!keyMetadata) {
        return { allowed: false, remaining: 0, resetTime: now };
      }

      const limits = keyMetadata.rateLimit;
      const currentMinute = parseInt(minuteCount || "0");
      const currentHour = parseInt(hourCount || "0");
      const currentDay = parseInt(dayCount || "0");

      // Check limits
      if (currentMinute >= limits.requestsPerMinute ||
          currentHour >= limits.requestsPerHour ||
          currentDay >= limits.requestsPerDay) {
        return { 
          allowed: false, 
          remaining: 0, 
          resetTime: new Date(now.getTime() + 60000) // Next minute
        };
      }

      // Increment counters
      await Promise.all([
        this.redis.incr(minuteKey),
        this.redis.incr(hourKey),
        this.redis.incr(dayKey)
      ]);

      // Set expiration
      await Promise.all([
        this.redis.expire(minuteKey, 60),
        this.redis.expire(hourKey, 3600),
        this.redis.expire(dayKey, 86400)
      ]);

      return {
        allowed: true,
        remaining: Math.min(
          limits.requestsPerMinute - currentMinute - 1,
          limits.requestsPerHour - currentHour - 1,
          limits.requestsPerDay - currentDay - 1
        ),
        resetTime: new Date(now.getTime() + 60000)
      };

    } catch (error) {
      logger.error("Failed to check rate limit:", error);
      return { allowed: false, remaining: 0, resetTime: new Date() };
    }
  }

  /**
   * Hash API key for secure storage
   */
  private async hashAPIKey(apiKey: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(apiKey);
    const hashBuffer = await crypto.subtle.digest("SHA-256", data);
    return encode(new Uint8Array(hashBuffer));
  }

  /**
   * Store API key in database
   */
  private async storeAPIKey(keyData: APIKey): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      await client.queryObject(`
        INSERT INTO api_keys (
          key_id, tenant_id, key_hash, key_type, permissions,
          rate_limit, is_active, created_at, expires_at, usage_count
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `, [
        keyData.keyId,
        keyData.tenantId,
        keyData.keyHash,
        keyData.keyType,
        JSON.stringify(keyData.permissions),
        JSON.stringify(keyData.rateLimit),
        keyData.isActive,
        keyData.createdAt,
        keyData.expiresAt,
        keyData.usageCount
      ]);
    } finally {
      client.release();
    }
  }

  /**
   * Cache key metadata in Redis
   */
  private async cacheKeyMetadata(keyId: string, metadata: any): Promise<void> {
    await this.redis.setex(`key_metadata:${keyId}`, 3600, JSON.stringify(metadata));
  }

  /**
   * Get key metadata from cache or database
   */
  private async getKeyMetadata(keyId: string): Promise<any> {
    // Try cache first
    const cached = await this.redis.get(`key_metadata:${keyId}`);
    if (cached) {
      return JSON.parse(cached);
    }

    // Fallback to database
    const client = await this.pool.connect();
    try {
      const result = await client.queryObject(`
        SELECT tenant_id, permissions, rate_limit, is_active
        FROM api_keys
        WHERE key_id = $1
      `, [keyId]);

      if (result.rows.length > 0) {
        const metadata = result.rows[0];
        await this.cacheKeyMetadata(keyId, metadata);
        return metadata;
      }

      return null;
    } finally {
      client.release();
    }
  }

  /**
   * Update key usage statistics
   */
  private async updateKeyUsage(keyId: string): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      await client.queryObject(`
        UPDATE api_keys 
        SET usage_count = usage_count + 1, last_used_at = NOW()
        WHERE key_id = $1
      `, [keyId]);
    } finally {
      client.release();
    }
  }

  /**
   * Cleanup resources
   */
  async close(): Promise<void> {
    await this.pool.end();
    this.redis.close();
  }
}
