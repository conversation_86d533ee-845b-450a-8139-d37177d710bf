/**
 * Structured Logger for Onboarding Automation Service
 * Provides consistent logging with performance tracking and monitoring integration
 */

export interface LogContext {
  tenantId?: string;
  onboardingId?: string;
  step?: string;
  duration?: number;
  [key: string]: any;
}

export class Logger {
  private serviceName: string;
  private logLevel: string;

  constructor(serviceName = "onboarding-automation") {
    this.serviceName = serviceName;
    this.logLevel = Deno.env.get("LOG_LEVEL") || "info";
  }

  private formatMessage(level: string, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      service: this.serviceName,
      message,
      ...context
    };
    
    return JSON.stringify(logEntry);
  }

  private shouldLog(level: string): boolean {
    const levels = ["debug", "info", "warn", "error"];
    const currentLevelIndex = levels.indexOf(this.logLevel);
    const messageLevelIndex = levels.indexOf(level);
    
    return messageLevelIndex >= currentLevelIndex;
  }

  debug(message: string, context?: LogContext): void {
    if (this.shouldLog("debug")) {
      console.log(this.formatMessage("debug", message, context));
    }
  }

  info(message: string, context?: LogContext): void {
    if (this.shouldLog("info")) {
      console.log(this.formatMessage("info", message, context));
    }
  }

  warn(message: string, context?: LogContext): void {
    if (this.shouldLog("warn")) {
      console.warn(this.formatMessage("warn", message, context));
    }
  }

  error(message: string, error?: Error | any, context?: LogContext): void {
    if (this.shouldLog("error")) {
      const errorContext = {
        ...context,
        error: error instanceof Error ? {
          name: error.name,
          message: error.message,
          stack: error.stack
        } : error
      };
      console.error(this.formatMessage("error", message, errorContext));
    }
  }

  performance(operation: string, duration: number, context?: LogContext): void {
    this.info(`Performance: ${operation}`, {
      ...context,
      operation,
      duration,
      performanceMetric: true
    });
  }
}

export const logger = new Logger();
