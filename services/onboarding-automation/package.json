{"name": "@ecommerce-analytics/onboarding-automation", "version": "1.0.0", "description": "Automated 15-minute client onboarding service with performance validation", "type": "module", "scripts": {"start": "deno run --allow-all main.ts", "dev": "deno run --allow-all --watch main.ts", "test": "deno test --allow-all tests/", "test:integration": "deno run --allow-all tests/integration.test.ts", "lint": "deno lint", "fmt": "deno fmt", "check": "deno check main.ts", "build": "deno compile --allow-all --output onboarding-automation main.ts", "docker:build": "docker build -t onboarding-automation .", "docker:run": "docker run -p 3006:3006 onboarding-automation"}, "keywords": ["onboarding", "automation", "saas", "analytics", "performance", "deno", "timescaledb"], "author": "E-commerce Analytics Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ecommerce-analytics/onboarding-automation"}, "engines": {"deno": ">=1.40.0"}, "dependencies": {"oak": "^12.6.1", "postgres": "^0.17.0", "redis": "^0.29.0", "djwt": "^2.8.0", "cors": "^1.2.2"}, "devDependencies": {"std": "^0.208.0"}}