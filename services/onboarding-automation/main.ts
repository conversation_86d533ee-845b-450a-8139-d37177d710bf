/**
 * Client Onboarding Automation Service
 * Automated 15-minute client setup with performance validation
 * 
 * Performance Targets:
 * - Tenant Provisioning: <3 minutes
 * - API Key Generation: <30 seconds  
 * - Integration Validation: <5 minutes
 * - Total Onboarding: <15 minutes
 */

import { Application, Router } from "https://deno.land/x/oak@v12.6.1/mod.ts";
import { oakCors } from "https://deno.land/x/cors@v1.2.2/mod.ts";
import { OnboardingService } from "./services/OnboardingService.ts";
import { TenantProvisioningService } from "./services/TenantProvisioningService.ts";
import { APIKeyService } from "./services/APIKeyService.ts";
import { ValidationService } from "./services/ValidationService.ts";
import { MonitoringService } from "./services/MonitoringService.ts";
import { logger } from "./utils/logger.ts";

// Environment configuration
const PORT = parseInt(Deno.env.get("ONBOARDING_PORT") || "3006");
const DATABASE_URL = Deno.env.get("DATABASE_URL") || "postgresql://postgres:password@localhost:5432/ecommerce_analytics";
const REDIS_URL = Deno.env.get("REDIS_URL") || "redis://localhost:6379";

// Initialize services
const tenantService = new TenantProvisioningService(DATABASE_URL);
const apiKeyService = new APIKeyService(DATABASE_URL, REDIS_URL);
const validationService = new ValidationService();
const monitoringService = new MonitoringService(REDIS_URL);
const onboardingService = new OnboardingService({
  tenantService,
  apiKeyService,
  validationService,
  monitoringService
});

// Initialize Oak application
const app = new Application();
const router = new Router();

// Middleware
app.use(oakCors({
  origin: "*",
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "X-Tenant-ID"]
}));

app.use(async (ctx, next) => {
  const start = Date.now();
  await next();
  const ms = Date.now() - start;
  logger.info(`${ctx.request.method} ${ctx.request.url.pathname} - ${ms}ms`);
});

// Health check endpoint
router.get("/health", (ctx) => {
  ctx.response.body = {
    status: "healthy",
    service: "onboarding-automation",
    timestamp: new Date().toISOString(),
    version: "1.0.0"
  };
});

// Onboarding endpoints
router.post("/api/onboarding/start", async (ctx) => {
  try {
    const body = await ctx.request.body().value;
    const onboardingRequest = {
      companyName: body.companyName,
      email: body.email,
      plan: body.plan || "professional",
      industry: body.industry,
      expectedVolume: body.expectedVolume || 10000,
      integrations: body.integrations || []
    };

    const result = await onboardingService.startOnboarding(onboardingRequest);
    
    ctx.response.status = 201;
    ctx.response.body = {
      success: true,
      data: result,
      message: "Onboarding started successfully"
    };
  } catch (error) {
    logger.error("Failed to start onboarding:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: error.message
    };
  }
});

router.get("/api/onboarding/status/:id", async (ctx) => {
  try {
    const onboardingId = ctx.params.id;
    const status = await onboardingService.getOnboardingStatus(onboardingId);
    
    ctx.response.body = {
      success: true,
      data: status
    };
  } catch (error) {
    logger.error("Failed to get onboarding status:", error);
    ctx.response.status = 404;
    ctx.response.body = {
      success: false,
      error: error.message
    };
  }
});

router.post("/api/onboarding/validate/:id", async (ctx) => {
  try {
    const onboardingId = ctx.params.id;
    const body = await ctx.request.body().value;
    
    const validationResult = await onboardingService.validateIntegration(
      onboardingId,
      body.integrationData
    );
    
    ctx.response.body = {
      success: true,
      data: validationResult
    };
  } catch (error) {
    logger.error("Failed to validate integration:", error);
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: error.message
    };
  }
});

router.get("/api/onboarding/metrics/:id", async (ctx) => {
  try {
    const onboardingId = ctx.params.id;
    const metrics = await onboardingService.getPerformanceMetrics(onboardingId);
    
    ctx.response.body = {
      success: true,
      data: metrics
    };
  } catch (error) {
    logger.error("Failed to get performance metrics:", error);
    ctx.response.status = 404;
    ctx.response.body = {
      success: false,
      error: error.message
    };
  }
});

// Monitoring endpoints
router.get("/api/onboarding/dashboard", async (ctx) => {
  try {
    const dashboard = await monitoringService.getDashboardData();
    
    ctx.response.body = {
      success: true,
      data: dashboard
    };
  } catch (error) {
    logger.error("Failed to get dashboard data:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: error.message
    };
  }
});

router.get("/api/onboarding/analytics", async (ctx) => {
  try {
    const timeframe = ctx.request.url.searchParams.get("timeframe") || "24h";
    const analytics = await monitoringService.getOnboardingAnalytics(timeframe);
    
    ctx.response.body = {
      success: true,
      data: analytics
    };
  } catch (error) {
    logger.error("Failed to get onboarding analytics:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: error.message
    };
  }
});

// Webhook endpoints for external integrations
router.post("/api/webhooks/onboarding", async (ctx) => {
  try {
    const body = await ctx.request.body().value;
    await onboardingService.handleWebhook(body);
    
    ctx.response.status = 200;
    ctx.response.body = { success: true };
  } catch (error) {
    logger.error("Failed to handle webhook:", error);
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: error.message
    };
  }
});

// Error handling middleware
app.use(async (ctx, next) => {
  try {
    await next();
  } catch (error) {
    logger.error("Unhandled error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Internal server error"
    };
  }
});

// Register routes
app.use(router.routes());
app.use(router.allowedMethods());

// Start server
console.log(`🚀 Onboarding Automation Service starting on port ${PORT}`);
console.log(`📊 Performance Targets:`);
console.log(`   - Tenant Provisioning: <3 minutes`);
console.log(`   - API Key Generation: <30 seconds`);
console.log(`   - Integration Validation: <5 minutes`);
console.log(`   - Total Onboarding: <15 minutes`);

await app.listen({ port: PORT });
