# Onboarding Automation Service - Deployment Guide
## Production-Ready Deployment for 15-Minute Client Onboarding

This guide provides step-by-step instructions for deploying the **Client Onboarding Automation Service** that delivers on our **15-minute setup promise** with **validated performance benchmarks**.

## 🎯 **Deployment Overview**

### **Service Architecture**
```
┌─────────────────────────────────────────────────────────────┐
│                 Onboarding Automation Service               │
├─────────────────────────────────────────────────────────────┤
│  Port 3006  │  Deno 2 + Oak  │  Performance Targets        │
│             │                │  • <15 min total            │
│             │                │  • <3 min provisioning      │
│             │                │  • <30 sec API keys         │
│             │                │  • <5 min validation        │
└─────────────────────────────────────────────────────────────┘
```

### **Dependencies**
- **Analytics Service** (Port 3002): Performance validation
- **Admin Service** (Port 3005): Tenant management  
- **PostgreSQL + TimescaleDB**: Tenant data storage
- **Redis**: Caching and session management

## 🚀 **Quick Deployment**

### **1. Environment Setup**
```bash
# Clone and navigate to service
cd services/onboarding-automation

# Set environment variables
export ONBOARDING_PORT=3006
export DATABASE_URL="postgresql://postgres:password@localhost:5432/ecommerce_analytics"
export REDIS_URL="redis://localhost:6379"
export JWT_SECRET="your-super-secret-jwt-key-change-in-production"
export ANALYTICS_SERVICE_URL="http://localhost:3002"
export ADMIN_SERVICE_URL="http://localhost:3005"
export LOG_LEVEL="info"
```

### **2. Database Preparation**
```sql
-- Create required tables for onboarding automation
CREATE TABLE IF NOT EXISTS tenants (
  tenant_id TEXT PRIMARY KEY,
  company_name TEXT NOT NULL,
  email TEXT NOT NULL,
  plan TEXT NOT NULL,
  industry TEXT,
  expected_volume INTEGER,
  features JSONB,
  status TEXT DEFAULT 'active',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS api_keys (
  key_id TEXT PRIMARY KEY,
  tenant_id TEXT NOT NULL REFERENCES tenants(tenant_id),
  key_hash TEXT NOT NULL UNIQUE,
  key_type TEXT NOT NULL,
  permissions JSONB NOT NULL,
  rate_limit JSONB NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ,
  last_used_at TIMESTAMPTZ,
  usage_count INTEGER DEFAULT 0
);

-- Create indexes for performance
CREATE INDEX idx_tenants_status ON tenants(status);
CREATE INDEX idx_api_keys_tenant_id ON api_keys(tenant_id);
CREATE INDEX idx_api_keys_active ON api_keys(is_active) WHERE is_active = true;
```

### **3. Start Service**
```bash
# Development mode with auto-reload
deno run --allow-all --watch main.ts

# Production mode
deno run --allow-all main.ts
```

## 🔧 **Production Deployment**

### **Docker Deployment**
```dockerfile
# Dockerfile
FROM denoland/deno:1.40.0

WORKDIR /app

# Copy dependency files
COPY deps.ts .
RUN deno cache deps.ts

# Copy application code
COPY . .
RUN deno cache main.ts

# Expose port
EXPOSE 3006

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3006/health || exit 1

# Start service
CMD ["run", "--allow-all", "main.ts"]
```

```bash
# Build and run Docker container
docker build -t onboarding-automation .
docker run -p 3006:3006 \
  -e DATABASE_URL="postgresql://postgres:<EMAIL>:5432/ecommerce_analytics" \
  -e REDIS_URL="redis://host.docker.internal:6379" \
  onboarding-automation
```

### **Kubernetes Deployment**
```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: onboarding-automation
  labels:
    app: onboarding-automation
spec:
  replicas: 3
  selector:
    matchLabels:
      app: onboarding-automation
  template:
    metadata:
      labels:
        app: onboarding-automation
    spec:
      containers:
      - name: onboarding-automation
        image: onboarding-automation:latest
        ports:
        - containerPort: 3006
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3006
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3006
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: onboarding-automation-service
spec:
  selector:
    app: onboarding-automation
  ports:
    - protocol: TCP
      port: 3006
      targetPort: 3006
  type: ClusterIP
```

## 📊 **Monitoring Setup**

### **Health Checks**
```bash
# Service health
curl http://localhost:3006/health

# Expected response:
{
  "status": "healthy",
  "service": "onboarding-automation",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "version": "1.0.0"
}
```

### **Performance Monitoring**
```bash
# Dashboard metrics
curl http://localhost:3006/api/onboarding/dashboard

# Analytics data
curl http://localhost:3006/api/onboarding/analytics?timeframe=24h
```

### **Prometheus Metrics** (Optional)
```yaml
# Add to main.ts for Prometheus integration
import { register, Counter, Histogram } from "https://deno.land/x/prometheus@v0.1.0/mod.ts";

const onboardingCounter = new Counter({
  name: "onboarding_total",
  help: "Total number of onboarding attempts",
  labelNames: ["status"]
});

const onboardingDuration = new Histogram({
  name: "onboarding_duration_seconds",
  help: "Onboarding completion time in seconds",
  buckets: [60, 300, 600, 900, 1200] // 1min, 5min, 10min, 15min, 20min
});
```

## 🔒 **Security Configuration**

### **Environment Variables**
```bash
# Production security settings
export JWT_SECRET="$(openssl rand -base64 32)"
export API_RATE_LIMIT_ENABLED=true
export CORS_ORIGIN="https://dashboard.ecommerce-analytics.com"
export ENABLE_REQUEST_LOGGING=true
export MAX_CONCURRENT_ONBOARDINGS=50
```

### **Database Security**
```sql
-- Create dedicated onboarding user
CREATE USER onboarding_service WITH PASSWORD 'secure_password_here';

-- Grant minimal required permissions
GRANT SELECT, INSERT, UPDATE ON tenants TO onboarding_service;
GRANT SELECT, INSERT, UPDATE ON api_keys TO onboarding_service;
GRANT USAGE ON SCHEMA public TO onboarding_service;
```

## 🎯 **Performance Optimization**

### **Database Optimization**
```sql
-- Optimize for onboarding queries
CREATE INDEX CONCURRENTLY idx_tenants_created_at ON tenants(created_at DESC);
CREATE INDEX CONCURRENTLY idx_api_keys_created_at ON api_keys(created_at DESC);

-- Enable query plan caching
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET pg_stat_statements.track = 'all';
```

### **Redis Configuration**
```bash
# Redis optimization for onboarding cache
redis-cli CONFIG SET maxmemory 1gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru
redis-cli CONFIG SET save "900 1 300 10 60 10000"
```

## 📋 **Deployment Checklist**

### **Pre-Deployment**
- [ ] Database tables created and indexed
- [ ] Redis instance configured and accessible
- [ ] Environment variables set securely
- [ ] Dependencies (Analytics/Admin services) running
- [ ] SSL certificates configured (production)

### **Post-Deployment**
- [ ] Health check endpoint responding
- [ ] Test onboarding workflow end-to-end
- [ ] Verify performance targets (<15 minutes)
- [ ] Monitor logs for errors
- [ ] Set up alerting for failures
- [ ] Configure backup procedures

### **Performance Validation**
- [ ] Tenant provisioning <3 minutes
- [ ] API key generation <30 seconds
- [ ] Integration validation <5 minutes
- [ ] Total onboarding <15 minutes
- [ ] Query response times <11ms

## 🚨 **Troubleshooting**

### **Common Issues**

**Service won't start:**
```bash
# Check dependencies
deno run --allow-all --check main.ts

# Verify database connection
psql $DATABASE_URL -c "SELECT 1;"

# Test Redis connection
redis-cli -u $REDIS_URL ping
```

**Slow onboarding performance:**
```bash
# Check database performance
EXPLAIN ANALYZE SELECT * FROM tenants WHERE status = 'provisioning';

# Monitor Redis latency
redis-cli --latency -i 1

# Review service logs
tail -f /var/log/onboarding-automation.log
```

**High error rates:**
```bash
# Check service metrics
curl http://localhost:3006/api/onboarding/analytics

# Review failed onboardings
curl http://localhost:3006/api/onboarding/dashboard | jq '.alerts'
```

## 📞 **Support**

For deployment issues or questions:
- **Documentation**: [Full API Documentation](./API_DOCUMENTATION.md)
- **Monitoring**: [Monitoring Guide](./MONITORING_GUIDE.md)
- **Performance**: [Performance Tuning](./PERFORMANCE_GUIDE.md)

---

**Built with Deno 2 + Oak + TimescaleDB for exceptional performance and reliability**
