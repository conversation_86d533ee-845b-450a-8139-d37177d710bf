import { defineRoute } from "$fresh/server.ts";
import { Head } from "$fresh/runtime.ts";

// Interactive Demo Environment for Sales Presentations
// Showcases real-time analytics, AI-powered optimization, and revenue impact

interface DemoData {
  realtimeMetrics: {
    eventsPerSecond: number;
    queryResponseTime: number;
    activeUsers: number;
    revenueToday: number;
  };
  revenueOptimization: {
    dynamicPricing: {
      currentPrice: number;
      optimizedPrice: number;
      revenueImpact: number;
      confidence: number;
    };
    churnPrediction: {
      atRiskCustomers: number;
      accuracy: number;
      interventionSuccess: number;
    };
  };
  performanceComparison: {
    ourPlatform: {
      eventsPerSec: number;
      queryTime: number;
      accuracy: number;
    };
    competitors: Array<{
      name: string;
      eventsPerSec: number;
      queryTime: number;
      accuracy: number;
    }>;
  };
}

export default defineRoute(async (req, ctx) => {
  // Generate realistic demo data
  const demoData: DemoData = {
    realtimeMetrics: {
      eventsPerSecond: 24390 + Math.floor(Math.random() * 1000),
      queryResponseTime: 8 + Math.random() * 3,
      activeUsers: 15420 + Math.floor(Math.random() * 500),
      revenueToday: 125000 + Math.floor(Math.random() * 25000),
    },
    revenueOptimization: {
      dynamicPricing: {
        currentPrice: 99.99,
        optimizedPrice: 114.99,
        revenueImpact: 15.2,
        confidence: 87.3,
      },
      churnPrediction: {
        atRiskCustomers: 23,
        accuracy: 87.3,
        interventionSuccess: 73.2,
      },
    },
    performanceComparison: {
      ourPlatform: {
        eventsPerSec: 24390,
        queryTime: 11,
        accuracy: 87.3,
      },
      competitors: [
        { name: "Google Analytics", eventsPerSec: 2500, queryTime: 500, accuracy: 0 },
        { name: "Adobe Analytics", eventsPerSec: 1800, queryTime: 800, accuracy: 0 },
        { name: "Klaviyo", eventsPerSec: 3200, queryTime: 300, accuracy: 65 },
        { name: "Segment", eventsPerSec: 2100, queryTime: 600, accuracy: 0 },
      ],
    },
  };

  return (
    <html lang="en">
      <Head>
        <title>Revenue Optimization Platform - Interactive Demo</title>
        <meta name="description" content="Experience the power of real-time revenue optimization with AI-powered analytics" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://d3js.org/d3.v7.min.js"></script>
      </Head>
      <body class="bg-gray-50 dark:bg-gray-900">
        {/* Demo Header */}
        <header class="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-3xl font-bold">Revenue Optimization Platform</h1>
                <p class="text-blue-100 mt-2">Interactive Demo - Real-time Analytics & AI-Powered Optimization</p>
              </div>
              <div class="flex items-center space-x-4">
                <div class="bg-green-500 px-3 py-1 rounded-full text-sm font-medium">
                  🟢 LIVE DEMO
                </div>
                <div class="text-right">
                  <div class="text-sm text-blue-100">Performance Advantage</div>
                  <div class="text-xl font-bold">97-98% Faster</div>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Real-time Metrics Dashboard */}
        <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Real-time Performance Metrics
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Events per Second */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Events/Second</p>
                    <p class="text-3xl font-bold text-green-600" id="eventsPerSec">
                      {demoData.realtimeMetrics.eventsPerSecond.toLocaleString()}
                    </p>
                  </div>
                  <div class="text-green-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                  </div>
                </div>
                <div class="mt-2 text-sm text-green-600">
                  ↗ 975% faster than competitors
                </div>
              </div>

              {/* Query Response Time */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Query Response</p>
                    <p class="text-3xl font-bold text-blue-600" id="queryTime">
                      {demoData.realtimeMetrics.queryResponseTime.toFixed(1)}ms
                    </p>
                  </div>
                  <div class="text-blue-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                </div>
                <div class="mt-2 text-sm text-blue-600">
                  ↗ 4,545% faster than average
                </div>
              </div>

              {/* Active Users */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Users</p>
                    <p class="text-3xl font-bold text-purple-600" id="activeUsers">
                      {demoData.realtimeMetrics.activeUsers.toLocaleString()}
                    </p>
                  </div>
                  <div class="text-purple-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                  </div>
                </div>
                <div class="mt-2 text-sm text-purple-600">
                  ↗ Real-time tracking
                </div>
              </div>

              {/* Revenue Today */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Revenue Today</p>
                    <p class="text-3xl font-bold text-orange-600" id="revenueToday">
                      ${demoData.realtimeMetrics.revenueToday.toLocaleString()}
                    </p>
                  </div>
                  <div class="text-orange-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                  </div>
                </div>
                <div class="mt-2 text-sm text-orange-600">
                  ↗ +18.5% vs yesterday
                </div>
              </div>
            </div>
          </div>

          {/* AI-Powered Revenue Optimization */}
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              AI-Powered Revenue Optimization
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Dynamic Pricing */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Dynamic Pricing Optimization
                </h3>
                <div class="space-y-4">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-400">Current Price</span>
                    <span class="text-xl font-bold text-gray-900 dark:text-white">
                      ${demoData.revenueOptimization.dynamicPricing.currentPrice}
                    </span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-400">AI Optimized Price</span>
                    <span class="text-xl font-bold text-green-600">
                      ${demoData.revenueOptimization.dynamicPricing.optimizedPrice}
                    </span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-400">Revenue Impact</span>
                    <span class="text-xl font-bold text-green-600">
                      +{demoData.revenueOptimization.dynamicPricing.revenueImpact}%
                    </span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-400">Confidence</span>
                    <span class="text-xl font-bold text-blue-600">
                      {demoData.revenueOptimization.dynamicPricing.confidence}%
                    </span>
                  </div>
                  <button class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition-colors">
                    Apply Optimized Pricing
                  </button>
                </div>
              </div>

              {/* Churn Prediction */}
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Predictive Churn Prevention
                </h3>
                <div class="space-y-4">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-400">At-Risk Customers</span>
                    <span class="text-xl font-bold text-red-600">
                      {demoData.revenueOptimization.churnPrediction.atRiskCustomers}
                    </span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-400">Prediction Accuracy</span>
                    <span class="text-xl font-bold text-green-600">
                      {demoData.revenueOptimization.churnPrediction.accuracy}%
                    </span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-400">Intervention Success</span>
                    <span class="text-xl font-bold text-blue-600">
                      {demoData.revenueOptimization.churnPrediction.interventionSuccess}%
                    </span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-400">Revenue Saved</span>
                    <span class="text-xl font-bold text-green-600">
                      $47,250
                    </span>
                  </div>
                  <button class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors">
                    Launch Retention Campaign
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Performance Comparison */}
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Performance vs Competitors
            </h2>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Platform
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Events/Second
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Query Time
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        AI Accuracy
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Advantage
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <tr class="bg-green-50 dark:bg-green-900">
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-green-900 dark:text-green-100">
                        Our Platform ⭐
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-green-900 dark:text-green-100">
                        {demoData.performanceComparison.ourPlatform.eventsPerSec.toLocaleString()}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-green-900 dark:text-green-100">
                        {demoData.performanceComparison.ourPlatform.queryTime}ms
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-green-900 dark:text-green-100">
                        {demoData.performanceComparison.ourPlatform.accuracy}%
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-green-600">
                        Market Leader
                      </td>
                    </tr>
                    {demoData.performanceComparison.competitors.map((competitor, index) => (
                      <tr key={index}>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {competitor.name}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {competitor.eventsPerSec.toLocaleString()}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {competitor.queryTime}ms
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {competitor.accuracy > 0 ? `${competitor.accuracy}%` : 'N/A'}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                          {Math.round((demoData.performanceComparison.ourPlatform.eventsPerSec / competitor.eventsPerSec - 1) * 100)}% slower
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Demo Actions */}
          <div class="text-center">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Experience the Difference
            </h2>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                Schedule Executive Demo
              </button>
              <button class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                Start Free Trial
              </button>
              <button class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                Calculate Your ROI
              </button>
            </div>
          </div>
        </section>

        {/* Real-time Updates Script */}
        <script dangerouslySetInnerHTML={{
          __html: `
            // Simulate real-time updates
            function updateMetrics() {
              const eventsElement = document.getElementById('eventsPerSec');
              const queryElement = document.getElementById('queryTime');
              const usersElement = document.getElementById('activeUsers');
              const revenueElement = document.getElementById('revenueToday');
              
              if (eventsElement) {
                const baseEvents = 24390;
                const newEvents = baseEvents + Math.floor(Math.random() * 1000);
                eventsElement.textContent = newEvents.toLocaleString();
              }
              
              if (queryElement) {
                const newQueryTime = (8 + Math.random() * 3).toFixed(1);
                queryElement.textContent = newQueryTime + 'ms';
              }
              
              if (usersElement) {
                const baseUsers = 15420;
                const newUsers = baseUsers + Math.floor(Math.random() * 500);
                usersElement.textContent = newUsers.toLocaleString();
              }
              
              if (revenueElement) {
                const baseRevenue = 125000;
                const newRevenue = baseRevenue + Math.floor(Math.random() * 25000);
                revenueElement.textContent = '$' + newRevenue.toLocaleString();
              }
            }
            
            // Update metrics every 2 seconds
            setInterval(updateMetrics, 2000);
            
            // Add click handlers for demo buttons
            document.addEventListener('DOMContentLoaded', function() {
              const buttons = document.querySelectorAll('button');
              buttons.forEach(button => {
                button.addEventListener('click', function() {
                  if (this.textContent.includes('Schedule')) {
                    alert('Demo scheduled! Our sales team will contact you within 24 hours.');
                  } else if (this.textContent.includes('Trial')) {
                    alert('Free trial activated! Check your email for access credentials.');
                  } else if (this.textContent.includes('ROI')) {
                    window.open('/sales/tools/roi-calculator.html', '_blank');
                  } else if (this.textContent.includes('Apply')) {
                    alert('Pricing optimization applied! Revenue impact: +15.2%');
                  } else if (this.textContent.includes('Launch')) {
                    alert('Retention campaign launched! 23 at-risk customers targeted.');
                  }
                });
              });
            });
          `
        }} />
      </body>
    </html>
  );
});
