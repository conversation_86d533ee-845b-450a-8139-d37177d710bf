import { query, queryOne, transaction } from "../utils/database.ts";
import { set, get, getJSON } from "../utils/redis.ts";
import { logger } from "../utils/logger.ts";

// Customer Success Analytics Service for Revenue Optimization
// Implements churn prediction, health scoring, and expansion opportunity identification

export interface CustomerSuccessOptions {
  tenantId: string;
  customerId?: string;
  segment?: string;
  timeRange?: string;
  riskThreshold?: number;
}

export interface CustomerHealthScore {
  customerId: string;
  healthScore: number; // 0-100
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  churnProbability: number; // 0-1
  expansionProbability: number; // 0-1
  lastActivity: string;
  engagementTrend: 'increasing' | 'stable' | 'decreasing';
  usageMetrics: {
    featureAdoption: number;
    apiUsage: number;
    loginFrequency: number;
    supportTickets: number;
  };
  revenueMetrics: {
    currentMrr: number;
    lifetimeValue: number;
    paymentHistory: 'excellent' | 'good' | 'concerning' | 'poor';
  };
  predictiveInsights: {
    likelyChurnDate?: string;
    expansionOpportunity?: string;
    recommendedActions: string[];
  };
}

export interface ChurnPrediction {
  customerId: string;
  churnProbability: number;
  churnRisk: 'low' | 'medium' | 'high' | 'critical';
  predictedChurnDate: string;
  confidenceScore: number;
  keyRiskFactors: string[];
  preventionRecommendations: string[];
  revenueAtRisk: number;
  interventionPriority: number; // 1-10
}

export interface ExpansionOpportunity {
  customerId: string;
  opportunityType: 'upgrade' | 'add_on' | 'usage_increase' | 'feature_unlock';
  expansionProbability: number;
  potentialRevenue: number;
  recommendedPlan?: string;
  recommendedFeatures: string[];
  timeToExpansion: number; // days
  confidenceScore: number;
  triggerEvents: string[];
  nextBestAction: string;
}

export interface CustomerSegmentAnalysis {
  segment: string;
  customerCount: number;
  averageHealthScore: number;
  churnRate: number;
  expansionRate: number;
  averageLifetimeValue: number;
  revenueContribution: number;
  keyCharacteristics: string[];
  recommendedStrategies: string[];
}

export class CustomerSuccessAnalyticsService {
  private cachePrefix = "customer_success";
  private cacheTTL = 300; // 5 minutes

  /**
   * Calculate customer health scores
   */
  async getCustomerHealthScores(options: CustomerSuccessOptions): Promise<CustomerHealthScore[]> {
    const cacheKey = `${this.cachePrefix}:health:${options.tenantId}:${options.customerId || 'all'}`;
    
    try {
      // Try cache first
      const cached = await getJSON(cacheKey);
      if (cached) {
        logger.info("Customer health scores cache hit", { tenantId: options.tenantId });
        return cached as CustomerHealthScore[];
      }

      const startTime = performance.now();

      // Get comprehensive customer data for health scoring
      const healthDataQuery = `
        WITH customer_usage AS (
          SELECT 
            c.id as customer_id,
            c.tenant_id,
            c.created_at,
            c.last_activity,
            c.status,
            s.plan_id,
            s.amount as current_mrr,
            cu.feature_adoption_score,
            cu.api_calls_30d,
            cu.login_count_30d,
            cu.support_tickets_30d,
            EXTRACT(EPOCH FROM (NOW() - c.last_activity))/86400 as days_since_activity,
            CASE 
              WHEN c.last_activity >= NOW() - INTERVAL '7 days' THEN 'active'
              WHEN c.last_activity >= NOW() - INTERVAL '30 days' THEN 'moderate'
              ELSE 'inactive'
            END as activity_level
          FROM customers c
          LEFT JOIN subscriptions s ON c.id = s.customer_id AND s.status = 'active'
          LEFT JOIN customer_usage cu ON c.id = cu.customer_id
          WHERE c.tenant_id = $1
            ${options.customerId ? 'AND c.id = $2' : ''}
            AND c.status != 'deleted'
        ),
        engagement_trends AS (
          SELECT 
            customer_id,
            AVG(CASE WHEN event_date >= NOW() - INTERVAL '7 days' THEN engagement_score END) as recent_engagement,
            AVG(CASE WHEN event_date >= NOW() - INTERVAL '30 days' AND event_date < NOW() - INTERVAL '7 days' THEN engagement_score END) as previous_engagement
          FROM customer_engagement_daily
          WHERE tenant_id = $1
            ${options.customerId ? 'AND customer_id = $2' : ''}
          GROUP BY customer_id
        ),
        payment_history AS (
          SELECT 
            customer_id,
            COUNT(*) as total_payments,
            COUNT(*) FILTER (WHERE status = 'succeeded') as successful_payments,
            COUNT(*) FILTER (WHERE status = 'failed') as failed_payments,
            AVG(amount) as avg_payment_amount
          FROM payment_history
          WHERE tenant_id = $1
            ${options.customerId ? 'AND customer_id = $2' : ''}
            AND created_at >= NOW() - INTERVAL '12 months'
          GROUP BY customer_id
        )
        SELECT 
          cu.*,
          et.recent_engagement,
          et.previous_engagement,
          CASE 
            WHEN et.recent_engagement > et.previous_engagement THEN 'increasing'
            WHEN et.recent_engagement = et.previous_engagement THEN 'stable'
            ELSE 'decreasing'
          END as engagement_trend,
          ph.successful_payments,
          ph.failed_payments,
          ph.total_payments,
          CASE 
            WHEN ph.failed_payments = 0 THEN 'excellent'
            WHEN ph.failed_payments::float / ph.total_payments < 0.1 THEN 'good'
            WHEN ph.failed_payments::float / ph.total_payments < 0.3 THEN 'concerning'
            ELSE 'poor'
          END as payment_history_rating
        FROM customer_usage cu
        LEFT JOIN engagement_trends et ON cu.customer_id = et.customer_id
        LEFT JOIN payment_history ph ON cu.customer_id = ph.customer_id
      `;

      const params = options.customerId ? [options.tenantId, options.customerId] : [options.tenantId];
      const healthData = await query(healthDataQuery, params, options.tenantId);

      const healthScores: CustomerHealthScore[] = [];

      for (const customer of healthData.rows) {
        const healthScore = await this.calculateHealthScore(customer);
        const churnProbability = await this.predictChurnProbability(customer);
        const expansionProbability = await this.predictExpansionProbability(customer);
        
        healthScores.push({
          customerId: customer.customer_id,
          healthScore: healthScore.score,
          riskLevel: healthScore.riskLevel,
          churnProbability: churnProbability,
          expansionProbability: expansionProbability,
          lastActivity: customer.last_activity,
          engagementTrend: customer.engagement_trend || 'stable',
          usageMetrics: {
            featureAdoption: customer.feature_adoption_score || 0,
            apiUsage: customer.api_calls_30d || 0,
            loginFrequency: customer.login_count_30d || 0,
            supportTickets: customer.support_tickets_30d || 0,
          },
          revenueMetrics: {
            currentMrr: customer.current_mrr || 0,
            lifetimeValue: await this.calculateCustomerLTV(customer.customer_id, options.tenantId),
            paymentHistory: customer.payment_history_rating || 'good',
          },
          predictiveInsights: {
            likelyChurnDate: churnProbability > 0.7 ? this.predictChurnDate(customer) : undefined,
            expansionOpportunity: expansionProbability > 0.6 ? await this.identifyExpansionOpportunity(customer) : undefined,
            recommendedActions: await this.generateRecommendedActions(customer, healthScore.score, churnProbability),
          },
        });
      }

      const queryTime = performance.now() - startTime;

      // Cache the results
      await set(cacheKey, JSON.stringify(healthScores), this.cacheTTL);

      logger.info("Customer health scores calculated", {
        tenantId: options.tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        customersAnalyzed: healthScores.length,
        averageHealthScore: healthScores.reduce((sum, c) => sum + c.healthScore, 0) / healthScores.length,
      });

      return healthScores;
    } catch (error) {
      logger.error("Failed to calculate customer health scores", {
        tenantId: options.tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Generate churn predictions
   */
  async getChurnPredictions(options: CustomerSuccessOptions): Promise<ChurnPrediction[]> {
    const cacheKey = `${this.cachePrefix}:churn:${options.tenantId}:${options.riskThreshold || 0.5}`;
    
    try {
      // Try cache first
      const cached = await getJSON(cacheKey);
      if (cached) {
        return cached as ChurnPrediction[];
      }

      const startTime = performance.now();

      // Get customers with churn risk factors
      const churnRiskQuery = `
        WITH churn_features AS (
          SELECT 
            c.id as customer_id,
            c.tenant_id,
            EXTRACT(EPOCH FROM (NOW() - c.last_activity))/86400 as days_inactive,
            cu.feature_adoption_score,
            cu.api_calls_30d,
            cu.login_count_30d,
            cu.support_tickets_30d,
            s.amount as mrr,
            CASE WHEN s.canceled_at IS NOT NULL THEN 1 ELSE 0 END as cancellation_intent,
            ph.failed_payments_ratio,
            ce.engagement_decline_rate
          FROM customers c
          LEFT JOIN subscriptions s ON c.id = s.customer_id
          LEFT JOIN customer_usage cu ON c.id = cu.customer_id
          LEFT JOIN (
            SELECT 
              customer_id,
              failed_payments::float / NULLIF(total_payments, 0) as failed_payments_ratio
            FROM (
              SELECT 
                customer_id,
                COUNT(*) as total_payments,
                COUNT(*) FILTER (WHERE status = 'failed') as failed_payments
              FROM payment_history
              WHERE created_at >= NOW() - INTERVAL '6 months'
              GROUP BY customer_id
            ) payment_stats
          ) ph ON c.id = ph.customer_id
          LEFT JOIN (
            SELECT 
              customer_id,
              (first_value(engagement_score) OVER (PARTITION BY customer_id ORDER BY event_date DESC) - 
               first_value(engagement_score) OVER (PARTITION BY customer_id ORDER BY event_date ASC)) / 
               NULLIF(first_value(engagement_score) OVER (PARTITION BY customer_id ORDER BY event_date ASC), 0) as engagement_decline_rate
            FROM customer_engagement_daily
            WHERE event_date >= NOW() - INTERVAL '90 days'
          ) ce ON c.id = ce.customer_id
          WHERE c.tenant_id = $1
            AND c.status = 'active'
        )
        SELECT *
        FROM churn_features
        WHERE (days_inactive > 7 OR 
               feature_adoption_score < 0.3 OR 
               api_calls_30d < 100 OR
               failed_payments_ratio > 0.1 OR
               engagement_decline_rate < -0.2)
      `;

      const churnRiskData = await query(churnRiskQuery, [options.tenantId], options.tenantId);

      const churnPredictions: ChurnPrediction[] = [];

      for (const customer of churnRiskData.rows) {
        const prediction = await this.generateChurnPrediction(customer);
        if (prediction.churnProbability >= (options.riskThreshold || 0.5)) {
          churnPredictions.push(prediction);
        }
      }

      // Sort by intervention priority
      churnPredictions.sort((a, b) => b.interventionPriority - a.interventionPriority);

      const queryTime = performance.now() - startTime;

      // Cache the results
      await set(cacheKey, JSON.stringify(churnPredictions), this.cacheTTL);

      logger.info("Churn predictions generated", {
        tenantId: options.tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        predictionsCount: churnPredictions.length,
        highRiskCustomers: churnPredictions.filter(p => p.churnRisk === 'high' || p.churnRisk === 'critical').length,
      });

      return churnPredictions;
    } catch (error) {
      logger.error("Failed to generate churn predictions", {
        tenantId: options.tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Identify expansion opportunities
   */
  async getExpansionOpportunities(options: CustomerSuccessOptions): Promise<ExpansionOpportunity[]> {
    const cacheKey = `${this.cachePrefix}:expansion:${options.tenantId}`;
    
    try {
      // Try cache first
      const cached = await getJSON(cacheKey);
      if (cached) {
        return cached as ExpansionOpportunity[];
      }

      const startTime = performance.now();

      // Get customers with expansion potential
      const expansionQuery = `
        WITH expansion_candidates AS (
          SELECT 
            c.id as customer_id,
            c.tenant_id,
            s.plan_id as current_plan,
            s.amount as current_mrr,
            cu.feature_adoption_score,
            cu.api_calls_30d,
            cu.usage_growth_rate,
            cu.feature_limit_approaching,
            ce.engagement_score,
            CASE 
              WHEN cu.api_calls_30d > sp.api_limit * 0.8 THEN 'usage_limit'
              WHEN cu.feature_adoption_score > 0.8 THEN 'feature_adoption'
              WHEN ce.engagement_score > 0.7 THEN 'high_engagement'
              ELSE 'other'
            END as expansion_trigger
          FROM customers c
          JOIN subscriptions s ON c.id = s.customer_id AND s.status = 'active'
          JOIN subscription_plans sp ON s.plan_id = sp.id
          LEFT JOIN customer_usage cu ON c.id = cu.customer_id
          LEFT JOIN (
            SELECT 
              customer_id,
              AVG(engagement_score) as engagement_score
            FROM customer_engagement_daily
            WHERE event_date >= NOW() - INTERVAL '30 days'
            GROUP BY customer_id
          ) ce ON c.id = ce.customer_id
          WHERE c.tenant_id = $1
            AND c.status = 'active'
            AND (cu.usage_growth_rate > 0.1 OR 
                 cu.feature_adoption_score > 0.6 OR
                 cu.api_calls_30d > sp.api_limit * 0.7)
        )
        SELECT *
        FROM expansion_candidates
        ORDER BY 
          CASE expansion_trigger
            WHEN 'usage_limit' THEN 1
            WHEN 'feature_adoption' THEN 2
            WHEN 'high_engagement' THEN 3
            ELSE 4
          END,
          current_mrr DESC
      `;

      const expansionData = await query(expansionQuery, [options.tenantId], options.tenantId);

      const expansionOpportunities: ExpansionOpportunity[] = [];

      for (const customer of expansionData.rows) {
        const opportunity = await this.generateExpansionOpportunity(customer);
        expansionOpportunities.push(opportunity);
      }

      const queryTime = performance.now() - startTime;

      // Cache the results
      await set(cacheKey, JSON.stringify(expansionOpportunities), this.cacheTTL);

      logger.info("Expansion opportunities identified", {
        tenantId: options.tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        opportunitiesCount: expansionOpportunities.length,
        totalPotentialRevenue: expansionOpportunities.reduce((sum, o) => sum + o.potentialRevenue, 0),
      });

      return expansionOpportunities;
    } catch (error) {
      logger.error("Failed to identify expansion opportunities", {
        tenantId: options.tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  // Helper methods
  private async calculateHealthScore(customer: any): Promise<{score: number, riskLevel: 'low' | 'medium' | 'high' | 'critical'}> {
    // Multi-factor health score calculation
    let score = 100;

    // Activity factor (30% weight)
    const daysSinceActivity = customer.days_since_activity || 0;
    if (daysSinceActivity > 30) score -= 30;
    else if (daysSinceActivity > 14) score -= 20;
    else if (daysSinceActivity > 7) score -= 10;

    // Engagement factor (25% weight)
    const engagementScore = customer.recent_engagement || 0;
    score += (engagementScore - 0.5) * 25;

    // Usage factor (25% weight)
    const featureAdoption = customer.feature_adoption_score || 0;
    score += (featureAdoption - 0.5) * 25;

    // Payment factor (20% weight)
    if (customer.payment_history_rating === 'poor') score -= 20;
    else if (customer.payment_history_rating === 'concerning') score -= 10;

    // Normalize score
    score = Math.max(0, Math.min(100, score));

    const riskLevel = score >= 80 ? 'low' : score >= 60 ? 'medium' : score >= 40 ? 'high' : 'critical';

    return { score, riskLevel };
  }

  private async predictChurnProbability(customer: any): Promise<number> {
    // Simplified churn probability calculation
    // In production, this would use a trained ML model
    let probability = 0;

    if (customer.days_since_activity > 30) probability += 0.4;
    if (customer.feature_adoption_score < 0.3) probability += 0.3;
    if (customer.payment_history_rating === 'poor') probability += 0.2;
    if (customer.engagement_trend === 'decreasing') probability += 0.1;

    return Math.min(1, probability);
  }

  private async predictExpansionProbability(customer: any): Promise<number> {
    // Simplified expansion probability calculation
    let probability = 0;

    if (customer.feature_adoption_score > 0.8) probability += 0.3;
    if (customer.api_calls_30d > 1000) probability += 0.2;
    if (customer.engagement_trend === 'increasing') probability += 0.2;
    if (customer.payment_history_rating === 'excellent') probability += 0.1;

    return Math.min(1, probability);
  }

  private async calculateCustomerLTV(customerId: string, tenantId: string): Promise<number> {
    // Calculate customer lifetime value
    const ltvQuery = `
      SELECT COALESCE(SUM(amount), 0) as total_revenue
      FROM payment_history
      WHERE customer_id = $1 AND tenant_id = $2 AND status = 'succeeded'
    `;
    
    const result = await queryOne(ltvQuery, [customerId, tenantId], tenantId);
    return parseFloat(result?.total_revenue || 0);
  }

  private predictChurnDate(customer: any): string {
    // Predict likely churn date based on current trends
    const daysToChurn = Math.max(7, 30 - (customer.days_since_activity || 0));
    const churnDate = new Date(Date.now() + (daysToChurn * 24 * 60 * 60 * 1000));
    return churnDate.toISOString();
  }

  private async identifyExpansionOpportunity(customer: any): Promise<string> {
    // Identify specific expansion opportunity
    if (customer.api_calls_30d > 1000) return "API usage upgrade";
    if (customer.feature_adoption_score > 0.8) return "Premium features unlock";
    return "Plan upgrade";
  }

  private async generateRecommendedActions(customer: any, healthScore: number, churnProbability: number): Promise<string[]> {
    const actions: string[] = [];

    if (churnProbability > 0.7) {
      actions.push("Immediate intervention required");
      actions.push("Schedule customer success call");
    }
    if (healthScore < 50) {
      actions.push("Provide onboarding assistance");
      actions.push("Offer training resources");
    }
    if (customer.support_tickets_30d > 5) {
      actions.push("Review support ticket patterns");
    }

    return actions;
  }

  private async generateChurnPrediction(customer: any): Promise<ChurnPrediction> {
    const churnProbability = await this.predictChurnProbability(customer);
    const riskLevel = churnProbability >= 0.8 ? 'critical' : churnProbability >= 0.6 ? 'high' : churnProbability >= 0.4 ? 'medium' : 'low';

    return {
      customerId: customer.customer_id,
      churnProbability,
      churnRisk: riskLevel,
      predictedChurnDate: this.predictChurnDate(customer),
      confidenceScore: 0.85, // Would be calculated based on model performance
      keyRiskFactors: this.identifyRiskFactors(customer),
      preventionRecommendations: await this.generatePreventionRecommendations(customer),
      revenueAtRisk: customer.mrr * 12, // Annual revenue at risk
      interventionPriority: Math.round(churnProbability * customer.mrr * 10),
    };
  }

  private identifyRiskFactors(customer: any): string[] {
    const factors: string[] = [];
    
    if (customer.days_inactive > 14) factors.push("Extended inactivity");
    if (customer.feature_adoption_score < 0.3) factors.push("Low feature adoption");
    if (customer.failed_payments_ratio > 0.1) factors.push("Payment issues");
    if (customer.support_tickets_30d > 3) factors.push("High support volume");

    return factors;
  }

  private async generatePreventionRecommendations(customer: any): Promise<string[]> {
    const recommendations: string[] = [];

    if (customer.days_inactive > 14) {
      recommendations.push("Re-engagement campaign");
      recommendations.push("Product usage training");
    }
    if (customer.feature_adoption_score < 0.3) {
      recommendations.push("Feature adoption workshop");
      recommendations.push("Personalized onboarding");
    }

    return recommendations;
  }

  private async generateExpansionOpportunity(customer: any): Promise<ExpansionOpportunity> {
    const expansionProbability = await this.predictExpansionProbability(customer);
    
    return {
      customerId: customer.customer_id,
      opportunityType: this.determineOpportunityType(customer),
      expansionProbability,
      potentialRevenue: customer.current_mrr * 0.5, // Estimated 50% increase
      recommendedPlan: await this.recommendUpgradePlan(customer),
      recommendedFeatures: this.recommendFeatures(customer),
      timeToExpansion: Math.round(30 / expansionProbability), // Days
      confidenceScore: 0.8,
      triggerEvents: this.identifyTriggerEvents(customer),
      nextBestAction: this.determineNextBestAction(customer),
    };
  }

  private determineOpportunityType(customer: any): 'upgrade' | 'add_on' | 'usage_increase' | 'feature_unlock' {
    if (customer.expansion_trigger === 'usage_limit') return 'usage_increase';
    if (customer.expansion_trigger === 'feature_adoption') return 'feature_unlock';
    return 'upgrade';
  }

  private async recommendUpgradePlan(customer: any): Promise<string> {
    // Logic to recommend next tier plan
    const planHierarchy = ['core', 'professional', 'enterprise', 'custom'];
    const currentIndex = planHierarchy.indexOf(customer.current_plan);
    return planHierarchy[currentIndex + 1] || 'custom';
  }

  private recommendFeatures(customer: any): string[] {
    const features: string[] = [];
    
    if (customer.api_calls_30d > 1000) features.push("Higher API limits");
    if (customer.feature_adoption_score > 0.7) features.push("Advanced analytics");
    
    return features;
  }

  private identifyTriggerEvents(customer: any): string[] {
    return ["High API usage", "Feature limit reached", "Engagement increase"];
  }

  private determineNextBestAction(customer: any): string {
    if (customer.expansion_trigger === 'usage_limit') return "Proactive upgrade outreach";
    return "Feature demonstration";
  }
}
