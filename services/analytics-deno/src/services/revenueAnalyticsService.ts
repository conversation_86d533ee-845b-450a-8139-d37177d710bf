import { query, queryOne, transaction } from "../utils/database.ts";
import { set, get, getJSON } from "../utils/redis.ts";
import { logger } from "../utils/logger.ts";

// Revenue Analytics Service for Revenue Optimization & Growth Analytics
// Implements advanced revenue analytics, pricing optimization, and growth forecasting

export interface RevenueAnalyticsOptions {
  tenantId: string;
  timeRange?: string; // '7d', '30d', '90d', '1y'
  dateFrom?: string;
  dateTo?: string;
  granularity?: 'hour' | 'day' | 'week' | 'month';
  currency?: string;
}

export interface PricingOptimizationOptions {
  tenantId: string;
  planId?: string;
  customerSegment?: string;
  usagePattern?: string;
  competitorData?: boolean;
}

export interface RevenueMetrics {
  totalRevenue: number;
  recurringRevenue: number;
  oneTimeRevenue: number;
  averageRevenuePerUser: number;
  monthlyRecurringRevenue: number;
  annualRecurringRevenue: number;
  revenueGrowthRate: number;
  customerLifetimeValue: number;
  revenuePerCustomer: number;
  conversionRate: number;
  churnRate: number;
  expansionRevenue: number;
  contractionRevenue: number;
  netRevenueRetention: number;
}

export interface PricingRecommendation {
  planId: string;
  currentPrice: number;
  recommendedPrice: number;
  priceChangePercentage: number;
  expectedRevenueImpact: number;
  confidenceScore: number;
  reasoning: string;
  implementationRisk: 'low' | 'medium' | 'high';
  customerSegmentImpact: Record<string, number>;
}

export interface RevenueForecasting {
  period: string;
  forecastedRevenue: number;
  confidenceInterval: {
    lower: number;
    upper: number;
  };
  growthRate: number;
  seasonalityFactor: number;
  trendComponent: number;
  forecastAccuracy: number;
  keyDrivers: string[];
}

export class RevenueAnalyticsService {
  private cachePrefix = "revenue_analytics";
  private cacheTTL = 300; // 5 minutes

  /**
   * Get comprehensive revenue metrics
   */
  async getRevenueMetrics(options: RevenueAnalyticsOptions): Promise<RevenueMetrics> {
    const cacheKey = `${this.cachePrefix}:metrics:${options.tenantId}:${options.timeRange || '30d'}`;
    
    try {
      // Try cache first
      const cached = await getJSON(cacheKey);
      if (cached) {
        logger.info("Revenue metrics cache hit", { tenantId: options.tenantId });
        return cached as RevenueMetrics;
      }

      const startTime = performance.now();
      
      // Calculate date range
      const { dateFrom, dateTo } = this.calculateDateRange(options);

      // Get revenue metrics using TimescaleDB continuous aggregates
      const metricsQuery = `
        WITH revenue_base AS (
          SELECT 
            tenant_id,
            SUM(total_revenue) as total_revenue,
            SUM(CASE WHEN billing_type = 'recurring' THEN total_revenue ELSE 0 END) as recurring_revenue,
            SUM(CASE WHEN billing_type = 'one_time' THEN total_revenue ELSE 0 END) as one_time_revenue,
            COUNT(DISTINCT customer_id) as unique_customers,
            AVG(total_revenue) as avg_revenue_per_transaction
          FROM revenue_events 
          WHERE tenant_id = $1 
            AND event_timestamp >= $2 
            AND event_timestamp <= $3
          GROUP BY tenant_id
        ),
        subscription_metrics AS (
          SELECT 
            tenant_id,
            SUM(CASE WHEN billing_cycle = 'monthly' THEN amount ELSE amount/12 END) as mrr,
            SUM(amount) as arr,
            AVG(amount) as avg_subscription_value,
            COUNT(*) as active_subscriptions
          FROM subscriptions s
          JOIN subscription_plans sp ON s.plan_id = sp.id
          WHERE s.tenant_id = $1 
            AND s.status = 'active'
            AND s.created_at <= $3
          GROUP BY tenant_id
        ),
        customer_metrics AS (
          SELECT 
            tenant_id,
            AVG(lifetime_value) as avg_clv,
            AVG(EXTRACT(EPOCH FROM (last_activity - first_activity))/86400) as avg_customer_lifespan,
            COUNT(*) FILTER (WHERE status = 'churned' AND updated_at >= $2) as churned_customers,
            COUNT(*) as total_customers
          FROM customers 
          WHERE tenant_id = $1
          GROUP BY tenant_id
        )
        SELECT 
          COALESCE(rb.total_revenue, 0) as total_revenue,
          COALESCE(rb.recurring_revenue, 0) as recurring_revenue,
          COALESCE(rb.one_time_revenue, 0) as one_time_revenue,
          COALESCE(rb.total_revenue / NULLIF(rb.unique_customers, 0), 0) as average_revenue_per_user,
          COALESCE(sm.mrr, 0) as monthly_recurring_revenue,
          COALESCE(sm.arr, 0) as annual_recurring_revenue,
          COALESCE(cm.avg_clv, 0) as customer_lifetime_value,
          COALESCE(cm.churned_customers::float / NULLIF(cm.total_customers, 0) * 100, 0) as churn_rate
        FROM revenue_base rb
        FULL OUTER JOIN subscription_metrics sm ON rb.tenant_id = sm.tenant_id
        FULL OUTER JOIN customer_metrics cm ON rb.tenant_id = cm.tenant_id
      `;

      const metricsResult = await queryOne(
        metricsQuery,
        [options.tenantId, dateFrom, dateTo],
        options.tenantId
      );

      // Calculate additional metrics
      const revenueGrowthRate = await this.calculateRevenueGrowthRate(options);
      const netRevenueRetention = await this.calculateNetRevenueRetention(options);
      const expansionMetrics = await this.calculateExpansionMetrics(options);

      const metrics: RevenueMetrics = {
        totalRevenue: parseFloat(metricsResult?.total_revenue || 0),
        recurringRevenue: parseFloat(metricsResult?.recurring_revenue || 0),
        oneTimeRevenue: parseFloat(metricsResult?.one_time_revenue || 0),
        averageRevenuePerUser: parseFloat(metricsResult?.average_revenue_per_user || 0),
        monthlyRecurringRevenue: parseFloat(metricsResult?.monthly_recurring_revenue || 0),
        annualRecurringRevenue: parseFloat(metricsResult?.annual_recurring_revenue || 0),
        revenueGrowthRate: revenueGrowthRate,
        customerLifetimeValue: parseFloat(metricsResult?.customer_lifetime_value || 0),
        revenuePerCustomer: parseFloat(metricsResult?.average_revenue_per_user || 0),
        conversionRate: await this.calculateConversionRate(options),
        churnRate: parseFloat(metricsResult?.churn_rate || 0),
        expansionRevenue: expansionMetrics.expansion,
        contractionRevenue: expansionMetrics.contraction,
        netRevenueRetention: netRevenueRetention,
      };

      const queryTime = performance.now() - startTime;
      
      // Cache the results
      await set(cacheKey, JSON.stringify(metrics), this.cacheTTL);

      logger.info("Revenue metrics calculated", {
        tenantId: options.tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        totalRevenue: metrics.totalRevenue,
        mrr: metrics.monthlyRecurringRevenue,
      });

      return metrics;
    } catch (error) {
      logger.error("Failed to get revenue metrics", {
        tenantId: options.tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Generate pricing optimization recommendations
   */
  async getPricingRecommendations(options: PricingOptimizationOptions): Promise<PricingRecommendation[]> {
    const cacheKey = `${this.cachePrefix}:pricing:${options.tenantId}:${options.planId || 'all'}`;
    
    try {
      // Try cache first
      const cached = await getJSON(cacheKey);
      if (cached) {
        return cached as PricingRecommendation[];
      }

      const startTime = performance.now();

      // Get current pricing and usage data
      const pricingAnalysisQuery = `
        WITH plan_metrics AS (
          SELECT 
            sp.id as plan_id,
            sp.name as plan_name,
            sp.monthly_price as current_price,
            COUNT(s.id) as active_subscriptions,
            AVG(cu.monthly_usage) as avg_monthly_usage,
            AVG(cu.feature_utilization) as avg_feature_utilization,
            COUNT(*) FILTER (WHERE s.created_at >= NOW() - INTERVAL '30 days') as new_subscriptions_30d,
            COUNT(*) FILTER (WHERE s.canceled_at >= NOW() - INTERVAL '30 days') as cancellations_30d
          FROM subscription_plans sp
          LEFT JOIN subscriptions s ON sp.id = s.plan_id AND s.status = 'active'
          LEFT JOIN customer_usage cu ON s.customer_id = cu.customer_id
          WHERE sp.tenant_id = $1
            ${options.planId ? 'AND sp.id = $2' : ''}
          GROUP BY sp.id, sp.name, sp.monthly_price
        ),
        competitor_pricing AS (
          SELECT 
            plan_category,
            AVG(price) as market_avg_price,
            PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY price) as market_median_price
          FROM competitor_pricing_data 
          WHERE updated_at >= NOW() - INTERVAL '7 days'
          GROUP BY plan_category
        )
        SELECT 
          pm.*,
          cp.market_avg_price,
          cp.market_median_price,
          CASE 
            WHEN pm.avg_feature_utilization > 0.8 THEN 'high_utilization'
            WHEN pm.avg_feature_utilization > 0.5 THEN 'medium_utilization'
            ELSE 'low_utilization'
          END as utilization_segment
        FROM plan_metrics pm
        LEFT JOIN competitor_pricing cp ON pm.plan_name = cp.plan_category
      `;

      const params = options.planId ? [options.tenantId, options.planId] : [options.tenantId];
      const pricingData = await query(pricingAnalysisQuery, params, options.tenantId);

      const recommendations: PricingRecommendation[] = [];

      for (const plan of pricingData.rows) {
        const recommendation = await this.generatePricingRecommendation(plan, options);
        recommendations.push(recommendation);
      }

      const queryTime = performance.now() - startTime;

      // Cache the results
      await set(cacheKey, JSON.stringify(recommendations), this.cacheTTL);

      logger.info("Pricing recommendations generated", {
        tenantId: options.tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        recommendationsCount: recommendations.length,
      });

      return recommendations;
    } catch (error) {
      logger.error("Failed to generate pricing recommendations", {
        tenantId: options.tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Generate revenue forecasting
   */
  async getRevenueForecast(options: RevenueAnalyticsOptions): Promise<RevenueForecasting[]> {
    const cacheKey = `${this.cachePrefix}:forecast:${options.tenantId}:${options.timeRange || '90d'}`;
    
    try {
      // Try cache first
      const cached = await getJSON(cacheKey);
      if (cached) {
        return cached as RevenueForecasting[];
      }

      const startTime = performance.now();

      // Get historical revenue data for forecasting
      const historicalDataQuery = `
        SELECT 
          DATE_TRUNC('${options.granularity || 'month'}', event_timestamp) as period,
          SUM(total_revenue) as revenue,
          COUNT(DISTINCT customer_id) as unique_customers,
          AVG(total_revenue) as avg_transaction_value
        FROM revenue_events 
        WHERE tenant_id = $1 
          AND event_timestamp >= NOW() - INTERVAL '12 months'
        GROUP BY DATE_TRUNC('${options.granularity || 'month'}', event_timestamp)
        ORDER BY period
      `;

      const historicalData = await query(historicalDataQuery, [options.tenantId], options.tenantId);

      // Generate forecasts using time series analysis
      const forecasts = await this.generateRevenueForecasts(historicalData.rows, options);

      const queryTime = performance.now() - startTime;

      // Cache the results
      await set(cacheKey, JSON.stringify(forecasts), this.cacheTTL);

      logger.info("Revenue forecasts generated", {
        tenantId: options.tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        forecastPeriods: forecasts.length,
      });

      return forecasts;
    } catch (error) {
      logger.error("Failed to generate revenue forecasts", {
        tenantId: options.tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  // Helper methods
  private calculateDateRange(options: RevenueAnalyticsOptions) {
    const now = new Date();
    let dateFrom: string;
    let dateTo: string = options.dateTo || now.toISOString();

    if (options.dateFrom) {
      dateFrom = options.dateFrom;
    } else {
      const daysBack = this.getTimeRangeDays(options.timeRange || '30d');
      const fromDate = new Date(now.getTime() - (daysBack * 24 * 60 * 60 * 1000));
      dateFrom = fromDate.toISOString();
    }

    return { dateFrom, dateTo };
  }

  private getTimeRangeDays(timeRange: string): number {
    const ranges: Record<string, number> = {
      '7d': 7,
      '30d': 30,
      '90d': 90,
      '1y': 365,
    };
    return ranges[timeRange] || 30;
  }

  private async calculateRevenueGrowthRate(options: RevenueAnalyticsOptions): Promise<number> {
    // Implementation for revenue growth rate calculation
    // This would compare current period vs previous period
    return 0; // Placeholder
  }

  private async calculateNetRevenueRetention(options: RevenueAnalyticsOptions): Promise<number> {
    // Implementation for net revenue retention calculation
    return 0; // Placeholder
  }

  private async calculateExpansionMetrics(options: RevenueAnalyticsOptions): Promise<{expansion: number, contraction: number}> {
    // Implementation for expansion and contraction revenue calculation
    return { expansion: 0, contraction: 0 }; // Placeholder
  }

  private async calculateConversionRate(options: RevenueAnalyticsOptions): Promise<number> {
    // Implementation for conversion rate calculation
    return 0; // Placeholder
  }

  private async generatePricingRecommendation(planData: any, options: PricingOptimizationOptions): Promise<PricingRecommendation> {
    // Implementation for pricing recommendation generation
    return {
      planId: planData.plan_id,
      currentPrice: planData.current_price,
      recommendedPrice: planData.current_price * 1.1, // Placeholder
      priceChangePercentage: 10,
      expectedRevenueImpact: 1000,
      confidenceScore: 0.85,
      reasoning: "Based on utilization and market analysis",
      implementationRisk: 'low',
      customerSegmentImpact: {},
    };
  }

  private async generateRevenueForecasts(historicalData: any[], options: RevenueAnalyticsOptions): Promise<RevenueForecasting[]> {
    // Implementation for revenue forecasting using time series analysis
    return []; // Placeholder
  }
}
