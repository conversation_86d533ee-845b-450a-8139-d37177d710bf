import { Application } from "@oak/oak";
import { config } from "./config/config.ts";
import { initializeLogger, logger } from "./utils/logger.ts";
import { initializeDatabase, closeDatabase } from "./utils/database.ts";
import { initializeRedis, closeRedis } from "./utils/redis.ts";
import { setupMiddleware } from "./middleware/index.ts";
import { setupRoutes } from "./routes/index.ts";

// Initialize logger first
initializeLogger();

// Create Oak application
const app = new Application();

// Global error handler
app.addEventListener("error", (evt) => {
  logger.error("Unhandled application error", {
    error: evt.error?.message,
    stack: evt.error?.stack,
  });
});

// Graceful shutdown handler
async function gracefulShutdown(signal: string): Promise<void> {
  logger.info(`Received ${signal}, starting graceful shutdown...`);
  
  try {
    // Close database connections
    await closeDatabase();
    
    // Close Redis connections
    await closeRedis();
    
    logger.info("Graceful shutdown completed");
    Deno.exit(0);
  } catch (error) {
    logger.error("Error during graceful shutdown", error as Error);
    Deno.exit(1);
  }
}

// Setup signal handlers
Deno.addSignalListener("SIGINT", () => gracefulShutdown("SIGINT"));
Deno.addSignalListener("SIGTERM", () => gracefulShutdown("SIGTERM"));

// Start server
async function startServer(): Promise<void> {
  try {
    // Initialize database
    await initializeDatabase();
    logger.info("Database initialized successfully");

    // Initialize Redis
    await initializeRedis();
    logger.info("Redis initialized successfully");

    // Setup middleware
    setupMiddleware(app);

    // Setup routes
    await setupRoutes(app);

    // Start HTTP server
    logger.info(`Starting Analytics service on ${config.host}:${config.port}`, {
      environment: config.nodeEnv,
      port: config.port,
      host: config.host,
    });
    
    await app.listen({ 
      hostname: config.host, 
      port: config.port 
    });
  } catch (error) {
    logger.error("Failed to start Analytics service", error as Error);
    Deno.exit(1);
  }
}

// Handle uncaught exceptions
globalThis.addEventListener("unhandledrejection", (event) => {
  logger.error("Unhandled promise rejection", {
    reason: event.reason?.message || event.reason,
    stack: event.reason?.stack,
  });
  event.preventDefault();
});

globalThis.addEventListener("error", (event) => {
  logger.error("Uncaught exception", {
    error: event.error?.message || event.message,
    stack: event.error?.stack,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
  });
});

// Start the server
if (import.meta.main) {
  startServer();
}
