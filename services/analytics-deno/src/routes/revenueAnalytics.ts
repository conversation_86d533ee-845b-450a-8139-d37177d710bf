import { Router } from "@oak/oak";
import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { RevenueAnalyticsService } from "../services/revenueAnalyticsService.ts";
import { CustomerSuccessAnalyticsService } from "../services/customerSuccessAnalyticsService.ts";
import { EnhancedCustomerSuccessService } from "../services/enhancedCustomerSuccessService.ts";
import { UnifiedGrowthAnalyticsService } from "../services/unifiedGrowthAnalyticsService.ts";
import { authMiddleware } from "../middleware/auth.ts";
import { validateTenantAccess } from "../middleware/tenant.ts";
import { metricsMiddleware } from "../middleware/metrics.ts";

export const revenueAnalyticsRouter = new Router();

// Apply middleware
revenueAnalyticsRouter.use(authMiddleware);
revenueAnalyticsRouter.use(validateTenantAccess);
revenueAnalyticsRouter.use(metricsMiddleware);

// Initialize services
const revenueAnalyticsService = new RevenueAnalyticsService();
const customerSuccessService = new CustomerSuccessAnalyticsService();
const enhancedCustomerSuccessService = new EnhancedCustomerSuccessService();
const unifiedGrowthAnalyticsService = new UnifiedGrowthAnalyticsService();

// Validation schemas
const RevenueAnalyticsQuerySchema = z.object({
  timeRange: z.enum(['7d', '30d', '90d', '1y']).optional().default('30d'),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  granularity: z.enum(['hour', 'day', 'week', 'month']).optional().default('day'),
  currency: z.string().optional().default('USD'),
});

const PricingOptimizationQuerySchema = z.object({
  planId: z.string().optional(),
  customerSegment: z.string().optional(),
  usagePattern: z.string().optional(),
  competitorData: z.boolean().optional().default(false),
});

const CustomerSuccessQuerySchema = z.object({
  customerId: z.string().optional(),
  segment: z.string().optional(),
  timeRange: z.enum(['7d', '30d', '90d', '1y']).optional().default('30d'),
  riskThreshold: z.number().min(0).max(1).optional().default(0.5),
});

/**
 * Get comprehensive revenue metrics
 * GET /api/revenue-analytics/metrics
 */
revenueAnalyticsRouter.get("/metrics", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const queryParams = Object.fromEntries(ctx.request.url.searchParams.entries());
    const validatedParams = RevenueAnalyticsQuerySchema.parse(queryParams);

    const startTime = performance.now();

    const metrics = await revenueAnalyticsService.getRevenueMetrics({
      tenantId,
      ...validatedParams,
    });

    const queryTime = performance.now() - startTime;

    ctx.response.body = {
      success: true,
      data: metrics,
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        timeRange: validatedParams.timeRange,
        granularity: validatedParams.granularity,
        generatedAt: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    logger.info("Revenue metrics retrieved", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      totalRevenue: metrics.totalRevenue,
      mrr: metrics.monthlyRecurringRevenue,
    });
  } catch (error) {
    logger.error("Failed to get revenue metrics", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Validation failed",
        details: error.errors,
        timestamp: new Date().toISOString(),
      };
      return;
    }

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Internal server error",
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * Get pricing optimization recommendations
 * GET /api/revenue-analytics/pricing-recommendations
 */
revenueAnalyticsRouter.get("/pricing-recommendations", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const queryParams = Object.fromEntries(ctx.request.url.searchParams.entries());
    const validatedParams = PricingOptimizationQuerySchema.parse(queryParams);

    const startTime = performance.now();

    const recommendations = await revenueAnalyticsService.getPricingRecommendations({
      tenantId,
      ...validatedParams,
    });

    const queryTime = performance.now() - startTime;

    ctx.response.body = {
      success: true,
      data: recommendations,
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        recommendationsCount: recommendations.length,
        totalPotentialImpact: recommendations.reduce((sum, r) => sum + r.expectedRevenueImpact, 0),
        generatedAt: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    logger.info("Pricing recommendations generated", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      recommendationsCount: recommendations.length,
    });
  } catch (error) {
    logger.error("Failed to get pricing recommendations", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Validation failed",
        details: error.errors,
        timestamp: new Date().toISOString(),
      };
      return;
    }

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Internal server error",
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * Get revenue forecasting
 * GET /api/revenue-analytics/forecast
 */
revenueAnalyticsRouter.get("/forecast", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const queryParams = Object.fromEntries(ctx.request.url.searchParams.entries());
    const validatedParams = RevenueAnalyticsQuerySchema.parse(queryParams);

    const startTime = performance.now();

    const forecasts = await revenueAnalyticsService.getRevenueForecast({
      tenantId,
      ...validatedParams,
    });

    const queryTime = performance.now() - startTime;

    ctx.response.body = {
      success: true,
      data: forecasts,
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        forecastPeriods: forecasts.length,
        totalForecastedRevenue: forecasts.reduce((sum, f) => sum + f.forecastedRevenue, 0),
        generatedAt: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    logger.info("Revenue forecasts generated", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      forecastPeriods: forecasts.length,
    });
  } catch (error) {
    logger.error("Failed to generate revenue forecasts", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Validation failed",
        details: error.errors,
        timestamp: new Date().toISOString(),
      };
      return;
    }

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Internal server error",
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * Get customer health scores
 * GET /api/revenue-analytics/customer-health
 */
revenueAnalyticsRouter.get("/customer-health", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const queryParams = Object.fromEntries(ctx.request.url.searchParams.entries());
    const validatedParams = CustomerSuccessQuerySchema.parse(queryParams);

    const startTime = performance.now();

    const healthScores = await customerSuccessService.getCustomerHealthScores({
      tenantId,
      ...validatedParams,
    });

    const queryTime = performance.now() - startTime;

    // Calculate summary statistics
    const averageHealthScore = healthScores.reduce((sum, c) => sum + c.healthScore, 0) / healthScores.length;
    const riskDistribution = healthScores.reduce((acc, c) => {
      acc[c.riskLevel] = (acc[c.riskLevel] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    ctx.response.body = {
      success: true,
      data: healthScores,
      summary: {
        totalCustomers: healthScores.length,
        averageHealthScore: Math.round(averageHealthScore * 100) / 100,
        riskDistribution,
        highRiskCustomers: healthScores.filter(c => c.riskLevel === 'high' || c.riskLevel === 'critical').length,
      },
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    logger.info("Customer health scores calculated", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      customersAnalyzed: healthScores.length,
      averageHealthScore,
    });
  } catch (error) {
    logger.error("Failed to get customer health scores", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Validation failed",
        details: error.errors,
        timestamp: new Date().toISOString(),
      };
      return;
    }

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Internal server error",
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * Get churn predictions
 * GET /api/revenue-analytics/churn-predictions
 */
revenueAnalyticsRouter.get("/churn-predictions", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const queryParams = Object.fromEntries(ctx.request.url.searchParams.entries());
    const validatedParams = CustomerSuccessQuerySchema.parse(queryParams);

    const startTime = performance.now();

    const churnPredictions = await customerSuccessService.getChurnPredictions({
      tenantId,
      ...validatedParams,
    });

    const queryTime = performance.now() - startTime;

    // Calculate summary statistics
    const totalRevenueAtRisk = churnPredictions.reduce((sum, p) => sum + p.revenueAtRisk, 0);
    const riskDistribution = churnPredictions.reduce((acc, p) => {
      acc[p.churnRisk] = (acc[p.churnRisk] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    ctx.response.body = {
      success: true,
      data: churnPredictions,
      summary: {
        totalPredictions: churnPredictions.length,
        totalRevenueAtRisk,
        riskDistribution,
        criticalRiskCustomers: churnPredictions.filter(p => p.churnRisk === 'critical').length,
        averageChurnProbability: churnPredictions.reduce((sum, p) => sum + p.churnProbability, 0) / churnPredictions.length,
      },
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        riskThreshold: validatedParams.riskThreshold,
        generatedAt: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    logger.info("Churn predictions generated", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      predictionsCount: churnPredictions.length,
      totalRevenueAtRisk,
    });
  } catch (error) {
    logger.error("Failed to generate churn predictions", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Validation failed",
        details: error.errors,
        timestamp: new Date().toISOString(),
      };
      return;
    }

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Internal server error",
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * Get expansion opportunities
 * GET /api/revenue-analytics/expansion-opportunities
 */
revenueAnalyticsRouter.get("/expansion-opportunities", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const queryParams = Object.fromEntries(ctx.request.url.searchParams.entries());
    const validatedParams = CustomerSuccessQuerySchema.parse(queryParams);

    const startTime = performance.now();

    const expansionOpportunities = await customerSuccessService.getExpansionOpportunities({
      tenantId,
      ...validatedParams,
    });

    const queryTime = performance.now() - startTime;

    // Calculate summary statistics
    const totalPotentialRevenue = expansionOpportunities.reduce((sum, o) => sum + o.potentialRevenue, 0);
    const opportunityTypes = expansionOpportunities.reduce((acc, o) => {
      acc[o.opportunityType] = (acc[o.opportunityType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    ctx.response.body = {
      success: true,
      data: expansionOpportunities,
      summary: {
        totalOpportunities: expansionOpportunities.length,
        totalPotentialRevenue,
        opportunityTypes,
        averageExpansionProbability: expansionOpportunities.reduce((sum, o) => sum + o.expansionProbability, 0) / expansionOpportunities.length,
        highConfidenceOpportunities: expansionOpportunities.filter(o => o.confidenceScore > 0.8).length,
      },
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    logger.info("Expansion opportunities identified", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      opportunitiesCount: expansionOpportunities.length,
      totalPotentialRevenue,
    });
  } catch (error) {
    logger.error("Failed to identify expansion opportunities", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Validation failed",
        details: error.errors,
        timestamp: new Date().toISOString(),
      };
      return;
    }

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Internal server error",
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * Get revenue analytics dashboard summary
 * GET /api/revenue-analytics/dashboard
 */
revenueAnalyticsRouter.get("/dashboard", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const queryParams = Object.fromEntries(ctx.request.url.searchParams.entries());
    const validatedParams = RevenueAnalyticsQuerySchema.parse(queryParams);

    const startTime = performance.now();

    // Get all key metrics in parallel
    const [
      revenueMetrics,
      healthScores,
      churnPredictions,
      expansionOpportunities,
    ] = await Promise.all([
      revenueAnalyticsService.getRevenueMetrics({ tenantId, ...validatedParams }),
      customerSuccessService.getCustomerHealthScores({ tenantId, timeRange: validatedParams.timeRange }),
      customerSuccessService.getChurnPredictions({ tenantId, riskThreshold: 0.6 }),
      customerSuccessService.getExpansionOpportunities({ tenantId }),
    ]);

    const queryTime = performance.now() - startTime;

    // Create dashboard summary
    const dashboard = {
      revenue: revenueMetrics,
      customerHealth: {
        averageScore: healthScores.reduce((sum, c) => sum + c.healthScore, 0) / healthScores.length,
        totalCustomers: healthScores.length,
        riskDistribution: healthScores.reduce((acc, c) => {
          acc[c.riskLevel] = (acc[c.riskLevel] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
      },
      churnRisk: {
        totalAtRisk: churnPredictions.length,
        revenueAtRisk: churnPredictions.reduce((sum, p) => sum + p.revenueAtRisk, 0),
        criticalCustomers: churnPredictions.filter(p => p.churnRisk === 'critical').length,
      },
      expansion: {
        totalOpportunities: expansionOpportunities.length,
        potentialRevenue: expansionOpportunities.reduce((sum, o) => sum + o.potentialRevenue, 0),
        highConfidenceOpportunities: expansionOpportunities.filter(o => o.confidenceScore > 0.8).length,
      },
    };

    ctx.response.body = {
      success: true,
      data: dashboard,
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        timeRange: validatedParams.timeRange,
        generatedAt: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    logger.info("Revenue analytics dashboard generated", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      totalRevenue: revenueMetrics.totalRevenue,
      customersAnalyzed: healthScores.length,
    });
  } catch (error) {
    logger.error("Failed to generate revenue analytics dashboard", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Internal server error",
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * Get advanced customer segments with ML-powered clustering
 * GET /api/revenue-analytics/advanced-segments
 */
revenueAnalyticsRouter.get("/advanced-segments", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const startTime = performance.now();

    const segments = await enhancedCustomerSuccessService.getAdvancedCustomerSegments(tenantId);

    const queryTime = performance.now() - startTime;

    ctx.response.body = {
      success: true,
      data: segments,
      summary: {
        totalSegments: segments.length,
        totalCustomers: segments.reduce((sum, s) => sum + s.customerCount, 0),
        totalRevenue: segments.reduce((sum, s) => sum + s.revenueContribution, 0),
        averageHealthScore: segments.reduce((sum, s) => sum + s.averageHealthScore, 0) / segments.length,
      },
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    logger.info("Advanced customer segments calculated", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      segmentsCount: segments.length,
    });
  } catch (error) {
    logger.error("Failed to get advanced customer segments", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Internal server error",
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * Get ML-powered churn predictions with feature importance
 * GET /api/revenue-analytics/ml-churn-predictions
 */
revenueAnalyticsRouter.get("/ml-churn-predictions", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const queryParams = Object.fromEntries(ctx.request.url.searchParams.entries());
    const riskThreshold = parseFloat(queryParams.riskThreshold || "0.5");

    const startTime = performance.now();

    const predictions = await enhancedCustomerSuccessService.getMLChurnPredictions(tenantId, riskThreshold);

    const queryTime = performance.now() - startTime;

    // Calculate summary statistics
    const totalRevenueAtRisk = predictions.reduce((sum, p) => sum + p.revenueAtRisk, 0);
    const averageConfidence = predictions.reduce((sum, p) => sum + p.confidenceScore, 0) / predictions.length;

    ctx.response.body = {
      success: true,
      data: predictions,
      summary: {
        totalPredictions: predictions.length,
        totalRevenueAtRisk,
        averageConfidence: Math.round(averageConfidence * 100) / 100,
        modelVersion: predictions[0]?.modelVersion || "v2.1",
        criticalRiskCustomers: predictions.filter(p => p.churnRisk === 'critical').length,
      },
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        riskThreshold,
        generatedAt: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    logger.info("ML churn predictions generated", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      predictionsCount: predictions.length,
      modelVersion: predictions[0]?.modelVersion,
    });
  } catch (error) {
    logger.error("Failed to generate ML churn predictions", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Internal server error",
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * Get unified growth analytics with integrated Phase 2 capabilities
 * GET /api/revenue-analytics/unified-growth-analysis
 */
revenueAnalyticsRouter.get("/unified-growth-analysis", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const queryParams = Object.fromEntries(ctx.request.url.searchParams.entries());
    const timeRange = queryParams.timeRange || "30d";
    const includeForecasting = queryParams.includeForecasting === "true";
    const includeRecommendations = queryParams.includeRecommendations !== "false";
    const includeSegmentation = queryParams.includeSegmentation === "true";
    const optimizationFocus = queryParams.optimizationFocus || "all";

    const startTime = performance.now();

    const analysis = await unifiedGrowthAnalyticsService.generateUnifiedGrowthAnalysis({
      tenantId,
      timeRange,
      includeForecasting,
      includeRecommendations,
      includeSegmentation,
      optimizationFocus: optimizationFocus as 'revenue' | 'retention' | 'expansion' | 'all',
    });

    const queryTime = performance.now() - startTime;

    ctx.response.body = {
      success: true,
      data: analysis,
      summary: {
        totalRecommendations: analysis.recommendations.length,
        highPriorityActions: analysis.recommendations.filter(r => r.priority === 'high').length,
        confidenceScore: analysis.metadata.confidenceScore,
        totalOptimizationImpact: analysis.recommendations.reduce((sum, r) => sum + r.expectedImpact.revenueIncrease, 0),
      },
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        analysisType: "unified_growth_analytics",
        generatedAt: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    logger.info("Unified growth analysis generated", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      recommendationsCount: analysis.recommendations.length,
      confidenceScore: analysis.metadata.confidenceScore,
    });
  } catch (error) {
    logger.error("Failed to generate unified growth analysis", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Internal server error",
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * Get revenue optimization insights
 * GET /api/revenue-analytics/revenue-optimization-insights
 */
revenueAnalyticsRouter.get("/revenue-optimization-insights", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const queryParams = Object.fromEntries(ctx.request.url.searchParams.entries());
    const timeRange = queryParams.timeRange || "30d";

    const startTime = performance.now();

    const insights = await unifiedGrowthAnalyticsService.getRevenueOptimizationInsights(tenantId, timeRange);

    const queryTime = performance.now() - startTime;

    ctx.response.body = {
      success: true,
      data: insights,
      summary: {
        currentMRR: insights.currentPerformance.monthlyRecurringRevenue,
        optimizationOpportunities: insights.optimizationOpportunities.length,
        totalPotentialImpact: insights.optimizationOpportunities.reduce((sum, o) => sum + o.impact, 0),
        projectedAnnualIncrease: insights.projectedImpact.annualRevenueIncrease,
      },
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        timeRange,
        generatedAt: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    logger.info("Revenue optimization insights generated", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      opportunitiesCount: insights.optimizationOpportunities.length,
    });
  } catch (error) {
    logger.error("Failed to generate revenue optimization insights", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Internal server error",
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * Get cohort-revenue integration analysis
 * GET /api/revenue-analytics/cohort-revenue-analysis
 */
revenueAnalyticsRouter.get("/cohort-revenue-analysis", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const queryParams = Object.fromEntries(ctx.request.url.searchParams.entries());
    const timeRange = queryParams.timeRange || "12m";

    const startTime = performance.now();

    const analysis = await unifiedGrowthAnalyticsService.getCohortRevenueAnalysis(tenantId, timeRange);

    const queryTime = performance.now() - startTime;

    // Calculate summary statistics
    const totalCohortRevenue = analysis.cohortRevenueMetrics.reduce((sum, c) => sum + c.totalRevenue, 0);
    const averageRetentionRate = analysis.cohortRevenueMetrics.reduce((sum, c) => sum + c.retentionRate, 0) / analysis.cohortRevenueMetrics.length;

    ctx.response.body = {
      success: true,
      data: analysis,
      summary: {
        totalCohorts: analysis.cohortRevenueMetrics.length,
        totalCohortRevenue,
        averageRetentionRate: Math.round(averageRetentionRate * 100) / 100,
        bestPerformingCohorts: analysis.insights.bestPerformingCohorts.length,
        optimizationOpportunities: analysis.insights.optimizationOpportunities.length,
      },
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        timeRange,
        generatedAt: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    logger.info("Cohort revenue analysis generated", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      cohortsAnalyzed: analysis.cohortRevenueMetrics.length,
      totalRevenue: totalCohortRevenue,
    });
  } catch (error) {
    logger.error("Failed to generate cohort revenue analysis", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Internal server error",
      timestamp: new Date().toISOString(),
    };
  }
});
